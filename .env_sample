NODE_ENV=
PORT=

DB_HOST=
DB_PORT=
DB_USERNAME=
DB_PASSWORD=
DB_NAME=

PUB_SUB_REDIS_DB_PASSWORD=
PUB_SUB_REDIS_DB_HOST=
PUB_SUB_REDIS_DB_PORT=

LOG_LEVEL=

# auth variables
APP_SECRET=
TOKEN_EXPIRE_TIME=
RES_TOKEN_EXPIRE_TIME=
LIVE_CASINO_TOKEN_EXPIRE_TIME=

# aws s3 credentials
S3_REGION=
S3_ACCESS_KEY_ID=
S3_SECRET_ACCESS_KEY=
S3_BUCKET=

# dynamodb credentials
DYNAMODB_REGION=
DYNAMODB_ENDPOINT=
DYNAMODB_ACCESS_KEY_ID=
DYNAMODB_SECRET_ACCESS_KEY=
