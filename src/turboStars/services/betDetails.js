import { CASINO_PROVIDER, COMMON_RESPONSE_CODES, ENVIORNMENT, JETFAIR_INTEGRATION_CONSTANT } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  userToken: {
    type: 'string'
  },
  marketId: {
    type: 'string'
  },
  transactionId: {
    type: 'string'
  }
}

/**
 * Provides service for the Auth api in the live-spribe
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class BetDetails extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }

    try {
      // Token verification process
      const userId = await errorChecks.TokenCheck(this.args.userToken, responseObject)
      if (!userId) {
        return responseObject
      }

      const BetsTransactionModel = this.context.databaseConnection.BetsTransaction
      const BetModel = this.context.databaseConnection.BetsBet
      const BetSlipModel = this.context.databaseConnection.BetsBetslip
      const PullsEventModel = this.context.databaseConnection.PullsEvent
      const BetsBetModel = this.context.databaseConnection.BetsBet

      const provider = this.args.provider
      const betId = this.args.transactionId

      const isProduction = config.get('env') === ENVIORNMENT.PRODUCTION
      const whereCondition = {
        transactionId: betId,
        marketId: this.args.marketId,
        userId: userId,
        transactionCode: 'PlaceMatchedBet'
      }

      const providerMap = {
        turbostars: isProduction ? CASINO_PROVIDER.TURBO_STARS.PROD : CASINO_PROVIDER.TURBO_STARS.STAGE
      }

      if (providerMap[provider]) {
        whereCondition.providerId = providerMap[this.args.provider]
      }

      const betTransaction = await BetsTransactionModel.findOne({
        where: whereCondition,
        raw: true
      })

      if (!betTransaction) {
        return {
          code: COMMON_RESPONSE_CODES.SUCCESS,
          message: 'Not Data Found',
          data: {}
        }
      }

      const betSlipDetail = await BetSlipModel.findOne({
        where: { id: betTransaction.betslipId },
        include: {
          model: BetModel,
          as: 'bets',
          attributes: ['id', 'eventId', 'market', 'betId']
        },
        attributes: ['id', 'settlementStatus', 'bettype', 'stake', 'multiPrice']
      })

      if (!betSlipDetail) {
        return {
          code: COMMON_RESPONSE_CODES.SUCCESS,
          message: 'Not Data Found',
          data: {}
        }
      }

      const eventDetail = await PullsEventModel.findOne({
        where: {
          id: betSlipDetail.bets[0].eventId
        },
        attributes: ['id', 'startDate']
      })

      if (betSlipDetail.settlementStatus === 'in_game') {
        betSlipDetail.settlementStatus = 'Not Settled/ Not Declaired'
      }

      const marketDetail = {
        marketId: this.args.marketId,
        marketName: betSlipDetail.bets[0].market,
        result: betSlipDetail.settlementStatus,
        matchDate: eventDetail?.startDate ? eventDetail.startDate : null
      }

      const betsBetData = await BetsBetModel.findAll({
        where: {
          betId: betId
        },
        attributes: ['id', 'market']
      })

      const newBetList = betsBetData.reduce((acc, curr) => {
        const bet = {}

        let isBetWon = true
        let isBack = true
        if (betTransaction.journalEntry === 'DR') {
          isBetWon = false
        }
        if (betSlipDetail?.bettype === 2) {
          isBack = false
        }

        bet.betId = betId
        bet.betType = betSlipDetail?.bettype
        bet.runnerName = betTransaction?.runnerName
        bet.marketName = curr?.market
        bet.rate = parseFloat(betSlipDetail?.multiPrice)
        bet.stake = parseFloat(betSlipDetail?.stake)
        bet.betPL = parseFloat(betTransaction?.amount || 0) + parseFloat(betTransaction?.nonCashAmount || 0)
        bet.isBetWon = isBetWon
        bet.isBack = isBack
        bet.run = betSlipDetail?.run
        bet.point = null
        bet.createdDate = betTransaction?.createdAt

        acc.push(bet)

        return acc
      }, [])

      const apiResponse = {}
      apiResponse.provider = provider
      apiResponse.marketDetail = marketDetail
      apiResponse.betList = newBetList

      responseObject.code = JETFAIR_INTEGRATION_CONSTANT.SUCCESS_CODE
      responseObject.message = COMMON_RESPONSE_CODES.SUCCESS
      responseObject.data = apiResponse

      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
