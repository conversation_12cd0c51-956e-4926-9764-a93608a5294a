import { COMMON_RESPONSE_CODES, TURBO_STARS_CREDENTIALS } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  user_token: {
    type: 'string'
  }
}

/**
 * API to get turbo creds for accessing widgets.
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class WidgetCreds extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }

    try {
      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const userId = await errorChecks.TokenCheck(this.args.user_token, responseObject)
      if (!userId) {
        return responseObject
      }

      const keys = {
        iframeUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_IFRAME_URL,
        apiKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_API_KEY
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
          values[creds] = val.value
        }
      })

      delete responseObject.code
      responseObject.message = COMMON_RESPONSE_CODES.SUCCESS
      responseObject.data = {
        host: values.iframeUrl,
        cid: values.apiKey
      }

      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
