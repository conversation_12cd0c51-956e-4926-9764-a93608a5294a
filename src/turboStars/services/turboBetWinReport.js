import moment from 'moment'
import { COMMON_RESPONSE_CODES, SPORTS_PROVIDER_PROD, SPORTS_PROVIDERS_STAGE } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  userToken: {
    type: 'string'
  },
  transactionId: {
    type: 'string'
  },
  page: {
    type: 'string',
    presence: true
  },
  limit: {
    type: 'string',
    presence: true
  },
  startDate: {
    type: 'string'
  },
  endDate: {
    type: 'string'
  }
}

/**
 * Provides service for Bet Win Report For Turbo
 * @export
 * @class TurboBetWinReport
 * @extends {ServiceBase}
 */
export default class TurboBetWinReport extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }
    try {
      const {
        tenant: Tenant
      } = this.context

      const userId = await errorChecks.TokenCheck(this.args.userToken, responseObject)
      if (!userId) {
        return responseObject
      }

      const tenantId = Tenant.id

      const providerName = 'TurboStars'
      const providerId = config.get('env') === 'production' ? SPORTS_PROVIDER_PROD[providerName?.toUpperCase()] : SPORTS_PROVIDERS_STAGE[providerName?.toUpperCase()]

      let startDate = moment.utc(this.args?.startDate).startOf('day').toISOString()
      let endDate = moment.utc(this.args?.endDate).endOf('day').toISOString()

      if (this.args.transactionId) {
        startDate = ''
        endDate = ''
      }

      const limit = +this.args.limit || 25
      const offset = limit * (parseInt(this.args.page) - 1)

      const betWinTransactions = await sequelize.query(
        `with bets_data as (
            select
              transaction_id as bet_id,
              min(betslip_id) as betslip_id,
              sum(coalesce(amount, 0) + coalesce(non_cash_amount, 0) + coalesce(sports_freebet_amount, 0))
                filter (where transaction_code = 'PlaceMatchedBet') as bet_amount,
              min(created_at)
                filter (where transaction_code = 'PlaceMatchedBet') as bet_created_at,
              min(id) filter (where transaction_code = 'PlaceMatchedBet') as bets_transaction_id,
              max(id) filter (where transaction_code = 'SettledMarket') as latest_win_id,
              min(id) filter (where transaction_code = 'CancelMatchedBet') as cancel_bet_id

            from bets_transactions
            where
              tenant_id = :tenantId
              and provider_id = :providerId
              and user_id = :userId
              ${this.args?.transactionId ? 'and transaction_id = :transactionId' : ''}
              ${startDate && endDate ? 'and created_at between :startDate and :endDate' : ''}

            group by transaction_id
          ),
          bet_win_data as (
            select
              bd.*,
              bps.status as bt_payout_status,
              bps.comments as bt_payout_comments,
              (coalesce(bt.amount, 0) + coalesce(bt.non_cash_amount, 0) + coalesce(bt.sports_freebet_amount, 0)) as latest_win_amt
            from bets_data bd
              left join bets_transactions bt on (bd.latest_win_id = bt.id)
              left join bet_payout_status bps on bd.bets_transaction_id = bps.bet_id
            where
              bets_transaction_id is not null
            order by bet_created_at desc
            limit :limit offset :offset
          ),
          bets_bets_data as (
            select
              bet_id,
              json_agg(json_build_object(
                'price', price,
                'market', market,
                'match', match
              )) as bet_list
            from bets_bets
            where
              bet_id in (select bet_id from bet_win_data)
            group by bet_id
          )
          select
            bwd.*,
            case
              when bwd.cancel_bet_id is not null then 'Cancelled'
              when bwd.latest_win_id is null and bwd.cancel_bet_id is null then 'Pending'
              when bwd.latest_win_amt >= bwd.bet_amount then 'Win'
              when bwd.latest_win_amt < bwd.bet_amount then 'Lost'
            end as status,
            bbd.bet_list
          from
            bet_win_data bwd
            join bets_bets_data bbd on (bwd.bet_id = bbd.bet_id);`,
        {
          replacements: {
            providerId: providerId,
            userId: userId,
            tenantId: tenantId,
            startDate: startDate,
            endDate: endDate,
            limit: limit,
            offset: offset,
            transactionId: this.args.transactionId
          },
          type: sequelize.QueryTypes.SELECT
        })

      const countResult = await sequelize.query(
        `with bets_data as (
                select
                  transaction_id
                from bets_transactions
                where
                  tenant_id = :tenantId
                  and provider_id = :providerId
                  and user_id = :userId
                  ${this.args?.transactionId ? 'and transaction_id = :transactionId' : ''}
                  ${startDate && endDate ? 'and created_at between :startDate and :endDate' : ''}
                group by transaction_id
            )
            select count(*) as total from bets_data;`,
        {
          replacements: {
            providerId: providerId,
            userId: userId,
            tenantId: tenantId,
            startDate: startDate,
            endDate: endDate,
            transactionId: this.args.transactionId
          },
          type: sequelize.QueryTypes.SELECT
        }
      )

      const data = betWinTransactions.reduce((acc, curr) => {
        const newObj = {}
        newObj.betId = curr.bet_id
        newObj.status = curr.status
        newObj.betSlipId = curr.betslip_id
        newObj.createdAt = curr.bet_created_at
        newObj.betAmount = curr.bet_amount
        newObj.winAmount = curr.latest_win_amt
        newObj.betList = curr.bet_list
        newObj.payoutStatus = curr.bt_payout_status ? curr.bt_payout_status : 3
        newObj.payoutComment = curr.bt_payout_comments || ''
        acc.push(newObj)
        return acc
      }, [])

      responseObject.code = 0
      responseObject.success = 1
      responseObject.message = 'Success'
      responseObject.data = data
      responseObject.total = countResult[0].total

      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
