import axios from 'axios'
import * as jwt from 'jsonwebtoken'
import { COMMON_RESPONSE_CODES, TURBO_STARS_CREDENTIALS } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  user_token: {
    type: 'string'
  },
  baseUrl: {
    type: 'string'
  }
}

/**
 * API to get live and prematch from tubro stars.
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class MarketingLivePrematch extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const reqData = this.args

    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }

    try {
      const TenantCredentialModel = this.context.databaseConnection.TenantCredential

      const userId = await errorChecks.TokenCheck(reqData.user_token, responseObject)
      if (!userId) {
        return responseObject
      }

      const keys = {
        s2sApiUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_S2S_API_URL,
        apiKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_API_KEY,
        secretKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_SECRET_KEY,
        iframeUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_IFRAME_URL
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: reqData.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
          values[creds] = val.value
        }
      })

      const payload = {
        cid: values.apiKey
      }

      if (reqData.baseUrl) {
        payload.baseUrl = reqData.baseUrl
      }
      if (reqData.type) {
        payload.type = reqData.type
      }
      if (reqData.disciplineIds) {
        payload.disciplineIds = reqData.disciplineIds
      }
      if (reqData.eventIds) {
        payload.eventIds = reqData.eventIds
      }
      if (reqData.tournamentIds) {
        payload.tournamentIds = reqData.tournamentIds
      }
      if (reqData.pageSize) {
        payload.pageSize = reqData.pageSize
      }
      if (reqData.pageNumber) {
        payload.pageNumber = reqData.pageNumber
      }

      const token = jwt.sign(payload, values.secretKey, {
        algorithm: 'HS256',
        noTimestamp: true
      })

      const [jwtHeader, , jwtSignature] = token.split('.')
      const signature = [jwtHeader, '', jwtSignature].join('.')

      const config = {
        method: 'post',
        url: `${values.s2sApiUrl}/content/marketing`,
        headers: {
          'Content-Type': 'application/json',
          'x-sign-jws': signature
        },
        data: JSON.stringify(payload)
      }

      const response = await axios(config)

      delete responseObject.code
      responseObject.message = COMMON_RESPONSE_CODES.SUCCESS
      responseObject.data = {
        host: values.iframeUrl,
        data: response.data?.data
      }
      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
