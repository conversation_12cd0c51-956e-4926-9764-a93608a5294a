import { Op } from 'sequelize'
import { COMMON_RESPONSE_CODES, PROD_TENANTS, SPORTS_BET_TYPE, SPORTS_PROVIDER_PROD, SPORTS_PROVIDERS_STAGE, SPORTS_TRXN_CODES, STAGE_TENANTS } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'

const constraints = {
  userToken: {
    type: 'string'
  },
  transactionId: {
    type: 'string'
  },
  providerName: {
    type: 'string'
  }
}

/**
 * Provides service for bet results
 * @export
 * @class BetResults
 * @extends {ServiceBase}
 */
export default class BetResults extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }
    try {
      const {
        databaseConnection: {
          BetsBet: BetsBetModel,
          BetsBetslip: BetSlipModel,
          BetsTransaction: BetsTransactionModel,
          BetPayoutStatus: BetPayoutStatusModel,
          AdminUser: AdminUserModel,
          User: UserModel
        },
        tenant: Tenant
      } = this.context

      const tenantId = Tenant.id

      const providerId = config.get('env') === 'production' ? SPORTS_PROVIDER_PROD[this.args?.providerName?.toUpperCase()] : SPORTS_PROVIDERS_STAGE[this.args?.providerName?.toUpperCase()]
      const tenants = config.get('env') === 'production' ? PROD_TENANTS : STAGE_TENANTS
      const tenantDetails = Object.values(tenants).find(tenant => tenant.id === '' + tenantId)

      if (!providerId) {
        responseObject.message = 'Provider Not Found.'
        return responseObject
      }

      const betId = this.args.transactionId

      const whereCondition = {
        tenantId,
        providerId: providerId,
        [Op.or]: {
          transactionId: betId,
          reverseTransactionId: betId
        }
      }

      const betTransaction = await BetsTransactionModel.findAll({
        where: whereCondition,
        raw: true
      })

      if (!betTransaction.length) {
        responseObject.message = 'Bet Transaction Data Not Found.'
        return responseObject
      }

      const allTransactions = betTransaction.reduce((acc, curr) => {
        acc[curr.transactionCode] = curr
        return acc
      }, {})

      const betSlipId = betTransaction[0].betslipId

      const betSlipDetail = await BetSlipModel.findOne({
        where: {
          id: betSlipId
        },
        attributes: ['id', 'settlementStatus', 'bettype', 'stake', 'multiPrice'],
        raw: true
      })

      if (!betSlipDetail) {
        responseObject.message = 'Not Data Found'
        return responseObject
      }

      if (betSlipDetail.settlementStatus === 'in_game') {
        betSlipDetail.settlementStatus = 'Not Settled/ Not Declaired'
      }

      const betsBetData = await BetsBetModel.findAll({
        where: {
          betId: betId
        },
        attributes: ['id', 'market']
      })

      const marketDetail = {
        marketId: betTransaction[0].marketId,
        marketName: betsBetData[0].market,
        marketStatus: betSlipDetail.settlementStatus
      }

      let settlementStatus = SPORTS_TRXN_CODES.PLACE_BET_TXN_CODE

      const placeBetTransaction = allTransactions[SPORTS_TRXN_CODES.PLACE_BET_TXN_CODE]
      const settleBetTransaction = allTransactions[SPORTS_TRXN_CODES.SETTLE_MARKET_TXN_CODE]
      const cancelBetTransaction = allTransactions[SPORTS_TRXN_CODES.CANCEL_BET_TXN_CODE]

      if (!placeBetTransaction) {
        responseObject.message = 'Place Bet Details Not Found'
        return responseObject
      }

      if (cancelBetTransaction) {
        settlementStatus = SPORTS_TRXN_CODES.CANCEL_BET_TXN_CODE
      } else if (settleBetTransaction) {
        settlementStatus = SPORTS_TRXN_CODES.SETTLE_MARKET_TXN_CODE
      }

      const betPayoutStatus = await BetPayoutStatusModel.findOne({
        where: {
          betId: placeBetTransaction.id
        },
        attributes: ['status', 'comments']
      })

      const userDetails = await UserModel.findOne({
        where: {
          id: placeBetTransaction.userId
        },
        include: [
          {
            model: AdminUserModel,
            attributes: ['id', 'phone', 'email']
          }
        ],
        attributes: ['id'],
        raw: true,
        nest: true
      })

      const betAmount = parseFloat(placeBetTransaction?.amount || 0)
      const settlementAmount = parseFloat(settleBetTransaction?.amount || 0)

      const betDetails = {
        betId: betId,
        betType: SPORTS_BET_TYPE[betSlipDetail?.bettype],
        settlementStatus: settlementStatus,
        betAmount: betAmount,
        settlementAmount: settlementAmount,
        createdDate: placeBetTransaction?.createdAt,
        payoutStatus: betPayoutStatus?.status || 3,
        payoutComment: betPayoutStatus?.comments || '',
        status: cancelBetTransaction ? 'Cancelled' : settleBetTransaction ? settlementAmount >= betAmount ? 'Win' : 'Lost' : 'Pending',
        betQrCode: `${tenantDetails?.dataValues?.cashierDomain}bet-details?bet_id=${betId}`
      }

      const newBetList = betsBetData.reduce((acc, curr) => {
        const bet = {}
        bet.betId = betId
        bet.betType = SPORTS_BET_TYPE[betSlipDetail?.bettype]
        bet.marketName = curr?.market
        bet.rate = parseFloat(betSlipDetail?.multiPrice)
        bet.stake = parseFloat(betSlipDetail?.stake)
        bet.run = betSlipDetail?.run
        acc.push(bet)
        return acc
      }, [])

      const apiResponse = {}
      apiResponse.provider = this.args.providerName
      apiResponse.marketDetail = marketDetail
      apiResponse.betDetails = betDetails
      apiResponse.betList = newBetList
      apiResponse.customerSupportDetails = userDetails?.AdminUser

      responseObject.code = 0
      responseObject.success = 1
      responseObject.message = 'Success'
      responseObject.data = apiResponse

      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
