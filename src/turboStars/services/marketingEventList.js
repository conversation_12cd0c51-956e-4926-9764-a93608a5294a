import axios from 'axios'
import * as jwt from 'jsonwebtoken'
import { COMMON_RESPONSE_CODES, TURBO_STARS_CREDENTIALS } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  user_token: {
    type: 'string'
  }
}

/**
 * API to get live and prematch from tubro stars.
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class MarketingEventList extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const reqData = this.args

    const responseObject = {
      code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code,
      message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message
    }

    try {
      const TenantCredentialModel = this.context.databaseConnection.TenantCredential

      if (reqData.user_token) { // Allowing this api access without token as well.
        const userId = await errorChecks.TokenCheck(reqData.user_token, responseObject)
        if (!userId) {
          return responseObject
        }
      }

      const keys = {
        s2sApiUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_S2S_API_URL,
        apiKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_API_KEY,
        secretKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_SECRET_KEY
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: reqData.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
          values[creds] = val.value
        }
      })

      const payload = {
        cid: values.apiKey
      }

      const token = jwt.sign(payload, values.secretKey, {
        algorithm: 'HS256',
        noTimestamp: true
      })

      const [jwtHeader, , jwtSignature] = token.split('.')
      const signature = [jwtHeader, '', jwtSignature].join('.')

      const config = {
        method: 'post',
        url: `${values.s2sApiUrl}/content/disciplines`,
        headers: {
          'Content-Type': 'application/json',
          'x-sign-jws': signature
        },
        data: JSON.stringify(payload)
      }

      const response = await axios(config)

      const sportsList = response?.data?.data?.availableDisciplines

      if (sportsList?.length > 0) {
        sportsList.sort((a, b) => a.name.localeCompare(b.name))
      }

      delete responseObject.code
      responseObject.message = COMMON_RESPONSE_CODES.SUCCESS
      responseObject.data = {
        availableDisciplines: sportsList
      }

      return responseObject
    } catch (error) {
      return responseObject
    }
  }
}
