import { verify } from 'jsonwebtoken'
import { COMMON_RESPONSE_CODES } from '../../common/constants'
import config from '../../config/app'

export const errorChecks = {
  TokenCheck: async (token, responseObject) => {
    try {
      const authConfig = config.getProperties().auth
      const decodedToken = verify(token, authConfig.jwt_secret)
      const userId = decodedToken.id
      return userId
    } catch (error) {
      responseObject.code = COMMON_RESPONSE_CODES.TOKEN_EXPIRED.code
      responseObject.message = COMMON_RESPONSE_CODES.TOKEN_EXPIRED.message
      return false
    }
  }
}
