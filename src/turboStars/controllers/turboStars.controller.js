import { COMMON_RESPONSE_CODES } from '../../common/constants'
import { response } from '../common/response'
import BetDetails from '../services/betDetails'
import BetResults from '../services/betResults'
import MarketingEventList from '../services/marketingEventList'
import MarketingLivePrematch from '../services/marketingLivePreMatch'
import TurboBetWinReport from '../services/turboBetWinReport'
import WidgetCreds from '../services/widgetCreds'

export const widgetCreds = async (req, res) => {
  try {
    const data = await WidgetCreds.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    if (data.successful) return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}

export const marketingLivePrematch = async (req, res) => {
  try {
    const data = await MarketingLivePrematch.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    if (data.successful) return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}

export const betDetails = async (req, res) => {
  try {
    const data = await BetDetails.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    if (data.successful) return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}

export const marketingEventList = async (req, res) => {
  try {
    const data = await MarketingEventList.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}

export const betResults = async (req, res) => {
  try {
    const data = await BetResults.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}

export const turboBetWinReport = async (req, res) => {
  try {
    const data = await TurboBetWinReport.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.code, message: COMMON_RESPONSE_CODES.INTERNAL_ERROR_CODE.message })
  }
}
