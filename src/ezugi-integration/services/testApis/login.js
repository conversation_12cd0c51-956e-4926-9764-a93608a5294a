
// import bcrypt from 'bcrypt'
import * as jwt from 'jsonwebtoken'
import md5 from 'md5'
import { Op } from 'sequelize'
import ServiceBase from '../../../common/serviceBase'
import config from '../../../config/app'
// import { updateUserLastLogin } from '../../elastic-search'
import translate from '../../../lib/languageTranslate'

const constraints = {
  userNameOrEmail: {
    type: 'string'
  },
  password: {
    type: 'string',
    presence: { message: 'Password is required' }
  }
}

/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class Login extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      headers: { language },
      databaseConnection: {
        User: UserModel
      },
      tenant: Tenant
    } = this.context

    const responseObject = {}
    const whereCondition = {
      tenantId: Tenant.id,
      [Op.or]: [
        {
          userName: this.userNameOrEmail
        },
        {
          email: (this.userNameOrEmail)?.toLowerCase?.()
        }
      ]
    }

    const user = await UserModel.findOne({ where: whereCondition,
      include: [
      {
        model: UserReferralCodeModel,
        where: { tenantId: Tenant.id },
        attributes: ['referralCode'],
        required: false
      },
    ], }, )
    if (!user) {
      responseObject.message = translate('INVALID_USERNAME_OR_EMAIL', language)
      responseObject.status = 417
      return responseObject
    }
    user.referralCode = user?.UserReferralCode?.referralCode  || ''


    if (!user.active) {
      responseObject.message = translate('USER_ACCOUNT_DEACTIVATED', language)
      responseObject.status = 417
      return responseObject
    }

    if (!user.emailVerified) {
      responseObject.message = translate('EMAIL_NOT_VERIFIED', language)
      responseObject.status = 417
      return responseObject
    }

    // if (!(await bcrypt.compare(this.args.password, user.encryptedPassword))) {
    //   throw new AuthenticationError(translate('WRONG_PASSWORD', language))
    // }
    this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')

    if (md5(this.args.password) !== user.encryptedPassword) {
      responseObject.message = translate('WRONG_PASSWORD', language)
      responseObject.status = 417
      return responseObject
    }

    const credentials = await this.context.databaseConnection.TenantCredential.findAll({
      attributes: ['key', 'value'],
      where: {
        key: {
          [Op.in]: ['APP_EZUGI_OPERATOR_ID', 'APP_EZUGI_HASH_KEY']
        },
        tenantId: this.context.tenant.id
      },
      raw: true
    })

    const ezugiCredentials = credentials.reduce((platformConfig, config) => {
      platformConfig[config.key] = config.value
      return platformConfig
    }, {})

    const authConfig = config.getProperties().auth
    const resToken = await jwt.sign({ id: user.id }, ezugiCredentials.APP_EZUGI_HASH_KEY, { expiresIn: authConfig.res_token_expiry_time })

    user.signInCount += 1
    user.lastLoginDate = new Date()

    await user.save()
    // await updateUserLastLogin(user)

    const token = await jwt.sign({ id: user.id }, authConfig.jwt_secret, {
      expiresIn: authConfig.expiry_time
    })
    responseObject.data = { token, user, resToken, status: 200 }
    responseObject.status = 200
    return responseObject
  }
}
