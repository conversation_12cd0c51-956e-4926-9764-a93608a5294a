
/**
 * The function is responsible to generate the response for the live ezugi api
 * @param {*} res - object contains all the response params sent to the client
 * @param {*} payload - contains the json object to be sent
 * @param {*} hash - contains the hash of the given payload
 */
export const response = async (res, payload) => {
  // console.log('========response', payload)
  res.status(200).json(payload)
}
