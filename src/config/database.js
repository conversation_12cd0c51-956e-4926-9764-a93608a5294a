const config = require('./app')

const commonSetting = {
  username: config.get('db.username'),
  password: config.get('db.password'),
  database: config.get('db.name'),
  host: config.get('db.host'),
  port: config.get('db.port'),
  dialect: 'postgres',
  logging: (process.env.NODE_ENV !== 'production'),
  dialectOptions: {
    application_name: 'user-backend'
  }
}

export const development = {
  ...commonSetting
}

export const test = {
  ...commonSetting
}

export const staging = {
  ...commonSetting
}

export const production = {
  database: config.get('db.name'),
  replication: {
    read: {
      username: config.get('db.read_username'),
      password: config.get('db.read_password'),
      host: config.get('db.read_host'),
      port: config.get('db.read_port')
    },
    write: {
      username: config.get('db.username'),
      password: config.get('db.password'),
      host: config.get('db.host'),
      port: config.get('db.port')
    }
  },
  pool: {
    max: 1000,
    min: 5,
    idle: 5000,
    evict: 5000,
    acquire: 200000
  },
  dialect: 'postgres',
  logging: (process.env.NODE_ENV === 'development'),
  benchmark: (process.env.NODE_ENV === 'development'),
  dialectOptions: {
    application_name: 'user-backend'
  }
}
