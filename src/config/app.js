const convict = require('convict')

const config = convict({
  app: {
    name: {
      doc: 'Name of the service',
      format: String,
      default: 'user-backend'
    }
  },

  env: {
    doc: 'The application environment.',
    format: ['production', 'development', 'staging', 'test'],
    default: 'development',
    env: 'NODE_ENV'
  },
  origin: {
    doc: 'cors origin',
    format: String,
    default: 'true',
    env: 'ORIGIN'
  },
  google_recaptcha_url: {
    doc: 'cors recaptcha_url',
    format: String,
    default: '',
    env: 'RECAPTCHA_VERIFY_URL'
  },
  captcha_verification: {
    doc: 'captcha encryption key',
    format: String,
    default: '',
    env: 'CAPTCHA_ENCRYPTION_KEY'
  },
  port: {
    doc: 'The port to bind.',
    format: 'port',
    default: 8080,
    env: 'PORT'
  },
  elastic: {
    url: {
      default: 'localhost',
      env: 'ELASTICSEARCH_HOST'
    },
    user: {
      default: 'elastic',
      env: 'ELASTICSEARCH_USER'
    },
    password: {
      default: 'elastic__notrqwgf',
      env: 'ELASTICSEARCH_PASS'
    },
    port: {
      default: '8001',
      env: 'ELASTICSEARCH_PORT'
    },
    protocal: {
      default: 'http://',
      env: 'ELASTICSEARCH_PROTOCAL'
    },
    httpCrtPath: {
      default: '',
      env: 'ELASTIC_HTTP_CRT_PATH'
    }
  },
  dynamodb: {
    region: {
      doc: 'Region dynamodb located.',
      format: String,
      default: 'us-east-1',
      env: 'DYNAMODB_REGION'
    },
    endpoint: {
      doc: 'Endpoint dynamodb access.',
      format: String,
      default: 'dynamodb.us-east-1.amazonaws.com',
      env: 'DYNAMODB_ENDPOINT'
    },
    access_key_id: {
      doc: 'Access key for dynamodb.',
      format: String,
      default: '********************',
      env: 'DYNAMODB_ACCESS_KEY_ID'
    },
    secret_access_key: {
      doc: 'Secret key for dynamodb.',
      format: String,
      default: 'CNCrJ2yzf70VEXheF/vdV/ttXuUpnRrqemenhZrd',
      env: 'DYNAMODB_SECRET_ACCESS_KEY'
    }
  },
  auth: {
    jwt_secret: {
      doc: 'Secret key for token.',
      format: String,
      default: 'notronisca',
      env: 'APP_SECRET'
    },
    expiry_time: {
      doc: 'Secret key for token.',
      format: Number,
      default: 60 * 30,
      env: 'TOKEN_EXPIRE_TIME'
    },
    res_token_expiry_time: {
      doc: 'Secret key for token.',
      format: Number,
      default: 60 * 60,
      env: 'RES_TOKEN_EXPIRE_TIME'
    },
    live_casino_token_expiry_time: {
      doc: 'Secret key for token',
      format: Number,
      default: 60 * 30,
      env: 'LIVE_CASINO_TOKEN_EXPIRE_TIME'
    },
    encrytion_secret: {
      doc: 'Secret key for encrytion.',
      format: String,
      default: '',
      env: 'ENCRYPT_SECRET'
    }
  },

  pub_sub_redis_db: {
    password: {
      doc: 'Redis Database password',
      format: '*',
      default: '',
      env: 'PUB_SUB_REDIS_DB_PASSWORD'
    },
    host: {
      doc: 'Redis DB host',
      format: String,
      default: '127.0.0.1',
      env: 'PUB_SUB_REDIS_DB_HOST'
    },
    port: {
      doc: 'Redis DB PORT',
      format: 'port',
      default: 6379,
      env: 'PUB_SUB_REDIS_DB_PORT'
    }
  },

  db: {
    name: {
      doc: 'Database Name',
      format: String,
      default: '',
      env: 'DB_NAME'
    },
    username: {
      doc: 'Database user',
      format: String,
      default: 'postgres',
      env: 'DB_USERNAME'
    },
    password: {
      doc: 'Database password',
      format: '*',
      default: 'postgres',
      env: 'DB_PASSWORD'
    },
    host: {
      doc: 'DB host',
      format: String,
      default: '127.0.0.1',
      env: 'DB_HOST'
    },
    port: {
      doc: 'DB PORT',
      format: 'port',
      default: '5432',
      env: 'DB_PORT'
    },
    read_username: {
      doc: 'Database user',
      format: String,
      default: '',
      env: 'READ_DB_USERNAME'
    },
    read_password: {
      doc: 'Database password',
      format: '*',
      default: '',
      env: 'READ_DB_PASSWORD'
    },
    read_host: {
      doc: 'DB host',
      format: String,
      default: '',
      env: 'READ_DB_HOST'
    },
    read_name: {
      doc: 'Database Name',
      format: String,
      default: '',
      env: 'READ_DB_NAME'
    },
    read_port: {
      doc: 'DB PORT',
      format: 'port',
      default: '5432',
      env: 'READ_DB_PORT'
    }
  },

  log_level: {
    doc: 'level of logs to show',
    format: String,
    default: 'debug',
    env: 'LOG_LEVEL'
  },

  fast2sms_post_url: {
    doc: 'fast2sms Authentication key',
    format: String,
    default: '',
    env: 'FAST2SMS_POST_URL'
  },

  s3: {
    region: {
      doc: 'Region where s3 located.',
      format: String,
      default: '',
      env: 'S3_REGION'
    },
    access_key_id: {
      doc: 'Access key for s3.',
      format: String,
      default: '',
      env: 'S3_ACCESS_KEY_ID'
    },
    secret_access_key: {
      doc: 'Secret key for s3.',
      format: String,
      default: '',
      env: 'S3_SECRET_ACCESS_KEY'
    },
    bucket: {
      doc: 'Bucket used in S3',
      format: String,
      default: '',
      env: 'S3_BUCKET'
    }
  },

  index: {
    transactions_index_name: {
      doc: 'transactions index',
      format: String,
      default: 'transactions_development',
      env: 'TRANSACTIONS_INDEX_NAME'
    },
    users_index_name: {
      doc: 'users index',
      format: String,
      default: 'users_development',
      env: 'USERS_INDEX_NAME'
    }
  }
})

config.validate({ allowed: 'strict' })

module.exports = config
