// import { FONEPESA_CANCELLED_ERROR_CODES, FONEPESA_INTERNAL_ERROR_CODES, FONEPESA_USER_ERROR_CODES } from '../../common/constants'

import addLog from '../../../../common/addLog'
import { SUBSCRIPTION_CHANNEL } from '../../../../common/constants'
import updateLog from '../../../../common/updateLog'
import { sequelize } from '../../../../db/models'
import { response } from '../../../../ezugi-integration/common/response'
import paymentCallback from '../services/paymentCallback'

/**
 * process payment using fhonepaisa
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const zoviPayInPaymentCallback = async (req, res) => {
  console.log('------zoviPayInPaymentCallback----req.body------', req.body)
  const service = 'userbackend-payin-paymentCallback'
  const createLogId = await addLog(req, service)
  try {
    req.sequelizeTransaction = await sequelize.transaction()
    const data = await paymentCallback.execute(req.body, req)
    await req.sequelizeTransaction.commit()
    try {
      req.pubSub.publish(
        SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
        { QueueLog: { queueLogId: data.result.queueLogId } }
      )
    } catch (e) {

    }
    delete data.result.queueLogId
    await updateLog({ id: createLogId, response: data.result, responseCode: res.statusCode, isSuccess: true })
    return response(res, data.result)
  } catch (error) {
    await req.sequelizeTransaction.rollback()
    await updateLog({ id: createLogId, response: { 'Error code': 1, description: 'internal server error', errorMsg: error.message }, responseCode: res.statusCode })
    const payload = { error: 1, errorDescription: 'Internal server error', success: 0 }
    return response(res, payload)
  }
}
