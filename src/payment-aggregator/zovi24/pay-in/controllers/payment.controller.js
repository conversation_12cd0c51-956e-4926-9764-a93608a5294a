// import { FONEPESA_CANCELLED_ERROR_CODES, FONEPESA_INTERNAL_ERROR_CODES, FONEPESA_USER_ERROR_CODES } from '../../common/constants'

import { response } from '../../../../ezugi-integration/common/response'
import ProcessPayment from '../services/processPayment'

/**
 * process payment using fhonepaisa
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const zoviPayInProcessPayment = async (req, res) => {
  const data = await ProcessPayment.execute(req.body, req)
  return response(res, data.result)
}
