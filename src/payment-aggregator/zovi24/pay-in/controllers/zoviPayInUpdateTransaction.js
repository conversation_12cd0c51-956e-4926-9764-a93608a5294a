// import { FONEPESA_CANCELLED_ERROR_CODES, FONEPESA_INTERNAL_ERROR_CODES, FONEPESA_USER_ERROR_CODES } from '../../common/constants'

import { response } from '../../../../ezugi-integration/common/response'
import updateTransaction from '../services/updateTransaction'
import { sequelize } from '../../../../db/models'
import { SUBSCRIPTION_CHANNEL } from '../../../../common/constants'
import addLog from '../../../../common/addLog'
import updateLog from '../../../../common/updateLog'

/**
 * process payment using fhonepaisa
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const zoviPayInUpdateTransaction = async (req, res) => {
  console.log('------zoviPayInUpdateTransaction----req.body------', req.body)
  const service = 'userbackend-payin-updateTransaction'
  const createLogId = await addLog(req, service)
  try {
    req.sequelizeTransaction = await sequelize.transaction()
    const data = await updateTransaction.execute(req.body, req)
    await req.sequelizeTransaction.commit()
    req.pubSub.publish(
      SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
      { QueueLog: { queueLogId: data.result.queueLogId } }
      )
    delete data.result.queueLogId
    await updateLog({ id: createLogId, response: data.result, responseCode: res.statusCode, isSuccess: true })
    return response(res, data.result)
  } catch (error){
    await req.sequelizeTransaction.rollback()
    await updateLog({ id: createLogId, response: { 'Error code': 1, description: 'internal server error', errorMsg: error.message }, responseCode: res.statusCode })
    const payload = { error: 1, errorDescription: 'Internal server error', success: 0 }
    return response(res, payload)
  }
}
