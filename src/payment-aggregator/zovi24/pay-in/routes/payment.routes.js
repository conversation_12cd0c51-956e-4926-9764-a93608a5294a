import { Router } from 'express'
import { zoviPayInProcessPayment } from '../controllers/payment.controller'
import { zoviPayInPaymentCallback } from '../controllers/zoviPayInPaymentCallback'
import { zoviPayInUpdateTransaction } from '../controllers/zoviPayInUpdateTransaction'
const auth = require('../middleware/auth')
const router = Router()

router.route('/processPayment').post(auth, zoviPayInProcessPayment)
router.route('/paymentCallback').post(zoviPayInPaymentCallback)
router.route('/updateTransaction').post(zoviPayInUpdateTransaction)

export default router
