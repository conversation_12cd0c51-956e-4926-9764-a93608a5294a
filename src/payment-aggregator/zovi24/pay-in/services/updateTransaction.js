import { Op } from 'sequelize'
import {
  DEPOSIT_REQUEST_STATUS,
  QUEUE_WORKER_CONSTANT,
  SUBSCRIPTION_CHANNEL,
  TRANSACTION_TYPES
} from '../../../../common/constants'
import currencyConversion from '../../../../common/currencyConversion'
import ServiceBase from '../../../../common/serviceBase'
import userCurrencyExchange from '../../../../common/userCurrencyExchange'
import { walletLocking } from '../../../../common/walletLocking'
import { depositBonusCheck } from '../common/depositBonusCheck'
import paymentCallbackResponse from '../common/paymentCallbackResponse'
/**
 * process payment
 * @export
 * @class paymentCallback
 * @extends {ServiceBase}
 */
const constraints = {
  TransactionId: {
    type: 'string',
    presence: { message: '' }
  },
  Description: {
    type: 'string'
  },
  ResMessage: {
    type: 'string'
  },
  GatewayName: {
    type: 'string'
  },
  Amount: {
    type: 'integer',
    presence: { message: '' }
  },
  Order_Id: {
    type: 'string',
    presence: { message: '' }
  },
  ClientCode: {
    type: 'string'
  },
  IsSuccess: {
    type: 'boolean',
    presence: { message: '' }
  }
}
export default class updateTransaction extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: bodyArgs,
      context: {
        databaseConnection: {
          Transaction: TransactionModel,
          Wallet: WalletModel,
          DepositRequest: DepositRequestModel,
          User: UserModel,
          QueueLog: QueueLogModel
        }
      }
    } = this

    const { sequelizeTransaction } = this.context

    bodyArgs.Amount = +(bodyArgs.Amount / 100)
    const responseObject = paymentCallbackResponse(this.args)
    try {
      const orderDetails = await DepositRequestModel.findOne({
        where: {
          order_id: bodyArgs.TransactionId,
          status: {
            [Op.in]: [
              DEPOSIT_REQUEST_STATUS.OPEN,
              DEPOSIT_REQUEST_STATUS.IN_PROCESS
            ]
          }
        },
        transaction: sequelizeTransaction,
        useMaster: true
      })
      if (!orderDetails) {
        responseObject.error = 1
        responseObject.errorDescription = 'No Transaction found for this txnId'
        responseObject.success = 0
        return responseObject
      }

      let user = await UserModel.findOne({
        where: {
          id: orderDetails.userId
        },
        include: WalletModel,
        useMaster: true
      })

      // failed
      if (bodyArgs.IsSuccess === false) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.FAILED
        const transactionStatus = TRANSACTION_TYPES.FAILED
        //const errorDescription = 'Transaction Failed'
        const errorCode = 1
        const transactionStatusString = 'failed'
        const success = false

        let transactionFailedObject = {
          targetWalletId: user.Wallet.id,
          targetCurrencyId: user.Wallet.currencyId,
          amount: bodyArgs.Amount,
          conversionRate: await userCurrencyExchange(this.context, user.Wallet.currencyId),
          targetBeforeBalance: user.Wallet.amount,
          targetAfterBalance: user.Wallet.amount,
          comments: bodyArgs.Description,
          actioneeId: orderDetails.userId,
          actioneeType: 'User',
          tenantId: user.tenantId,
          timestamp: new Date().getTime(),
          transactionType: transactionStatus,
          transactionId: bodyArgs.TransactionId,
          debitTransactionId: bodyArgs.Order_Id,
          paymentMethod: bodyArgs.PaymentMode,
          paymentProviderId: orderDetails.paymentProviderId,
          //errorDescription: errorDescription,
          errorCode: errorCode,
          status: transactionStatusString,
          success: success
        }

        transactionFailedObject = await currencyConversion(this.context, transactionFailedObject, user.Wallet, bodyArgs.Amount)

        const skipTransactionHook = true
        const txn1 = await TransactionModel.create(transactionFailedObject, { transaction: sequelizeTransaction, skipTransactionHook })

        const txnIds = []
        if (txn1) {
          txnIds.push(txn1.id)
        }

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.TYPE,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }

        const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
        responseObject.queueLogId = queueLog.id

        await orderDetails.save()

        // Create Transaction history with failed status

        responseObject.error = 0
        responseObject.errorDescription = 'Updated Failed Transaction'
        responseObject.success = 1
        return responseObject
      }
      // success
      if (bodyArgs.IsSuccess === true) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      }

      const userWallet = await walletLocking(this.context, user)
      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

      user = { ...user.dataValues, Wallet: userWallet }

      userWallet.amount = +userWallet.amount

      let transactionObject = {
        targetWalletId: userWallet.id,
        targetCurrencyId: userWallet.currencyId,
        amount: bodyArgs.Amount,
        conversionRate: await userCurrencyExchange(this.context, userWallet.currencyId),
        targetBeforeBalance: userWallet.amount,
        targetAfterBalance: userWallet.amount + bodyArgs.Amount,
        comments: bodyArgs.Description,
        actioneeId: orderDetails.userId,
        actioneeType: 'User',
        tenantId: user.tenantId,
        timestamp: new Date().getTime(),
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: bodyArgs.TransactionId,
        debitTransactionId: bodyArgs.Order_Id,
        paymentMethod: bodyArgs.PaymentMode,
        paymentProviderId: orderDetails.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true
      }
      userWallet.amount = +userWallet.amount + bodyArgs.Amount
      const skipWalletHook = true
      await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

      transactionObject = await currencyConversion(this.context, transactionObject, userWallet, bodyArgs.Amount)
      const skipTransactionHook = true
      const txn2 = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })

      const txnIds = []
      if (txn2) {
        txnIds.push(txn2.id)
      }

      await depositBonusCheck(this.context, bodyArgs.Amount, user, txnIds)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
      responseObject.queueLogId = queueLog.id

      await orderDetails.save()

      this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, {
        UserWalletBalance: {
          walletBalance: userWallet.amount,
          userId: user.id,
          nonCashAmount: userWallet.nonCashAmount
        }
      })
      this.context.pubSub.publish(
        SUBSCRIPTION_CHANNEL.USER_DEPOSIT_NOTIFICATION,
        {
          userWithdrawAmount: bodyArgs.Amount,
          UserWalletBalance: {
            walletBalance: userWallet.amount,
            userId: user.id,
            nonCashAmount: userWallet.nonCashAmount
          }
        }
      )
      responseObject.error = 0
      responseObject.errorDescription = 'This payment has been authorized & updated'
      responseObject.success = 1
      return responseObject
    } catch (error) {
      console.log('------updateTransaction service ----error------', error)
      throw new Error(error)
    }
  }
}
