import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../../common/checkLimit'
import ErrorLogHelper from '../../../../common/errorLog'
import ServiceBase from '../../../../common/serviceBase'
const axios = require('axios')
/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}
export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          DepositRequest: DepositRequestModel,
          tenantPaymentConfiguration: tenantPaymentConfigurationModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this
    const depositType = 'payment_providers'
    const responseObject = {
      error: 0,
      errorDescription: '',
      success: 1
    }
    const userId = userDetail.id
    const paymentProviderId = this.args.paymentProviderId
    const orderId = uuidv4().toUpperCase()
    try {
      const allowed = await checkLimit.depositLimitCheck(this.context, userId, this.args.amount)
      if (allowed === 'Daily' || allowed === 'Weekly' || allowed === 'Monthly') {
        responseObject.error = 1
        responseObject.errorDescription = `${allowed} Deposit Limit exceeded`
        responseObject.success = 0
        return responseObject
      }

      // Fetch Payment Provider Details (Keys and URl of select Payment Provider ID)
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }

      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        responseObject.error = 1
        responseObject.errorDescription = 'No Payment Provider Found'
        responseObject.success = 0
        return responseObject
      }

      // Whatever amount is come we into 100 in original amount
      const amount = this.args.amount
      this.args.amount = amount * 100

      // Zovi  Payment Gateway Request Request Send
      const data = JSON.stringify({
        Amount: this.args.amount,
        TransactionId: orderId,
        ClientUsername: userDetail.userName,
        ClientCode: paymentProviders.providerKeyValues.clientCode, // fetch from database
        Description: 'Deposit Request',
        Email: userDetail.email,
        MobileNo: userDetail.phone
      })

      const configs = {
        method: 'post',
        maxBodyLength: Infinity,
        url: paymentProviders.providerKeyValues.paymentUrl, // fetch from database
        headers: { PrivateKey: paymentProviders.providerKeyValues.privateKey }, // fetch from database
        data: data
      }
      // console.log('============== ProcessPayment ===== configs ===========', configs)
      const apiResponse = await axios.request(configs)
        .then(async (response) => {
          // console.log('============== ProcessPayment Response ===== for the ===========', response)

          if (response.data.Status.code !== 0) {
            responseObject.success = 0
            responseObject.error = 1
            responseObject.errorDescription = response.data.Status.returnMessage
            return responseObject
          }
          // if everything good, we send final response to API
          await DepositRequestModel.create({ orderId, userId, paymentProviderId, amount, tenantId, depositType })

          responseObject.success = 1
          responseObject.url = response.data.Url
          return responseObject
        })
        .catch((_error) => {
          console.log('============== ProcessPayment _error ===== for the ===========', _error)
          responseObject.error = 1
          responseObject.errorDescription = 'Something Went Wrong'
          responseObject.success = 0
          return responseObject
          // throw new Error('Erros:' + error)
        })
      return apiResponse
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userId, tenantId })
      throw new Error(err)
    }
  }
}
