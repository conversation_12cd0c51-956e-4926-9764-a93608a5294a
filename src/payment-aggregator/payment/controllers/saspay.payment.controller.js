import SaspayProcessPayment from '../services/saspayProcessPayment'

/**
 * process payment using saspay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const saspayProcessPayment = async (req, res) => {
  const data = await SaspayProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
