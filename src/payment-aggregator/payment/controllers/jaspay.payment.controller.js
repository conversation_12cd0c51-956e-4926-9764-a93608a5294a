import JaspayProcessPayment from '../services/jaspayProcessPayment';

/**
 * process payment using jaspay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const jaspayProcessPayment = async (req, res) => {
  const data = await JaspayProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
