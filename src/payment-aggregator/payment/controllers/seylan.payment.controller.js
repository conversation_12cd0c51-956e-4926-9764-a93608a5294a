import SeylanrocessPayment from '../services/seylanProcessPayment'

/**
 * process payment using zenxpay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const seylanProcessPayment = async (req, res) => {
  const data = await SeylanrocessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
