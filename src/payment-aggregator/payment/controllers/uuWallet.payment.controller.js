import UUWalletGetUserAddress from '../services/uuWalletGetUserAddress';

/**
 * UU Wallet Controller
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const uuWalletGetUserAddress = async (req, res) => {
  const data = await UUWalletGetUserAddress.execute(req.body, req)
  return res.status(200).json(data.result)
}
