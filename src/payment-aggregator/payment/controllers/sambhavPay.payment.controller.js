import SambhavPayProcessPayment from '../services/sambhavPayProcessPayment'

/**
 * process payment using sambhavPay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const sambhavPayProcessPayment = async (req, res) => {
  const data = await SambhavPayProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
