import XamaxProcessPayment from '../services/xamaxProcessPayment';

/**
 * process payment using xamax
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const xamaxProcessPayment = async (req, res) => {
  const data = await XamaxProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
