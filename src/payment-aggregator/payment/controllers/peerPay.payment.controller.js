import PeerPayProcessPayment from '../services/peerPayProcessPayment'

/**
 * process payment using peerPay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const peerPayProcessPayment = async (req, res) => {
  const data = await PeerPayProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
