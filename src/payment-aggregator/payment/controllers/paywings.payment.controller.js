import PaywingsProcessPayment from '../services/paywingsProcessPayment'

/**
 * process payment using paywings
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const paywingsProcessPayment = async (req, res) => {
  const data = await PaywingsProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
