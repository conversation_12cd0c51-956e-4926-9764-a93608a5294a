import PaycookiesProcessPayment from '../services/paycookiesProcessPayment'

/**
 * process payment using paycookies
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const paycookiesProcessPayment = async (req, res) => {
  const data = await PaycookiesProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
