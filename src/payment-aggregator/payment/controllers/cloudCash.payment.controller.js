import CloudCashProcessPayment from '../services/cloudCashProcessPayment';

/**
 * process payment using cloudcash
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const cloudCashProcessPayment = async (req, res) => {
  const data = await CloudCashProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
