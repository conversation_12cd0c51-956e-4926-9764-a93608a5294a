import AckoPayProcessPayment from '../services/ackopayProcessPayment'

/**
 * process payment using ackopay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const ackoPayProcessPayment = async (req, res) => {
  const data = await AckoPayProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
