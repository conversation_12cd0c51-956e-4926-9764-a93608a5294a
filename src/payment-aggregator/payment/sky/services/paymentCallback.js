import { Op } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import {
  DEPOSIT_REQUEST_STATUS,
  QUEUE_WORKER_CONSTANT,
  SKY_PG_INTEGRATION_CONSTANT,
  SUBSCRIPTION_CHANNEL,
  TRANSACTION_TYPES
} from '../../../../common/constants'
import currencyConversion from '../../../../common/currencyConversion'
import ServiceBase from '../../../../common/serviceBase'
import userCurrencyExchange from '../../../../common/userCurrencyExchange'
import { walletLocking } from '../../../../common/walletLocking'
import createHash from '../common/createHash'
import { depositBonusCheck } from '../common/depositBonusCheck'

/**
 * payment callback
 * @export
 * @class paymentCallback
 * @extends {ServiceBase}
 */
const constraints = {
  Merchant_RefID: {
    type: 'string',
    presence: { message: '' }
  },
  Gateway_RefID: {
    type: 'string',
    presence: { message: '' }
  },
  Bank_RefID: {
    type: 'string',
    presence: { message: '' }
  },
  StatusCode: {
    type: 'string',
    presence: { message: '' }
  },
  TxnStatus: {
    type: 'string',
    presence: { message: '' }
  },
  Amount: {
    type: 'string',
    presence: { message: '' }
  },
  MDR: {
    type: 'string',
    presence: { message: '' }
  },
  GST: {
    type: 'string',
    presence: { message: '' }
  },
  Message: {
    type: 'string',
    presence: { message: '' }
  },
  CustomerVPA: {
    type: 'string',
    presence: { message: '' }
  },
  CustomerName: {
    type: 'string',
    presence: { message: '' }
  }
}

export default class paymentCallback extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: bodyArgs,
      context: {
        databaseConnection: {
          Transaction: TransactionModel,
          Wallet: WalletModel,
          DepositRequest: DepositRequestModel,
          User: UserModel,
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          QueueLog: QueueLogModel
        }
      }
    } = this

    const { sequelizeTransaction } = this.context

    const responseObject = {}

    try {
      const orderDetails = await DepositRequestModel.findOne({
        attributes: ['id', 'tenantId', 'paymentProviderId', 'userId', 'status', 'remark'],
        where: {
          order_id: bodyArgs.Gateway_RefID,
          tracking_id: bodyArgs.Merchant_RefID,
          amount: bodyArgs.Amount,
          status: {
            [Op.in]: [
              DEPOSIT_REQUEST_STATUS.OPEN,
              DEPOSIT_REQUEST_STATUS.IN_PROCESS
            ]
          }
        },
        transaction: sequelizeTransaction,
        useMaster: true
      })
      if (!orderDetails) {
        responseObject.status = SKY_PG_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = SKY_PG_INTEGRATION_CONSTANT.INVALID_REQUEST
        return responseObject
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: orderDetails.tenantId,
          providerId: orderDetails.paymentProviderId
        }
      })

      const payinAPIKey = paymentProviders.providerKeyValues.payinAPIKey

      const hash = this.context.headers.hash
      const recreatedHash = createHash(bodyArgs, payinAPIKey)
      if (!(hash === recreatedHash)) {
        responseObject.status = SKY_PG_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = SKY_PG_INTEGRATION_CONSTANT.INVALID_HASH
        return responseObject
      }

      // Get User Details
      let user = await UserModel.findOne({
        attributes: ['id', 'tenantId', 'userName'],
        where: {
          id: orderDetails.userId
        },
        include: {
          model: WalletModel,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount']
        }
      })

      const depositAmount = parseFloat(bodyArgs.Amount)

      const { SUCCESS_STATUS_CODE, FAILED_STATUS_CODE, PENDING_STATUS_CODE, CHARGEBACK_STATUS_CODE, EXCEPTION_STATUS_CODE, REFUNDED_STATUS_CODE } = SKY_PG_INTEGRATION_CONSTANT

      // success
      if (bodyArgs.StatusCode === SUCCESS_STATUS_CODE) {
        user.Wallet.amount = parseFloat(user.Wallet.amount)
        orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED

        const conversionRate = await userCurrencyExchange(this.context, user.Wallet.currencyId)

        let transactionObject = {
          targetWalletId: user.Wallet.id,
          targetCurrencyId: user.Wallet.currencyId,
          amount: depositAmount,
          conversionRate,
          targetBeforeBalance: user.Wallet.amount,
          actioneeId: orderDetails.userId,
          actioneeType: 'User',
          tenantId: user.tenantId,
          transactionType: TRANSACTION_TYPES.DEPOSIT,
          transactionId: uuidv4(),
          metaData: {
            Merchant_RefID: bodyArgs.Merchant_RefID,
            Gateway_RefID: bodyArgs.Gateway_RefID,
            Bank_RefID: bodyArgs.Bank_RefID,
            MDR: bodyArgs.MDR,
            GST: bodyArgs.GST,
            CustomerVPA: bodyArgs.CustomerVPA,
            CustomerName: bodyArgs.CustomerName
          },
          paymentMethod: SKY_PG_INTEGRATION_CONSTANT.PAYMENT_METHOD,
          paymentProviderId: orderDetails.paymentProviderId,
          //errorDescription: 'Completed Successfully',
          errorCode: 0,
          status: 'success',
          success: true,
          comments: SKY_PG_INTEGRATION_CONSTANT.SKY_TRANSACTION_COMMENTS
        }

        const userWallet = await walletLocking(this.context, user)
        await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

        user = { ...user.dataValues, Wallet: userWallet }
        userWallet.amount = userWallet.amount + depositAmount

        const skipWalletHook = true
        await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

        transactionObject = await currencyConversion(this.context, transactionObject, userWallet, depositAmount)
        transactionObject.targetAfterBalance = userWallet.amount
        const skipTransactionHook = true
        const txn = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })

        const txnIds = []
        if (txn) {
          txnIds.push(txn.id)
        }

        await depositBonusCheck(this.context, depositAmount, user, txnIds)

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.TYPE,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }

        const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
        responseObject.queueLogId = queueLog.id
      } else if (bodyArgs.StatusCode === FAILED_STATUS_CODE) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.FAILED
      } else if ([PENDING_STATUS_CODE, EXCEPTION_STATUS_CODE, CHARGEBACK_STATUS_CODE].includes(bodyArgs.StatusCode)) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.IN_PROCESS
      } else if (bodyArgs.StatusCode === REFUNDED_STATUS_CODE) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.IN_PROCESS
        orderDetails.remark = 'Transaction Refunded'
      }

      await orderDetails.save({ transaction: sequelizeTransaction })

      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, {
          UserWalletBalance: {
            walletBalance: user.Wallet.amount,
            userId: user.id,
            nonCashAmount: user.Wallet.nonCashAmount
          }
        })

        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.USER_PG_DEPOSIT_NOTIFICATION,
          {
            UserPgDepositNotification:
            {
              userWithdrawAmount: depositAmount,
              success: bodyArgs.StatusCode === SKY_PG_INTEGRATION_CONSTANT.RESPCODE_SUCCESS_CODE,
              message: `${SKY_PG_INTEGRATION_CONSTANT.SUCCESS} ${bodyArgs.TxnStatus}`,
              userWalletBalanceDetail: {
                walletBalance: user.Wallet.amount,
                userId: user.id,
                nonCashAmount: user.Wallet.nonCashAmount
              }
            }
          })
      } catch (error) {
        console.log('------sky PG subscription callback------', error)
        // throw new Error(error)
      }

      responseObject.status = SKY_PG_INTEGRATION_CONSTANT.SUCCESS_CODE
      responseObject.message = `${SKY_PG_INTEGRATION_CONSTANT.SUCCESS} ${bodyArgs.TxnStatus}`
      return responseObject
    } catch (error) {
      console.log('------paymentCallback service ----error------', error)
      throw new Error(error)
    }
  }
}
