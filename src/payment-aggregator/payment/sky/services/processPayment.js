import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../../common/checkLimit'
import { SKY_PG_INTEGRATION_CONSTANT } from '../../../../common/constants'
import ServiceBase from '../../../../common/serviceBase'
import createHash from '../common/createHash'
import processPaymentResponse from '../common/processPaymentResponse'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const userId = userDetail.id
    const paymentProviderId = this.args.paymentProviderId
    const amount = parseFloat(this.args.amount)

    if (amount < 1) {
      responseObject.error = 1
      responseObject.errorDescription = SKY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      responseObject.success = 0
      return responseObject
    }

    try {
      // Deposit limit check
      const allowed = await checkLimit.depositLimitCheck(this.context, userId, amount)
      if (allowed === SKY_PG_INTEGRATION_CONSTANT.DAILY_DEPOSIT_LIMIT || allowed === SKY_PG_INTEGRATION_CONSTANT.WEEKLY_DEPOSIT_LIMIT || allowed === SKY_PG_INTEGRATION_CONSTANT.MONTHLY_DEPOSIT_LIMIT) {
        responseObject.error = 1
        responseObject.errorDescription = `${allowed} ${SKY_PG_INTEGRATION_CONSTANT.DEPOSIT_LIMIT_EXCEED}`
        responseObject.success = 0
        return responseObject
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        responseObject.error = 1
        responseObject.errorDescription = SKY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      const { paymentUrl, merchantUserId, payinAPIKey } = paymentProviders.providerKeyValues
      if (!paymentUrl || !merchantUserId || !payinAPIKey) {
        responseObject.error = 1
        responseObject.errorDescription = SKY_PG_INTEGRATION_CONSTANT.CONFIGURATION_KEYS_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      // Checking user phone number and email
      const { phone, email } = userDetail
      const missingField = !phone ? 'Phone' : !email ? 'Email' : null
      if (missingField) {
        responseObject.error = 1
        responseObject.errorDescription = `${missingField} ${SKY_PG_INTEGRATION_CONSTANT.MISSING_FIELD_REQUIRED}`
        responseObject.success = 0
        return responseObject
      }

      if (!userDetail.firstName) {
        responseObject.success = 0
        responseObject.error = 1
        responseObject.errorDescription = SKY_PG_INTEGRATION_CONSTANT.USER_NAME_VALIDATION_ERROR
        return responseObject
      }

      const name = userDetail?.lastName ? `${userDetail?.firstName} ${userDetail?.lastName}` : `${userDetail?.firstName}`

      const requestData = {
        Merchant_RefID: uuidv4().replace(/[^\w]/g, ''),
        Customer_Name: name,
        Customer_Mobile: userDetail.phone,
        Customer_Email: userDetail.email,
        Amount: (this.args.amount).toString(),
        Remark: 'test' // to do remove
      }

      requestData.Hash = createHash(requestData, payinAPIKey)

      const makeApiCall = async () => {
        try {
          const credentials = Buffer.from(`${merchantUserId}:${payinAPIKey}`).toString('base64')
          const response = await axios.post(paymentUrl, requestData, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Basic ${credentials}`
            }
          })

          const { RESPCODE_SUCCESS_CODE, RESPCODE_FAILED_CODE, RESPCODE_PENDING_CODE, REQUEST_REJECTED, REQUEST_PENDING } = SKY_PG_INTEGRATION_CONSTANT
          const respCode = response.data.respCode

          if (respCode === RESPCODE_SUCCESS_CODE) { // success
            const depositType = SKY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
            await DepositRequestModel.create({ orderId: response.data.data.Gateway_RefID, trackingId: response.data.data.Merchant_RefID, userId, paymentProviderId, amount, tenantId, depositType })
            responseObject.success = 1
            responseObject.url = response.data.data.Intent_URL
            return responseObject
          } else if (respCode === RESPCODE_FAILED_CODE) { // failed
            responseObject.errorDescription = REQUEST_REJECTED
          } else if (respCode === RESPCODE_PENDING_CODE) { // pending
            responseObject.errorDescription = REQUEST_PENDING
          } else {
            responseObject.errorDescription = response.data.respMsg
          }

          responseObject.success = 0
          responseObject.error = 1
          return responseObject
        } catch (error) {
          responseObject.error = 1
          responseObject.errorDescription = 'Something Went Wrong'
          responseObject.success = 0
          return responseObject
        }
      }

      await makeApiCall()
      return responseObject
    } catch (err) {
      throw new Error(err)
    }
  }
}
