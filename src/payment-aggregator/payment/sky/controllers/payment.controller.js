import ProcessPayment from '../services/processPayment'

/**
 * process payment using sky
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const skyProcessPayment = async (req, res) => {
  const data = await ProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
