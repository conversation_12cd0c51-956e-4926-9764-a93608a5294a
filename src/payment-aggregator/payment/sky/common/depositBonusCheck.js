import { Op, Sequelize } from 'sequelize'
import { BONUS_STATUS, BONUS_TYPES, SPORTS, TRANSACTION_TYPES } from '../../../../common/constants'
import currencyConversion from '../../../../common/currencyConversion'
import userCurrencyExchange from '../../../../common/userCurrencyExchange'

export const depositBonusCheck = async (context, amount, user, txnIds) => {
  const {
    databaseConnection: {
      Bonus: BonusModel,
      UserBonus: UserBonusModel,
      DepositBonusSetting: DepositBonusSettingModel,
      Transaction: TransactionModel,
      BetsTransaction: BetsTransactionModel
    }
  } = context

  const { sequelizeTransaction } = context

  const userActiveDepositBonus = await UserBonusModel.findOne({
    attributes: ['id', 'bonusId'],
    where: {
      status: BONUS_STATUS.ACTIVE,
      [Op.or]: [{ kind: BONUS_TYPES.DEPOSIT }, { kind: BONUS_TYPES.DEPOSIT_SPORTS }],
      expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
      bonusAmount: 0,
      userId: user.id
    }
  })

  if (!userActiveDepositBonus) {
    return null
  }

  const bonus = await BonusModel.findOne({
    attributes: ['id', 'percentage', 'kind'],
    where: {
      id: userActiveDepositBonus.bonusId,
      enabled: true
    },
    include: {
      model: DepositBonusSettingModel,
      attributes: ['id', 'minDeposit', 'maxDeposit', 'maxBonus', 'rolloverMultiplier', 'validForDays']
    }
  })

  // bonus not found check
  if (!bonus) {
    userActiveDepositBonus.status = BONUS_STATUS.EXPIRED
    await userActiveDepositBonus.save({ transaction: sequelizeTransaction })
    return null
  }

  // deposit amount less than min deposit amount and greater than max deposit amount check
  if (amount < bonus.DepositBonusSetting.minDeposit || amount > bonus.DepositBonusSetting.maxDeposit) {
    return null
  }
  const bonusToBeGiven = (amount * (bonus.percentage / 100)) > bonus.DepositBonusSetting.maxBonus ? bonus.DepositBonusSetting.maxBonus : amount * (bonus.percentage / 100)
  userActiveDepositBonus.bonusAmount = bonusToBeGiven
  userActiveDepositBonus.rolloverBalance = bonusToBeGiven * bonus.DepositBonusSetting.rolloverMultiplier
  userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString()

  await userActiveDepositBonus.save({ transaction: sequelizeTransaction })
  let transactionDetail = null
  if (bonus.kind === BONUS_TYPES.DEPOSIT) {
    const conversionRate = await userCurrencyExchange(context, user.Wallet.currencyId)
    let transactionObject = {
      targetWalletId: user.Wallet.id,
      targetCurrencyId: user.Wallet.currencyId,
      amount: bonusToBeGiven,
      conversionRate,
      targetBeforeBalance: user.Wallet.amount,
      targetAfterBalance: user.Wallet.amount + bonusToBeGiven,
      comments: 'Pending Transaction for deposit bonus',
      actioneeId: user.id,
      actioneeType: 'User',
      tenantId: user.tenantId,
      timestamp: new Date().getTime(),
      transactionType: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM,
      //errorDescription: 'Completed Successfully',
      errorCode: 0,
      status: 'pending',
      success: true
    }

    transactionObject = await currencyConversion(context, transactionObject, user.Wallet, bonusToBeGiven)

    const skipTransactionHook = true
    transactionDetail = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })

    if (transactionDetail) {
      txnIds.push(transactionDetail.id)
    }
  }
  if (bonus.kind === BONUS_TYPES.DEPOSIT_SPORTS) {
    const conversionRate = await userCurrencyExchange(context, user.Wallet.currencyId)
    let transactionObject = {
      isDeleted: false,
      amount: bonusToBeGiven,
      nonCashAmount: 0,
      journalEntry: 'credit',
      merchantId: null,
      reference: `${user.userName}-${new Date().toISOString()}`,
      description: 'Pending Transaction for deposit bonus',
      userId: user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      tenantId: user.tenantId,
      actioneeId: user.id,
      conversionRate,
      paymentFor: SPORTS.DEPOSIT_BONUS_PENDING,
      currentBalance: (user.Wallet.amount + user.Wallet.nonCashAmount),
      targetCurrencyId: user.Wallet.currencyId,
      targetWalletId: user.Wallet.id
    }

    transactionObject = await currencyConversion(context, transactionObject, user.Wallet, bonusToBeGiven)

    const skipTransactionHook = true
    transactionDetail = await BetsTransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })
    if (transactionDetail) {
      txnIds.push(transactionDetail.id)
    }
  }

  userActiveDepositBonus.transactionId = transactionDetail.id
  await userActiveDepositBonus.save({ transaction: sequelizeTransaction })
}
