import CryptoJS from 'crypto-js'

export default function createHash (requestData, apiKey) {
  const sortedObject = Object.fromEntries(
    Object.entries(requestData)
      .filter(([_, value]) => value !== null && value !== undefined && value !== '')
      .sort((a, b) => a[0].localeCompare(b[0]))
  )
  const concatenatedString = Object.entries(sortedObject).map(([key, value]) => `${key}=${value}`).join('|')
  const hash = CryptoJS.HmacSHA256(concatenatedString, apiKey).toString(CryptoJS.enc.Hex)
  return hash.toUpperCase()
}
