import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../../common/checkLimit'
import { NEXA_PG_INTEGRATION_CONSTANT } from '../../../../common/constants'
import ServiceBase from '../../../../common/serviceBase'
import createHash from '../common/createHash'
import decrypt from '../common/decrypt'
import encrypt from '../common/encrypt'
import processPaymentResponse from '../common/processPaymentResponse'

const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  upi: {
    type: 'string',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const userId = userDetail.id
    const paymentProviderId = this.args.paymentProviderId
    let amount = (parseFloat(this.args.amount) * 100) // convert rs into paise

    if (amount < 100) {
      responseObject.error = 1
      responseObject.errorDescription = NEXA_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      responseObject.success = 0
      return responseObject
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT } = NEXA_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userId, amount)
      if (allowed === DAILY_DEPOSIT_LIMIT || allowed === WEEKLY_DEPOSIT_LIMIT || allowed === MONTHLY_DEPOSIT_LIMIT) {
        responseObject.error = 1
        responseObject.errorDescription = `${allowed} ${NEXA_PG_INTEGRATION_CONSTANT.DEPOSIT_LIMIT_EXCEED}`
        responseObject.success = 0
        return responseObject
      }

      // // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        responseObject.error = 1
        responseObject.errorDescription = NEXA_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      // Checking user phone number and email
      const { phone, email } = userDetail
      const missingField = !phone ? 'Phone' : !email ? 'Email' : null
      if (missingField) {
        responseObject.error = 1
        responseObject.errorDescription = `${missingField} ${NEXA_PG_INTEGRATION_CONSTANT.MISSING_FIELD_REQUIRED}`
        responseObject.success = 0
        return responseObject
      }

      const { salt, payId, collectPayApi, merchantHostedKey } = paymentProviders.providerKeyValues
      const orderId = uuidv4()

      const params = {
        PAY_ID: payId,
        AMOUNT: amount,
        ORDER_ID: orderId,
        CURRENCY_CODE: NEXA_PG_INTEGRATION_CONSTANT.INR_CURRENCY_CODE,
        TXNTYPE: NEXA_PG_INTEGRATION_CONSTANT.TXNTYPE,
        MOP_TYPE: NEXA_PG_INTEGRATION_CONSTANT.MOP_TYPE,
        PAYMENT_TYPE: NEXA_PG_INTEGRATION_CONSTANT.PAYMENT_TYPE,
        CUST_EMAIL: email,
        CUST_PHONE: phone,
        PAYER_ADDRESS: this.args.upi
      }

      const sortedParams = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('~')
      const hash = createHash(sortedParams, salt)
      const reqString = sortedParams + `~HASH=${hash}`

      const iv = merchantHostedKey.substring(0, 16)
      const ciphertextBase64 = encrypt(reqString, merchantHostedKey, iv)
      const requestData = {
        PAY_ID: payId,
        ENCDATA: ciphertextBase64
      }

      const makeApiCall = async () => {
        try {
          const { data } = await axios.post(collectPayApi, requestData, {
            headers: {
              'Content-Type': 'application/json'
            }
          })
          const EncData = data.ENCDATA
          const decryptedText = decrypt(EncData, merchantHostedKey, iv)
          const resultObject = decryptedText.split('~').reduce((obj, pair) => {
            const [key, value] = pair.split('=')
            obj[key] = value
            return obj
          }, {})

          if (resultObject.RESPONSE_CODE === NEXA_PG_INTEGRATION_CONSTANT.SUCCESS_CODE) {
            const depositType = NEXA_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
            amount = parseFloat(resultObject.TOTAL_AMOUNT / 100)
            const extraCharge = parseFloat(resultObject.TOTAL_AMOUNT - resultObject.AMOUNT) / 100
            const remark = extraCharge ? `Extra amount is ${extraCharge}` : undefined

            await DepositRequestModel.create({ orderId: resultObject.ORDER_ID, trackingId: resultObject.PAYER_ADDRESS, userId, paymentProviderId, amount, tenantId, depositType, remark })

            responseObject.success = 1
            responseObject.url = ''
            responseObject.successDescription = NEXA_PG_INTEGRATION_CONSTANT.SUCCESS_DESCRIPTION
            return responseObject
          } else {
            responseObject.errorDescription = 'Request rejected by Provider'
          }
          responseObject.error = 1
          responseObject.success = 0
          return responseObject
        } catch (error) {
          responseObject.error = 1
          responseObject.errorDescription = 'Something Went Wrong'
          responseObject.success = 0
          return responseObject
        }
      }

      await makeApiCall()
      return responseObject
    } catch (err) {
      throw new Error(err)
    }
  }
}
