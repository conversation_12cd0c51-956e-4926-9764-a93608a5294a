import CryptoJS from 'crypto-js'

export default function decrypt (ciphertextBase64, key, iv) {
  const keyHex = CryptoJS.enc.Utf8.parse(key)
  const ivHex = CryptoJS.enc.Utf8.parse(iv)
  const ciphertext = CryptoJS.enc.Base64.parse(ciphertextBase64)

  const decrypted = CryptoJS.AES.decrypt({ ciphertext }, keyHex, {
    iv: ivHex,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })

  const decyptedText = CryptoJS.enc.Utf8.stringify(decrypted)

  return decyptedText
}
