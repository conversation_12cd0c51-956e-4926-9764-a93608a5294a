import { isEmpty } from 'lodash'
import md5 from 'md5'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { FAKE_USER_INFO, PAYWINGS_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import createHash from '../common/hashSHA256Hex'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, PAYWINGS_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = PAYWINGS_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      const userName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email() ? faker.internet.email() : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10) ? faker.string.numeric(10) : FAKE_USER_INFO.PHONE

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, PAYWINGS_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { paymentRequestApi, merchantId, payinApiKey } = paymentProviders?.providerKeyValues

      const orderId = md5(uuidv4()).substring(0, 20) // for generating unique id of length 20

      const dataString = `AMOUNT=${amount}|CUST_EMAIL=${email}|CUST_MOBILE=${phone}|CUST_NAME=${userName}|MERCHANT_ID=${merchantId}|ORDER_ID=${orderId}|RETURN_URL=NA`
      const hash = createHash(dataString, payinApiKey).toUpperCase()

      const urlParams = new URLSearchParams()
      urlParams.append('MERCHANT_ID', merchantId)
      urlParams.append('ORDER_ID', orderId)
      urlParams.append('AMOUNT', amount)
      urlParams.append('CUST_NAME', userName)
      urlParams.append('CUST_EMAIL', email)
      urlParams.append('CUST_MOBILE', phone)
      urlParams.append('PAY_MODE', 'UPI')
      urlParams.append('UPI_CHANNEL', 'INTENT')
      urlParams.append('RETURN_URL', 'NA')
      urlParams.append('HASH', hash)

      const { data } = await axios.post(paymentRequestApi, urlParams, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      if (data.respCode === PAYWINGS_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        const depositType = PAYWINGS_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
        await DepositRequestModel.create({ orderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType, trackingId: data.data.GatewayTID })
        responseObject.success = 1
        responseObject.url = data.data.IntentURL
      } else {
        return setError(responseObject, 'Request rejected by Provider')
      }

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
