import { isEmpty } from 'lodash'
import qs from 'querystring'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { XAMAX_COUNTRY_CURRENCY_CONSTANT, XAMAX_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel,
          Wallet: WalletModel,
          Currency: CurrencyModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    let amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, XAMAX_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = XAMAX_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)

      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }
      //Getting wallet dedtails for currency code
      const wallet = await WalletModel.findOne({
        where: {
          ownerId: userDetail.id,
          ownerType: 'User'
        },
        include: {
          model: CurrencyModel
        },
        raw: true,
        nest: true
      })

      if (!wallet) {
        return setError(responseObject, "User Wallet Details Not Found")
      }

      let currencyCode = wallet?.Currency?.code

      let currencyConfig;
      // currencyConfig = XAMAX_COUNTRY_CURRENCY_CONSTANT[currencyCode];

      // Using INR as the main currency for transaction
      currencyConfig = XAMAX_COUNTRY_CURRENCY_CONSTANT['INR'];

      /* if (['chips', 'LKR'].includes(currencyCode)) {
        currencyConfig = XAMAX_COUNTRY_CURRENCY_CONSTANT['INR'];


        //  --- Wallet based conversion
        //   let convesionObj = {}
        //   convesionObj = await currencyConversionV3(this.context, convesionObj, wallet, this.args.amount);
        //   convesionObj = JSON.parse(convesionObj.otherCurrencyAmount)
        //   if (!convesionObj?.INR) {
        //     return setError(responseObject, "INR Not Supported")
        //   }
        //   amount = parseFloat(convesionObj?.INR)

        // Direct INR Conversion
        const conversionRate = wallet?.Currency?.exchangeRate
        let currencyExchangeRate = await CurrencyModel.findOne({
          where: {
            code: 'INR'
          },
          attributes: ['id', 'exchange_rate'],
          raw: true
        })
        if (!currencyExchangeRate) {
          return setError(responseObject, `${currencyCode} INR Currency Not Found`)
        }
        amount = parseFloat((parseFloat(amount) * (currencyExchangeRate.exchange_rate / conversionRate)).toFixed(4))
      }
      */



      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, XAMAX_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { apiKey, tokenApi, createPaymentApi, successRedirectUrl, failedRedirectUrl } = paymentProviders?.providerKeyValues

      const clientOrderId = uuidv4().replace(/-/g, '')

      // Generate Token:
      const data = qs.stringify({
        'refresh_token': apiKey
      });
      let tokenResponse;
      try {
        tokenResponse = await axios.post(tokenApi, data, {
          maxBodyLength: Infinity,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          }
        });
        tokenResponse = tokenResponse?.data
      } catch (error) {
        console.log(error);
        return setError(responseObject, 'Token Generation Failed')
      }

      // Step 1: Prepare the request payload (this would be sent in the POST request body)
      const requestPayload = {
        // transaction_id: clientOrderId,
        payment_link_id: clientOrderId,
        payment_method: currencyConfig.PAYMENT_METHODS[0],
        amount: +amount,
        country: currencyConfig?.COUNTRY,
        currency: currencyConfig?.CURRENCY,
        description: XAMAX_PG_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS,
        success_url: successRedirectUrl,
        fail_url: failedRedirectUrl,
        type_of_calculation:XAMAX_PG_INTEGRATION_CONSTANT.TYPE_OF_CALCULATION.FORWARD_WITHOUT_FEE ,
        transaction_type: XAMAX_PG_INTEGRATION_CONSTANT.TRANSACTION_TYPE
      }

      // Step 2: Perform the payment request

      let httpApiReponse = null;
      try {
        const config = {
          maxBodyLength: Infinity,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${tokenResponse.access_token}` // Replace with your actual authorization token
          },
        };
        httpApiReponse = await axios.post(createPaymentApi, requestPayload, config)
      } catch (error) {
        console.log(error);
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse?.data

      if (parseInt(httpApiReponse?.status) !== XAMAX_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) return setError(responseObject, 'Request rejected by Provider')


      const redirectUrl = responseData?.value?.link

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: XAMAX_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
      })

      responseObject.success = 1
      responseObject.url = redirectUrl

      return responseObject
    } catch (err) {
      console.log('------Xamax ProcessPayment service error------', err.message)
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
