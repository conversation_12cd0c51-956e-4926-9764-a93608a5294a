import CryptoJS from 'crypto-js'
import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { SASPAY_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, SASPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = SASPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      const { phone, email, userName } = userDetail
      if (!phone || !email) {
        return setError(responseObject, `${!phone ? 'Phone' : 'Email'} ${SASPAY_PG_INTEGRATION_CONSTANT.MISSING_FIELD_REQUIRED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, SASPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { apiEndPoint, merchantId, saltKey, callbackUrl } = paymentProviders?.providerKeyValues
      const orderId = uuidv4()

      const stringToHash = orderId + amount + saltKey + merchantId
      const signature = CryptoJS.SHA256(stringToHash).toString()

      const params = {
        order_id: orderId,
        amount,
        name: userName,
        email,
        mobile_no: phone,
        signature,
        callback_url: callbackUrl,
        device_type: ''
      }

      const makeApiCall = async () => {
        try {
          const { data } = await axios.post(apiEndPoint, params, {
            headers: {
              'Content-Type': 'application/json'
            }
          })

          if (data.statusCode === SASPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
            const depositType = SASPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
            await DepositRequestModel.create({ orderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType })
            responseObject.success = 1
            responseObject.url = data.intentLink
            return responseObject
          } else {
            return setError(responseObject, 'Request rejected by Provider')
          }
        } catch (error) {
          console.log('--------------error-data---------', error)
          return setError(responseObject, 'Something Went Wrong')
        }
      }

      await makeApiCall()
      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
