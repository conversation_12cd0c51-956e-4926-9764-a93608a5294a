import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import {
  FAKE_USER_INFO,
  SEYLAN_PG_INTEGRATION_CONSTANT
} from '../../../common/constants'
import <PERSON>rrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  returnUrl: {
    type: 'string'
  },
  cancelUrl: {
    type: 'string'
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }
    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = parseFloat(this.args.amount)

    if (amount < 1) {
      return setError(
        responseObject,
        SEYLAN_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      )
    }

    try {
      // Deposit limit check
      const {
        DAILY_DEPOSIT_LIMIT,
        WEEKLY_DEPOSIT_LIMIT,
        MONTHLY_DEPOSIT_LIMIT,
        DEPOSIT_LIMIT_EXCEED
      } = SEYLAN_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(
        this.context,
        userDetail.id,
        amount
      )

      if (
        [
          DAILY_DEPOSIT_LIMIT,
          WEEKLY_DEPOSIT_LIMIT,
          MONTHLY_DEPOSIT_LIMIT
        ].includes(allowed)
      ) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(
          responseObject,
          SEYLAN_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        )
      }

      const { merchantId, password, apiUrl } = paymentProviders?.providerKeyValues
      const userName = `merchant.${merchantId}`

      const email = faker.internet.email()
        ? faker.internet.email()
        : FAKE_USER_INFO.EMAIL

      const clientOrderId = uuidv4().replace(/-/g, '')

      // Sample Payload

      const payload = {
        apiOperation: 'INITIATE_CHECKOUT',
        order: {
          id: `${clientOrderId}`,
          amount: amount,
          currency: 'LKR',
          description: 'Deposit'
        },
        interaction: {
          operation: 'PURCHASE',
          merchant: {
            address: { line1: 'Test Address1' },
            email: email,
            name: merchantId
          },
          returnUrl: `${this.args.returnUrl}/${clientOrderId}`,
          cancelUrl: this.args.cancelUrl
        }
      }

      let httpApiReponse = null
      const intentGenerationUrl = `${apiUrl}api/rest/version/73/merchant/${merchantId}/session`
      try {
        const response = await axios.post(intentGenerationUrl, payload, {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            Authorization: 'Basic ' + Buffer.from(`${userName}:${password}`).toString('base64')
          }
        })

        httpApiReponse = response.data
      } catch (error) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse
      if (responseData?.result !== SEYLAN_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const sessionId = (responseData.session && responseData.session.id) ? responseData.session.id : ''
      const paymentUrl = `${apiUrl}checkout/pay/${sessionId}?checkoutVersion=1.0.0`

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: SEYLAN_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
      })

      responseObject.success = 1
      responseObject.data = responseData
      responseObject.url = paymentUrl
      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
