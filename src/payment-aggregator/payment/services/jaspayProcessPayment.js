import { isEmpty } from 'lodash'
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { FAKE_USER_INFO, JASPAY_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import { decryptAES256CBC, encryptAES256CBC, generateHmacSha256Signature } from '../../../lib/encryption'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel,
          UserLoginHistory: UserLoginHistoryModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, JASPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = JASPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)

      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, JASPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { apiKey, hashSecret, encryptionKey, merchatCode, createPaymentApi, redirectUri } = paymentProviders?.providerKeyValues

      const userName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email() ? faker.internet.email() : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10) ? faker.string.numeric(10) : FAKE_USER_INFO.PHONE
      const clientOrderId = uuidv4().replace(/-/g, '')

      const userLoginInfo = await UserLoginHistoryModel.findOne({
        attributes: ['id', 'ip'],
        where: { tenantId, userId: userDetail.id },
        order: [['id', 'DESC']]
      })

      // Sample Payload
      const payload = {
        customer_name: userName,
        customer_ip: userLoginInfo?.ip,
        customer_mobile: phone,
        customer_email: email,
        merchant_order_id: clientOrderId,
        merchant_code: merchatCode,
        merchant_transaction_date: moment().format('DD MMMM YYYY'),
        payin_amount: +amount,
        mode: 'cash',
        redirect_url: redirectUri
      }

      // Step 1: Stringify the Payload
      const payloadString = JSON.stringify(payload)

      // Step 2: Create AES-256-CBC encryption
      const encryptedData = encryptAES256CBC(payloadString, encryptionKey)

      // Step 3: Generate the HMAC signature based on the JSON string
      const signatureHash = generateHmacSha256Signature(encryptedData, hashSecret)

      // Step 4: Prepare the request payload (this would be sent in the POST request body)
      const requestPayload = { data: encryptedData }

      // Step 5: Perform the payment request
      let httpApiReponse = null

      try {
        httpApiReponse = await axios.post(createPaymentApi, requestPayload, {
          headers: {
            api_key: apiKey,
            signature: signatureHash,
            'Content-Type': 'application/json'
          }
        })
      } catch (error) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse?.data

      if (parseInt(responseData?.status) !== JASPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) return setError(responseObject, 'Request rejected by Provider')

      const responseSignature = httpApiReponse.headers.signature

      const generatedResponseSignature = generateHmacSha256Signature(responseData.data, hashSecret)

      if (generatedResponseSignature !== responseSignature) throw new Error('Response signature validation failed')

      const decryptedData = decryptAES256CBC(responseData.data, encryptionKey)
      const parsedDecryptedData = JSON.parse(decryptedData)
      const redirectUrl = parsedDecryptedData.redirect_url

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: JASPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
      })

      responseObject.success = 1
      responseObject.url = redirectUrl

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
