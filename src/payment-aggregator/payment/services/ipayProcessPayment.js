import { isEmpty } from 'lodash'
import md5 from 'md5'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { PAYWINGS_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import config from '../../../config/app'
import createHash from '../common/ipayHash'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, PAYWINGS_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = PAYWINGS_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, PAYWINGS_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { merchantWebToken, returnUrl, cancelUrl, url } = paymentProviders?.providerKeyValues

      const orderId = md5(uuidv4()).substring(0, 20) // for generating unique id of length 20

      const dataString = `AMOUNT=${amount}|MERCHANT_ID=${merchantWebToken}|ORDER_ID=${orderId}|RETURN_URL=${returnUrl}|CANCEL_URL=${cancelUrl}`

      const hash = await createHash(dataString, config.get('auth.jwt_secret'))

      await DepositRequestModel.create({ orderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType: 'payment_providers', trackingId: hash })

      responseObject.merchantWebToken = merchantWebToken
      responseObject.returnUrl = returnUrl
      responseObject.cancelUrl = cancelUrl
      responseObject.orderId = orderId
      responseObject.hash = hash
      responseObject.url = url

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
