import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import {
  FAKE_USER_INFO,
  ZENXPAY_PG_INTEGRATION_CONSTANT
} from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import { encryptAES256CBCUsingHex } from '../../../lib/encryption'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }
    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = parseFloat(this.args.amount)

    if (amount < 1) {
      return setError(
        responseObject,
        ZENXPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      )
    }

    try {
      // Deposit limit check
      const {
        DAILY_DEPOSIT_LIMIT,
        WEEKLY_DEPOSIT_LIMIT,
        MONTHLY_DEPOSIT_LIMIT,
        DEPOSIT_LIMIT_EXCEED
      } = ZENXPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(
        this.context,
        userDetail.id,
        amount
      )

      if (
        [
          DAILY_DEPOSIT_LIMIT,
          WEEKLY_DEPOSIT_LIMIT,
          MONTHLY_DEPOSIT_LIMIT
        ].includes(allowed)
      ) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(
          responseObject,
          ZENXPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        )
      }

      const { secretId, clientId, apiKey, apiUrl } =
        paymentProviders?.providerKeyValues

      const userName = faker.person.firstName()
        ? faker.person.firstName()
        : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email()
        ? faker.internet.email()
        : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10)
        ? faker.string.numeric(10)
        : FAKE_USER_INFO.PHONE
      const clientOrderId = uuidv4().replace(/-/g, '')
      const cleanAPIKey = apiKey.replace(/^mk_test_/, '')

      // Sample Payload
      const payload = {
        amount: amount,
        order_id: clientOrderId,
        remark: 'Deposite',
        customerName: userName,
        customerEmail: email,
        mobilenumber: phone
      }

      // Step 1: Stringify the Payload
      const payloadString = JSON.stringify(payload)

      // Step 2: Create AES-256-CBC encryption
      const encryptedData = encryptAES256CBCUsingHex(
        payloadString,
        cleanAPIKey
      )

      // Step 3: Prepare the request payload (this would be sent in the POST request body)
      const requestPayload = { data: encryptedData }

      // Step 4: Perform the payment request
      let httpApiReponse = null

      const intentGenerationUrl = `${apiUrl}/v1/api/upi/qr/generate/intent`

      try {
        httpApiReponse = await axios.post(intentGenerationUrl, requestPayload, {
          headers: {
            'Content-Type': 'application/json',
            'x-client-id': clientId,
            'x-secret-id': secretId
          }
        })
      } catch (error) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse

      if (
        parseInt(responseData?.status) !==
        ZENXPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE
      ) { return setError(responseObject, 'Request rejected by Provider') }

      const parsedDecryptedData = responseData?.data?.data
      const upiIntentString = parsedDecryptedData.upiIntentString

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: ZENXPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
      })

      responseObject.success = 1
      responseObject.url = upiIntentString
      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
