import { isEmpty } from 'lodash'
import md5 from 'md5'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { COUNTRIES, PAYCOOKIES_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import generateSignatureHash from '../common/paycookiesSignature'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, PAYCOOKIES_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = PAYCOOKIES_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, PAYCOOKIES_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { appId, appSecret, signatureHashKey, domain, callBackUrl, returnUrl } = paymentProviders?.providerKeyValues

      const auth = Buffer.from(`${appId}:${appSecret}`).toString('base64')

      const tokenResponse = await axios.get(`${domain}/pg/v2/token`, {
        headers: {
          'Authorization': `Basic ${auth}`
        }
      })

      if (!tokenResponse?.data?.data?.token) {
        return setError(responseObject, 'Request rejected by Provider')
      }
      const jwtToken = tokenResponse?.data?.data?.token

      const referenceNumber = md5(uuidv4()).substring(0, 28)

      const country = COUNTRIES.find(item => {
        if(item.code === userDetail?.countryCode)
          return item
      })

      const params = {
        returnUrl,
        callBackUrl,
        currency: "INR",
        amount: amount + '.00',
        referenceNumber,
        mobileNo: userDetail?.phone,
        email: userDetail?.email,
        name: userDetail?.userName,
        zipCode: userDetail?.zipCode || '110001',
        country: country?.name || 'India',
        city: userDetail?.city || 'Delhi'
      }

      params.signature = generateSignatureHash(params, signatureHashKey)

      // creating order
      const response = await axios.post(`${domain}/pg/v2/public/order`, params, {
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        }
      })

      const responseData = response.data
      if (responseData.data) {
        const { checkout_page, order_id, token, signature } = responseData.data
        const responseSignature = generateSignatureHash({ checkout_page, token }, signatureHashKey)

        if (responseSignature === signature) {
          const depositType = PAYCOOKIES_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
          await DepositRequestModel.create({ orderId: referenceNumber, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType, trackingId: order_id })
          responseObject.success = 1
          responseObject.url = checkout_page
        }
        else {
          return setError(responseObject, 'Request rejected by Provider')
        }
      } else {
        return setError(responseObject, 'Request rejected by Provider')
      }

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
