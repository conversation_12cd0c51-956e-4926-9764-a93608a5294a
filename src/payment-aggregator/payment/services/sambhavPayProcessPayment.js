import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { FAKE_USER_INFO, SAMBHAVPAY_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import <PERSON>rrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import { getCheckSum, getEncrypt, getResponse } from '../common/sambhavPay'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, SAMBHAVPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = SAMBHAVPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      const userName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email() ? faker.internet.email() : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10) ? faker.string.numeric(10) : FAKE_USER_INFO.PHONE

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, SAMBHAVPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const credentials = paymentProviders?.providerKeyValues

      const orderId = uuidv4()
      const params = {
        Mid: credentials?.mid,
        SecretKey: credentials?.secretKey,
        SaltKey: credentials?.saltKey,
        OrderNo: orderId,
        TotalAmount: parseFloat(amount * 100),
        CurrencyName: 'INR',
        MeTransReqType: 'S',
        EmailId: email,
        MobileNo: phone,
        TransactionMethod: 'UPI',
        UPIType: 'intent',
        ResponseUrl: '',
        AddField1: '',
        AddField2: '',
        AddField3: '',
        AddField4: '',
        AddField5: '',
        AddField6: '',
        AddField7: '',
        AddField8: '',
        AddField9: '',
        AddField10: '',
        Source: '',
        Address: '',
        City: '',
        State: '',
        Pincode: '',
        BankCode: '',
        VPA: '',
        CardNumber: '',
        ExpiryDate: '',
        CVV: '',
        CardHolderName: '',
        CustomerName: userName
      }

      const encryptReq = await getEncrypt(params, credentials)
      const checkSum = await getCheckSum(params, credentials?.saltKey)
      const requestData = {
        mid: credentials?.mid,
        encryptReq,
        checkSum
      }

      const { data } = await axios.post(credentials?.payinAPi, requestData, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (data.respCode === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.SUCCESS_RESPCODE && data.respMsg === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.SUCCESS_RESPMSG) {
        const response = JSON.parse(data?.data)
        const respData = response?.respData
        const mid = response?.mid
        const checkSum = response?.checkSum
        let finalResponse = await getResponse(respData, mid, checkSum, credentials)
        finalResponse = JSON.parse(finalResponse)

        if (!finalResponse) {
          return setError(responseObject, 'CheckSum Miss Match!')
        } else {
          if (finalResponse.RespCode === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.PENDING_FOR_AUTHORIZATION_CODE && finalResponse.UPIString !== null) {
            const depositType = SAMBHAVPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
            await DepositRequestModel.create({ orderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType, trackingId: finalResponse.TxnRefNo })
            responseObject.success = 1
            responseObject.url = finalResponse.UPIString
          } else {
            return setError(responseObject, 'Request rejected by Provider')
          }
        }
      } else {
        return setError(responseObject, 'Request rejected by Provider')
      }

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
