import { isEmpty } from 'lodash'
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { CLOUD_CASH_PG_INTEGRATION_CONSTANT, FAKE_USER_INFO } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import { md5Encryption } from '../../../lib/encryption'
import toFixedDecimal from '../../../lib/toFixedDecimal'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  bankCode:{
    type: 'string',
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {

    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel,
          UserLoginHistory: UserLoginHistoryModel,
          Wallet: WalletModel,
          Currency:CurrencyModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, CLOUD_CASH_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = CLOUD_CASH_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)

      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

       //Getting wallet dedtails for currency code
       const wallet = await WalletModel.findOne({
        where: {
          ownerId: userDetail.id,
          ownerType: 'User'
        },
        include: {
          model: CurrencyModel
        },
        raw: true,
        nest: true
      })

      if (!wallet) {
        return setError(responseObject, "User Wallet Details Not Found")
      }

      let currencyCode = wallet?.Currency?.code

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, CLOUD_CASH_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { merchant, securityCode, createPaymentApi, redirectionUrl, callbackUrl } = paymentProviders?.providerKeyValues

      const firstName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const lastName = faker.person.lastName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const clientOrderId = uuidv4().replace(/-/g, '')

      const userLoginInfo = await UserLoginHistoryModel.findOne({
        attributes: ['id', 'ip'],
        where: { tenantId, userId: userDetail.id },
        order: [['id', 'DESC']]
      })

      if (currencyCode!='INR') {
        return setError(responseObject, CLOUD_CASH_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      // Step 1: Stringify the Payload
      const currentDate= new Date();
      const formattedDatetime = moment(currentDate).tz('Asia/Singapore').format('YYYYMMDDHHmmss')
      const payloadString = `${merchant}${clientOrderId}${userDetail.id}${toFixedDecimal(+amount)}${currencyCode}${formattedDatetime}${securityCode}${userLoginInfo?.ip}`;

      // Step 2: Create MD5 Hash encryption
      const stringToHash = md5Encryption(payloadString)

      // Sample Payload
      const payload = {
        Merchant: merchant,
        Currency: currencyCode,
        Customer: userDetail.id,
        FirstName: firstName,
        LastName: lastName,
        Reference: clientOrderId,
        Key: stringToHash,
        Amount: toFixedDecimal(+amount),
        Note: "Deposit Request",
        Datetime: moment(currentDate).tz('Asia/Singapore').format('YYYY-MM-DD HH:mm:ssA'),
        BackURI: callbackUrl,
        FrontURI: redirectionUrl,
        Bank: this.args.bankCode,
        Language: "en-us",
        ClientIP: userLoginInfo?.ip,
        CompanyName: ""
      }

      // Step 3: Perform the payment request
      let httpApiReponse;
      try {
        httpApiReponse = await axios.post(createPaymentApi, payload, {
          headers: {
            'Content-Type': 'application/json'
          }
        })

      } catch (error) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse?.data

      if ((!responseData?.IsSuccess) || parseInt(httpApiReponse?.status) !== CLOUD_CASH_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const redirectUrl = responseData.RedirectionUrl

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: CLOUD_CASH_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
      })

      responseObject.success = 1
      responseObject.url = redirectUrl

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
