import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { FAKE_USER_INFO, PEERPAY_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
import <PERSON>rrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import { decryptAES256GCM, encryptAES256GCM } from '../../../lib/encryption'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, PEERPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = PEERPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)

      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, PEERPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { createOrderApiUrl, token, encryptionKey, decryptionKey, returnUrl } = paymentProviders?.providerKeyValues

      const userName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email() ? faker.internet.email() : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10) ? faker.string.numeric(10) : FAKE_USER_INFO.PHONE
      const orderId = uuidv4().replace(/-/g, '')

      const payload = {
        name: userName,
        mobile: phone,
        email,
        amount,
        purpose: PEERPAY_PG_INTEGRATION_CONSTANT.PURPOSE,
        return_url: returnUrl,
        merc_ref_id: orderId
      }

      const encryptedRequest = encryptAES256GCM(payload, encryptionKey)

      let response
      try {
        const { encrypted_data: encryptedData, iv, tag } = encryptedRequest || {}
        response = await axios.post(
          createOrderApiUrl,
          {
            encrypted_data: encryptedData,
            iv,
            tag
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`
            }
          }
        )
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, userDetail)
        return setError(responseObject, 'Request rejected by Provider')
      }

      const decryptedData = decryptAES256GCM(response?.data, decryptionKey)

      if (decryptedData?.code === PEERPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        const depositType = PEERPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
        const { order_id: trackingId, uuid: sessionId, full_url: fullUrl } = decryptedData?.data || {}
        await DepositRequestModel.create({ orderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType, trackingId, sessionId })
        responseObject.success = 1
        responseObject.url = fullUrl
      } else {
        return setError(responseObject, 'Request rejected by Provider')
      }

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
