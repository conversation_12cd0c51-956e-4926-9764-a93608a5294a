import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import { ACKOPAY_PG_INTEGRATION_CONSTANT, FAKE_USER_INFO } from '../../../common/constants'
import <PERSON>rrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
const { faker } = require('@faker-js/faker')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = (parseFloat(this.args.amount))

    if (amount < 1) {
      return setError(responseObject, ACKOPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT)
    }

    try {
      // Deposit limit check
      const { DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT, DEPOSIT_LIMIT_EXCEED } = ACKOPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(this.context, userDetail.id, amount)
      if ([DAILY_DEPOSIT_LIMIT, WEEKLY_DEPOSIT_LIMIT, MONTHLY_DEPOSIT_LIMIT].includes(allowed)) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(responseObject, ACKOPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { clientId, secretKey, createOrderApi, deepLinkApi } = paymentProviders?.providerKeyValues

      const userName = faker.person.firstName() ? faker.person.firstName() : FAKE_USER_INFO.USERNAME
      const email = faker.internet.email() ? faker.internet.email() : FAKE_USER_INFO.EMAIL
      const phone = faker.string.numeric(10) ? faker.string.numeric(10) : FAKE_USER_INFO.PHONE
      const clientOrderId = uuidv4()

      const createOrderParams = {
        clientId,
        secretKey,
        name: userName,
        mobileNo: phone,
        emailID: email,
        amount: amount.toString(),
        clientOrderId
      }

      const createOrder = await axios.post(`${createOrderApi}`, createOrderParams, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (createOrder?.data?.statusCode !== ACKOPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const getDeepLinkParams = {
        clientId,
        secretKey,
        note: 'testing',
        orderId: createOrder.data.orderId
      }
      const getDeepLink = await axios.post(`${deepLinkApi}`, getDeepLinkParams, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const responseData = getDeepLink.data
      if (responseData) {
        const { statusCode, orderId, upiurl } = responseData
        if (statusCode === ACKOPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
          const depositType = ACKOPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
          await DepositRequestModel.create({ orderId: clientOrderId, userId: userDetail.id, paymentProviderId, amount, tenantId, depositType, trackingId: orderId })
          responseObject.success = 1
          responseObject.url = upiurl
        }
        else {
          return setError(responseObject, 'Request rejected by Provider')
        }
      } else {
        return setError(responseObject, 'Request rejected by Provider')
      }

      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, { id: userDetail.id, tenantId })
      throw new Error(err)
    }
  }
}
