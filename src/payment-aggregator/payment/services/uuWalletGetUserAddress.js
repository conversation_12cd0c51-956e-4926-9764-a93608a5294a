import FormData from 'form-data'
import { isEmpty } from 'lodash'
import { UUWALLET_PG_INTEGRATION_CONSTANT, UUWALLET_PG_RESPONSE, UUWALLET_PG_RESPONSE_CODES, USER_SPECIFIC_PERMISSIONS_MODULES, USER_SPECIFIC_PERMISSIONS_ACTION } from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import { uuWalletDecryptByPublicKeyForOut, uuWalletEncryptByPublicKeyForOut } from '../../../lib/encryption'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')
import checkUserPermission from '../../../common/checkUserPermission'

/**
 * GetUserAddress
 * @export
 * @class getUserAddress
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  chainName: {
    type: 'string',
    presence: { message: '' }
  },
  symbol: {
    type: 'string',
    presence: { message: '' }
  },
}

export default class GetUserAddress extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {

    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          UserBankDetails: UserBankDetailsModel,
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId, chainName, symbol }
    } = this

    const responseObject = processPaymentResponse(this.args)

    try {

      const hasPermission = await checkUserPermission(tenantId, userDetail.id, USER_SPECIFIC_PERMISSIONS_MODULES.CRYPTO_DEPOSIT, USER_SPECIFIC_PERMISSIONS_ACTION.CREATE)
      if (!hasPermission) {
        return setError(responseObject, UUWALLET_PG_INTEGRATION_CONSTANT.CRYPTO_DEPOSIT_PERMISSION_DENIED)
      }

      const userCryptoAccountDetails = await UserBankDetailsModel.findOne({
        where: {
          userId: userDetail.id,
          tenantId,
          bankName: 'Crypto',
          name: chainName,
          bankIfscCode: symbol
        },
        raw: true,
        attributes: ['id', 'accountNumber']
      })

      const paymentConfig = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      if (isEmpty(paymentConfig)) {
        return setError(responseObject, UUWALLET_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND)
      }

      const { merchantId, apiUrl, apiKey, publicKey } = paymentConfig?.providerKeyValues

      const payload = {
        chainName: chainName,
        symbol: symbol,
        tenantUserId: userDetail.id,
        env: '',
        userId: merchantId,
        tenantType: 1
      }

      const encryptedData = await uuWalletEncryptByPublicKeyForOut(payload, publicKey);
      if (!encryptedData) {
        responseObject.errCode = UUWALLET_PG_RESPONSE.ENCRYPTION_FAILED.code
        responseObject.message = UUWALLET_PG_RESPONSE.ENCRYPTION_FAILED.message
        return responseObject
      }

      const form = new FormData()

      form.append('data', encryptedData)

      const config = {
        method: 'post',
        url: `${apiUrl}/api/user/getUserAddress`,
        headers: {
          'X-API-KEY': apiKey,
          ...form.getHeaders()
        },
        data: form
      };

      try {
        let response = await axios(config)
        const responseData = response?.data

        if (responseData.code != 0) {
          return setError(responseObject, responseData.message)
        }

        let decryptedRes = await uuWalletDecryptByPublicKeyForOut(responseData.data, publicKey);
        if(!decryptedRes){
          responseObject.errCode = UUWALLET_PG_RESPONSE.DECRYPTION_FAILED.code
          responseObject.message = UUWALLET_PG_RESPONSE.DECRYPTION_FAILED.message
          return responseObject
        }

        if (responseData.code == UUWALLET_PG_RESPONSE_CODES.SUCCESS) {
          const cryptoAccountDetails = {
            userId: userDetail.id,
            tenantId,
            bankName: 'Crypto',
            name: chainName,
            bankIfscCode: symbol,
            accountNumber: decryptedRes?.address,
            status: decryptedRes?.state == 1 ? 'active' : 'inactive',
            phoneCode: decryptedRes?.riskScore
          }

          if (!userCryptoAccountDetails) {
            await UserBankDetailsModel.create(cryptoAccountDetails)
          } else if (userCryptoAccountDetails && userCryptoAccountDetails.accountNumber != decryptedRes?.address) {
            await UserBankDetailsModel.update({
              accountNumber: decryptedRes?.address
            }, {
              where: {
                id: userCryptoAccountDetails.id
              }
            })
          }

          responseObject.data = {
            address: decryptedRes?.address
          }
        }
        return responseObject
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, userDetail)
        return setError(responseObject, 'Request rejected by Provider')
      }

    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
