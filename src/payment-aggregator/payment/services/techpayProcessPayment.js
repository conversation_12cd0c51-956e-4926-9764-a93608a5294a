import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../common/checkLimit'
import {
  TECHPAY_PG_INTEGRATION_CONSTANT
} from '../../../common/constants'
import ErrorLogHelper from '../../../common/errorLog'
import ServiceBase from '../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'
import setError from '../common/setError'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: TenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail, paymentProviderId }
    } = this

    const responseObject = processPaymentResponse(this.args)
    const amount = parseFloat(this.args.amount)

    if (amount < 1) {
      return setError(
        responseObject,
        TECHPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      )
    }

    try {
      // Deposit limit check
      const {
        DAILY_DEPOSIT_LIMIT,
        WEEKLY_DEPOSIT_LIMIT,
        MONTHLY_DEPOSIT_LIMIT,
        DEPOSIT_LIMIT_EXCEED
      } = TECHPAY_PG_INTEGRATION_CONSTANT
      const allowed = await checkLimit.depositLimitCheck(
        this.context,
        userDetail.id,
        amount
      )

      if (
        [
          DAILY_DEPOSIT_LIMIT,
          WEEKLY_DEPOSIT_LIMIT,
          MONTHLY_DEPOSIT_LIMIT
        ].includes(allowed)
      ) {
        return setError(responseObject, `${allowed} ${DEPOSIT_LIMIT_EXCEED}`)
      }

      // Fetch Payment Provider Details
      const paymentProviders = await TenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        return setError(
          responseObject,
          TECHPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        )
      }

      const { merchantApiKey, merchantApiID, apiUrl } = paymentProviders?.providerKeyValues
      const clientOrderId = uuidv4().replace(/-/g, '')

      // Sample Payload
      const payload = {
        amount: amount,
        orderNumber: clientOrderId,
        description: 'Deposite',
        merchantApiKey: merchantApiKey,
        merchantApiID: merchantApiID
      }

      // const payloadString = JSON.stringify(payload)

      let httpApiReponse = null

      const intentGenerationUrl = `${apiUrl}/api/v1/hc/gettoken`

      try {
        httpApiReponse = await axios.post(intentGenerationUrl, payload)
      } catch (error) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const responseData = httpApiReponse

      if (parseInt(responseData?.responsecode) !== TECHPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) {
        return setError(responseObject, 'Request rejected by Provider')
      }

      const token = responseData?.data?.token
      const upiIntentString = `${apiUrl}/checkout/${token}`

      await DepositRequestModel.create({
        orderId: clientOrderId,
        userId: userDetail.id,
        paymentProviderId,
        amount,
        tenantId,
        depositType: TECHPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE,
        trackingId: token
      })

      responseObject.success = 1
      responseObject.url = upiIntentString
      return responseObject
    } catch (err) {
      await ErrorLogHelper.logError(err, this.context, userDetail)
      throw new Error(err)
    }
  }
}
