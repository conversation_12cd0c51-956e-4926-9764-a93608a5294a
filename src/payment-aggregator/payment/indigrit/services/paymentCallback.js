import { Op } from 'sequelize'
import {
  DEPOSIT_REQUEST_STATUS,
  INDIGRIT_INTEGRATION_CONSTANT,
  QUEUE_WORKER_CONSTANT,
  SUBSCRIPTION_CHANNEL,
  TRANSACTION_TYPES
} from '../../../../common/constants'
import currencyConversion from '../../../../common/currencyConversion'
import ServiceBase from '../../../../common/serviceBase'
import userCurrencyExchange from '../../../../common/userCurrencyExchange'
import { walletLocking } from '../../../../common/walletLocking'
import { depositBonusCheck } from '../common/depositBonusCheck'

const CryptoJS = require('crypto-js')

/**
 * payment callback
 * @export
 * @class paymentCallback
 * @extends {ServiceBase}
 */
const constraints = {
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  customer_id: {
    type: 'string',
    presence: { message: '' }
  },
  deposited_at: {
    type: 'string',
    presence: { message: '' }
  },
  deposit_id: {
    type: 'string',
    presence: { message: '' }
  },
  tracking_id: {
    type: 'string',
    presence: { message: '' }
  },
  status: {
    type: 'string',
    presence: { message: '' }
  }
}
export default class paymentCallback extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: bodyArgs,
      context: {
        databaseConnection: {
          Transaction: TransactionModel,
          Wallet: WalletModel,
          DepositRequest: DepositRequestModel,
          User: UserModel,
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          QueueLog: QueueLogModel
        }
      }
    } = this

    const { sequelizeTransaction } = this.context

    const responseObject = {}

    try {
      const orderDetails = await DepositRequestModel.findOne({
        where: {
          order_id: bodyArgs.deposit_id,
          tracking_id: bodyArgs.tracking_id,
          amount: bodyArgs.amount,
          status: {
            [Op.in]: [
              DEPOSIT_REQUEST_STATUS.OPEN,
              DEPOSIT_REQUEST_STATUS.IN_PROCESS
            ]
          }
        },
        transaction: sequelizeTransaction,
        useMaster: true
      })
      if (!orderDetails) {
        responseObject.status = INDIGRIT_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = INDIGRIT_INTEGRATION_CONSTANT.INVALID_REQUEST
        return responseObject
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: orderDetails.tenantId,
          providerId: orderDetails.paymentProviderId
        }
      })

      const payload = this.context.headers['x-indigrit-payload']
      const signature = this.context.headers['x-indigrit-signature']
      const secretKey = paymentProviders.providerKeyValues.secret_key
      const recreatedSignature = CryptoJS.enc.Hex.stringify(CryptoJS.HmacSHA512(payload, secretKey))

      if (!(signature === recreatedSignature)) {
        responseObject.status = INDIGRIT_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = INDIGRIT_INTEGRATION_CONSTANT.INVALID_SIGNATURE
        return responseObject
      }

      const data = JSON.parse(payload)

      if (!(data.customer_id === bodyArgs.customer_id && data.amount === bodyArgs.amount)) {
        responseObject.status = INDIGRIT_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = INDIGRIT_INTEGRATION_CONSTANT.INVALID_PAYLOAD
        return responseObject
      }

      const transaction = await TransactionModel.findOne({
        attributes: ['id'],
        where: { transactionId: this.context.headers['x-indigrit-idempotency-key'] },
        transaction: sequelizeTransaction,
        useMaster: true
      })

      if (transaction) {
        responseObject.status = INDIGRIT_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = INDIGRIT_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION
        return responseObject
      }

      // Get User Details
      let user = await UserModel.findOne({
        attributes: ['id', 'tenantId', 'userName'],
        where: {
          id: orderDetails.userId
        },
        include: {
          model: WalletModel,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount']
        }
      })

      user.Wallet.amount = parseFloat(user.Wallet.amount)

      // success
      if (bodyArgs.status === 'success') {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      }

      const depositAmount = parseFloat(bodyArgs.amount)

      const conversionRate = await userCurrencyExchange(this.context, user.Wallet.currencyId)

      let transactionObject = {
        targetWalletId: user.Wallet.id,
        targetCurrencyId: user.Wallet.currencyId,
        amount: depositAmount,
        conversionRate,
        targetBeforeBalance: user.Wallet.amount,
        targetAfterBalance: user.Wallet.amount + depositAmount,
        actioneeId: orderDetails.userId,
        actioneeType: 'User',
        tenantId: user.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: this.context.headers['x-indigrit-idempotency-key'],
        debitTransactionId: bodyArgs.deposit_id,
        metaData: { trackingId: bodyArgs.tracking_id },
        paymentMethod: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: INDIGRIT_INTEGRATION_CONSTANT.INDIGRIT_TRANSACTION_COMMENTS
      }

      const userWallet = await walletLocking(this.context, user)
      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

      user = { ...user.dataValues, Wallet: userWallet }
      userWallet.amount = userWallet.amount + depositAmount

      const skipWalletHook = true
      await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

      // id fetch
      transactionObject = await currencyConversion(this.context, transactionObject, userWallet, depositAmount)
      const skipTransactionHook = true
      const txn = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })
      const txnIds = []
      if (txn) {
        txnIds.push(txn.id)
      }

      await depositBonusCheck(this.context, depositAmount, user, txnIds)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
      responseObject.queueLogId = queueLog.id

      await orderDetails.save({ transaction: sequelizeTransaction })

      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, {
          UserWalletBalance: {
            walletBalance: userWallet.amount,
            userId: user.id,
            nonCashAmount: userWallet.nonCashAmount
          }
        })
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.USER_DEPOSIT_NOTIFICATION,
          {
            userWithdrawAmount: depositAmount,
            UserWalletBalance: {
              walletBalance: userWallet.amount,
              userId: user.id,
              nonCashAmount: userWallet.nonCashAmount
            }
          }
        )
      } catch (error) {
        // throw new Error(error)
      }
      responseObject.status = INDIGRIT_INTEGRATION_CONSTANT.SUCCESS_CODE
      responseObject.message = INDIGRIT_INTEGRATION_CONSTANT.SUCCESS
      return responseObject
    } catch (error) {
      throw new Error(error)
    }
  }
}
