import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../../common/checkLimit'
import { INDIGRIT_INTEGRATION_CONSTANT } from '../../../../common/constants'
import ServiceBase from '../../../../common/serviceBase'
import processPaymentResponse from '../common/processPaymentResponse'

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const userId = userDetail.id

    const paymentProviderId = this.args.paymentProviderId
    const amount = parseFloat(this.args.amount)

    if (amount < 1) {
      responseObject.error = 1
      responseObject.errorDescription = INDIGRIT_INTEGRATION_CONSTANT.INVALID_AMOUNT
      responseObject.success = 0
      return responseObject
    }

    try {
      // Deposit limit check
      const allowed = await checkLimit.depositLimitCheck(this.context, userId, amount)
      if (allowed === 'Daily' || allowed === 'Weekly' || allowed === 'Monthly') {
        responseObject.error = 1
        responseObject.errorDescription = `${allowed} ${INDIGRIT_INTEGRATION_CONSTANT.DEPOSIT_LIMIT_EXCEED}`
        responseObject.success = 0
        return responseObject
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        responseObject.error = 1
        responseObject.errorDescription = INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      const depositType = INDIGRIT_INTEGRATION_CONSTANT.DEPOSIT_TYPE

      // maintaing tracking Id
      const trackingId = uuidv4()
      await DepositRequestModel.create({ userId, paymentProviderId, amount, tenantId, depositType, trackingId })

      responseObject.trackingId = trackingId

      return responseObject
    } catch (err) {
      throw new Error(err)
    }
  }
}
