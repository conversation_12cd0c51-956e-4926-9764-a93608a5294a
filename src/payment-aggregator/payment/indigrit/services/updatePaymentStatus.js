import { DEPOSIT_REQUEST_STATUS, INDIGRIT_INTEGRATION_CONSTANT } from '../../../../common/constants'
import ServiceBase from '../../../../common/serviceBase'
import UpdatePaymentStatusResponse from '../common/updatePaymentStatusResponse'

/**
 * update payment status
 * @export
 * @class UpdatePaymentStatus
 * @extends {ServiceBase}
 */

const constraints = {
  type: {
    type: 'string',
    presence: { message: '' }
  },
  event: {
    type: 'string',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  customer_id: {
    type: 'string',
    presence: { message: '' }
  },
  session_id: {
    type: 'string',
    presence: { message: '' }
  },
  deposit_id: {
    type: 'string',
    presence: { message: '' }
  },
  payment_provider_id: {
    type: 'integer',
    presence: { message: '' }
  },
  tracking_id: {
    type: 'string',
    presence: { message: '' }
  }
}
export default class updatePaymentStatus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          DepositRequest: DepositRequestModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this

    const userId = userDetail.id

    try {
      const { deposit_id: depositId, payment_provider_id: paymentProviderId, event, session_id: sessionId, tracking_id: trackingId, amount, customer_id: customerId } = this.args

      const responseObject = UpdatePaymentStatusResponse(this.args)

      if (userId !== customerId) {
        responseObject.error = 1
        responseObject.errorDescription = INDIGRIT_INTEGRATION_CONSTANT.USER_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      const eventStatusMap = {
        payment_form_opened: DEPOSIT_REQUEST_STATUS.OPEN,
        payment_processing_started: DEPOSIT_REQUEST_STATUS.OPEN,
        payment_failed: DEPOSIT_REQUEST_STATUS.FAILED
      }

      if (['payment_form_opened', 'payment_processing_started', 'payment_failed'].includes(event)) {
        const depositRequest = await DepositRequestModel.findOne({
          where: { userId, paymentProviderId, tenantId, amount, trackingId }
        })

        if (!depositRequest) {
          responseObject.error = 1
          responseObject.errorDescription = INDIGRIT_INTEGRATION_CONSTANT.INVALID_DEPOSIT_REQUEST
          responseObject.success = 0
          return responseObject
        }

        await depositRequest.update({
          status: eventStatusMap[event], orderId: depositId, sessionId, paymentInitiate: true
        })
      }

      const eventMessages = {
        payment_form_opened: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_FORM_OPENED,
        payment_processing_started: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_PROCESSING_STARTED,
        payment_completed: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_COMPLETED,
        payment_failed: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_FAILED
      }

      responseObject.successDescription = eventMessages[event]
      return responseObject
    } catch (err) {
      throw new Error(err)
    }
  }
}
