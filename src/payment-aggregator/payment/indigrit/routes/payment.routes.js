import { Router } from 'express'
import { indigritProcessPayment } from '../controllers/payment.controller'
import { indigritUpdatePaymentStatus } from '../controllers/indigritUpdatePaymentStatus'
import { indigritPaymentCallback } from '../controllers/indigritPaymentCallback'

const auth = require('../middleware/auth')
const router = Router()

router.route('/processPayment').post(auth, indigritProcessPayment)
router.route('/updatePaymentStatus').post(auth, indigritUpdatePaymentStatus)
router.route('/paymentCallback').post(indigritPaymentCallback)

export default router
