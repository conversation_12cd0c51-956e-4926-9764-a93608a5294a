import updatePaymentStatus from '../services/updatePaymentStatus'

/**
 * update payment status using indigrit
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const indigritUpdatePaymentStatus = async (req, res) => {
  const data = await updatePaymentStatus.execute(req.body, req)
  return res.status(200).json(data.result)
}
