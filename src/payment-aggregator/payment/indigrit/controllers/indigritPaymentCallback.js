import paymentCallback from '../services/paymentCallback'
import addLog from '../../../../common/addLog'
import updateLog from '../../../../common/updateLog'
import { sequelize } from '../../../../db/models'
import { SUBSCRIPTION_CHANNEL } from '../../../../common/constants'

/**
 * indigrit payment callback
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const indigritPaymentCallback = async (req, res) => {
  const service = 'userbackend-indigrit'
  const createLogId = await addLog(req, service)
  try {
    req.sequelizeTransaction = await sequelize.transaction()

    const data = await paymentCallback.execute(req.body, req)
    requestConsoleLog(req)
    await req.sequelizeTransaction.commit()
    req.pubSub.publish(
      SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
      { QueueLog: { queueLogId: data.result.queueLogId } }
      )
    delete data.result.queueLogId
    await updateLog({ id: createLogId, response: data.result, responseCode: res.statusCode, isSuccess:true})
    return res.status(data.result.status).json(data.result)
  } catch (error) {
    errorConsoleLog(error)
    await req.sequelizeTransaction.rollback()
    await updateLog({ id: createLogId, response: { 'Error code': 1, description: 'internal server error', errorMsg: error.message }, responseCode: res.statusCode})
    return res.status(400).json({ 'Error code': 1, description: 'internal server error' })
  }
}

const requestConsoleLog = (req) => {
  console.log('=====Request', req.route.path, req.body)
}

const errorConsoleLog = (error) => {
  console.log('=======error', error)
}
