/**
 * The function is responsible to generate the response for the live ezugi api
 * @param {*} res - object contains all the response params sent to the client
 * @param {*} payload - contains the json object to be sent
 * @param {*} hash - contains the hash of the given payload
 */
export const response = async (res, { status = 200, data = {}, message, token = '' }) => {
  const payload = {
    status,
    message,
    data,
    token
  }

  res.status(status).json(payload)
}
