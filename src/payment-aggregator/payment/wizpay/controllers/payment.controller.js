import ProcessPayment from '../services/processPayment'

/**
 * process payment using wizpay
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const wizpayProcessPayment = async (req, res) => {
  console.log('------wizpayProcessPayment----req.body------', req.body)
  const data = await ProcessPayment.execute(req.body, req)
  return res.status(200).json(data.result)
}
