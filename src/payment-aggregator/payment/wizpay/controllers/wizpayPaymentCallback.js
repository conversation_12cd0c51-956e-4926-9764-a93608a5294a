import addLog from '../../../../common/addLog'
import { SUBSCRIPTION_CHANNEL } from '../../../../common/constants'
import updateLog from '../../../../common/updateLog'
import { sequelize } from '../../../../db/models'
import paymentCallback from '../services/paymentCallback'

/**
 * wizpay payment callback
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const wizpayPaymentCallback = async (req, res) => {
  const service = 'userbackend-wizpay'
  const createLogId = await addLog(req, service)
  try {
    req.sequelizeTransaction = await sequelize.transaction()
    const data = await paymentCallback.execute(req.body, req)
    requestConsoleLog(req)
    if (data.result.queueLogId) {
      try {
        req.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: data.result.queueLogId } }
        )
      } catch (error) {
        errorConsoleLog(error)
        // throw error
      }
      delete data.result.queueLogId
    }
    await req.sequelizeTransaction.commit()
    await updateLog({ id: createLogId, response: data.result, responseCode: data.result.status, isSuccess: true })
    return res.status(data.result.status).json(data.result)
  } catch (error) {
    errorConsoleLog(error)
    await req.sequelizeTransaction.rollback()
    await updateLog({ id: createLogId, response: { 'Error code': 1, description: 'internal server error', errorMsg: error.message }, responseCode: 400 })
    return res.status(400).json({ 'Error code': 1, description: 'internal server error' })
  }
}

const requestConsoleLog = (req) => {
  console.log('=====Request', req.route.path, req.body)
}

const errorConsoleLog = (error) => {
  console.log('=======error', error)
}
