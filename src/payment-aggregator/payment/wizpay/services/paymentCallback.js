import { Op } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import {
  DEPOSIT_REQUEST_STATUS,
  QUEUE_WORKER_CONSTANT,
  SUBSCRIPTION_CHANNEL,
  TRANSACTION_TYPES,
  WIZPAY_PG_INTEGRATION_CONSTANT
} from '../../../../common/constants'
import currencyConversion from '../../../../common/currencyConversion'
import ServiceBase from '../../../../common/serviceBase'
import userCurrencyExchange from '../../../../common/userCurrencyExchange'
import { walletLocking } from '../../../../common/walletLocking'
import { depositBonusCheck } from '../common/depositBonusCheck'

/**
 * payment callback
 * @export
 * @class paymentCallback
 * @extends {ServiceBase}
 */
const constraints = {
  // type: {
  //   type: 'string',
  //   presence: { message: '' }
  // },
  amount: {
    type: 'integer',
    presence: { message: '' }
  },
  orderId: {
    type: 'string',
    presence: { message: '' }
  },
  // utr: {
  //   type: 'string',
  //   presence: { message: '' }
  // },
  status: {
    type: 'string',
    presence: { message: '' }
  }
}

export default class paymentCallback extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: bodyArgs,
      context: {
        databaseConnection: {
          Transaction: TransactionModel,
          Wallet: WalletModel,
          DepositRequest: DepositRequestModel,
          User: UserModel,
          QueueLog: QueueLogModel
        }
      }
    } = this

    const { sequelizeTransaction } = this.context

    const responseObject = {}

    try {
      const orderDetails = await DepositRequestModel.findOne({
        attributes: ['id', 'tenantId', 'paymentProviderId', 'userId', 'status'],
        where: {
          orderId: bodyArgs.orderId,
          amount: bodyArgs.amount,
          status: {
            [Op.in]: [
              DEPOSIT_REQUEST_STATUS.OPEN,
              DEPOSIT_REQUEST_STATUS.IN_PROCESS
            ]
          }
        },
        transaction: sequelizeTransaction,
        useMaster: true
      })
      if (!orderDetails) {
        responseObject.status = WIZPAY_PG_INTEGRATION_CONSTANT.VALIDATION_ERROR
        responseObject.message = WIZPAY_PG_INTEGRATION_CONSTANT.INVALID_REQUEST
        return responseObject
      }

      // Get User Details
      let user = await UserModel.findOne({
        attributes: ['id', 'tenantId', 'userName'],
        where: {
          id: orderDetails.userId
        },
        include: {
          model: WalletModel,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount']
        }
      })

      const depositAmount = parseFloat(bodyArgs.amount)

      // success (approved)
      if (bodyArgs.status === WIZPAY_PG_INTEGRATION_CONSTANT.APPROVED_STATUS) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED

        let transactionObject = {
          targetWalletId: user.Wallet.id,
          targetCurrencyId: user.Wallet.currencyId,
          amount: depositAmount,
          conversionRate: await userCurrencyExchange(this.context, user.Wallet.currencyId),
          targetBeforeBalance: user.Wallet.amount,
          actioneeId: orderDetails.userId,
          actioneeType: 'User',
          tenantId: user.tenantId,
          transactionType: TRANSACTION_TYPES.DEPOSIT,
          transactionId: uuidv4(),
          metaData: {
            orderId: bodyArgs.orderId,
            utr: bodyArgs.utr
          },
          paymentMethod: WIZPAY_PG_INTEGRATION_CONSTANT.PAYMENT_METHOD,
          paymentProviderId: orderDetails.paymentProviderId,
          //errorDescription: 'Completed Successfully',
          errorCode: 0,
          status: 'success',
          success: true,
          comments: WIZPAY_PG_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
        }

        // wallet locking
        const userWallet = await walletLocking(this.context, user)
        await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
        user = { ...user.dataValues, Wallet: userWallet }
        userWallet.amount = parseFloat(userWallet.amount)

        userWallet.amount = userWallet.amount + depositAmount

        const skipWalletHook = true
        await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

        transactionObject = await currencyConversion(this.context, transactionObject, user.Wallet, depositAmount)
        transactionObject.targetAfterBalance = userWallet.amount
        const skipTransactionHook = true
        const txn = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction, skipTransactionHook })

        const txnIds = []
        if (txn) {
          txnIds.push(txn.id)
        }

        await depositBonusCheck(this.context, depositAmount, user, txnIds)

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.TYPE,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }

        const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
        responseObject.queueLogId = queueLog.id
      }

      // failed
      if (bodyArgs.status === WIZPAY_PG_INTEGRATION_CONSTANT.FAILED_STATUS) {
        orderDetails.status = DEPOSIT_REQUEST_STATUS.FAILED
      }

      await orderDetails.save({ transaction: sequelizeTransaction })

      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, {
          UserWalletBalance: {
            walletBalance: user.Wallet.amount,
            userId: user.id,
            nonCashAmount: user.Wallet.nonCashAmount
          }
        })

        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.USER_PG_DEPOSIT_NOTIFICATION,
          {
            UserPgDepositNotification:
            {
              userWithdrawAmount: depositAmount,
              success: bodyArgs.status === WIZPAY_PG_INTEGRATION_CONSTANT.APPROVED_STATUS,
              message: `Transaction ${bodyArgs.status}`,
              userWalletBalanceDetail: {
                walletBalance: user.Wallet.amount,
                userId: user.id,
                nonCashAmount: user.Wallet.nonCashAmount
              }
            }
          })
      } catch (error) {
        // throw new Error(error)
      }

      const isSuccess = bodyArgs.status === WIZPAY_PG_INTEGRATION_CONSTANT.FAILED_STATUS || bodyArgs.status === WIZPAY_PG_INTEGRATION_CONSTANT.APPROVED_STATUS
      responseObject.status = isSuccess ? WIZPAY_PG_INTEGRATION_CONSTANT.SUCCESS_CODE : WIZPAY_PG_INTEGRATION_CONSTANT.VALIDATION_ERROR
      responseObject.message = isSuccess ? `Transaction ${bodyArgs.status}` : WIZPAY_PG_INTEGRATION_CONSTANT.INVALID_STATUS_CODE

      return responseObject
    } catch (error) {
      throw new Error(error)
    }
  }
}
