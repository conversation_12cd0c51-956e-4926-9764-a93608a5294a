import { isEmpty } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { checkLimit } from '../../../../common/checkLimit'
import { WIZPAY_PG_INTEGRATION_CONSTANT } from '../../../../common/constants'
import ServiceBase from '../../../../common/serviceBase'
import createHash from '../common/createHash'
import processPaymentResponse from '../common/processPaymentResponse'
const axios = require('axios')

/**
 * process payment
 * @export
 * @class ProcessPayment
 * @extends {ServiceBase}
 */

const constraints = {
  paymentProvider: {
    type: 'string',
    presence: { message: '' }
  },
  paymentProviderId: {
    type: 'integer',
    presence: { message: '' }
  },
  amount: {
    type: 'integer',
    presence: { message: '' }
  }
}

export default class ProcessPayment extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          tenantPaymentConfiguration: tenantPaymentConfigurationModel,
          DepositRequest: DepositRequestModel,
          Tenant: TenantModel
        },
        tenant: { id: tenantId }
      },
      args: { userDetail }

    } = this

    const responseObject = processPaymentResponse(this.args)
    const userId = userDetail.id
    const paymentProviderId = this.args.paymentProviderId
    const amount = parseFloat(this.args.amount)

    // amount check
    if (amount < 1) {
      responseObject.error = 1
      responseObject.errorDescription = WIZPAY_PG_INTEGRATION_CONSTANT.INVALID_AMOUNT
      responseObject.success = 0
      return responseObject
    }

    try {
      // Deposit limit check
      const allowed = await checkLimit.depositLimitCheck(this.context, userId, amount)
      if (allowed === WIZPAY_PG_INTEGRATION_CONSTANT.DAILY_DEPOSIT_LIMIT || allowed === WIZPAY_PG_INTEGRATION_CONSTANT.WEEKLY_DEPOSIT_LIMIT || allowed === WIZPAY_PG_INTEGRATION_CONSTANT.MONTHLY_DEPOSIT_LIMIT) {
        responseObject.error = 1
        responseObject.errorDescription = `${allowed} ${WIZPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_LIMIT_EXCEED}`
        responseObject.success = 0
        return responseObject
      }

      // Fetch Payment Provider Details
      const paymentProviders = await tenantPaymentConfigurationModel.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: tenantId,
          providerId: paymentProviderId
        }
      })

      // Checking If Payment Provider is not found for the tenant then we return false
      if (isEmpty(paymentProviders)) {
        responseObject.error = 1
        responseObject.errorDescription = WIZPAY_PG_INTEGRATION_CONSTANT.PAYMENT_PROVIDER_NOT_FOUND
        responseObject.success = 0
        return responseObject
      }

      // for finding return URL
      const tenantDetails = await TenantModel.findOne({
        attributes: ['domain'],
        where: {
          id: tenantId
        }
      })

      const paymentUrl = paymentProviders.providerKeyValues.paymentUrl
      const xkey = paymentProviders.providerKeyValues.xkey
      const secretKey = paymentProviders.providerKeyValues.secretKey
      const vendorName = paymentProviders.providerKeyValues.vendorName
      const merchantOrderID = uuidv4()
      const requestData = {
        customerName: userDetail.firstName || '',
        customerIp: '',
        customerMobile: userDetail.phone || '',
        customerUPIID: '',
        merchantOrderID,
        amount,
        mode: WIZPAY_PG_INTEGRATION_CONSTANT.REQUEST_DATA_MODE,
        type: WIZPAY_PG_INTEGRATION_CONSTANT.REQUEST_DATA_TYPE,
        clientName: '',
        paymentStatus: '',
        returnUrl: `https://${tenantDetails.domain}`
      }

      const hash = createHash(requestData, secretKey)

      const makeApiCall = async () => {
        try {
          const response = await axios.post(paymentUrl, requestData, {
            headers: {
              'x-key': xkey,
              'x-hash': hash,
              vendor: vendorName
            }
          })

          const { PENDING_ORDER_STATUS, REJECTED_ORDER_STATUS, UNASSIGNED_ORDER_STATUS, FAILED_ORDER_STATUS, REQUEST_REJECTED, REQUEST_UNASSIGNED, REQUEST_FAILED, INTERNAL_ERROR } = WIZPAY_PG_INTEGRATION_CONSTANT

          if (!response.data.success) {
            responseObject.errorDescription = response.data.message ? response.data.message : INTERNAL_ERROR
          } else {
            const orderStatus = response.data.data.orderStatus
            if (orderStatus === PENDING_ORDER_STATUS) {
              const depositType = WIZPAY_PG_INTEGRATION_CONSTANT.DEPOSIT_TYPE
              await DepositRequestModel.create({ orderId: merchantOrderID, trackingId: response.data.data.refID, userId, paymentProviderId, amount, tenantId, depositType })
              responseObject.success = 1
              responseObject.url = response.data.data.redirectURL
              return responseObject
            } else if (orderStatus === REJECTED_ORDER_STATUS) {
              responseObject.errorDescription = REQUEST_REJECTED
            } else if (orderStatus === UNASSIGNED_ORDER_STATUS) {
              responseObject.errorDescription = REQUEST_UNASSIGNED
            } else if (orderStatus === FAILED_ORDER_STATUS) {
              responseObject.errorDescription = REQUEST_FAILED
            } else {
              responseObject.errorDescription = INTERNAL_ERROR
            }
          }

          responseObject.error = 1
          responseObject.success = 0
          return responseObject
        } catch (error) {
          console.log('--------------error-data---------', error.response)
          console.log('--------------error making API call---------', error.message)
          responseObject.error = 1
          responseObject.errorDescription = 'Something Went Wrong'
          responseObject.success = 0
          return responseObject
        }
      }

      await makeApiCall()
      return responseObject
    } catch (err) {
      console.log('------ProcessPayment service ----error------', err)
      throw new Error(err)
    }
  }
}
