import { WIZPAY_PG_INTEGRATION_CONSTANT } from '../../../../common/constants'
import config from '../../../../config/app'
import { response } from '../common/response'
const jwt = require('jsonwebtoken')
module.exports = async (req, res, next) => {
  try {
    const authConfig = config.getProperties().auth
    const { databaseConnection: model } = req
    const secretKey = authConfig.jwt_secret
    const token = req.headers.authorization.split(' ')[1]
    const decodedToken = jwt.verify(token, secretKey)
    const userId = decodedToken.userId
    if (req.body.userId && req.body.userId !== userId) {
      return response(res, { status: 403, message: WIZPAY_PG_INTEGRATION_CONSTANT.INVALID_TOKEN })
    } else {
      const user = await model.User.findOne({
        where: {
          id: decodedToken.id,
          active: true
        },
        raw: true
      })
      if (!user) {
        return response(res, { status: 403, message: WIZPAY_PG_INTEGRATION_CONSTANT.USER_NOT_FOUND })
      }

      req.body.id = user.id
      req.body.userDetail = user
      req.query.id = user.id
      req.query.userDetail = user
      req.body.impersonated = decodedToken?.impersonated || false
      next()
    }
  } catch {
    res.status(401).json({
      error: WIZPAY_PG_INTEGRATION_CONSTANT.INVALID_TOKEN
    })
  }
}
