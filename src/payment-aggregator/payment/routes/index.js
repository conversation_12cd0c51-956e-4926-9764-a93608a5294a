import express from 'express'
import ackopayRoutes from './ackopay.payment.routes'
import cloudCashRoutes from './cloudCash.payment.routes'
import ipayPaymentRoutes from './ipay.payment.routes'
import jaspayRoutes from './jaspay.payment.routes'
import paycookiesRoutes from './paycookies.payment.routes'
import paywingsPaymentRoutes from './paywings.payment.routes'
import peerPayRoutes from './peerPay.payment.routes'
import sambhavPayPaymentRoutes from './sambhavPay.payment.routes'
import saspayPaymentRoutes from './saspay.payment.routes'
import seylanRoutes from './seylan.payment.routes'
import uuWalletRoutes from './uuWallet.routes'
import xamaxRoutes from './xamax.payment.routes'
import zenxpayRoutes from './zenxpay.payment.routes'
import techpayRoutes from './techpay.payment.routes'

const paymentRouter = express.Router()

paymentRouter.use('/saspay', saspayPaymentRoutes)
paymentRouter.use('/paywings', paywingsPaymentRoutes)
paymentRouter.use('/sambhavPay', sambhavPayPaymentRoutes)
paymentRouter.use('/ipay', ipayPaymentRoutes)
paymentRouter.use('/paycookies', paycookiesRoutes)
paymentRouter.use('/ackopay', ackopayRoutes)
paymentRouter.use('/jaspay', jaspayRoutes)
paymentRouter.use('/xamax', xamaxRoutes)
paymentRouter.use('/cloudCash', cloudCashRoutes)
paymentRouter.use('/peerPay', peerPayRoutes)
paymentRouter.use('/uu-wallet', uuWalletRoutes)
paymentRouter.use('/zenxpay', zenxpayRoutes)
paymentRouter.use('/seylan', seylanRoutes)
paymentRouter.use('/techpay', techpayRoutes)

export default paymentRouter
