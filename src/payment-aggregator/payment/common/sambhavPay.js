import { SAMBHAVPAY_PG_INTEGRATION_CONSTANT } from '../../../common/constants'
const CryptoJS = require('crypto-js')

async function getEncrypt (params, credentials) {
  let hashVarsSeq = []
  hashVarsSeq = SAMBHAVPAY_PG_INTEGRATION_CONSTANT.REQUEST_SEQUENCE.split('|')
  let hashString = ''
  let i = 1
  const count = hashVarsSeq.length
  hashVarsSeq.forEach((hashVar) => {
    hashString = hashString + params[hashVar]
    if (i !== count) hashString += ','
    i++
  })
  const encryptData = await encrypt(hashString, credentials)
  return encryptData
}

async function getCheckSum (params, saltKey) {
  const dataString = params.OrderNo + ',' + params.TotalAmount + ',' + params.TransactionMethod + ',' + params.BankCode + ',' + params.VPA + ',' + params.CardNumber + ',' + params.ExpiryDate + ',' + params.CVV
  saltKey = await base64Encode(saltKey)
  let hashValue = CryptoJS.HmacSHA512(dataString, saltKey)
  hashValue = CryptoJS.enc.Hex.stringify(hashValue)
  hashValue = hashValue.toString().toUpperCase()
  return hashValue
}

async function getResponse (respData, mid, checkSum, credentials) {
  if (mid === credentials.mid) {
    const response = await decrypt(respData, credentials)
    const respArray = response.split(',')
    const hashRespSeq = SAMBHAVPAY_PG_INTEGRATION_CONSTANT.RESPONSE_SEQUENCE.split('|')
    let i = 0
    const returnArray = {}
    hashRespSeq.forEach((hashVar) => {
      returnArray[hashVar] = respArray[i]
      i++
    })
    const checkSumTxnResp = await createCheckSumTxnResp(credentials.saltKey, returnArray.OrderNo, returnArray.PayAmount, returnArray.RespCode, returnArray.RespMessage)
    if (checkSum === checkSumTxnResp) {
      const ResponseArray = {}
      ResponseArray.Mid = returnArray.Mid
      ResponseArray.OrderNo = returnArray.OrderNo
      ResponseArray.TxnRefNo = returnArray.TxnRefNo
      ResponseArray.TotalAmount = parseFloat(returnArray.TotalAmount) / 100
      ResponseArray.CurrencyName = returnArray.CurrencyName
      ResponseArray.MeTransReqType = returnArray.MeTransReqType
      ResponseArray.AddField1 = returnArray.AddField1
      ResponseArray.AddField2 = returnArray.AddField2
      ResponseArray.AddField3 = returnArray.AddField3
      ResponseArray.AddField4 = returnArray.AddField4
      ResponseArray.AddField5 = returnArray.AddField5
      ResponseArray.AddField6 = returnArray.AddField6
      ResponseArray.AddField7 = returnArray.AddField7
      ResponseArray.AddField8 = returnArray.AddField8
      ResponseArray.AddField9 = returnArray.AddField9
      ResponseArray.AddField10 = returnArray.AddField10
      ResponseArray.EmailId = returnArray.EmailId
      ResponseArray.MobileNo = returnArray.MobileNo
      ResponseArray.Address = returnArray.Address
      ResponseArray.City = returnArray.City
      ResponseArray.State = returnArray.State
      ResponseArray.Pincode = returnArray.Pincode
      ResponseArray.RespCode = returnArray.RespCode
      ResponseArray.RespMessage = returnArray.RespMessage
      ResponseArray.PayAmount = parseFloat(returnArray.PayAmount) / 100
      ResponseArray.TxnRespDate = returnArray.TxnRespDate
      ResponseArray.UPIString = returnArray.UPIString

      return JSON.stringify(ResponseArray, null, 2)
    } else {
      return false // CheckSum Miss Match!
    }
  }
}

async function createCheckSumTxnResp (saltKey, orderNo, amount, RespCode, RespMessage) {
  const dataString = orderNo + ',' + amount + ',' + RespCode + ',' + RespMessage
  saltKey = await base64Encode(saltKey)
  let hashValue = CryptoJS.HmacSHA512(dataString, saltKey)
  hashValue = CryptoJS.enc.Hex.stringify(hashValue)
  hashValue = hashValue.toString().toUpperCase()
  return hashValue
}

async function base64Encode (data) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data))
}

async function encrypt (hashString, credentials) {
  const iv = CryptoJS.lib.WordArray.create(16)
  let key = await fixKey(credentials)
  key = await derivateKey(key, credentials.spSaltKey, 65536, 256)
  const cipher = CryptoJS.AES.encrypt(hashString, key, {
    iv: iv,
    format: CryptoJS.format.OpenSSL
  })
  return cipher.toString()
}

async function decrypt (data, credentials) {
  const iv = CryptoJS.lib.WordArray.create(16)
  let key = await fixKey(credentials)
  key = await derivateKey(key, credentials.spSaltKey, 65536, 256)
  const decrypted = CryptoJS.AES.decrypt(data, key, {
    iv: iv,
    format: CryptoJS.format.OpenSSL
  })
  return decrypted.toString(CryptoJS.enc.Utf8)
}

async function fixKey (credentials) {
  if (credentials.secretKey.length < credentials.keyLen) {
    // 0 pad to length keyLength
    return credentials.secretKey.padEnd(credentials.keyLen, '0')
  }
  if (credentials.secretKey.length > credentials.keyLen) {
    // Truncate to keyLength characters
    return credentials.secretKey.substring(0, credentials.keyLen)
  }
  return credentials.secretKey
}

async function derivateKey (password, salt, iterations, keyLengthBits) {
  const key = CryptoJS.PBKDF2(password, salt, {
    keySize: keyLengthBits / 32,
    iterations: iterations,
    hasher: CryptoJS.algo.SHA256
  })
  return key
}

module.exports = {
  getEncrypt,
  getCheckSum,
  getResponse
}
