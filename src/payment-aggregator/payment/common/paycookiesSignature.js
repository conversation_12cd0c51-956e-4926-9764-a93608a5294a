import CryptoJS from "crypto-js"
/**
 * Generates a secure hash key based on specified parameters and a salt value.
 *
 * @param {Object} parameters - An object containing parameters to be included in the hash.
 * @param {string} salt - A salt value used to enhance security.
 *
 * @return {string|null} - The generated secure hash key or null if the hashData is empty.
 */
export default function generateSignatureHash (parameters, salt) {
  if (salt === undefined || salt.trim() === '') {
    throw new Error('Salt value is required to generate signature hash.')
  }
  Object.keys(parameters).forEach((key) => {
    if (parameters[key] === undefined || parameters[key].toString().trim() === '' ||
      key === 'signature' ) {
      delete parameters[key]
    }
  })
  const sortedKeys = Object.keys(parameters).sort()
  let hashData = salt

  for (const key of sortedKeys) {
    const value = parameters[key]
    if (value.length > 0) {
      hashData += '|' + value.toString().trim()
    }
  }

  if (hashData.length > 0) {
    return CryptoJS.SHA512(hashData).toString(CryptoJS.enc.Hex).toUpperCase()
  }
  return null
}
