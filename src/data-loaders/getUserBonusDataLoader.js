import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get user bonus for both losing and deposit bonus
 * @export
 * @class GetUserBonusDataLoader
 * @extends {ServiceBase}
 */
export default class GetUserBonusDataLoader extends ServiceBase {
  async run () {
    const { UserBonus: UserBonusModel } = this.context.databaseConnection
    const bonusIds = this.args.map(ele => (ele.bonusId))
    const userBonus = await UserBonusModel.findAll({
      where: {
        bonusId: { [Op.in]: bonusIds },
        userId: this.args[0]?.userId
      },
      raw: true
    })
    const userBonusSorted = dataloaderSort(bonusIds, userBonus, 'bonusId')
    return userBonusSorted
  }
}
