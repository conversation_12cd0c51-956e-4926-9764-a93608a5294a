import { PAGINATION_CONSTANT } from '../common/constants'
import ServiceBase from '../common/serviceBase'

/**
 * get menu items of page menu
 * @export
 * @class GetMenuItemDataLoader
 * @extends {ServiceBase}
 */
export default class GetMenuItemDataLoader extends ServiceBase {
  async run () {
    const MenuItemModel = this.context.databaseConnection.MenuItem

    const limit = this.args?.[0]?.limit !== undefined ? this.args[0].limit : PAGINATION_CONSTANT.LIMIT
    const offset = this.args?.[0]?.limit !== undefined ? this.args[0].offset : PAGINATION_CONSTANT.OFFSET
    const menuId = this.args?.[0]?.menuId

    const pageMenuIds = []
    for (let i = 0; i < this.args.length; i++) {
      pageMenuIds.push(this.args[i].parentPageMenuId)
    }

    let menuItems = []

    if (menuId) {
      menuItems = await MenuItemModel.findAll({
        where: { pageMenuId: menuId, active: true },
        order: [['featured', 'DESC'], ['order', 'ASC'], ['id', 'ASC']],
        raw: true,
        limit,
        offset
      })
    } else {
      for (const id of pageMenuIds) {
        const items = await MenuItemModel.findAll({
          where: { pageMenuId: id, active: true },
          order: [['featured', 'DESC'], ['order', 'ASC'], ['id', 'ASC']],
          raw: true,
          limit,
          offset
        })

        menuItems.push(...items)
      }
    }
    const responseArray = []

    const argsObj = menuItems.reduce((currentObj, element) => {
      (currentObj[element.pageMenuId] = currentObj[element.pageMenuId] || []).push(element)
      return currentObj
    }, {})

    pageMenuIds.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })

    return responseArray
  }
}
