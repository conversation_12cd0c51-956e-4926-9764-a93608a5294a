import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get wallet's currency
 * @export
 * @class WalletCurrency
 * @extends {ServiceBase}
 */
export default class GetUserWalletCurrencyDataLoader extends ServiceBase {
  async run () {
    const CurrencyModel = this.context.databaseConnection.Currency

    const usersCurrency = await CurrencyModel.findAll({
      where: {
        id: { [Op.in]: this.args }
      },
      raw: true
    })
    const usersWalletSorted = dataloaderSort(this.args, usersCurrency, 'id')
    return usersWalletSorted
  }
}
