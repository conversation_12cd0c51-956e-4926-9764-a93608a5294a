import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'

/**
 * get casino game provider
 * @export
 * @class GetCasinoGameProviderDataLoader
 * @extends {ServiceBase}
 */

export default class GetCasinoGameProviderDataLoader extends ServiceBase {
  async run () {
    const { CasinoProvider: CasinoProviderModel } = this.context.databaseConnection

    const gameProvider = await CasinoProviderModel.findAll({
      where: {
        id: { [Op.in]: this.args }
      },
      raw: true
    })

    const gameProviderSorted = dataloaderSort(this.args, gameProvider, 'id')

    return gameProviderSorted
  }
}
