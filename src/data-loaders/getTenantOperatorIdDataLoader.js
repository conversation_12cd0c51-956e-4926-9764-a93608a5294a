import ServiceBase from '../common/serviceBase'
import keyValue<PERSON><PERSON><PERSON><PERSON> from '../lib/keyValueToJSON'
/**
 * get tenant's operator id
 * @export
 * @class GetTenantOperatorIdDataLoader
 * @extends {ServiceBase}
 */
export default class GetTenantOperatorIdDataLoader extends ServiceBase {
  async run () {
    const TenantCredentialModel = this.args[0].databaseConnection.TenantCredential
    const tenant = this.args[0].Tenant
    const operatorId = await keyValueTo<PERSON>son(TenantCredentialModel, ['APP_EZUGI_OPERATOR_ID'], 'tenantId', tenant.id)
    const response = { id: operatorId.APP_EZUGI_OPERATOR_ID }
    return [response]
  }
}
