import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
import { sequelize } from '../db/models'
/**
 * get losing bonus setting
 * @export
 * @class GetLosingBonusSettingDataLoader
 * @extends {ServiceBase}
 */
export default class GetLosingBonusSettingDataLoader extends ServiceBase {
  async run () {
    const LosingBonusSettingModel = this.context.databaseConnection.LosingBonusSetting
    const PagesModel = this.context.databaseConnection.Page
    const losingBonusSetting = await LosingBonusSettingModel.findAll({
      where: {
        bonusId: { [Op.in]: this.args }
      },
      raw: true
    })

    const losingBonusSettingWithPages = await Promise.all(
      losingBonusSetting.map(async (setting) => {
        const { providerDetails } = setting
        delete setting.providerDetails

        let providers = []

        if (providerDetails) {
          providers = await Promise.all(
            providerDetails.map(async (providerDetail) => {
              try {
                const { top_menu_id: topMenuId, game_id: gameIds } = providerDetail || {}

                // Fetch pages
                const pages = await PagesModel.findAll({
                  where: { id: providerDetail.id, topMenuId },
                  attributes: ['id', 'title'],
                  raw: true
                })

                const titles = pages.map((page) => page.title)
                let gameArray = ['Bonus applies to every game']

                if (gameIds?.length > 0) {
                  const replacements = {
                    tenantId: this.context.tenant?.id,
                    topMenuId,
                    id: providerDetail.id,
                    gameIds
                  }

                  const pageIds = pages.map((page) => page.id)
                  let providerId

                  // Query to fetch ProviderId by page Name
                  const reqQuery = `
                  SELECT DISTINCT CAST(casino_items.provider AS INT) AS provider_id
                  FROM pages
                  JOIN aggregator a ON pages.id = a.page_id AND a.status = true
                  LEFT JOIN page_menus ON pages.id = page_menus.page_id
                  LEFT JOIN menu_items ON page_menus.id = menu_items.page_menu_id
                  LEFT JOIN casino_items ON menu_items.casino_item_id = casino_items.id
                  WHERE pages.id = :pageId;
                `
                  let gameProviders = []

                  const results = await sequelize.query(reqQuery, {
                    replacements: { pageId: pageIds[0] }, // Ensure pageIds is an array
                    type: sequelize.QueryTypes.SELECT
                  })

                  gameProviders = results.map(row => '' + row.provider_id)

                  if (gameProviders.length) {
                    providerId = gameProviders[0]
                  }

                  const gameNames = await sequelize.query(`
                  WITH game_names AS (
                  SELECT g.name
                  FROM casino_items g
                  WHERE g.uuid = ANY(ARRAY[:gameIds])
                  AND tenant_id = :tenantId
                  ${providerId ? 'AND g.provider = :providerId' : ''}
                  )
                  SELECT array_agg(game_names.name) AS gameName
                  FROM game_names;
                `, { replacements: { ...replacements, providerId }, type: sequelize.QueryTypes.SELECT, raw: true, useMaster: false })

                  if (gameNames[0]?.gamename?.length > 0) {
                    gameArray = gameNames[0]?.gamename
                  }
                }

                return { titles, games: gameArray }
              } catch (error) {
                return { titles: [], games: ['Error fetching games'] }
              }
            })
          )
        }
        return {
          ...setting,
          providers: providers
        }
      })
    )
    const losingBonusSettingSorted = dataloaderSort(this.args, losingBonusSettingWithPages, 'bonusId')
    return losingBonusSettingSorted
  }
}
