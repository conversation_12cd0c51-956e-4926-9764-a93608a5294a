import dataloaderSort from 'dataloader-sort'
import { Op, Sequelize } from 'sequelize'
import { ENVIORNMENT, OTB_TENANTS_CONFIG, SPORTS_FREEBET_CONFIG } from '../../src/common/constants'
import ServiceBase from '../common/serviceBase'
import config from '../config/app'

/**
 * get user's wallet
 * @export
 * @class UserWallet
 * @extends {ServiceBase}
 */
export default class GetDemoUserWalletDataLoader extends ServiceBase {
  async run () {
    const WalletModel = this.context.databaseConnection.Wallet
    const CurrencyModel = this.context.databaseConnection.Currency
    const UserBonusModel = this.context.databaseConnection.UserBonus
    const Tenant = this.context.tenant
    const isOTBEnabled = config.get('env') === ENVIORNMENT.PRODUCTION
      ? OTB_TENANTS_CONFIG.PROD[Tenant.id]
      : OTB_TENANTS_CONFIG.STAGE[Tenant.id]
    const isSFBEnabled = config.get('env') === ENVIORNMENT.PRODUCTION
      ? SPORTS_FREEBET_CONFIG.PROD[Tenant.id]
      : SPORTS_FREEBET_CONFIG.STAGE[Tenant.id]
    const usersWallet = await WalletModel.findAll({
      attributes: ['amount', 'id', 'currencyId', 'nonCashAmount', 'ownerId', 'oneTimeBonusAmount', 'sportsFreebetAmount'],
      where: {
        ownerId: { [Op.in]: this.args },
        ownerType: 'User'
      },
      include: [{
        model: CurrencyModel,
        attributes: ['code'],
        where: {
          code: {
            [Op.eq]: Sequelize.literal(`(
              SELECT "value"
              FROM "tenant_credentials"
              WHERE "key" = 'TENANT_BASE_CURRENCY'
                AND "tenant_id" = ${Tenant.id}
            )`)
          }
        }
      }],
      raw: true
    })
    const bonusAmount = await UserBonusModel.findOne({
      where: {
        status: 'active',
        kind: 'deposit',
        userId: { [Op.in]: this.args },
        expiresAt: {
          [Op.gte]: new Date()
        }
      },
      attributes: ['bonusAmount'],
      raw: true
    })

    const transformedWallets = usersWallet.map(wallet => ({
      amount: wallet.amount,
      id: wallet.id,
      currencyId: wallet.currencyId,
      nonCashAmount: wallet.nonCashAmount,
      ownerId: wallet.ownerId,
      bonusAmount: bonusAmount || 0,
      ...(!isOTBEnabled ? { oneTimeBonusAmount: wallet.oneTimeBonusAmount } : {}),
      ...(!isSFBEnabled ? { sportsFreebetAmount: wallet.sportsFreebetAmount } : {}),
    }))

    const usersWalletSorted = dataloaderSort(this.args, transformedWallets, 'ownerId')
    return usersWalletSorted
  }
}
