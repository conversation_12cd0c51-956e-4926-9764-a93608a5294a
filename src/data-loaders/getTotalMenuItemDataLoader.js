import { Sequelize } from 'sequelize'
import ServiceBase from '../common/serviceBase'

/**
 * get menu items of page menu
 * @export
 * @class GetMenuItemDataLoader
 * @extends {ServiceBase}
 */
export default class GetTotalMenuItemDataLoader extends ServiceBase {
  async run () {
    const MenuItemModel = this.context.databaseConnection.MenuItem

    const menuId = this.args?.[0]?.menuId

    const pageMenuIds = []
    for (let i = 0; i < this.args.length; i++) {
      pageMenuIds.push(this.args[i].parentPageMenuId)
    }

    const totalMenuItems = []
    let totalCount = 0
    if (menuId) {
      totalCount = await MenuItemModel.findAll({
        where: { pageMenuId: menuId, active: true },
        raw: true,
        attributes: ['pageMenuId',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: 'pageMenuId'
      })
    } else {
      for (const id of pageMenuIds) {
        totalCount = await MenuItemModel.findAll({
          where: { pageMenuId: id, active: true },
          raw: true,
          attributes: ['pageMenuId',
            [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
          ],
          group: 'pageMenuId'

        })
        totalMenuItems.push(...totalCount)
      }
    }
    const responseArray = []
    const argsObj = totalMenuItems.reduce((currentObj, element) => {
      (currentObj[element.pageMenuId] = currentObj[element.pageMenuId] || []).push(element)
      return currentObj
    }, {})
    pageMenuIds.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })
    return responseArray
  }
}
