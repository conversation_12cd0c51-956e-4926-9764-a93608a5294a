import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get casino games for transactions
 * @export
 * @class GetCasinoGameDataLoader
 * @extends {ServiceBase}
 */

export default class GetCasinoGameDataLoader extends ServiceBase {
  async run () {
    const { CasinoGame: CasinoGameModel } = this.context.databaseConnection

    const uniquePairs = new Set();

    this.args.forEach(element => {
      if (element.gameId !== null && element.providerId !== null) {
        uniquePairs.add(`${element.gameId}|${element.providerId}`);
      }
    });

    const conditions = Array.from(uniquePairs).map(pair => {
      const [gameId, providerId] = pair.split('|');
      return {
        [Op.and]: [
          { gameId: gameId },
          { casinoProviderId: providerId }
        ]
      };
    });

    const casinoGames = await CasinoGameModel.findAll({
      where: {
        [Op.or]: conditions
      },
      raw: true
    })

    const casinoGamesSorted = []
    const argsObj = casinoGames.reduce((currentObj, element) => {
      currentObj[element.gameId] = element
      return currentObj
    }, {})

    this.args.forEach(element => {
      casinoGamesSorted.push(argsObj[element.gameId] || null)
    })

    return casinoGamesSorted
  }
}
