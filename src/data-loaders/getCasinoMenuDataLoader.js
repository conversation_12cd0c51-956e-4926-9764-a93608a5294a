import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get page menus
 * @export
 * @class GetPageMenuDataLoader
 * @extends {ServiceBase}
 */
export default class GetCasinoMenuDataLoader extends ServiceBase {
  async run () {
    const CasinoMenuModel = this.context.databaseConnection.CasinoMenu
    const casinoMenus = await CasinoMenuModel.findAll({
      where: {
        id: { [Op.in]: this.args }
      },
      raw: true
    })
    const casinoMenusSorted = dataloaderSort(this.args, casinoMenus, 'id')
    return casinoMenusSorted
  }
}
