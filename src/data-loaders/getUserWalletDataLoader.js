import dataloaderSort from 'dataloader-sort'
import { Op, Sequelize } from 'sequelize'
import { BONUS_TYPES, ENVIORNMENT, MULTI_BONUS_STATUS, OTB_TENANTS_CONFIG, SPORTS_FREEBET_CONFIG } from '../../src/common/constants'
import ServiceBase from '../common/serviceBase'
import config from '../config/app'

/**
 * get user's wallet
 * @export
 * @class UserWallet
 * @extends {ServiceBase}
 */
export default class GetUserWalletDataLoader extends ServiceBase {
  async run () {
    const WalletModel = this.context.databaseConnection.Wallet
    const UserBonusModel = this.context.databaseConnection.UserBonus
    const UserBonusQueueModel = this.context.databaseConnection.UserBonusQueue
    const BonusModel = this.context.databaseConnection.Bonus
    const tenantId = this.context.tenant.id
    const isOTBEnabled = config.get('env') === ENVIORNMENT.PRODUCTION
      ? OTB_TENANTS_CONFIG.PROD[tenantId]
      : OTB_TENANTS_CONFIG.STAGE[tenantId]
    const isSFBEnabled = config.get('env') === ENVIORNMENT.PRODUCTION
      ? SPORTS_FREEBET_CONFIG.PROD[tenantId]
      : SPORTS_FREEBET_CONFIG.STAGE[tenantId]

    const usersWallet = await WalletModel.findAll({
      where: {
        ownerId: { [Op.in]: this.args },
        ownerType: 'User'
      },
      attributes: {
        ...(!isOTBEnabled ? { exclude: ['oneTimeBonusAmount'] } : {}),
        ...(!isSFBEnabled ? { exclude: ['sportsFreebetAmount'] } : {})
      },
      raw: true
    })

    const userBonusAmount = await UserBonusModel.findOne({
      where: {
        status: 'active',
        kind: { [Op.in]: ['deposit', 'deposit_sport', 'deposit_both'] },
        userId: { [Op.in]: this.args },
        expiresAt: {
          [Op.gte]: new Date()
        }
      },
      attributes: [
        'bonusAmount', 'rolloverBalance', 'initialRolloverBalance', 'kind',
        [
          Sequelize.literal(`(
            SELECT b.code
            FROM bonus AS b
            WHERE b.id = "UserBonus".bonus_id
          )`),
          'code'
        ]
      ],
      raw: true
    })

    const bonusesInQueue = await UserBonusQueueModel.findAll({
      attributes: ['id', 'bonusAmount', 'rolloverTarget', 'remainingRollover'],
      where: {
        userId: { [Op.in]: this.args },
        status: MULTI_BONUS_STATUS.PENDING
      },
      include: {
        model: BonusModel,
        as: 'Bonus',
        attributes: ['code', 'kind'],
        required: true,
        where: {
          kind: { [Op.in]: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_SPORTS] },
          enabled: true,
          validUpto: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') }
        },
      },
      order: [['ordering', 'ASC']]
    });

    if (usersWallet.length > 0) {
      usersWallet[0].bonusAmount = userBonusAmount?.bonusAmount || 0
      usersWallet[0].rolloverBalance = userBonusAmount?.rolloverBalance || 0
      usersWallet[0].initialRolloverBalance = userBonusAmount?.initialRolloverBalance || 0
      usersWallet[0].rolloverAchived = parseFloat(usersWallet[0].initialRolloverBalance) - parseFloat(usersWallet[0].rolloverBalance)
      usersWallet[0].kind = userBonusAmount?.kind
      usersWallet[0].code = userBonusAmount?.code
      usersWallet[0].bonusesInQueue = bonusesInQueue
    }

    const usersWalletSorted = dataloaderSort(this.args, usersWallet, 'ownerId')
    return usersWalletSorted
  }
}
