import DataLoader from 'dataloader'
import GetCasinoGameDataLoader from './getCasinoGameDataLoader'
import GetCasinoGameProviderDataLoader from './getCasinoGameProviderDataLoader'
import GetCasinoItemDataLoader from './getCasinoItemDataLoader'
import GetCasinoMenuDataLoader from './getCasinoMenuDataLoader'
import GetDemoUserWalletDataLoader from './getDemoUserWalletDataLoader'
import GetDepositBonusSettingDataLoader from './getDepositBonusSettingDataLoader'
import GetDepositBonusTierDataLoader from './getDepositBonusTierDataLoader'
import GetLosingBonusSettingDataLoader from './getLosingBonusSettingDataLoader'
import GetLosingBonusTierDataLoader from './getLosingBonusTierDataLoader'
import GetMenuItemDataLoader from './getMenuItemDataLoader'
import GetNotificationDataLoader from './getNotificationDataLoader'
import GetPageMenuDataLoader from './getPageMenuDataLoader'
import GetTenantOperatorIdDataLoader from './getTenantOperatorIdDataLoader'
import GetTotalMenuItemDataLoader from './getTotalMenuItemDataLoader'
import GetUserBonusDataLoader from './getUserBonusDataLoader'
import GetUserNotificationDataLoader from './getUserNotificationDataLoader'
import GetUserWalletCurrencyDataLoader from './getUserWalletCurrencyDataLoader'
import GetUserWalletDataLoader from './getUserWalletDataLoader'

export default (databaseConnection, tenant) => {
  const getUserWalletDataLoader = new DataLoader(async (keys) => {
    const result = await GetUserWalletDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getUserWalletCurrencyDataLoader = new DataLoader(async (keys) => {
    const result = await GetUserWalletCurrencyDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getPageMenuDataLoader = new DataLoader(async (keys) => {
    const result = await GetPageMenuDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getCasinoMenuDataLoader = new DataLoader(async (keys) => {
    const result = await GetCasinoMenuDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getMenuItemDataLoader = new DataLoader(async (keys) => {
    const result = await GetMenuItemDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getTotalMenuItemDataLoader = new DataLoader(async (keys) => {
    const result = await GetTotalMenuItemDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getNotificationDataLoader = new DataLoader(async (keys) => {
    const result = await GetNotificationDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getCasinoItemDataLoader = new DataLoader(async (keys) => {
    const result = await GetCasinoItemDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getLosingBonusSettingDataLoader = new DataLoader(async (keys) => {
    const result = await GetLosingBonusSettingDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getLosingBonusTierDataLoader = new DataLoader(async (keys) => {
    const result = await GetLosingBonusTierDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getDepositBonusSettingDataLoader = new DataLoader(async (keys) => {
    const result = await GetDepositBonusSettingDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getDepositBonusTierDataLoader = new DataLoader(async (keys) => {
    const result = await GetDepositBonusTierDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getUserBonusDataLoader = new DataLoader(async (keys) => {
    const result = await GetUserBonusDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getCasinoGameDataLoader = new DataLoader(async (keys) => {
    const result = await GetCasinoGameDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getCasinoGameProviderDataLoader = new DataLoader(async (keys) => {
    const result = await GetCasinoGameProviderDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getUserNotificationDataLoader = new DataLoader(async (keys) => {
    const result = await GetUserNotificationDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getDemoUserWalletDataLoader = new DataLoader(async (keys) => {
    const result = await GetDemoUserWalletDataLoader.execute(keys, { databaseConnection, tenant })
    return result.result
  })
  const getTenantOperatorIdDataLoader = new DataLoader(GetTenantOperatorIdDataLoader.run.bind(GetTenantOperatorIdDataLoader))

  return {
    getUserWalletDataLoader,
    getUserWalletCurrencyDataLoader,
    getTenantOperatorIdDataLoader,
    getPageMenuDataLoader,
    getCasinoMenuDataLoader,
    getMenuItemDataLoader,
    getTotalMenuItemDataLoader,
    getNotificationDataLoader,
    getCasinoItemDataLoader,
    getLosingBonusSettingDataLoader,
    getLosingBonusTierDataLoader,
    getDepositBonusSettingDataLoader,
    getDepositBonusTierDataLoader,
    getUserBonusDataLoader,
    getCasinoGameDataLoader,
    getCasinoGameProviderDataLoader,
    getUserNotificationDataLoader,
    getDemoUserWalletDataLoader
  }
}
