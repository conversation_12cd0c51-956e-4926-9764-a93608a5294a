import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get deposit bonus tier
 * @export
 * @class GetDepositBonusTierDataLoader
 * @extends {ServiceBase}
 */
export default class GetDepositBonusTierDataLoader extends ServiceBase {
  async run () {
    const DepositBonusTierModel = this.context.databaseConnection.DepositBonusTier
    const DepositBonusTier = await DepositBonusTierModel.findAll({
      where: {
        depositBonusSettingId: { [Op.in]: this.args }
      },
      raw: true
    })

    const responseArray = []

    const argsObj = DepositBonusTier.reduce((currentObj, element) => {
      (currentObj[element.depositBonusSettingId] = currentObj[element.depositBonusSettingId] || []).push(element)
      return currentObj
    }, {})

    this.args.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })
    return responseArray
  }
}
