import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get page menus
 * @export
 * @class GetPageMenuDataLoader
 * @extends {ServiceBase}
 */
export default class GetPageMenuDataLoader extends ServiceBase {
  async run () {
    const PageMenuModel = this.context.databaseConnection.PageMenu
    const CasinoMenuModel = this.context.databaseConnection.CasinoMenu
    const WebsiteContentModel = this.context.databaseConnection.WebsiteContent

    const pageIds = []
    for (let i = 0; i < this.args.length; i++) {
      pageIds.push(this.args[i].parentPageId)
    }

    const pageId = this.args[0].pageId

    const pageMenus = await PageMenuModel.findAll({
      where: { pageId },
      include: [{
        model: CasinoMenuModel,
        as: 'casinoMenu',
        where: {
          enabled: true
        },
        required: true
      }],
      order: ['menuOrder'],
      raw: true
    })

    const responseArray = []

    const websiteDetails = await WebsiteContentModel.findAll({
      attributes: ['id', 'heading', 'content', 'casinoPageId', 'casinoPageMenuId'],
      where: {
        isActive: true,
        tenantId: this.context.tenant.id,
        casinoPageId: pageId,
        [Op.or]: [
          { casinoPageMenuId: { [Op.ne]: null } },
          { casinoPageMenuId: { [Op.ne]: '0' } }
        ]
      },
      raw: true
    }).reduce((acc, cur) => {
      if (Object.prototype.hasOwnProperty.call(acc, cur.casinoPageMenuId)) {
        acc[cur.casinoPageMenuId].push(cur)
      } else {
        acc[cur.casinoPageMenuId] = [cur]
      }
      return acc
    }, {})

    const argsObj = pageMenus.reduce((currentObj, element) => {
      (currentObj[element.pageId] = currentObj[element.pageId] || []).push({ ...element, cms: websiteDetails[element.id] || [] })
      return currentObj
    }, {})

    pageIds.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })

    return responseArray
  }
}
