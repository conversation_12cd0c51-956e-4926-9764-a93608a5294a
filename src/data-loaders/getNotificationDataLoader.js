import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'

/**
 * get notification
 * @export
 * @class NotificationDataLoader
 * @extends {ServiceBase}
 */
export default class GetNotificationDataLoader extends ServiceBase {
  async run () {
    const NotificationModel = this.context.databaseConnection.Notification

    const notifications = await NotificationModel.findAll({
      where: {
        id: { [Op.in]: this.args }
      },
      raw: true
    })
    const notificationSorted = dataloaderSort(this.args, notifications, 'id')
    return notificationSorted
  }
}
