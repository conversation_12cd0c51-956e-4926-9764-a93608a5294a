import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get losing bonus tier
 * @export
 * @class GetLosingBonusTierDataLoader
 * @extends {ServiceBase}
 */
export default class GetLosingBonusTierDataLoader extends ServiceBase {
  async run () {
    const LosingBonusTierModel = this.context.databaseConnection.LosingBonusTier
    const losingBonusTier = await LosingBonusTierModel.findAll({
      where: {
        losingBonusSettingId: { [Op.in]: this.args }
      },
      raw: true
    })

    const responseArray = []

    const argsObj = losingBonusTier.reduce((currentObj, element) => {
      (currentObj[element.losingBonusSettingId] = currentObj[element.losingBonusSettingId] || []).push(element)
      return currentObj
    }, {})

    this.args.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })
    return responseArray
  }
}
