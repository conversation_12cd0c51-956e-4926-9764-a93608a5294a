// import dataloaderSort from 'dataloader-sort'
import { Op } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get user notifications
 * @export
 * @class GetUserNotificationDataLoader
 * @extends {ServiceBase}
 */
export default class GetUserNotificationDataLoader extends ServiceBase {
  async run () {
    const {
      NotificationReceiver: NotificationReceiverModel
    } = this.context.databaseConnection

    const notifications = await NotificationReceiverModel.findAll({
      where: {
        receiverId: { [Op.in]: this.args },
        receiverType: 'User'
      }
    })

    const responseArray = []

    const argsObj = notifications.reduce((currentObj, element) => {
      (currentObj[element.receiverId] = currentObj[element.receiverId] || []).push(element)
      return currentObj
    }, {})

    this.args.forEach(element => {
      responseArray.push(argsObj[element] || [])
    })

    return responseArray
  }
}
