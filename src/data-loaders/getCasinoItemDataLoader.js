import dataloaderSort from 'dataloader-sort'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../common/serviceBase'
/**
 * get casino items for page menu
 * @export
 * @class GetCasinoItemDataLoader
 * @extends {ServiceBase}
 */
export default class GetCasinoItemDataLoader extends ServiceBase {
  async run () {
      const CasinoItemModel = this.context.databaseConnection.CasinoItem
      const CasinoProviderModel = this.context.databaseConnection.CasinoProvider

      const casinoItems = await CasinoItemModel.findAll({
        where: {
          id: { [Op.in]: this.args }
        },
        raw: true,
        include: {
          model: CasinoProviderModel,
          attributes: ['name'],
          on: Sequelize.literal(`CAST("CasinoItem"."provider" AS BIGINT) = "CasinoProvider"."id"`)
        }
      })

      // Map over the results, rename 'CasinoProvider.name' to 'providerName' and remove 'CasinoProvider.name'
      const transformedItems = casinoItems.map(item => {
        const { 'CasinoProvider.name': providerName, ...rest } = item;
        return {
          ...rest,
          providerName
        };
      });

      const casinoItemsSorted = dataloaderSort(this.args, transformedItems, 'id')
      return casinoItemsSorted
  }
}
