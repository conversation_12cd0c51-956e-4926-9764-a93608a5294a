import { Op, Sequelize } from 'sequelize'
import { BONUS_STATUS } from '../common/constants'

export default (cron, databaseConnection) => {
  const { UserBonus: UserBonusModel } = databaseConnection
  cron.schedule('1 1 */1 * * *', async () => {
    await UserBonusModel.update({
      status: BONUS_STATUS.EXPIRED
    }, {
      where: {
        expiresAt: { [Op.lt]: Sequelize.literal('CURRENT_TIMESTAMP') },
        status: BONUS_STATUS.ACTIVE
      }
    })
  })
}
