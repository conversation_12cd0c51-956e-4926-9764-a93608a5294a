import { Op } from 'sequelize'
import { CASINO_PROVIDER, ENVIORNMENT, JETFAIR_INTEGRATION_CONSTANT } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { errorChecks } from '../common/commonErrorCodeChecks'

const constraints = {
  userToken: {
    type: 'string'
  },
  marketId: {
    type: 'string'
  },
  betslipId: {
    type: ['string', null]
  }
}

/**
 * Provides service for the Auth api in the live-spribe
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class BetDetails extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
          // ToDo for testing purpose we have added console.log('---------------Auth callback---------------', this.args)
    const responseObject = {}

    // Token verification process
    const userId = await errorChecks.TokenCheck(this.args.userToken, responseObject)
    if (!userId) {
      return responseObject
    }

    // User check
    // const user = await errorChecks.userNotFoundCheck(this.context, userId, responseObject)
    // if (!user) return responseObject
    const BetsTransactionModel = this.context.databaseConnection.BetsTransaction
    const BetModel = this.context.databaseConnection.BetsBet
    const BetSlipModel = this.context.databaseConnection.BetsBetslip
    const PullsEventModel = this.context.databaseConnection.PullsEvent

    const isProduction = config.get('env') === ENVIORNMENT.PRODUCTION;
    const whereCondition = {
      marketId: this.args.marketId,
      userId: userId,
      transactionCode: 'PlaceMatchedBet'
    }

    const providerMap = {
      jetfair: isProduction ? CASINO_PROVIDER.JETFAIR.PROD : CASINO_PROVIDER.JETFAIR.STAGE,
      powerplay: isProduction ? CASINO_PROVIDER.POWERPLAY.PROD : CASINO_PROVIDER.POWERPLAY.STAGE
    }

    if (providerMap[this.args.provider]) {
      whereCondition.providerId = providerMap[this.args.provider];
    }

    const betTransaction = await BetsTransactionModel.findOne({
      where: whereCondition,
      attributes: ['betslipId'],
      raw: true
    })

    if (!betTransaction) return {
      code: JETFAIR_INTEGRATION_CONSTANT.SUCCESS,
      message: 'Not Data Found',
      data: { }
    }

    const betSlipDetail = await BetSlipModel.findOne({
      where: { id: betTransaction.betslipId },
      attributes: ['id', 'settlementStatus'],
      include: {
        model: BetModel,
        as: 'bets',
        attributes: ['id', 'eventId', 'market', 'betId']
      }
    })

    const betId = betSlipDetail.bets[0].betId;

    const eventDetail = await PullsEventModel.findOne({
      where: {
        id: betSlipDetail.bets[0].eventId
      },
      attributes: ['id', 'startDate']
    })

    if (betSlipDetail.settlementStatus === 'in_game') {
      betSlipDetail.settlementStatus = 'Not Settled/ Not Declaired'
    }

    const marketDetail = {
      marketId: this.args.marketId,
      marketName: betSlipDetail.bets[0].market,
      result: betSlipDetail.settlementStatus,
      matchDate: eventDetail?.startDate ? eventDetail.startDate : null
    }
    const betList = await BetsTransactionModel.findAll({
      where: {
        marketId: this.args.marketId,
        userId: userId,
        transactionCode: {
          [Op.or]: ['PlaceMatchedBet', 'CancelMarket']
        }
      },
      attributes: ['id', 'journalEntry', 'betslipId', 'amount', 'nonCashAmount', 'runnerName', 'createdAt', 'reverseTransactionId', 'transactionId'],
      raw: true
    })
    let newBetList
    if (betList.length > 0) {
      newBetList = await Promise.all(
        betList.map(async object => {
          if (object.reverseTransactionId) {
            const betTransaction = await BetsTransactionModel.findOne({
              where: { transactionId: object.reverseTransactionId },
              attributes: ['betslipId']
            })
            object.betslipId = betTransaction.betslipId
          }
          const betSlipDetail = await BetSlipModel.findOne({
            where: { id: object?.betslipId },
            attributes: ['id', 'stake', 'multiPrice', 'bettype', 'run']
          })
          const bet = {}
          let isBetWon = true
          let isBack = true
          if (object.journalEntry === 'DR') {
            isBetWon = false
          }
          if (betSlipDetail?.bettype === 2) {
            isBack = false
          }
          bet.betId = betId
          bet.runnerName = object?.runnerName
          bet.rate = parseFloat(betSlipDetail?.multiPrice)
          bet.stake = parseFloat(betSlipDetail?.stake)
          bet.betPL = parseFloat(object?.amount || 0) + parseFloat(object?.nonCashAmount || 0)
          bet.isBetWon = isBetWon
          bet.isBack = isBack
          bet.run = betSlipDetail?.run
          bet.point = null
          bet.createdDate = object?.createdAt
          return bet
        })
      )
    }

    const apiResponse = {}
    apiResponse.marketDetail = marketDetail
    apiResponse.betList = newBetList

    responseObject.code = JETFAIR_INTEGRATION_CONSTANT.SUCCESS_CODE
    responseObject.message = JETFAIR_INTEGRATION_CONSTANT.SUCCESS
    responseObject.data = apiResponse

    return responseObject
  }
}
