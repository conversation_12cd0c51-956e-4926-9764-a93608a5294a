import { verify } from 'jsonwebtoken'
import { JETFAIR_INTEGRATION_CONSTANT } from '../../common/constants'
import config from '../../config/app'

export const errorChecks = {
  userNotFoundCheck: async (context, data, responseObject) => {
    const WalletModel = context.databaseConnection.Wallet
    const CurrencyModel = context.databaseConnection.Currency

    const user = await context.databaseConnection.User.findOne({
      where: { id: data, active: true },
      include: [{
        model: WalletModel,
        where: { currency_id: JETFAIR_INTEGRATION_CONSTANT.CURRENCY_ID },
        include: {
          model: CurrencyModel
        }
      }]
    })

    if (!user) {
      responseObject.code = JETFAIR_INTEGRATION_CONSTANT.JETFAIR_INTERNAL_ERROR_CODE
      responseObject.message = JETFAIR_INTEGRATION_CONSTANT.USER_NOT_FOUND

      return false
    }

    return user
  },
  TokenCheck: async (token, responseObject) => {
    try {
      const authConfig = config.getProperties().auth
      const decodedToken = verify(token, authConfig.jwt_secret)
      const userId = decodedToken.id
      return userId
    } catch (error) {
      if (error.name === JETFAIR_INTEGRATION_CONSTANT.JSON_WEB_TOKEN_ERROR && error.message === JETFAIR_INTEGRATION_CONSTANT.INVALID_SIGNATURE) {
        responseObject.code = JETFAIR_INTEGRATION_CONSTANT.TOKEN_INVALID_CODE
        responseObject.message = JETFAIR_INTEGRATION_CONSTANT.TOKEN_INVALID
        return false
      } else {
        responseObject.code = JETFAIR_INTEGRATION_CONSTANT.TOKEN_EXPIRED_CODE
        responseObject.message = JETFAIR_INTEGRATION_CONSTANT.TOKEN_EXPIRED
        return false
      }
    }
  }

}
