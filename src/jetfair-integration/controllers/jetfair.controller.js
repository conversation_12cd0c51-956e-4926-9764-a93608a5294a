import { JETFAIR_INTEGRATION_CONSTANT } from '../../common/constants'
import { response } from '../common/response'
import BetDetails from '../services/betDetails'

/**
 * Live spribe gameLaunch end point
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const betDetails = async (req, res) => {
  try {
    const data = await BetDetails.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    if (data.successful) return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ code: JETFAIR_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE, message: JETFAIR_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR })
  }
}
