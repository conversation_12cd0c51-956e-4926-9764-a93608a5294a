import { Op } from 'sequelize'
import { paymentForCodes } from '../../betting-integration/common/constant'
import { BONUS_STATUS, BONUS_TYPES } from '../../common/constants'
import getBetslipDetailObject from '../common/getBetslipDetailObject'
import getUserDetailObject from '../common/getUserDetailObject'
import playerParentIds from '../common/playerParentIds'
import playerTotalSportBets from '../common/playerTotalSportBets'
import totalDebitSportTransaction from '../common/totalDebitSportTransaction'
import updatePlayerTotalBets from '../common/updatePlayerTotalBeta'

/**
 * insert Sport betting transaction in elastic-search DB
 *
 * @export
 * @param {object} params it contains inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export default async function addSportBetTransaction (params, sequelize, esClient) {
  const post = params.dataValues

  let sourceWalletOwnerDetails = null
  let targetWalletOwnerDetails = null
  const totalBetObj = {
    id: post.userId
  }

  // this return user detail, user wallet detail,
  // user tenant detail and associated agent/admin
  const userDetail = await getUserDetailObject(params.userId, sequelize.models)

  const userActiveDepositBonus = await sequelize.models.UserBonus.findOne({
    where: {
      status: BONUS_STATUS.ACTIVE,
      kind: BONUS_TYPES.DEPOSIT_SPORTS,
      userId: params.userId
    },
    raw: true
  })

  let betSlipDetail = null
  let betsArray = []
  let betslipDetailsObject = null
  if ((+post.paymentFor) !== paymentForCodes.DEPOSIT_BONUS_PENDING) {
    betSlipDetail = await getBetslipDetailObject(params.betslipId, sequelize.models)

    betsArray = betSlipDetail.bets.map(ele => {
      return {
        id: +ele.id,
        is_deleted: ele.isDeleted,
        bet_id: ele.betId,
        fixture_id: +ele.fixtureId,
        provider_id: +ele.providerId,
        champ: ele.champ,
        match: ele.match,
        market: ele.market,
        name: ele.name,
        price: ele.price,
        start_date: ele.startDate,
        betslip_id: +ele.betslipId,
        market_id: +ele.marketId,
        bet_status: +ele.betStatus,
        event_id: +ele.eventId,
        settlement_status: +ele.settlementStatus,
        livescore: `${JSON.stringify(ele.livescore)}`,
        created_at: ele.createdAt,
        updated_at: ele.updatedAt
      }
    })

    betslipDetailsObject = {
      id: +betSlipDetail.id,
      is_deleted: betSlipDetail.isDeleted,
      bettype: +betSlipDetail.bettype,
      stake: betSlipDetail.stake,
      user_id: +betSlipDetail.stake,
      multi_price: betSlipDetail.multiPrice,
      betslip_status: betSlipDetail.betslipStatus,
      coupon_id: +betSlipDetail.couponId,
      possible_win_amount: betSlipDetail.possibleWinAmount,
      created_at: betSlipDetail.createdAt,
      updated_at: betSlipDetail.updatedAt,
      settlement_status: betSlipDetail.settlementStatus,
      bets: betsArray
    }
  }

  const amountInCurrencies = {}

  const currencies = await sequelize.models.Currency.findAll({
    raw: true
  })

  currencies.map(x => (amountInCurrencies[x.code] = Math.abs(((+post.amount) + (+post.nonCashAmount)) * userDetail.Wallet.Currency.exchangeRate / x.exchangeRate))
  )

  const ownerParentIdArr = await playerParentIds(userDetail.dataValues.id, userDetail.tenantId, sequelize)
  const parentChainIds = []
  const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
    parentChainIds.push(+parent.id)
    currentObj[+parent.id] = parent
    return currentObj
  }, {})

  const AdminUserCommissions = await sequelize.models.AdminUserSetting.findAll({ where: { adminUserId: { [Op.in]: parentChainIds }, key: 'commission_percentage' }, raw: true })

  const reducedAdminCommission = AdminUserCommissions.reduce((currentObj, adminCommission) => {
    currentObj[+adminCommission.adminUserId] = adminCommission
    return currentObj
  }, {})

  const adminCommissionPercentage = []
  for (const [key, value] of Object.entries(parentChainIdsValues)) {
    let commissionPercentage = '000.00'
    if (reducedAdminCommission[key]) {
      const percentageStr = reducedAdminCommission[key].value.split('.')
      commissionPercentage = !percentageStr[1] ? `${percentageStr[0].padStart(3, '0')}.00` : `${percentageStr[0].padStart(3, '0')}.${percentageStr[1].padEnd(2, '0')}`
      adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
    } else {
      adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
    }
  }

  const playerDetails = {
    first_name: userDetail.firstName,
    last_name: userDetail.lastName,
    email: userDetail.email,
    phone: userDetail.phone,
    date_of_birth: userDetail.dateOfBirth,
    gender: userDetail.gender,
    parent_type: userDetail.parentType,
    parent_id: +userDetail.parentId,
    created_at: userDetail.createdAt,
    updated_at: userDetail.updatedAt,
    user_name: userDetail.userName,
    country_code: userDetail.countryCode,
    tenant_id: userDetail.tenantId,
    active: userDetail.active,
    demo: userDetail.demo,
    last_login_date: userDetail.lastLoginDate,
    self_exclusion: userDetail.selfExclusion,
    vip_level: userDetail.vipLevel,
    nick_name: userDetail.nickName,
    phone_code: userDetail.phoneCode,
    kyc_done: userDetail.kycDone,
    wallet_id: +userDetail.Wallet.id,
    amount: +userDetail.Wallet.amount,
    deducted_amount: 0.0,
    added_amount: 0.0,
    non_cash_amount: +userDetail.Wallet.nonCashAmount,
    currency_id: +userDetail.Wallet.currencyId,
    agent_first_name: userDetail.AdminUser.firstName,
    agent_email: userDetail.AdminUser.email,
    agent_last_name: userDetail.AdminUser.lastName,
    agent_name: userDetail.AdminUser.agentName,
    currency: userDetail.Wallet.Currency.code,
    parent_chain_ids: parentChainIds,
    player_id: +userDetail.id,
    deposit_bonus_details: null,
    player_id_s: userDetail.id,
    player_name: userDetail.userName,
    agent_full_name: `${userDetail.AdminUser.firstName} ${userDetail.AdminUser.lastName}`,
    parent_chain_detailed: adminCommissionPercentage
  }

  if (userActiveDepositBonus) {
    playerDetails.deposit_bonus_details = {
      unconverted_active_deposit_bonus: userActiveDepositBonus.bonusAmount,
      active_deposit_bonus_remaining_rollover: userActiveDepositBonus.rolloverBalance
    }
  }

  let currentBalance
  if (post.sourceWalletId != null) {
    sourceWalletOwnerDetails = {
      id: +userDetail.id,
      type: 'User',
      user_name: userDetail.userName,
      first_name: userDetail.firstName,
      last_name: userDetail.lastName,
      email: userDetail.email,
      parent_chain_ids: parentChainIds,
      before_balance: (+post.currentBalance),
      after_balance: ((+post.currentBalance) - ((+post.amount) + (+post.nonCashAmount)))
    }
    currentBalance = sourceWalletOwnerDetails.after_balance
    playerDetails.amount = ((+userDetail.Wallet.amount))
    playerDetails.non_cash_amount = ((+userDetail.Wallet.nonCashAmount))
    playerDetails.deducted_amount = ((+post.amount) + (+post.nonCashAmount))
  } else {
    targetWalletOwnerDetails = {
      id: +userDetail.id,
      type: 'User',
      user_name: userDetail.userName,
      first_name: userDetail.firstName,
      last_name: userDetail.lastName,
      email: userDetail.email,
      parent_chain_ids: parentChainIds,
      before_balance: (+post.currentBalance),
      after_balance: ((+post.currentBalance) + ((+post.amount) + (+post.nonCashAmount)))
    }
    currentBalance = targetWalletOwnerDetails.after_balance
    playerDetails.amount = ((+userDetail.Wallet.amount))
    playerDetails.non_cash_amount = ((+userDetail.Wallet.nonCashAmount))
    playerDetails.added_amount = ((+post.amount) + (+post.nonCashAmount))
  }

  try {
    const transaction = await esClient.index({
      index: 'bet_transaction',
      id: +post.id,
      body: {
        internal_tracking_id: +post.id,
        is_deleted: false,
        amount: +post.amount,
        journal_entry: post.journalEntry,
        status: post.status,
        reference: post.reference,
        description: post.description,
        user_id: +post.userId,
        player_details: playerDetails,
        betslip_id: +betSlipDetail?.id,
        betslip_details: betslipDetailsObject,
        created_at: post.createdAt,
        updated_at: post.updatedAt,
        tenant_id: +post.tenantId,
        target_currency: !post.sourceWalletId ? userDetail.Wallet.Currency.code : null,
        target_wallet: targetWalletOwnerDetails,
        source_currency: post.sourceWalletId ? userDetail.Wallet.Currency.code : null,
        source_wallet: sourceWalletOwnerDetails,
        transaction_type: 'bet_placement',
        transaction_id: post.transactionId,
        conversion_rate: post.conversionRate,
        payment_for: +post.paymentFor,
        non_cash_amount: +post.nonCashAmount,
        current_balance: currentBalance,
        amount_in_currencies: amountInCurrencies
      }
    })

    if (post.journalEntry === 'debit') {
      const playerTotalBet = await playerTotalSportBets(post, esClient)

      if (+playerTotalBet.totalBets) {
        const newParam = { ...params }
        newParam.dataValues.actioneeId = +post.userId
        const totalDebitAmount = await totalDebitSportTransaction(newParam, esClient)
        totalBetObj.totalBets = ++totalDebitAmount.totalBets
        totalBetObj.totalBetAmount = totalDebitAmount.totalBetAmount + (+post.amount)
        const sourceObj = `ctx._source["total_sport_bets"] = "${totalBetObj.totalBets}";
        ctx._source["total_sport_bet_amount"] = "${totalBetObj.totalBetAmount}";
        ctx._source["real_balance"] =  ${userDetail.Wallet.dataValues.amount};
        ctx._source["total_balance"] =  ${userDetail.Wallet.dataValues.amount}+${userDetail.Wallet.dataValues.nonCashAmount};
        ctx._source["non_cash_balance"] = ${userDetail.Wallet.dataValues.nonCashAmount}`

        await updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
      } else {
        totalBetObj.totalBets = +playerTotalBet.totalBets
        totalBetObj.totalSportBetAmount = playerTotalBet.totalBetAmount + (+post.amount)

        const sourceObj = `ctx._source["total_sport_bets"] = "${totalBetObj.totalBets}";
        ctx._source["total_sport_bet_amount"] = "${totalBetObj.totalSportBetAmount}";

        ctx._source["real_balance"] = ${userDetail.Wallet.dataValues.amount};
        ctx._source["total_balance"] =  ${userDetail.Wallet.dataValues.amount}+${userDetail.Wallet.dataValues.nonCashAmount};
        ctx._source["non_cash_balance"] = ${userDetail.Wallet.dataValues.nonCashAmount}`
        await updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
      }
    } else {
      const sourceObj = `
         ctx._source["real_balance"] =  ${playerDetails.amount};
         ctx._source["non_cash_balance"] = ${playerDetails.non_cash_amount};
         ctx._source["total_balance"] =  ${playerDetails.amount}+${playerDetails.non_cash_amount}`
      await updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
    }

    return transaction
  } catch (e) {
    throw new Error(e)
  }
}
