import config from "../../config/app"

/**
 * @export
 * @param {object} params it contain updated wallet values
 * @param {object} esClient
 * @return {object} it'll return updated wallet object
 */
export default async function updateWalletInUserDevelopmentIndex (params, sourceObj, esClient) {
  try {
    const wallet = await esClient.updateByQuery({
      index: config.getProperties().index.users_index_name,
      refresh: 'true',
      body: {
        script: {
          lang: 'painless',
          source: sourceObj
        },
        query: {
          match: {
            internal_tracking_id: params.id
          }
        }
      }
    })
    return wallet
  } catch (e) {
    throw new Error(e)
  }
}
