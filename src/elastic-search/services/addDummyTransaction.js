import playerParentIds from '../common/playerParentIds'
import config from '../../config/app'

/**
 * insert transaction in elastic-search DB
 *
 * @export
 * @param {object} params it contains inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export default async function addDummyTransaction (params, sequelize, esClient) {
  const { User, Wallet, Currency, Tenant, AdminUser, AdminUserSetting } = sequelize.models
  const post = params.dataValues
  // this return user detail, user wallet detail,
  // user tenant detail and associated agent/admin
  const userDetail = await User.findOne({
    where: {
      id: post.id
    },
    include: [{
      model: Wallet,
      include: {
        model: Currency
      }
    },
    {
      model: Tenant
    },
    {
      model: AdminUser
    },
    {
      model: AdminUserSetting
    }]
  })

  // parent id array ex. [3,9]
  const ownerParentIdArr = await playerParentIds(userDetail.dataValues.id, userDetail.dataValues.tenantId, sequelize)
  const parentChainIds = await ownerParentIdArr.map(item => +item.id)
  let commissionPercentage = '000.00'
  if (userDetail.AdminUserSetting) {
    const percentageStr = userDetail.AdminUserSetting.dataValues.value.split('.')
    commissionPercentage = !percentageStr[1] ? `${percentageStr[0].padStart(3, '0')}.00` : `${percentageStr[0].padStart(3, '0')}.${percentageStr[1].padEnd(2, '0')}`
  }

  const playerDetails = {
    player_id: +userDetail.dataValues.id,
    player_id_s: userDetail.dataValues.id,
    player_name: userDetail.dataValues.userName,
    agent_name: `${userDetail.AdminUser.dataValues.firstName} ${userDetail.AdminUser.dataValues.lastName}`,
    agent_email: userDetail.AdminUser.dataValues.email,
    email: userDetail.dataValues.email,
    parent_id: +userDetail.dataValues.parentId,
    parent_chain_ids: parentChainIds,
    currency: userDetail.Wallet.dataValues.Currency.dataValues.code,
    parent_chain_detailed: await ownerParentIdArr.map(item => `${item.id}-${item.first_name} ${item.last_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`),
    deposit_bonus_details: {
      unconverted_active_deposit_bonus: 0.0,
      active_deposit_bonus_remaining_rollover: 0.0
    }
  }

  try {
    const transaction = await esClient.index({
      index: config.getProperties().index.transactions_index_name,
      id: `${'dummy_for_user_'}${post.id}`,
      body: {
        id: +post.id,
        tenant_id: +userDetail.Tenant.dataValues.id,
        actionee_id: +post.id,
        transaction_type: 'dummy',
        created_at: post.createdAt,
        player_details: playerDetails
      }
    })
    return transaction
  } catch (e) {
    throw new Error(e)
  }
}
