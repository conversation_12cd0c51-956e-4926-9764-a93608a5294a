import config from "../../config/app"

/**
 *
 *
 * @export
 * @param {object} params it contain updated player values
 * @returns {object} it'll return updated player
 */
export default async function updatePlayer (params, sourceObj, esClient) {
  try {
    const player = await esClient.updateByQuery({
      index: config.getProperties().index.users_index_name,
      refresh: true,
      body: {
        script: {
          lang: 'painless',
          source: sourceObj
        },
        query: {
          match: {
            player_id: params.id
          }
        }
      }
    })
    return player
  } catch (e) {
    throw new Error(e)
  }
}
