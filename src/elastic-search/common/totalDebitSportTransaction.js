
/**
 * this return total debit sport transaction
 *
 * @export
 * @param {object} param
 * @param {object} esClient
 * @returns
 */
export default async function totalDebitSportTransaction (param, esClient) {
  try {
    const totalDebited = await esClient.search({
      index: 'bet_transaction',
      body: {
        query: {
          bool: {
            must: {
              match_all: {}
            },
            filter: [{ term: { user_id: { value: param.dataValues.actioneeId } } }, { terms: { transaction_type: ['bet_placement'] } }]

          }
        },
        aggs: {
          total_bet_amount: {
            sum: {
              field: 'player_details.deducted_amount'
            }
          },
          transaction_count: {
            cardinality: {
              field: 'internal_tracking_id'
            }
          }
        }
      }
    })
    return {
      totalBetAmount: +totalDebited.body.aggregations.total_bet_amount.value,
      totalBets: totalDebited.body.aggregations.transaction_count.value
    }
  } catch (e) {
    throw new Error(e)
  }
}
