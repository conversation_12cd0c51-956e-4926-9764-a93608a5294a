import config from '../../config/app'

/**
 *
 * Checks if a user has placed a bet based on transaction types and tenant ID.
 *
 * @export
 * @param {object} param
 * @param {object} esClient
 * @returns {boolean} True if a bet is placed, false otherwise
 */
export default async function checkPlayerPlacedBet (param, esClient) {
  try {
    const playerBetCount = await esClient.search({
      index: config.getProperties().index.users_index_name,
      body: {
        query: {
          bool: {
            must: [
              {
                match: { player_id: param.userId }
              }
            ]
          }
        }
      }
    })

    const totalBets = (+playerBetCount.body.hits.hits[0]._source.total_bets) + (+playerBetCount.body.hits.hits[0]._source.total_sport_bets)
    return totalBets > 0
  } catch (e) {
    throw new Error(e)
  }
}
