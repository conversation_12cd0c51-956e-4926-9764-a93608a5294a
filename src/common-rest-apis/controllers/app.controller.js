import { SPRIBE_INTEGRATION_CONSTANT } from '../../common/constants'
import { response } from '../common/response'
import checkMaintenanceMode from '../services/checkMaintenanceMode'
import GameLaunch from '../services/gameLaunch'
import PaymentSuccess from '../services/paymentSuccess'
import ReadSheetData from '../services/readSheetData'
import SendSmsAlerts from '../services/sendSmsAlerts'

/**
 * Live spribe gameLaunch end point
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const gameLaunch = async (req, res) => {
  try {
    const data = await GameLaunch.execute({ ...req.body, tenant: req.tenant.dataValues }, req)
    if (data.successful) return response(res, data.result)
  } catch (error) {
    console.log('--------gameLaunch----------', error)
    return res.status(200).json({ code: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE, message: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR })
  }
}

export const checkMaintenance = async (req, res) => {
  try {
    const data = await checkMaintenanceMode.execute(req.body, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ 'Error code': 1, description: 'internal server error' })
  }
}

export const healthCheckup = async (req, res) => {
  res.status(200).json({ message: 'ok' })
}

export const readSheetData = async (req, res) => {
  try {
    const data = await ReadSheetData.execute(req.body, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ 'Error code': 1, description: 'internal server error' })
  }
}

export const sendSmsAlerts = async (req, res) => {
  try {
    const data = await SendSmsAlerts.execute(req.body, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ 'Error code': 1, description: 'internal server error' })
  }
}

export const paymentSuccess = async (req, res) => {
  try {
    const data = await PaymentSuccess.execute(req.body, req)
    return response(res, data.result)
  } catch (error) {
    return res.status(200).json({ 'Error code': 1, description: 'internal server error' })
  }
}
