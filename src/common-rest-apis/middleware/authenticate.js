import { SPRIBE_INTEGRATION_CONSTANT } from '../../common/constants'
import { response } from '../common/response'

module.exports = async (req, res, next) => {
  try {
    const { databaseConnection: model } = req
    const verifySessionToken = await model.UserToken.findOne({
      where: { userId: req.body.user_id, tokenType: 'spribe_session_token', token: req.body.session_token },
      raw: true
    })
    if (!verifySessionToken) {
      return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID_CODE, message: SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID })
    }
    // if (verifySessionToken.token !== req.body.session_token) {
    //   return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID_CODE, message: SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID })
    // }
    const expirationDate = new Date(verifySessionToken.updatedAt)
    expirationDate.setDate(expirationDate.getDate() + 1)
    const currentDate = new Date()
    if (currentDate > expirationDate) {
      return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.TOKEN_EXPIRED_CODE, message: SPRIBE_INTEGRATION_CONSTANT.TOKEN_EXPIRED })
    }
    next()
  } catch {
    return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE, message: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR })
  }
}
