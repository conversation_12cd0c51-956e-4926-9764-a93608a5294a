import * as jwt from 'jsonwebtoken';
import { SPRIBE_INTEGRATION_CONSTANT } from '../../common/constants';
import config from '../../config/app';
import translate from '../../lib/languageTranslate';
import { response } from '../common/response';

module.exports = async (req, res, next) => {
  try {

    if (req?.headers?.authorization) {
      const language = req?.headers?.language
      const token = req?.headers?.authorization
      const authConfig = config.getProperties().auth
      const splitToken = token.replace('Bearer ', '')
      const secretKey = authConfig.jwt_secret

      try {
        const decodedToken = await jwt.verify(splitToken, secretKey)
       if(decodedToken.impersonated){
        return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.VALIDATION_ERROR, message:  translate('IMPERSONATED_USER_NOT_ALLOWED', language) })
       } else {
        next()
       }
      } catch (e) {
        throw new Error(e)
      }
    } else {
      next()
    }

  } catch {
    return response(res, { code: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE, message: SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR })
  }
}
