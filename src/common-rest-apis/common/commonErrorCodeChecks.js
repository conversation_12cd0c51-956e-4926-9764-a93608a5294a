import { verify } from 'jsonwebtoken'
import config from '../../config/app'
import { SPRIBE_INTEGRATION_CONSTANT } from '../../common/constants'

export const errorChecks = {
  insufficientFundsCheck: (user, amount, responseObject) => {
    if ((user.Wallet.amount + user.Wallet.nonCashAmount) < amount) {
      responseObject.code = SPRIBE_INTEGRATION_CONSTANT.INSUFFICIENT_FUND_CODE
      responseObject.message = SPRIBE_INTEGRATION_CONSTANT.INSUFFICIENT_FUND

      return false
    }
    return true
  },
  negativeAmountCheck: async (amount, responseObject) => {
    if (amount < 0) {
      responseObject.code = SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE
      responseObject.message = SPRIBE_INTEGRATION_CONSTANT.NEGATIVE_AMOUNT

      return false
    }
    return true
  },
  userNotFoundCheck: async (context, data, responseObject) => {
    const WalletModel = context.databaseConnection.Wallet
    const CurrencyModel = context.databaseConnection.Currency

    const user = await context.databaseConnection.User.findOne({
      where: { id: data, active: true },
      include: [{
        model: WalletModel,
        where: { currency_id: SPRIBE_INTEGRATION_CONSTANT.CURRENCY_ID },
        include: {
          model: CurrencyModel
        }
      }]
    })

    if (!user) {
      responseObject.code = SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE
      responseObject.message = SPRIBE_INTEGRATION_CONSTANT.USER_NOT_FOUND

      return false
    }

    return user
  },
  TokenCheck: async (token, responseObject) => {
    try {
      const authConfig = config.getProperties().auth
      const decodedToken = verify(token, authConfig.jwt_secret)
      const userId = decodedToken.id
      return userId
    } catch (error) {
      if (error.name === SPRIBE_INTEGRATION_CONSTANT.JSON_WEB_TOKEN_ERROR && error.message === SPRIBE_INTEGRATION_CONSTANT.INVALID_SIGNATURE) {
        responseObject.code = SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID_CODE
        responseObject.message = SPRIBE_INTEGRATION_CONSTANT.TOKEN_INVALID
        return false
      } else {
        responseObject.code = SPRIBE_INTEGRATION_CONSTANT.TOKEN_EXPIRED_CODE
        responseObject.message = SPRIBE_INTEGRATION_CONSTANT.TOKEN_EXPIRED
        return false
      }
    }
  }

}
