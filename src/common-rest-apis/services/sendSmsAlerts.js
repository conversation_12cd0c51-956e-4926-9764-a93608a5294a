import { ALLOWED_PERMISSIONS, CURRENCY_CODE, LOGIN_TYPE, PHONE_CODE, SMS_ALERT_MESSAGE, SMS_GATEWAY, TENANTS_WITHDRAW_SMS_ALERT_MESSAGE } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'

const constraints = {
  userId: {
    type: 'integer',
    presence: { message: 'is required' }
  },
  smsGateway: {
    type: 'string'
    // presence: { message: 'is required' }
  },
  allowedModules: {
    type: 'string',
    presence: { message: 'is required' }
  },
  smsPreference: {
    type: 'integer',
    presence: { message: 'is required' }
  },
  value: {
    type: 'string',
    presence: { message: 'is required' }
  }
}

/**
 * Send SMS alerts through respective sms gateway.
 * @export
 * @class SendSmsAlerts
 * @extends {ServiceBase}
 */
export default class SendSmsAlerts extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      databaseConnection: {
        User: UserModel,
        Wallet: WalletModel,
        Currency: CurrencyModel
      }
    } = this.context

    try {
      const environment = config.get('env')

      if (environment !== 'production') return { status: true }

      const user = await UserModel.findOne({
        attributes: ['phone', 'phoneCode', 'firstName', 'email', 'tenantId'],
        include: {
          model: WalletModel,
          attributes: ['ownerId', 'currencyId'],
          include: {
            model: CurrencyModel,
            attributes: ['code']
          }
        },
        where: { id: this.args.userId },
        raw: true
      })

      if (!user?.phone || !user?.phoneCode) { // in case of social signup.. phone number is not mandatory
        return { status: false }
      }
      let result, message
      if (TENANTS_WITHDRAW_SMS_ALERT_MESSAGE[user?.tenantId]) message = TENANTS_WITHDRAW_SMS_ALERT_MESSAGE[user?.tenantId][this.args.value]
      else message = SMS_ALERT_MESSAGE[this.args.value]
      if (environment === 'production' && this.args.smsPreference === LOGIN_TYPE.MOBILE) {
        if (this.args.smsGateway === SMS_GATEWAY.FAST2_SMS) {
          if (this.args.allowedModules && this.args.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY) &&
            (user['Wallet.Currency.code'] === CURRENCY_CODE.LKR)) {
            result = await sendKarixSMSPhoneVerificationOTP(user.phone, user.phoneCode, message, this.context, true)
          } else {
            result = await sendFast2SMSPhoneVerificationOTP(user.phone, user.phoneCode, message, this.context)
          }
        } else if (this.args.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
          if (user.phoneCode !== PHONE_CODE.SRILANKA) return { status: false }
          result = await sendZMessengerSMSPhoneVerificationOTP(user.firstName, user.phone, message, this.context, true)
        } else {
          result = await sendPhoneVerificationOTP(user.phone, user.phoneCode, message, this.context)
        }
        if (!result?.success) {
          return { status: false }
        }
      } else if (environment === 'production' && this.args.smsPreference === LOGIN_TYPE.EMAIL) {
        await sendOtpThroughSmtpRelay(message, user.email, this.context, true, this.args.smsGateway)
      }
      return { status: true }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, null)
      return { status: false }
    }
  }
}
