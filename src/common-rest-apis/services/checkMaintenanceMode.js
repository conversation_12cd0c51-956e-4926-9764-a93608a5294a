import { SUBSCRIPTION_CHANNEL } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'

const constraints = {
  tenantId: {
    type: 'integer',
    presence: { message: 'is required' }
  }
}

/**
 * Ezugi service to rollback amount when debit is failed (Will run only if debit is failed).
 * @export
 * @class checkMaintenanceMode
 * @extends {ServiceBase}
 */
export default class checkMaintenanceMode extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    try {
    const {
      // headers: { },
      databaseConnection: {
        TenantSiteMaintenance: TenantSiteMaintenanceModel
      }
      // tenant: Tenant
    } = this.context

    const siteData = await TenantSiteMaintenanceModel.findOne({
      where: {
        tenantId: this.args.tenantId
      }
    })
    // console.log(siteData)
    if (!siteData) {
      return { msg: 'No data found' }
    }
    if (this.context.pubSub.publish && this.args.type === 'subscription') {
      this.context.pubSub.publish(
        SUBSCRIPTION_CHANNEL.IS_MAINTENANCE_MODE,
        {
          MaintenanceMode: {
            id: siteData.id,
            tenantId: siteData.tenantId,
            siteDown: siteData.siteDown,
            announcementTitle: siteData.announcementTitle,
            isAnnouncementActive: siteData.isAnnouncementActive,
            type: siteData.type,
            title: siteData.title
          }
        }
      )
    }

    return siteData
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId: this.args.tenantId })
    throw error
  }
}
}
