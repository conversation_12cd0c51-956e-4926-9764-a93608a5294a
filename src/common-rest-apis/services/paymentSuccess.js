import { QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'

/**
 * Send SMS alerts through respective sms gateway.
 * @export
 * @class paymentSuccess
 * @extends {ServiceBase}
 */
export default class paymentSuccess extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        DepositRequest: DepositRequestModel,
        QueueLog: QueueLogModel
      }
    } = this.context

    try {
      const depositRequest = await DepositRequestModel.findOne({
        attributes: ['id'],
        where: {
          orderId: this.args.orderId,
          tenantId: this.args.tenantId
        },
        raw: true
      })
      if (depositRequest) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.PENDING_DEPOSIT_REQUEST,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [depositRequest.id]
        }

        const queueLog = await QueueLogModel.create(queueLogObject)
        if (queueLog) {
          this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
            QueueLog: { queueLogId: queueLog.id }
          })
        }
        return { status: true }
      } else {
        return { status: false, error: 'OrderId not found' }
      }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, null)
      return { status: false }
    }
  }
}
