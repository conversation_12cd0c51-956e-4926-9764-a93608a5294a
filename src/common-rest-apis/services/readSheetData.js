import axios from 'axios'
import moment from 'moment-timezone'
import { PassThrough } from 'stream'
import XLSX from 'xlsx'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'

const constraints = {
  sheetId: {
    type: 'string',
    presence: { message: 'is required' }
  }
}

/**
 * Read Google Sheet Data By Sheet Id.
 * @export
 * @class ReadSheetData
 * @extends {ServiceBase}
 */
export default class ReadSheetData extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    try {
    const resp = {}

    const spreadsheetId = this.args.sheetId.trim()

    // Build the export URL for XLSX format
    const exportUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=xlsx`

    // Make a request to the export URL and get data in stream
    let response
    try {
      response = await axios({
        url: exportUrl,
        method: 'GET',
        responseType: 'stream' // Use stream response type
      })
    } catch (error) {
      resp['Error code'] = 1
      resp.description = 'Can not access file!'
      return resp
    }

    // Create a PassThrough stream to handle the incoming data
    const passThrough = new PassThrough()
    response.data.pipe(passThrough)

    // Collect the data chunks into a buffer
    const chunks = []
    passThrough.on('data', chunk => {
      chunks.push(chunk)
    })

    // Once the stream is finished, concatenate the chunks into a single buffer
    const buffer = await new Promise((resolve, reject) => {
      passThrough.on('end', () => resolve(Buffer.concat(chunks)))
      passThrough.on('error', reject)
    })

    // Read XLSX file
    const workbook = XLSX.read(buffer, { type: 'buffer' })
    const sheetNameList = workbook.SheetNames
    const usersArray = XLSX.utils.sheet_to_json(
      workbook.Sheets[sheetNameList[0]],
      {
        blankrows: false,
        defval: '',
        raw: false
      }
    )

    const groupedData = {}

    usersArray.forEach(userObj => {
      // Validate Empty Fields and Trim all.
      Object.entries(userObj).forEach(([key, value]) => {
        let modifiedValue = value
        if (typeof modifiedValue === 'string') {
          modifiedValue = modifiedValue.trim()
        }
        userObj[key] = modifiedValue
      })

      // Group records date wise
      if (!groupedData[userObj.date]) groupedData[userObj.date] = []
      groupedData[userObj.date].push(userObj)
    })

    const dateFormat = 'YYYY-MM-DD'
    const resultObj = {}

    // Get last 30 available dates
    const datesToSend = Object.keys(groupedData).sort((a, b) => moment(b, dateFormat) - moment(a, dateFormat)).slice(0, 30)

    // Iterate through each date and sort its records based on amount
    datesToSend.forEach(date => (resultObj[date] = groupedData[date].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount)).slice(0, 20)))

    resp.result = resultObj
    return resp
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, null)
    throw error
  } }
}
