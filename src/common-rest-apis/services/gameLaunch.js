import * as jwt from 'jsonwebtoken'
import { Op, Sequelize } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import checkUserPermission from '../../common/checkUserPermission'
import { ALLOWED_PERMISSIONS, CURRENCY_TYPE, CUSTOM_GAME_CONSTANT, DARWIN_CREDENTIALS, DEMO_USER_ID, ENVIORNMENT, <PERSON><PERSON><PERSON><PERSON>_CREDENTIALS, FUNKY_GAMES_CREDENTIALS, FUNKY_GAMES_RESPONSE_CODES, GAME_LAUNCH, JETFAIR_INTEGRATION_CREDENTIALS, LOBBY_GAMES, LOTTERY_GAMES_CREDENTIALS, P<PERSON>O<PERSON>_CONSTANT, PG_BET_TYPE, P<PERSON>ABOOM_CREDENTIALS, PIGABOOM_PROVIDER_ID, POWER_PLAY_CREDENTIALS, PROD_TENANTS, SPINOCCHIO_CREDENTIALS, SPINOCCHIO_USER_TYPE, <PERSON><PERSON><PERSON>_INTEGRATION_CONSTANT, <PERSON><PERSON><PERSON>_INTEGRATION_CREDENTIALS, ST8_CONSTANT, STAGE_TENANTS, TEST_ACCOUNTS, TURBO_GAMES_CREDENTIALS, TURBO_STARS_CREDENTIALS, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES, WHITECLIFF_CREDENTIALS, getPGSoftGameLaunchUrl, getPGSoftGamePath } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { checkGameLaunchRateLimit } from '../../common/gameLaunchCaptcha'
import gamePlayHistory from '../../common/gamePlayHistory'
import gamePlayHistoryImpersonated from '../../common/gamePlayHistoryImpersonated'
import kycCheck from '../../common/kycCheck'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import checkImpersonated from '../../lib/checkImpersonated'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
const axios = require('axios')
const rs = require('jsrsasign')
const CryptoJS = require('crypto-js')
const crypto = require('crypto')

const constraints = {
  user_token: {
    type: 'string'
  },
  provider_name: {
    type: 'string'
  },
  game_name: {
    type: 'string'
  },
  res_token: {
    type: 'string'
  },
  open_table: {
    type: 'string'
  },
  ip: {
    type: 'string'
  },
  device_type: {
    type: 'string'
  },
  page_id: {
    type: 'string',
  },
  provider_id: {
    type: 'string',
  },
}

/**
 * Provides service for the Launch api in the live-spribe
 * @export
 * @class Launch
 * @extends {ServiceBase}
 */
export default class Launch extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    try {
    const TenantCredentialModel = this.context.databaseConnection.TenantCredential
    const language = this.context.headers.language
    const responseObject = {}
    const isImpersonated = await checkImpersonated(this.context)
    const userId = await errorChecks.TokenCheck(this.args.user_token, responseObject)

    if (userId) {
      const checkIfUserBannedORConditions = [];
      const providerName = this.args.provider_name;
      let gameDetails = null;
      this.args.tenant = this.context.tenant;
      const tenantId =  this.args.tenant.id;

      const executeGameDetailsQuery = async (context, query, replacements) => {
        try {
          const result = await context.databaseConnection.sequelize.query(
            query,
            {
              replacements,
              type: context.databaseConnection.sequelize.QueryTypes.SELECT,
              raw: true,
              useMaster: true,
            }
          );
          return result;
        } catch (error) {
          throw error;
        }
      };

      // Query definitions for game details
      const GAME_DETAILS_QUERIES = {
        withProviderAndPage: `
          SELECT
            CAST(casino_items.provider AS INT) AS provider_id,
            page_menus.id AS page_menu_id,
            pages.id AS page_id,
            casino_items.uuid AS game_uuid
          FROM pages
            JOIN aggregator a ON pages.id = a.page_id AND a.status = true
            LEFT JOIN page_menus ON pages.id = page_menus.page_id
            LEFT JOIN menu_items ON page_menus.id = menu_items.page_menu_id
            LEFT JOIN casino_items ON menu_items.casino_item_id = casino_items.id
          WHERE casino_items.uuid = :openTable
            AND casino_items.provider = :providerId
            AND pages.id = :pageId
            AND pages.tenant_id = :tenantId`,

        withProviderName: `
          SELECT
            CAST(casino_items.provider AS INT) AS provider_id,
            page_menus.id AS page_menu_id,
            pages.id AS page_id,
            casino_items.uuid AS game_uuid,
            casino_providers.name AS provider_name
          FROM pages
            JOIN aggregator a ON pages.id = a.page_id AND a.status = true
            LEFT JOIN page_menus ON pages.id = page_menus.page_id
            LEFT JOIN menu_items ON page_menus.id = menu_items.page_menu_id
            LEFT JOIN casino_items ON menu_items.casino_item_id = casino_items.id
            LEFT JOIN casino_providers ON NULLIF(casino_items.provider, '')::INT = casino_providers.id
          WHERE casino_items.uuid = :openTable
            AND pages.tenant_id = :tenantId
            AND casino_providers.name = :providerName`,
      };

      if (this.args.provider_id && this.args.page_id) {
        gameDetails = await executeGameDetailsQuery(
          this.context,
          GAME_DETAILS_QUERIES.withProviderAndPage,
          {
            openTable: this.args.open_table,
            providerId: this.args.provider_id,
            pageId: this.args.page_id,
            tenantId: tenantId,
          }
        );
      } else {
        gameDetails = await executeGameDetailsQuery(
          this.context,
          GAME_DETAILS_QUERIES.withProviderName,
          {
            openTable: this.args.open_table,
            providerName: this.args.provider_name,
            tenantId: tenantId,
          }
        );
      }

      if (gameDetails.length) {
        // one UUID may have entries with multiple page menus, for that we are looping over the gameDetails and appending all the page_menu_ids in the condition

        gameDetails.forEach((gameDetail) => {
          let pageMenuId = gameDetail?.page_menu_id;

          if (pageMenuId) {
            checkIfUserBannedORConditions.push({
              gameType: "menu",
              pageMenuId: pageMenuId,
            });
          }
        });

        // since all the gameDetails will have have same page_id and game_uuid, we can take the first entry only instead of puting the same condition multiple times through forEach loop
        const openTable = gameDetails?.[0]?.game_uuid;
        const pageId = gameDetails?.[0]?.page_id;

        if (openTable) {
          checkIfUserBannedORConditions.push({
            gameType: "casino",
            blockedGamesUuid: openTable,
          });
        }

        if (pageId) {
          checkIfUserBannedORConditions.push({
            gameType: "page",
            pageId: pageId,
          });
        }
      }

      if (providerName) {
        checkIfUserBannedORConditions.push({
          gameType: "sports",
          blockedGamesUuid: providerName,
        });
      }

      const blockedGamesForUser =
        await this.context.databaseConnection.UserBlockedGames.findOne({
          attributes: ["id", "gameType"],
          where: {
            userId,
            tenantId: tenantId,
            [Op.or]: checkIfUserBannedORConditions,
          },
          raw: true,
        });

      const customBanErros = {
        sports: GAME_LAUNCH.USER_BLOCKED_GAMES,
        casino: GAME_LAUNCH.USER_BLOCKED_GAMES,
        menu: GAME_LAUNCH.MENU_BANNED,
        page: GAME_LAUNCH.PAGE_BANNED,
      };

      if (blockedGamesForUser) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = customBanErros[blockedGamesForUser.gameType];
        return responseObject;
      }
    }
    if (!userId) {
      if (this.args.provider_name === GAME_LAUNCH.JETFAIR_SPORT_EXCHANGE) {
        const jetfairLoadingPage = await TenantCredentialModel.findOne({
          where: {
            key: [JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_SPORT_LANDING_PAGE],
            tenantId: this.args.tenant.id
          },
          attributes: ['value'],
          raw: true
        })
        const responseObj = {}
        responseObj.data = {
          lobbyURL: jetfairLoadingPage?.value
        }
        responseObj.Status = {
          returnMessage: GAME_LAUNCH.NOT_LOGIN,
          code: 1
        }
        return responseObj
      } else if (this.args.provider_name === GAME_LAUNCH.POWER_PLAY) {
        const powerLoadingPage = await TenantCredentialModel.findOne({
          where: {
            key: [JETFAIR_INTEGRATION_CREDENTIALS.POWERPLAY_SPORT_LANDING_PAGE],
            tenantId: this.args.tenant.id
          },
          attributes: ['value'],
          raw: true
        })
        const responseObj = {}
        responseObj.data = {
          lobbyURL: powerLoadingPage?.value
        }
        responseObj.Status = {
          returnMessage: GAME_LAUNCH.NOT_LOGIN,
          code: 1
        }
        return responseObj
      } else if (this.args.provider_name === GAME_LAUNCH.TURBO_STARS_PROVIDER_NAME) {
        const keys = {
          iframeUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_IFRAME_URL,
          apiKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_API_KEY,
          locale: TURBO_STARS_CREDENTIALS.TURBO_STARS_LOCALE,
          subPartnerId: TURBO_STARS_CREDENTIALS.TURBO_STARS_SUB_PARTNER_ID,
          customStyles: TURBO_STARS_CREDENTIALS.TURBO_STARS_CUSTOM_STYLE_URL
        }
        const credentials = await TenantCredentialModel.findAll({
          where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
          },
          raw: true
        })

        const values = {}
        Object.keys(keys).map((creds) => {
          const val = credentials.find(obj => obj.key === keys[creds])
          if (val) {
          values[creds] = val.value
          }
        })

        let gameLaunchUrl =`${values.iframeUrl}/?cid=${values.apiKey}`
        if(values.locale){
          gameLaunchUrl=`${gameLaunchUrl}&locale=${values.locale}`
        }
        if(values.subPartnerId){
          gameLaunchUrl=`${gameLaunchUrl}&sub_partner_id=${values.subPartnerId}`
        }
        if(values.customStyles){
          gameLaunchUrl=`${gameLaunchUrl}&customStyles=${values.customStyles}`
        }

      if (this.args.game_name) {
        if (!this.args.game_name.includes(',')) {
          this.args.game_name = this.args.game_name + ','
        }
        gameLaunchUrl = `${gameLaunchUrl}&lineFilter=${this.args.game_name}`
      }

        const responseObj = {}
        responseObj.data = {
          lobbyURL: gameLaunchUrl
        }
        responseObj.Status = {
          returnMessage: GAME_LAUNCH.NOT_LOGIN,
          code: 1
        }
        return responseObj
      }
      else {
        return responseObject
      }
    }
    const hasPermission = await checkUserPermission( this.context.tenant.id, userId, USER_SPECIFIC_PERMISSIONS_MODULES.GAMEPLAY , this.args.open_table ? USER_SPECIFIC_PERMISSIONS_ACTION.CASINO : USER_SPECIFIC_PERMISSIONS_ACTION.SPORTS)
    if (!hasPermission) {
      responseObject.code = ALLOWED_PERMISSIONS.UNAUTHORIZED_ERROR_CODE
      responseObject.message = translate('ACCESS DENIED', language)
      return responseObject
    }

    const rateLimitResult = await checkGameLaunchRateLimit(this.context, userId, this.args);
    if (rateLimitResult && rateLimitResult.success && rateLimitResult.isCustomCaptcha) {
      responseObject.code = GAME_LAUNCH.CAPTCHA_REQUIRED_CODE ,
      responseObject.message = GAME_LAUNCH.CAPTCHA_REQUIRED_MESSAGE,
      responseObject.data = rateLimitResult
      return responseObject
    }

    // Provider check
    const UserModel = this.context.databaseConnection.User
    const WalletModel = this.context.databaseConnection.Wallet
    const CurrencyModel = this.context.databaseConnection.Currency
    const TenantThemeSettingModel = this.context.databaseConnection.TenantThemeSetting
    const CasinoProviderModel = this.context.databaseConnection.CasinoProvider
    const BotUserModel = this.context.databaseConnection.BotUser

    const providerId = await CasinoProviderModel.findOne({
      where: Sequelize.where(
        Sequelize.fn('LOWER', Sequelize.col('name')),
        this.args.provider_name.toLowerCase()
      ),
    });
    if (!(providerId)) {
      responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
      responseObject.message = GAME_LAUNCH.PROVIDER_NOT_FOUND
      return responseObject
    }
    const userDetails = await UserModel.findOne({
      where: { id: userId },
      include: [{
        model: WalletModel,
        include: {
          model: CurrencyModel
        }
      }]
    })

    const demoUserId = config.get('env') === ENVIORNMENT.PRODUCTION ? DEMO_USER_ID.PROD : DEMO_USER_ID.STAGE
    if (userId === demoUserId) {
      const tenants = config.get('env') === ENVIORNMENT.PRODUCTION ? PROD_TENANTS : STAGE_TENANTS
      const tenantCurrency = Object.values(tenants).find(tenant => tenant.id === this.args.tenant.id)?.currency || ''
      userDetails.Wallet.Currency.code = tenantCurrency
      userDetails.Wallet.Currency.internalCode = tenantCurrency
    }

    // Bot User Check
    const isBotUser = await BotUserModel.findOne({ where: { userId } })
    if (isBotUser) {
      responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
      responseObject.message = GAME_LAUNCH.BOT_USER_RESPONSE
      return responseObject
    }

    // currency conversion for chip
    let convertCurrency
    if ((userDetails.Wallet.Currency.code).toLowerCase() === CURRENCY_TYPE.CHIPS) {
      convertCurrency = true
    }

    const providers = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: this.args.tenant.id
      }
    })
    if (!(providers.assignedProviders)) {
      responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
      responseObject.message = GAME_LAUNCH.PROVIDER_NOT_FOUND
      return responseObject
    }

    const array = providers.assignedProviders.split(',')
    if (!(array.includes(providerId.id))) {
      responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
      responseObject.message = GAME_LAUNCH.PROVIDER_NOT_FOUND
      return responseObject
    }

    // checking whether KYC is necessary for gameplay
    if (providers.allowedModules && providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REQUIRED_KYC_FOR_GAME)) {
      const isVerifiedDocument = await kycCheck(this.context, userId)
      if (!isVerifiedDocument) {
        responseObject.code = ALLOWED_PERMISSIONS.UNAUTHORIZED_ERROR_CODE
        responseObject.message = translate('KYC_NOT_DONE', language)
        return responseObject
      }
    }

    const TenantModel = this.context.databaseConnection.Tenant
    const tenant = await TenantModel.findOne({
      where: {
        id: this.args.tenant.id
      }
    })
      if (isImpersonated) {
        let isOldGame = await gamePlayHistoryImpersonated(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
        if (!isOldGame) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH.RECENT_GAME
          return responseObject
        }
      }

    // For Spribe
    if (this.args.provider_name === GAME_LAUNCH.SPRIBE_PROVIDER_NAME) {
      // if (userDetails.Wallet.currencyId !== SPRIBE_INTEGRATION_CONSTANT.CURRENCY_ID) {
      //   responseObject.code = SPRIBE_INTEGRATION_CONSTANT.SPRIBE_INTERNAL_ERROR_CODE
      //   responseObject.message = SPRIBE_INTEGRATION_CONSTANT.CURRENCY_NOT_SUPPORTED
      //   return responseObject
      // }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: [SPRIBE_INTEGRATION_CREDENTIALS.APP_SPRIBE_OPERATOR_ID,
            SPRIBE_INTEGRATION_CREDENTIALS.APP_SPRIBE_LAUNCH_URL,
            (convertCurrency) ? 'APP_CHIPS_CURRENCY_CODE' : null
          ].filter(Boolean),
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const foundOperator = credentials.find(obj => obj.key === SPRIBE_INTEGRATION_CREDENTIALS.APP_SPRIBE_OPERATOR_ID)
      if (!foundOperator) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.OPERATOR_ID_NOT_FOUND
        return responseObject
      }
      const foundUrl = credentials.find(obj => obj.key === SPRIBE_INTEGRATION_CREDENTIALS.APP_SPRIBE_LAUNCH_URL)
      if (!foundUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }

      // token modification
      const currency = convertCurrency ? (credentials.find(obj => obj.key === 'APP_CHIPS_CURRENCY_CODE')).value : userDetails.Wallet.Currency.code
      const decodedToken = { ...jwt.decode(this.args.user_token), game: this.args.game_name, currency: currency, uuid: uuidv4() }
      const authConfig = config.getProperties().auth
      const token = jwt.sign(decodedToken, authConfig.jwt_secret)

      const launchUrl = foundUrl.value
      const game = this.args.game_name
      const user = userId
      const lang = SPRIBE_INTEGRATION_CONSTANT.LANG
      const operator = foundOperator.value
      const returnUrl = this.context.headers.domain || tenant.domain
      const gameLaunchUrl = `https://${launchUrl}/${game}?user=${user}&token=${token}&lang=${lang}&currency=${currency}&operator=${operator}&return_url=${returnUrl}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // For Evolution AND Ezugi - ToDo
    // Ezugi and Evolution are internally same, they have same credentials, therefore here we are using only Ezugi credentials for generating game url for both Ezugi and Evolution
    // Front end requirement
    if (this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME || this.args.provider_name === GAME_LAUNCH.EZUGI_PROVIDER_NAME) {
      const data = [EZUGI_CREDENTIALS.APP_EZUGI_OPERATOR_ID,
        EZUGI_CREDENTIALS.APP_EZUGI_LAUNCH_URL]
      if ((this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME)) {
        data.push(EZUGI_CREDENTIALS.EVOLUTION_GAME_SECONDARY_CURRENCY_CONVERSION)
        data.push(EZUGI_CREDENTIALS.EVOLUTION_GAME_SECONDARY_CURRENCY)
        data.push(EZUGI_CREDENTIALS.APP_EVOLUTION_PROXY_LAUNCH_URL)
      }
      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: data,
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const foundOperator = credentials.find(obj => obj.key === data[0])
      if (!foundOperator) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.OPERATOR_ID_NOT_FOUND
        return responseObject
      }
      const foundUrl = credentials.find(obj => obj.key === data[1])
      if (!foundUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }
      const tokenType = (this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME) ? GAME_LAUNCH.EVOLUTION_TOKEN : GAME_LAUNCH.EZUGI_TOKEN
      const jwtTokenObj = { id: userId, tokenType: tokenType, timestamp: Math.floor(Date.now() / 1000) }
      // if (this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME) {
      //   const secConversion = (data[2] ? credentials.find(obj => obj.key === data[2] || '') : '')
      //   const SECONDARY_CURRENCY = (data[3] ? credentials.find(obj => obj.key === data[3] || '') : '')
      //   if (secConversion && SECONDARY_CURRENCY) {
      //     jwtTokenObj.option = { secondaryCurrency: SECONDARY_CURRENCY.value, conversion: secConversion.value }
      //   }
      // }
      const environment = config.get('env')

      const UserTokenModel = this.context.databaseConnection.UserToken
      // const [ezugiToken] = await UserTokenModel.findOrCreate({
      //   where: { userId: userId, tokenType: tokenType },
      //   defaults: {
      //     userId,
      //     tokenType: tokenType,
      //     token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      //   }
      // })
      const ezugiToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let tokenId
      if (!ezugiToken) {
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret) }
        const ezToken = await UserTokenModel.create(userTokenDataObject)
        tokenId = ezToken.token
      } else {
        const payload = jwt.decode(
          ezugiToken.token,
          config.getProperties().auth.jwt_secret
        );
        const newPayload = {
          ...payload,
          tokenType: tokenType,
          timestamp: Math.floor(Date.now() / 1000),
        };
        const userTokenData = jwt.sign(
          newPayload,
          config.getProperties().auth.jwt_secret
        );

        await UserTokenModel.update(
          { token: userTokenData },
          { where: { id: ezugiToken.id } }
        );
        tokenId = userTokenData;
      }

      if(isImpersonated) {
        jwtTokenObj.impersonated = true
        tokenId = jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      }
      let launchUrl = foundUrl.value
      const operatorId = foundOperator.value
      const openTable = this.args.open_table
      const currentDomain = tenant.domain
      if (this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME) {
        const proxyUrl = credentials.find(obj => obj.key === EZUGI_CREDENTIALS.APP_EVOLUTION_PROXY_LAUNCH_URL)
        if (proxyUrl) {
          launchUrl = proxyUrl.value
        }
      }
      const gameName = (this.args.provider_name === GAME_LAUNCH.EZUGI_PROVIDER_NAME && openTable === LOBBY_GAMES.BLACKJACK.NAME) ? `selectGame=${openTable}` : (this.args.provider_name === GAME_LAUNCH.EVOLUTION_PROVIDER_NAME && openTable === LOBBY_GAMES.BLACKJACK.NAME) ? `openTable=${LOBBY_GAMES.BLACKJACK.ID}` : `openTable=${openTable}`
      const gameLaunchUrl = `https://${launchUrl}/auth/?token=${tokenId}&operatorId=${operatorId}&clientType=html&language=en&${gameName}${environment === 'production' ? `&homeUrl=${currentDomain}` : ''}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // For Jetfair sport exchange
    if (this.args.provider_name === GAME_LAUNCH.JETFAIR_SPORT_EXCHANGE) {
      // Session token start
      const authConfig = config.getProperties().auth
      const newToken = await jwt.sign({ id: userDetails.id }, authConfig.jwt_secret)

      const UserTokenModel = this.context.databaseConnection.UserToken
      const sessionToken = await UserTokenModel.findOne({
        where: { userId, tokenType: JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_TOKEN }
      })
      if (!sessionToken) {
        await UserTokenModel.create({
          userId,
          tokenType: JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_TOKEN,
          token: newToken
        })
      }
      // Session token end

      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const launchUrl = await TenantCredentialModel.findOne({
        where: {
          key: [JETFAIR_INTEGRATION_CREDENTIALS.APP_JETFAIR_LAUNCH_URL],
          tenantId: this.args.tenant.id
        },
        raw: true
      })
      if (!launchUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }
      const secretKey = await TenantCredentialModel.findOne({
        where: {
          key: [JETFAIR_INTEGRATION_CREDENTIALS.APP_JETFAIR_SECRETKEY],
          tenantId: this.args.tenant.id
        },
        raw: true
      })
      if (!secretKey) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.SECRET_KEY_NOT_FOUND
        return responseObject
      }
      const agentName = await TenantCredentialModel.findOne({
        where: {
          key: [JETFAIR_INTEGRATION_CREDENTIALS.APP_JETFAIR_AGENT_USER_NAME],
          tenantId: this.args.tenant.id
        },
        raw: true
      })
      if (!agentName) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.AGENT_NAME_NOT_FOUND
        return responseObject
      }
      const tenantCurrency = await TenantCredentialModel.findOne({
        where: {
          key: [JETFAIR_INTEGRATION_CREDENTIALS.APP_CHIPS_CURRENCY_CODE],
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const data = JSON.stringify({
        agentUsername: agentName.value,
        clientUsername: userDetails.userName,
        secretKey: secretKey.value,
        point: 1,
        isDemoUser: 0,
        isAllowBet: true,
        websiteURL: tenant.domain,
        isIframe: 1,
        type: '',
        currency: convertCurrency ? tenantCurrency.value : userDetails.Wallet.Currency.code
      })

      var apiConfig = {
        method: JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_POST_METHOD,
        maxBodyLength: Infinity,
        url: launchUrl.value,
        headers: JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_HEADER,
        data: data
      }

      const apiResponse = await axios(apiConfig)
        .then(function (response) {
          return response.data
        })
        .catch(function (error) {
          console.log(error)
        })
      // console.log('----------------api Jetfair-------------response ----', apiResponse)
      // await UserTokenModel.update(
      //   { token: apiResponse.Data.token },
      //   { where: { userId, tokenType: JETFAIR_INTEGRATION_CREDENTIALS.JETFAIR_TOKEN } }
      // )
      if (!apiResponse?.data) apiResponse.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return apiResponse
    }

    // For ST8
    if (this.args.provider_name === GAME_LAUNCH.ST8) {
      const authConfig = config.getProperties().auth
      const urlParams = {}
      urlParams.token = jwt.sign({ id: userDetails.id, site: this.args.tenant.id, impersonated: isImpersonated }, authConfig.jwt_secret, {
        expiresIn: authConfig.expiry_time
      })
      urlParams.game_code = this.args.open_table
      urlParams.device = null
      urlParams.currency = userDetails.Wallet.Currency.internalCode
      urlParams.player = userDetails.id
      urlParams.player_profile = {
        id: userDetails.id,
        // to be changed later
        jurisdiction: 'CeG',
        default_currency: userDetails.Wallet.Currency.internalCode,
        reg_country: userDetails.countryCode ||  userDetails.Wallet.Currency.countryCode
      }

      const keys = {
        launchUrl: ST8_CONSTANT.ST8_LAUNCH_URL,
        privateKey: ST8_CONSTANT.ST8_PRIVATE_KEY,
        site: ST8_CONSTANT.ST8_SITE,
        lobby: ST8_CONSTANT.ST8_LOBBY
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        rew: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (!val) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        } else {
          values[creds] = val.value
        }
      })

      var sign = new rs.KJUR.crypto.Signature({ alg: ST8_CONSTANT.ST8_SIGN_ALGO })
      sign.init(values.privateKey)
      var signHex = sign.signString(JSON.stringify({ ...urlParams, ...{ site: { id: values?.site, lobby: values?.lobby } } }))
      var signB64 = rs.hextob64(signHex)
      // console.log('-------------------------ST8 API for launch game-------------------------------')
      // console.log('-launch URL-', `${values.launchUrl}/launch`)
      // console.log('-headers-', { 'x-st8-sign': signB64 })
      // console.log('-data-', { ...urlParams, ...{ site: { id: values?.site, lobby: values?.lobby } } })
      const apiResponse = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: `${values.launchUrl}/launch`,
        headers: { 'x-st8-sign': signB64 },
        data: { ...urlParams, ...{ site: { id: values?.site, lobby: values?.lobby } } }
      })
        .then(function (response) {
          return response.data
        })
        .catch(function (error) {
          throw error
        })

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = apiResponse.game_url
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // For pg-soft
    if (this.args.provider_name.toLowerCase() === GAME_LAUNCH.PGSOFT.toLowerCase()) {
      const authConfig = config.getProperties().auth
      const urlParams = new URLSearchParams()

      // operators token
      const ops = jwt.sign({ id: userDetails.id, site: this.args.tenant.id, impersonated:isImpersonated }, authConfig.jwt_secret, {
        expiresIn: authConfig.expiry_time
      })

      const extraArgsString = `ops=${ops}&l=${PGSOFT_CONSTANT.PG_LANGUAGE}&te=${PGSOFT_CONSTANT.PG_REALITY_CHECK}&ri=${PGSOFT_CONSTANT.PG_REALITY_CHECK_INTERVAL}&btt=${PG_BET_TYPE.PG_REAL_GAME}&iwk=0&oc=1`

      const keys = {
        launchUrl: PGSOFT_CONSTANT.PG_LAUNCH_URL,
        operator_token: PGSOFT_CONSTANT.PG_OPERATOR_KEY
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (!val) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        } else {
          values[creds] = val.value
        }
      })

      const gamePath = getPGSoftGamePath(this.args.open_table)

      urlParams.append('path', gamePath)
      urlParams.append('extra_args', extraArgsString)
      urlParams.append('url_type', PGSOFT_CONSTANT.PG_URL_TYPE)
      urlParams.append('operator_token', values.operator_token)
      urlParams.append('client_ip', this.args.ip)

      const apiResponse = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: getPGSoftGameLaunchUrl(values.launchUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: urlParams
      }).then(function (response) {
        return response.data
      })
        .catch(function (error) {
          throw error
        })

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.data = apiResponse
      responseObject.url = null
      /*
      console.log('-------------------------PGSoft API for launch game-------------------------------')
      console.log('-launch URL-', {
        method: 'post',
        maxBodyLength: Infinity,
        url: getPGSoftGameLaunchUrl(values.launchUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: urlParams
      })
      console.log('-apiResponse-', apiResponse) */
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for Custom games
    if (this.args.provider_name === GAME_LAUNCH.CUSTOM_GAME) {
      const keys = {
        launchUrl: CUSTOM_GAME_CONSTANT.LAUNCH_URL,
        operatorId: CUSTOM_GAME_CONSTANT.OPERATOR_ID
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })
      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])

        if (val) {
          values[creds] = val.value
        } else {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        }
      })
      const token = this.args.user_token
      const currency = userDetails.Wallet.Currency.internalCode
      const gameName = this.args.game_name
      const gameLaunchUrl = `${values.launchUrl}/${gameName}?token=${token}&currencyCode=${currency}&operator=${values.operatorId}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for darwin gaming
    if (this.args.provider_name === GAME_LAUNCH.DARWIN_GAMING) {

      const keys = {
        launchUrl: DARWIN_CREDENTIALS.DARWIN_URL,
        casinoId: DARWIN_CREDENTIALS.DARWIN_CASINOID,
        username: DARWIN_CREDENTIALS.DARWIN_USERNAME,
        password: DARWIN_CREDENTIALS.DARWIN_PASSWORD
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])

        if (val) {
          values[creds] = val.value
        } else {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        }
      })

      const jwtTokenObj = { id: userId ,tenantId: this.args.tenant.id}
      const tokenType = GAME_LAUNCH.DARWIN_GAMING
      const UserTokenModel = this.context.databaseConnection.UserToken

      const darwinToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let token
      if (!darwinToken) {
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret) }
        const dnToken = await UserTokenModel.create(userTokenDataObject)
        token = dnToken.token
      } else {
        token = darwinToken.token
      }

      if(isImpersonated) {
        jwtTokenObj.impersonated = true
        token = jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      }

      const gameLaunchUrl = `${values.launchUrl}/api/darwin/casinos/${values.casinoId}/games/launch`


      const headers = {
        Authorization: 'Basic ' + Buffer.from(`${values.username}:${values.password}`).toString('base64')
      }

      const { data: { url } } = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: gameLaunchUrl,
        headers,
        data: {
          gameID: this.args.open_table,
          currency: userDetails.Wallet.Currency.internalCode,
          language: 'en-us',
          token
        }
      })

      responseObject.url = url
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for powerplay
    if (this.args.provider_name === GAME_LAUNCH.POWER_PLAY) {
      const keys = {
        launchUrl: POWER_PLAY_CREDENTIALS.POWER_PLAY_BASE_URL,
        partnerId: POWER_PLAY_CREDENTIALS.POWER_PLAY_PARTNER_ID,
        publicKey: POWER_PLAY_CREDENTIALS.POWER_PLAY_PUBLIC_KEY
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])

        if (val) {
          values[creds] = val.value
        } else {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        }
      })

      const gameLaunchUrl = `${values.launchUrl}/ClientAuthentication`
      const headers = {
        'X-App': values.publicKey
      }

      let powerPlayData = {
        partnerId: values.partnerId,
        userName: userDetails.userName,
        isDemo: config.get('env') === ENVIORNMENT.PRODUCTION ? TEST_ACCOUNTS.includes(userDetails.userName) || this.args.tenant.id == 1 : false ,
        isBetAllow: true,
        isActive: userDetails.active,
        point: 1,
        isDarkTheme: true
      }

      if(this.args?.open_table){
        const eventArr = this.args.open_table.split("/")
        const PullEventModel = this.context.databaseConnection.PullsEvent
        const eventData = await PullEventModel.findOne({
          where: {
            fixtureId: eventArr[0],
            tenantId: this.args.tenant.id
          },
          attributes: ['id','fixtureStatus','sportId'],
          raw: true
        })
        if(eventData){
          if(eventData?.fixtureStatus < 2){
            powerPlayData.event = (!this.args?.open_table) ? '':this.args?.open_table
          }
          if(!this.args?.game_name){
            const PullsSportModel = this.context.databaseConnection.PullsSport
            const sportData = await PullsSportModel.findOne({
              where: {
                id: eventData.sportId,
                tenantId: this.args.tenant.id
              },
              attributes: ['id','nameEn'],
              raw: true
            })
                powerPlayData.sportName = sportData?.nameEn?.capitalize();
          }
        }
      } /*else if(!this.args?.open_table && this.args?.game_name){
          powerPlayData.sportName = this.args?.game_name?.capitalize();
      }*/

      const { data: { data: { url } } } = await axios({
        method: 'post',
        maxBodyLength: Infinity,
        url: gameLaunchUrl,
        headers,
        data: powerPlayData
      })
      this.args.open_table = ''
      responseObject.url = url
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for Arcade
    if (this.args.provider_name === GAME_LAUNCH.ARCADE_PROVIDER_NAME) {
      const data = [
        EZUGI_CREDENTIALS.APP_EZUGI_OPERATOR_ID,
        EZUGI_CREDENTIALS.APP_EZUGI_LAUNCH_URL
      ]

      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: data,
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const foundOperator = credentials.find(obj => obj.key === data[0])
      if (!foundOperator) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.OPERATOR_ID_NOT_FOUND
        return responseObject
      }

      const foundUrl = credentials.find(obj => obj.key === data[1])
      if (!foundUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }



      const environment = config.get('env')
      const tokenType = GAME_LAUNCH.ARCADE_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken
      const jwtTokenObj = { id: userId , tokenType: tokenType, timestamp: Math.floor(Date.now() / 1000) }

      // const [arcadeToken] = await UserTokenModel.findOrCreate({
      //   where: { userId: userId, tokenType: tokenType },
      //   defaults: {
      //     userId,
      //     tokenType: tokenType,
      //     token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      //   }
      // })
      const arcadeToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let tokenId
      if (!arcadeToken) {
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret) }
        const arToken = await UserTokenModel.create(userTokenDataObject)
        tokenId = arToken.token
      } else {
        const payload = jwt.decode(
          arcadeToken.token,
          config.getProperties().auth.jwt_secret
        );
        const newPayload = {
          ...payload,
          tokenType: tokenType,
          timestamp: Math.floor(Date.now() / 1000),
        };
        const userTokenData = jwt.sign(
          newPayload,
          config.getProperties().auth.jwt_secret
        );

        tokenId = userTokenData;
      }
      if(isImpersonated) {
        jwtTokenObj.impersonated = true
        tokenId = jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      }
      const launchUrl = foundUrl.value
      const operatorId = foundOperator.value
      const openTable = this.args.open_table
      const currentDomain = tenant.domain

      const gameLaunchUrl = `https://${launchUrl}/auth_wallet/?token=${tokenId}&operatorId=${operatorId}&clientType=html&language=en&openTable=${openTable}${environment === 'production' ? `&homeUrl=${currentDomain}` : ''}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for WhiteCliff Gaming
    if (this.args.provider_name === GAME_LAUNCH.WHITECLIFF_GAMING) {
      const userDetails = await UserModel.findOne({
        where: { id: userId },
        include: [{
          model: WalletModel,
          include: {
            model: CurrencyModel
          }
        }]
      })

      if (!userDetails) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
          responseObject.message = GAME_LAUNCH.USER_NOT_FOUND;
          return responseObject;
      }
      const currencyCode = userDetails.Wallet.Currency.internalCode;
      if(!WHITECLIFF_CREDENTIALS[currencyCode]){
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.CURRENCY_NOT_INTEGRATED;
        return responseObject;
      }
      const keys = {
          launchUrl: WHITECLIFF_CREDENTIALS.WHITECLIFF_URL,
          agCode: WHITECLIFF_CREDENTIALS[currencyCode].WHITECLIFF_AG_CODE,
          agToken: WHITECLIFF_CREDENTIALS[currencyCode].WHITECLIFF_AG_TOKEN,
          homeRedirectionUrl: WHITECLIFF_CREDENTIALS.WHITECLIFF_HOME_REDIRECTION_URL,
      };

      const credentials = await TenantCredentialModel.findAll({
          where: {
              key: Object.values(keys),
              tenantId: this.args.tenant.id
          },
          raw: true
      });

      const values = {};
      Object.keys(keys).forEach((creds) => {
          const val = credentials.find(obj => obj.key === keys[creds]);
          if (val) {
              values[creds] = val.value;
          } else {
              responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
              responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()];
              return responseObject;
          }
      });

      if(!this.args.open_table.includes("____")){
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.INVALID_GAME_CODE;
        return responseObject;
      }

      let gameIdDetails = this.args.open_table.split("____");
      if (gameIdDetails.length != 2) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.INVALID_GAME_CODE;
        return responseObject;
      }
      const whiteCliffPrdId = parseInt(gameIdDetails[0])
      const whiteCliffGameId = parseInt(gameIdDetails[1])

      const tokenType = WHITECLIFF_CREDENTIALS.WHITECLIFF_USER_TOKEN;
      const UserTokenModel = this.context.databaseConnection.UserToken
      const whitecliffToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let wcSessionId
      if (!whitecliffToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const wcToken = await UserTokenModel.create(userTokenDataObject)
        wcSessionId = wcToken.token
      } else {
        wcSessionId = whitecliffToken.token
      }

      if(isImpersonated) {
        wcSessionId = `${wcSessionId}XIMPT`
      }

      const gameLaunchUrl = `${values.launchUrl}/auth`;
      const headers = {
          "Content-Type": "application/json",
          "ag-code": values.agCode,
          "ag-token": values.agToken,
      };
      const requestBody = {
        user: {
          id: userId,
          name: userDetails.userName,
          balance: userDetails.Wallet.amount || 0,
          language: 'en',
          currency: currencyCode,
          sid: wcSessionId,
          home_url: values.homeRedirectionUrl
        },
        prd: {
          id: whiteCliffPrdId,  // Whitecliff prd_id
          type: whiteCliffGameId, // Mandatory for few games.
          is_mobile: this.args?.device_type == 2 ? true : false,
          table_id: '', // Optional
          category: '', // Optional
        }
      };

      let whiteCliffResponse = await axios.post(gameLaunchUrl, requestBody, { headers });
      whiteCliffResponse = whiteCliffResponse?.data

      if (whiteCliffResponse?.status !== 1) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = whiteCliffResponse?.error;
        return responseObject;
      }

      const tokenType2 = WHITECLIFF_CREDENTIALS.WHITECLIFF_USER_ID;
      const whitecliffUserId = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType2 }
      })
      let wcUserId = whiteCliffResponse?.user_id
      if (!whitecliffUserId) {
        const userIdObject = { userId: userId, tokenType: tokenType2, token: wcUserId }
        await UserTokenModel.create(userIdObject)
      }

      responseObject.url = whiteCliffResponse.launch_url
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject

    }
      // for funkyGames gaming
    if (this.args.provider_name === GAME_LAUNCH.FUNKY_GAMES_GAMING) {
      const keys = {
        launchUrl: FUNKY_GAMES_CREDENTIALS.FUNKY_GAMES_URL,
        authentication: FUNKY_GAMES_CREDENTIALS.FUNKY_GAMES_AUTHENTICATION,
        userAgent: FUNKY_GAMES_CREDENTIALS.FUNKY_GAMES_USER_AGENT,
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: Object.values(keys),
          tenantId: this.args.tenant.id
        },
        raw: true
      })
      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
          values[creds] = val.value
        } else {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
          responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
          return responseObject
        }
      })
      const fnGameId = this.args.open_table;

      const tokenType = FUNKY_GAMES_CREDENTIALS.FUNKY_GAMES_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken

      const funkyGamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let fnSessionId
      if (!funkyGamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const fgToken = await UserTokenModel.create(userTokenDataObject)
        fnSessionId = fgToken.token
      } else {
        fnSessionId = funkyGamesToken.token
      }

      if(isImpersonated) {
        fnSessionId = `${fnSessionId}XIMPT`
      }

      const gameLaunchUrl = `${values.launchUrl}Funky/Game/LaunchGame`
      const headers = {
        Authentication: values.authentication
      }

      headers[`User-Agent`] = values.userAgent;
      headers[`X-Request-ID`] = uuidv4().replace(/-/g, '')
      let requestObject = {
        method: 'post',
        maxBodyLength: Infinity,
        url: gameLaunchUrl,
        headers,
        data: {
          currency: userDetails.Wallet.Currency.internalCode,
          disablePromotion: true,
          gameCode: fnGameId,
          language: 'en-us',
          playerId: userId,
          playerIp: this.args.ip,
          // redirectUrl: '',
          sessionId: fnSessionId,
          userName: userDetails.userName,
          isTestAccount: TEST_ACCOUNTS.includes(userDetails.userName) || this.args.tenant.id == 1
        }
      }
      const launchResponse = await axios(requestObject)
      if (launchResponse?.data?.errorCode != 0) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = FUNKY_GAMES_RESPONSE_CODES[launchResponse?.data.errorCode];
        return responseObject
      }
      responseObject.url = `${launchResponse?.data?.data?.gameUrl}?token=${launchResponse?.data?.data?.token}`
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
   }
    // for lottery7 gaming
    if (this.args.provider_name === GAME_LAUNCH.LOTTERY_GAMING) {

      if (!userDetails) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
          responseObject.message = GAME_LAUNCH.USER_NOT_FOUND;
          return responseObject;
      }

      const currencyCode = userDetails?.Wallet?.Currency?.internalCode;

      if (!LOTTERY_GAMES_CREDENTIALS[currencyCode]) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.CURRENCY_NOT_INTEGRATED;
        return responseObject;
      }

      const keys = {
        launchUrl: LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_URL,
        agentId: LOTTERY_GAMES_CREDENTIALS[currencyCode].LOTTERY_GAMES_AGENT_ID,
        secretKey: LOTTERY_GAMES_CREDENTIALS[currencyCode].LOTTERY_GAMES_ENC_KEY
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
        key: Object.values(keys),
        tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
        values[creds] = val.value
        } else {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
        return responseObject
        }
      })

      if(!values.launchUrl || !values.agentId || !values.secretKey){
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.KEYS_NOT_FOUND;
        return responseObject;
      }

      const lotteryGameId = this.args.open_table

      const tokenType = LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_USER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken

      const GamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let SessionId
      if (!GamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const fgToken = await UserTokenModel.create(userTokenDataObject)
        SessionId = fgToken.token
      } else {
        SessionId = GamesToken.token
      }
      const requestBody = {
        name: userDetails.id,
        currency: userDetails?.Wallet?.Currency?.code,
        language: 'IN',
        game_id: parseInt(lotteryGameId)
      }
      // encyption
      const key = CryptoJS.enc.Utf8.parse(values.secretKey)
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(requestBody), key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      })

      const requestObject = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${values.launchUrl}v1/api/create`,
        data: {
        agentID: `${values.agentId}`,
        data: encrypted.toString()
        }
      }

      const launchResponse = await axios(requestObject)

      responseObject.url = launchResponse?.data?.data.url
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for RedTiger
    if (this.args.provider_name === GAME_LAUNCH.REDTIGER_PROVIDER_NAME) {
      const data = [
        EZUGI_CREDENTIALS.APP_EZUGI_OPERATOR_ID,
        EZUGI_CREDENTIALS.APP_EZUGI_LAUNCH_URL
      ]

      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: data,
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const foundOperator = credentials.find(obj => obj.key === data[0])
      if (!foundOperator) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.OPERATOR_ID_NOT_FOUND
        return responseObject
      }

      const foundUrl = credentials.find(obj => obj.key === data[1])
      if (!foundUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }



      const environment = config.get('env')
      const tokenType = GAME_LAUNCH.REDTIGER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken
      const jwtTokenObj = { id: userId , tokenType: tokenType, timestamp: Math.floor(Date.now() / 1000) }

      const redtigerToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let tokenId
      if (!redtigerToken) {
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret) }
        const rtToken = await UserTokenModel.create(userTokenDataObject)
        tokenId = rtToken.token
      } else {
        const payload = jwt.decode(
          redtigerToken.token,
          config.getProperties().auth.jwt_secret
        );
        const newPayload = {
          ...payload,
          tokenType: tokenType,
          timestamp: Math.floor(Date.now() / 1000),
        };
        const userTokenData = jwt.sign(
          newPayload,
          config.getProperties().auth.jwt_secret
        );

        await UserTokenModel.update(
          { token: userTokenData },
          { where: { id: redtigerToken.id } }
        );
        tokenId = userTokenData;
      }
      if(isImpersonated) {
        jwtTokenObj.impersonated = true
        tokenId = jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      }
      const launchUrl = foundUrl.value
      const operatorId = foundOperator.value
      const openTable = this.args.open_table
      const currentDomain = tenant.domain

      const gameLaunchUrl = `https://${launchUrl}/auth_wallet/?token=${tokenId}&operatorId=${operatorId}&clientType=html&language=en&openTable=${openTable}${environment === 'production' ? `&homeUrl=${currentDomain}` : ''}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // for netENT
    if (this.args.provider_name === GAME_LAUNCH.NETENT_PROVIDER_NAME) {
      const data = [
        EZUGI_CREDENTIALS.APP_EZUGI_OPERATOR_ID,
        EZUGI_CREDENTIALS.APP_EZUGI_LAUNCH_URL
      ]

      const TenantCredentialModel = this.context.databaseConnection.TenantCredential
      const credentials = await TenantCredentialModel.findAll({
        where: {
          key: data,
          tenantId: this.args.tenant.id
        },
        raw: true
      })

      const foundOperator = credentials.find(obj => obj.key === data[0])
      if (!foundOperator) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.OPERATOR_ID_NOT_FOUND
        return responseObject
      }

      const foundUrl = credentials.find(obj => obj.key === data[1])
      if (!foundUrl) {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH.LAUNCH_URL_NOT_FOUND
        return responseObject
      }



      const environment = config.get('env')
      const tokenType = GAME_LAUNCH.NETENT_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken
      const jwtTokenObj = { id: userId, tokenType: tokenType, timestamp: Math.floor(Date.now() / 1000) }

      const netentToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let tokenId
      if (!netentToken) {
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret) }
        const netToken = await UserTokenModel.create(userTokenDataObject)
        tokenId = netToken.token
      } else {
        const payload = jwt.decode(
          netentToken.token,
          config.getProperties().auth.jwt_secret
        );
        const newPayload = {
          ...payload,
          tokenType: tokenType,
          timestamp: Math.floor(Date.now() / 1000),
        };
        const userTokenData = jwt.sign(
          newPayload,
          config.getProperties().auth.jwt_secret
        );

        await UserTokenModel.update(
          { token: userTokenData },
          { where: { id: netentToken.id } }
        );
        tokenId = userTokenData;
      }

      if(isImpersonated) {
        jwtTokenObj.impersonated = true
        tokenId = jwt.sign(jwtTokenObj, config.getProperties().auth.jwt_secret)
      }
      const launchUrl = foundUrl.value
      const operatorId = foundOperator.value
      const openTable = this.args.open_table
      const currentDomain = tenant.domain

      const gameLaunchUrl = `https://${launchUrl}/auth_wallet/?token=${tokenId}&operatorId=${operatorId}&clientType=html&language=en&openTable=${openTable}${environment === 'production' ? `&homeUrl=${currentDomain}` : ''}`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // Turbostars
    if (this.args.provider_name === GAME_LAUNCH.TURBO_STARS_PROVIDER_NAME) {
      const keys = {
        iframeUrl: TURBO_STARS_CREDENTIALS.TURBO_STARS_IFRAME_URL,
        apiKey: TURBO_STARS_CREDENTIALS.TURBO_STARS_API_KEY,
        locale: TURBO_STARS_CREDENTIALS.TURBO_STARS_LOCALE,
        subPartnerId: TURBO_STARS_CREDENTIALS.TURBO_STARS_SUB_PARTNER_ID,
        customStyles: TURBO_STARS_CREDENTIALS.TURBO_STARS_CUSTOM_STYLE_URL
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
        key: Object.values(keys),
        tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
        values[creds] = val.value
        }
      })

      const tokenType = TURBO_STARS_CREDENTIALS.TURBO_STARS_USER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken

      const GamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let sessionId
      if (!GamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const userToken = await UserTokenModel.create(userTokenDataObject)
        sessionId = userToken.token
      } else {
        sessionId = GamesToken.token
      }

      let gameLaunchUrl =`${values.iframeUrl}/?cid=${values.apiKey}&token=${sessionId}`
      if(values.locale){
        gameLaunchUrl=`${gameLaunchUrl}&locale=${values.locale}`
      }
      if(values.subPartnerId){
        gameLaunchUrl=`${gameLaunchUrl}&sub_partner_id=${values.subPartnerId}`
      }
      if(values.customStyles){
        gameLaunchUrl=`${gameLaunchUrl}&customStyles=${values.customStyles}`
      }

      if (this.args.game_name) {
        if (!this.args.game_name.includes(',')) {
          this.args.game_name = this.args.game_name + ','
        }
        gameLaunchUrl = `${gameLaunchUrl}&lineFilter=${this.args.game_name}`
      }

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = {
        host: values.iframeUrl,
        cid: values.apiKey,
        token: sessionId
      }
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    //For Spinocchio
    if (this.args.provider_name === GAME_LAUNCH.SPINOCCHIO) {
      if (!userDetails) {
          responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
          responseObject.message = GAME_LAUNCH.USER_NOT_FOUND;
          return responseObject;
      }

      const currencyCode = userDetails?.Wallet?.Currency?.internalCode
      const keys = {
        launchUrl: SPINOCCHIO_CREDENTIALS.SPINOCCHIO_URL,
        id: SPINOCCHIO_CREDENTIALS.SPINOCCHIO_ID,
        secret: SPINOCCHIO_CREDENTIALS.SPINOCCHIO_SECRET
      }

      const credentials = await TenantCredentialModel.findAll({
        where: {
        key: Object.values(keys),
        tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
        values[creds] = val.value
        } else {
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE
        responseObject.message = GAME_LAUNCH[creds.replace(/([a-z]+)([A-Z]?[a-z]+)?/, '$1_$2_NOT_FOUND').toUpperCase()]
        return responseObject
        }
      })

      if(!values.launchUrl || !values.id || !values.secret){
        responseObject.code = GAME_LAUNCH.INTERNAL_ERROR_CODE;
        responseObject.message = GAME_LAUNCH.KEYS_NOT_FOUND;
        return responseObject;
      }

      const spinoccioGameId = this.args.open_table

      const tokenType = SPINOCCHIO_CREDENTIALS.SPINOCCHIO_GAMES_USER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken

      const GamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })

      const tenants = config.get('env') === ENVIORNMENT.PRODUCTION ? PROD_TENANTS : STAGE_TENANTS
      const tenantName = Object.values(tenants).find(tenant => tenant.id === this.args.tenant.id)?.dataValues?.name || ''


      let SessionId
      if (!GamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const fgToken = await UserTokenModel.create(userTokenDataObject)
        SessionId = fgToken.token
      } else {
        SessionId = GamesToken.token
      }
      //const mode = config.get('env') === ENVIORNMENT.PRODUCTION ? SPINOCCHIO_USER_TYPE.REAL : SPINOCCHIO_USER_TYPE.DEMO
      const mode = SPINOCCHIO_USER_TYPE.REAL
      const requestBody = {
        mode: mode,
        currency: currencyCode,
        language: 'en',
        game_id: spinoccioGameId,
        user_id: userId,
        session_id: SessionId,
        home_url: '',
        operator: tenantName
      }
      // encyption
      const sign = crypto.createHash('md5').update(JSON.stringify(requestBody) + values.secret).digest('hex')

      const headers = {
        id: values.id,
        Sign: sign
      }

      const requestObject = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${values.launchUrl}play?method=launch`,
        headers: headers,
        data: requestBody
      }

      const launchResponse = await axios(requestObject)

      responseObject.url = launchResponse?.data.url
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS
      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

    // TurboGames
    if (this.args.provider_name.toLowerCase() === GAME_LAUNCH.TURBO_GAMES.toLowerCase()) {
      const keys = {
        iframeUrl: TURBO_GAMES_CREDENTIALS.TURBO_GAMES_IFRAME_URL,
        locale: TURBO_GAMES_CREDENTIALS.TURBO_GAMES_LOCALE,
        subPartnerId: TURBO_GAMES_CREDENTIALS.TURBO_GAMES_SUB_PARTNER_ID,
        secretKey: TURBO_GAMES_CREDENTIALS.TURBO_GAMES_SECRET_KEY,
        cid: TURBO_GAMES_CREDENTIALS.TURBO_GAMES_CID,
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
        key: Object.values(keys),
        tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
        values[creds] = val.value
        }
      })

      const tokenType = TURBO_GAMES_CREDENTIALS.TURBO_GAMES_USER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken
      const openTable = this.args.open_table
      const GamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let sessionId
      if (!GamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const userToken = await UserTokenModel.create(userTokenDataObject)
        sessionId = userToken.token
      } else {
        sessionId = GamesToken.token
      }

      let gameLaunchUrl =`https://${openTable}${values.iframeUrl}/?locale=${values.locale}&cid=${values.cid}&token=${sessionId}&demo=false`

      if(values.subPartnerId){
        gameLaunchUrl=`${gameLaunchUrl}&sub_partner_id=${values.subPartnerId}`
      }
      gameLaunchUrl=`${gameLaunchUrl}&returnUrl=hidden`

      responseObject.message = GAME_LAUNCH.SUCCESS
      responseObject.url = gameLaunchUrl
      responseObject.data = null

      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

     // Pigaboom
    if (this.args.provider_name.toLowerCase() === GAME_LAUNCH.PIGABOOM.toLowerCase()) {


      const keys = {
        iframeUrl: PIGABOOM_CREDENTIALS.PIGABOOM_IFRAME_URL,
        brandId: PIGABOOM_CREDENTIALS.PIGABOOM_BRAND_ID
      }
      const credentials = await TenantCredentialModel.findAll({
        where: {
        key: Object.values(keys),
        tenantId: this.args.tenant.id
        },
        raw: true
      })

      const values = {}
      Object.keys(keys).map((creds) => {
        const val = credentials.find(obj => obj.key === keys[creds])
        if (val) {
        values[creds] = val.value
        }
      })

      const tokenType = PIGABOOM_CREDENTIALS.PIGABOOM_USER_TOKEN
      const UserTokenModel = this.context.databaseConnection.UserToken
      const GamesToken = await UserTokenModel.findOne({
        where: { userId: userId, tokenType: tokenType }
      })
      let sessionId
      if (!GamesToken) {
        const uniqueSessionId = uuidv4().replace(/-/g, '')
        const userTokenDataObject = { userId: userId, tokenType: tokenType, token: uniqueSessionId }
        const userToken = await UserTokenModel.create(userTokenDataObject)
        sessionId = userToken.token
      } else {
        sessionId = GamesToken.token
      }
      const openTable = this.args.open_table
      const exitUrl = tenant.domain
      const providerId = config.get('env') === ENVIORNMENT.PRODUCTION ? PIGABOOM_PROVIDER_ID.PROD : PIGABOOM_PROVIDER_ID.STAGE
      let playMode = 1
      // if(config.get('env') === ENVIORNMENT.PRODUCTION){
      //   if(TEST_ACCOUNTS.includes(userDetails.userName)){
      //     playMode = 2  // Demo play
      //   }else{
      //     playMode = 1  // Real play
      //   }
      // }
      const currencyCode = userDetails.Wallet.Currency.internalCode
      let gameLaunchUrl =`https://${values.iframeUrl}/launch_game?sessionToken=${sessionId}&gameId=${openTable}&providerId=${providerId}&language=en&platform=web&playMode=${playMode}&brandId=${values.brandId}&exitUrl=${exitUrl}&currency=${currencyCode}`

      const requestObject = {
        method: 'get',
        maxBodyLength: Infinity,
        url: `${gameLaunchUrl}`
      }

      const launchResponse = await axios(requestObject)
      responseObject.url = launchResponse?.data.gameUrl
      responseObject.data = null
      responseObject.message = GAME_LAUNCH.SUCCESS

      if (!isImpersonated) {
        await gamePlayHistory(this?.context?.databaseConnection?.GamePlayHistory, this?.context?.databaseConnection?.CasinoItem, { userId, tenantId: this?.args?.tenant?.id, deviceType: this?.args?.device_type, provider: this?.args?.provider_name, gameId: this?.args?.open_table })
      }
      return responseObject
    }

  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId: this.args.tenant.id })
    throw error
  }
  }
}
