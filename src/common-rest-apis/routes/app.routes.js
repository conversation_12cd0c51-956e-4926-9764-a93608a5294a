import express from 'express'
import { checkMaintenance, gameLaunch, healthCheckup, paymentSuccess, readSheetData, sendSmsAlerts } from '../controllers/app.controller'
const router = express.Router()

router.route('/read-sheet-data').post(readSheetData)
router.route('/gameLaunch').post(gameLaunch)
router.route('/checkMaintenance').post(checkMaintenance)
router.route('/health-checkup').get(healthCheckup)
router.route('/send-sms-alerts').post(sendSmsAlerts)
router.route('/payment-successs').post(paymentSuccess)
export default router
