/**
 * Used to check inactive agents
 * in agents tree
 * @export
 * @param {object} playerId
 * @param {object} sequelize
 * @returns
 */
export default async function CheckAgentInactive (playerId, tenantId, sequelize) {
  const [idsArr] = await sequelize.query(`
    WITH RECURSIVE children AS (
    SELECT id, first_name, last_name, user_name as agent_name, active, 'User' AS type, parent_id, parent_type, tenant_id, 0 AS relative_depth
    FROM users
    WHERE id = :playerId AND tenant_id = :tenantId

    UNION ALL

    SELECT parent.id, parent.first_name as agent_fn, parent.last_name as agent_ln, parent.agent_name, parent.active, 'AdminUser' AS type, parent.parent_id, parent.parent_type, parent.tenant_id, child.relative_depth - 1
    FROM admin_users parent, children child
    WHERE parent.id = child.parent_id AND child.parent_type LIKE 'AdminUser' AND
    (parent.id != child.id OR child.type != 'AdminUser') AND parent.tenant_id = child.tenant_id
    )
    SELECT * FROM children;
      `, {
    replacements: {
      playerId,
      tenantId
    }
  })

  const result = idsArr.filter((item) => {
    return (item.type !== 'User' && item.active === false)
  }).map((item) => {
    return item
  })
  return result
}
