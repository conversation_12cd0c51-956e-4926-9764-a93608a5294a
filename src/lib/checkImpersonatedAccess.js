import { ApolloError } from 'apollo-server-express';
import * as jwt from 'jsonwebtoken';
import config from '../config/app';
import translate from './languageTranslate';


export default async (context) => {
  const language = context.req?.headers?.language
  if (context.req?.headers?.authorization) {
    const token = context.req?.headers?.authorization
    const authConfig = config.getProperties().auth
    const splitToken = token.replace('Bearer ', '')
    const secretKey = authConfig.jwt_secret

    try {
      const decodedToken = await jwt.verify(splitToken, secretKey)
     if(decodedToken.impersonated){
      throw new ApolloError(translate('IMPERSONATED_USER_NOT_ALLOWED', language), 403)
     }
    } catch (e) {
      throw new Error(e)
    }
  }

  return true
}
