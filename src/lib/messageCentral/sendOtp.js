const axios = require('axios')

/**
 * This function sends a otp to a given phone number .
 * @export
 * @param {*} phone - phone
 * @param {*} phoneCode - phoneCode
 * @param {*} authToken - auth Token
 * @param {*} context - context
 * @return {*} token
 */
export async function SendOtp (messageCentralConfig, phone, phoneCode, authToken) {
  try {
    const baseUrl = messageCentralConfig.APP_MESSAGECENTRALSMS_BASE_URL
    const url = `${baseUrl}/verification/v3/send`
    const params = {
      customerId: messageCentralConfig.APP_MESSAGECENTRALSMS_CUSTOMER_ID,
      countryCode: phoneCode,
      otpLength: 6,
      mobileNumber: phone,
      flowType: 'SMS'
    }

    const queryParams = new URLSearchParams(params).toString()

    const config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${url}?${queryParams}`,
      headers: {
        authToken: authToken
      }
    }

    const { data } = await axios.request(config)
    if (data) return data
    else return false
  } catch (Error) {
    throw new Error()
  }
}
