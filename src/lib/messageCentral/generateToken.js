const axios = require('axios')

/**
 * This function generate a auth token for send otp & validate otp header .
 * @export
 * @param {*} messageCentralConfig - configuration details
 * @return {*} token
 */
export async function GenerateAuthToken (messageCentralConfig) {
  try {
    const baseUrl = messageCentralConfig.APP_MESSAGECENTRALSMS_BASE_URL
    const url = `${baseUrl}/auth/v1/authentication/token`
    const params = {
      customerId: messageCentralConfig.APP_MESSAGECENTRALSMS_CUSTOMER_ID,
      key: messageCentralConfig.APP_MESSAGECENTRALSMS_ENCRYPTED_KEY,
      scope: 'NEW'
    }

    const { data } = await axios.get(url, { params })
    if (data?.status !== 200) return false
    return data?.token
  } catch (Error) {
    console.log('----------error in generateToken is -----------', Error)
    throw Error
  }
}
