const axios = require('axios')

/**
 * This function verify otp .
 * @export
 * @param {*} verifyPhoneToken - verifyPhoneToken
 * @param {*} otp - otp
 * @param {*} authToken - auth Token
 * @param {*} messageCentralConfig - messageCentral configuration
 * @return {*} token
 */
export async function ValidateOtp (messageCentralConfig, authToken, verifyPhoneToken, otp) {
  try {
    const baseUrl = messageCentralConfig.APP_MESSAGECENTRALSMS_BASE_URL
    const url = `${baseUrl}/verification/v3/validateOtp`
    const params = {
      customerId: messageCentralConfig.APP_MESSAGECENTRALSMS_CUSTOMER_ID,
      code: otp,
      verificationId: verifyPhoneToken.token,
      mobileNumber: verifyPhoneToken.phone,
      countryCode: verifyPhoneToken.phoneCode
    }

    const queryParams = new URLSearchParams(params).toString()

    const config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${url}?${queryParams}`,
      headers: {
        authToken: authToken
      }
    }

    const { data } = await axios.request(config)
    return data
  } catch (Error) {
    throw new Error()
  }
}
