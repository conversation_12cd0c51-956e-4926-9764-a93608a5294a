import { BONUS_STATUS, BONUS_TYPES, TRANSACTION_TYPES } from '../common/constants'
import { walletLocking } from '../common/walletLocking'
// import generateRandomOrderId from '../lib/generateRandomOrderId'
import { v4 as uuidv4 } from 'uuid'
import currencyConversionV3 from '../common/currencyConversionV3'
import userCurrencyExchange from '../common/userCurrencyExchange'

/**
 * Used to check eligibility for joining bonus and update wallet if eligible
 * @export
 * @param {object} user
 * @param {object} context
 * @returns
 */
export default async ({ user, context }) => {
  const {
    databaseConnection: {
      UserBonus: UserBonusModel,
      Wallet: WalletModel,
      Bonus: BonusModel,
      Transaction: TransactionModel
    },
    tenant: Tenant,
    sequelizeTransaction
  } = context

  const findBonus = await BonusModel.findOne(
    {
      attributes: ['id', 'flat'],
      where: {
        tenantId: Tenant.id,
        kind: BONUS_TYPES.JOINING,
        enabled: true
      },
      raw: true
    })

  if (!findBonus) {
    return null
  }

  // checking joining bonus already exist or not
  const joiningBonusClaimed = await UserBonusModel.findOne({
    attributes: ['id'],
    where: {
      userId: user.id,
      bonusId: findBonus.id
    },
    raw: true
  })

  // if joining bonus exist return
  if (joiningBonusClaimed) {
    return null
  }

  const convertedAmount = (+findBonus.flat)
  // creating object for transaction banking
  let transactionObject = {
    sourceWalletId: null,
    sourceCurrencyId: null,
    amount: convertedAmount,
    transactionId: uuidv4(),
    comments: 'Joining Bonus Credited',
    actioneeId: user.id,
    actioneeType: 'User',
    tenantId: user.tenantId,
    timestamp: Date.now(),
    transactionType: TRANSACTION_TYPES.JOINING_BONUS_CLAIMED,
    //errorDescription: 'Completed Successfully',
    errorCode: 0,
    success: true,
    status: 'success'
  }

  const userWallet = await walletLocking(context, user)
  await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
  transactionObject.targetWalletId = userWallet.id
  transactionObject.targetCurrencyId = userWallet.currencyId
  transactionObject.targetBeforeBalance = +userWallet.nonCashAmount

  userWallet.nonCashAmount = (+userWallet.nonCashAmount) + convertedAmount

  //  adding joining bonus amount to non cash in wallet
  await userWallet.save({ transaction: sequelizeTransaction })
  transactionObject = await currencyConversionV3(context, transactionObject, userWallet, convertedAmount)

  transactionObject.targetAfterBalance = +userWallet.nonCashAmount
  transactionObject.conversionRate = await userCurrencyExchange(context, userWallet.currencyId)

  // adding to transaction banking
  const transactionBanking = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction })

  // creating user bonus object
  const userBonusObject = {
    status: BONUS_STATUS.CLAIMED,
    bonusAmount: convertedAmount,
    userId: user.id,
    bonusId: findBonus.id,
    kind: BONUS_TYPES.JOINING,
    tenantId: Tenant.id,
    claimedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    transactionId: transactionBanking.id
  }

  // adding data to user bonus
  await UserBonusModel.create(userBonusObject, { transaction: sequelizeTransaction })

  return transactionBanking
}
