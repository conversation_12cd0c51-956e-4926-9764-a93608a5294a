import * as jwt from 'jsonwebtoken';
import config from '../config/app';


export default async (context) => {
  if (context?.headers?.authorization) {
    const token = context?.headers?.authorization
    const authConfig = config.getProperties().auth
    const splitToken = token.replace('Bearer ', '')
    const secretKey = authConfig.jwt_secret

    try {
      const decodedToken = await jwt.verify(splitToken, secretKey)
      return decodedToken?.impersonated ? true : false
    } catch (e) {
      return false
    }
  }

  return false
}
