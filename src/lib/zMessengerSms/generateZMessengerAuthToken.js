const axios = require('axios')

export async function generateZMessengerSMSAuthToken (zMessengerSMSConfig) {
  try {
    const tokenResponse = await axios.post(zMessengerSMSConfig.ZMESSENGER_SMS_LOGIN_URL, {
      username: zMessengerSMSConfig.ZMESSENGER_SMS_USERNAME,
      password: zMessengerSMSConfig.ZMESSENGER_SMS_CREDENTIAL_SECRET
    });

    const { token } = tokenResponse.data.data;

    return token;
  } catch (error) {
    throw new Error('Error generating access token: ' + error.message);
  }
}
