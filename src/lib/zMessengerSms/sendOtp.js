import { UserInputError } from 'apollo-server-express';
import translate from '../../lib/languageTranslate';
import keyValueToJson from '../keyValueToJSON';
import { generateZMessengerSMSAuthToken } from './generateZMessengerAuthToken';
const axios = require('axios')

/**
 * This function sends a fixed message to a given phone number.
 * @export
 * @param {*} firstname - User's firstname
 * @param {*} phone - User's phone number
 * @param {*} otp - Generated OTP
 * @param {*} context - GraphQL context
 * @return {*} twilio object
 */

export async function sendZMessengerSMSPhoneVerificationOTP (firstname, phone, otp, context, smsAlerts = false) {
  try {
    const TenantCredential = context.databaseConnection.TenantCredential

    const zMessengerSMSConfig = await keyValueToJson(
      TenantCredential,
      ['ZMESSENGER_SMS_LOGIN_URL', 'ZMESSENGER_SEND_SMS_URL', 'ZMESSENGER_SMS_USERNAME', 'ZMESSENGER_SMS_CREDENTIAL_SECRET'],
      'tenantId', context.tenant.id
    )

    const tenantDomain = context?.req?.headers?.origin?.split('://').slice(1).join('.').split(':')[0]

    // Generate access token
    const authToken = await generateZMessengerSMSAuthToken(zMessengerSMSConfig)

    const url = zMessengerSMSConfig.ZMESSENGER_SEND_SMS_URL

    let message
    if (!smsAlerts)
    message = `Hi${firstname ? ' ' + firstname : ''}, ${otp} is your one-time password (OTP) to complete your request on the ${tenantDomain}.`
    else message = otp
    // Prepare JSON payload
    const payload = {
      msisdn: phone,
      content: message,
    }

    // Make the request with the access token
    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json',
        'zm-access-token': authToken,
        'zm-username': zMessengerSMSConfig.ZMESSENGER_SMS_USERNAME
      }
    })

    if (!response) {
      throw new Error('Unsupported OTP')
    }

    return {
      success: true,
      data: response.data
    }
  } catch (error) {
    const msg = error?.response?.data?.comment || error.message
    throw new UserInputError(translate('UNSUPPORTED_OTP', msg))
  }
}
