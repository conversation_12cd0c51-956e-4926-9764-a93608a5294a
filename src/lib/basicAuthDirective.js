import { AuthenticationError } from 'apollo-server-express'
import { DirectiveLocation, GraphQLDirective } from 'graphql'
import { SchemaDirectiveVisitor } from 'graphql-tools'
import { IncomingMessage } from 'http'

export default function BasicAuthDirective (basicUsername, basicPassword) {
  const verifyAndDecodeIdPass = ({ context }) => {
    const req =
      context instanceof IncomingMessage
        ? context
        : context.req || context.request

    if (
      (!req ||
        !req.headers ||
        (!req.headers.authorization && !req.headers.Authorization)) &&
      !(req.cookies && req.cookies.token)
    ) {
      throw new AuthenticationError('No authorization token.')
    }

    const token =
      req.headers.authorization || req.headers.Authorization || req.cookies.token
    try {
      const basicToken = token.replace('Basic ', '')
      const [username, password] = Buffer.from(basicToken, 'base64').toString().split(':')

      if (username !== basicUsername && password !== basicPassword) {
        throw new AuthenticationError('No authorization token.')
      }
    } catch (err) {
      throw new AuthenticationError('You are not authorized for this resource.')
    }
  }

  return class BasicAuthDirective extends SchemaDirectiveVisitor {
    static getDirectiveDeclaration (directiveName, schema) {
      return new GraphQLDirective({
        name: 'isBasicAuthenticated',
        locations: [DirectiveLocation.FIELD_DEFINITION, DirectiveLocation.OBJECT]
      })
    }

    visitObject (obj) {
      const fields = obj.getFields()

      Object.keys(fields).forEach(fieldName => {
        const field = fields[fieldName]
        const next = field.resolve

        field.resolve = function (result, args, context, info) {
          verifyAndDecodeIdPass({ context }) // will throw error if not valid signed jwt
          return next(result, args, { ...context, authenticated: true }, info)
        }
      })
    }

    visitFieldDefinition (field) {
      const next = field.resolve

      field.resolve = function (result, args, context, info) {
        verifyAndDecodeIdPass({ context })
        return next(result, args, { ...context, authenticated: true }, info)
      }
    }
  }
}
