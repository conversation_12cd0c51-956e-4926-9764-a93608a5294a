import * as jwt from 'jsonwebtoken';
import { Op, Sequelize } from 'sequelize';
import { DEMO_LOGIN_USER_CREDENTIAL, ENVIORNMENT, PROD_TENANTS, STAGE_TENANTS } from '../common/constants';
import config from '../config/app';

export default async (context, attributes) => {
  let decodedToken = null, user = null;

  const {
    databaseConnection: {
      User: UserModel,
      Wallet: WalletModel
    },
    tenant: Tenant,
    req: { headers: { authorization } },
  } = context

  if (authorization) {
    const token = authorization.replace('Bearer ', '');
    const secretKey = config.getProperties().auth.jwt_secret;

    decodedToken = jwt.verify(token, secretKey);
    const tenants = config.get('env') === ENVIORNMENT.PRODUCTION ? PROD_TENANTS : STAGE_TENANTS
    const tenantBaseCurrencyCode = Object.values(tenants).find(tenant => tenant.id === Tenant.id)?.currency || '';

    user = await UserModel.findOne({
      where: { id: decodedToken.id },
      attributes,
      include: [{
        model: WalletModel,
        attributes: ['currencyId'],
        ...(decodedToken.tenantId === DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID
          ? {
            where: {
              currencyId: {
                [Op.eq]: Sequelize.literal(`(
                    SELECT id FROM "currencies" WHERE code = '${tenantBaseCurrencyCode}'
                )`),
              },
            },
          }
          : {}
        ),
      }],
    });

    if (!user) throw new Error('Invalid or expired token');
  }

  return { decodedToken, user };
}
