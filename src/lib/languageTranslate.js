import i18n from 'i18n'
import path from 'path'

i18n.configure({
  locales: ['bn-IN', 'en-US', 'es-ES', 'fr-FR', 'hi-IN', 'ja-JP', 'pt-BR', 'si-LK', 'ta-IN', 'te-IN', 'tr-TR'],
  directory: path.join(__dirname, '../locals'),
  defaultLocale: 'en-US'
})

export default (phrase, language) => {
  const formattedLanguage = formatLanguageCode(language)
  i18n.setLocale(formattedLanguage)
  return i18n.__(phrase)
}

// Utility function to format language codes correctly
function formatLanguageCode (language) {
  if (!language) return 'en-US'
  const [lang = 'en', region = 'US'] = language.split('-')
  return `${lang.toLowerCase()}-${region.toUpperCase()}`
}
