import jwt from 'jsonwebtoken'
import { DEMO_LOGIN_USER_CREDENTIAL } from '../common/constants'
import config from '../config/app'
import redisConnection from '../lib/redisConnection'
import keyValueTo<PERSON>son from './keyValueToJSON'
/**
 * Provides service for the currency
 * @export
 * @class Currency
 * @extends {ServiceBase}
 */
export const isUserActive = async (token, tenantId, model, isRestAPI) => {
  const authConfig = config.getProperties().auth
  let splitToken
  let secretKey
  if (!isRestAPI) {
    splitToken = token.replace('Bearer ', '')
    secretKey = authConfig.jwt_secret
  } else {
    const ezugiCredentials = await keyValueToJson(model.TenantCredential, ['APP_JWT_SECRET_KEY'], 'tenantId', tenantId)
    secretKey = ezugiCredentials.APP_JWT_SECRET_KEY
    splitToken = token
  }

  let decodedToken
  try {
    decodedToken = await jwt.verify(splitToken, secretKey)
  } catch (e) {
    return null
  }

  const user = await model.User.findOne({
    where: {
      id: decodedToken?.id,
      active: true
    },
    raw: true
  })


  const { autoLogoutDuration } = await model.TenantThemeSetting.findOne({
    attributes: ['autoLogoutDuration'],
    where: {
      tenantId
    },
    raw: true
  })
  if (!user) {
    return null
  }

  const storedToken = await redisConnection.get(`user:${user.id}`)
  const ttl = await redisConnection.ttl(`user:${user.id}`)
  const maxTTL = config.get('auth.expiry_time')
  const threshHold = maxTTL - (autoLogoutDuration || 6) * 60 * 60

  if ((user.tenantId == DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID) || (decodedToken.impersonated)) {
    return user
  }

  if ((storedToken && (storedToken !== splitToken)) || ttl <= threshHold) {
    return null
  }

  return user || null
}
