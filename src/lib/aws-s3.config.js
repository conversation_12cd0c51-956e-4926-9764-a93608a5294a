import AWS from 'aws-sdk'
import config from '../config/app'

const s3Config = config.getProperties().s3
let s3Connection
if (process.env.NODE_ENV === 'production') {
  s3Connection = {
    region: s3Config.region
  }
} else {
  s3Connection = {
    accessKeyId: s3Config.access_key_id,
    secretAccessKey: s3Config.secret_access_key,
    region: s3Config.region
  }
}
export const s3 = new AWS.S3(s3Connection)
// export const s3 = new AWS.S3({
//   accessKeyId: s3Config.access_key_id,
//   secretAccessKey: s3Config.secret_access_key,
//   region: s3Config.region
// })
