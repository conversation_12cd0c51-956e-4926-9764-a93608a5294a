import { Op } from 'sequelize'

export default async function keyValueT<PERSON><PERSON><PERSON> (model, keyNames, keyId, valueId) {
  const keyValueArray = await model.findAll({
    attributes: ['key', 'value'],
    where: {
      key: {
        [Op.in]: keyNames
      },
      [keyId]: valueId
    }
  })

  const keyValueToJsonArray = keyValueArray.reduce((obj, entries) => {
    obj[entries.key] = entries.value
    return obj
  }, {})
  return keyValueToJsonArray
}
