import keyValueT<PERSON><PERSON><PERSON> from './keyValueToJSON'

/**
 * This function sends a fixed message to a given phone number.
 * @export
 * @param {*} phone - User's phone number
 * @param {*} countryCode - User's country code
 * @param {*} otp - Generated OTP
 * @param {*} context - GraphQL context
 * @return {*} twilio object
 */
export async function sendPhoneVerificationOTP (phone, countryCode, otp, context) {
  const TenantCredential = context.databaseConnection.TenantCredential

  const twilioConfig = await keyValueTo<PERSON>son(
    TenantCredential,
    [
      'APP_TWILIO_ACCOUNT_SID',
      'APP_TWILIO_AUTH_TOKEN',
      'APP_TWILIO_PHONE'
    ],
    'tenantId', context.tenant.id
  )

  const client = require('twilio')(twilioConfig.APP_TWILIO_ACCOUNT_SID, twilioConfig.APP_TWILIO_AUTH_TOKEN, {
    lazyLoading: true
  })
  const twilio = await client.messages.create({
    to: `+${countryCode}${phone}`,
    from: twilioConfig.APP_TWILIO_PHONE,
    body: otp
  })

  return twilio
}
