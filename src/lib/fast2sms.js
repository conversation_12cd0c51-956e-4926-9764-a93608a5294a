import config from '../config/app'
import keyValue<PERSON><PERSON><PERSON><PERSON> from './keyValueToJSON'
const axios = require('axios')

/**
 * This function sends a fixed message to a given phone number.
 * @export
 * @param {*} phone - User's phone number
 * @param {*} countryCode - User's country code
 * @param {*} otp - Generated OTP
 * @param {*} context - GraphQL context
 * @return {*} twilio object
 */
export async function sendFast2SMSPhoneVerificationOTP (phone, countryCode, otp, context) {
  try {
    const TenantCredential = context.databaseConnection.TenantCredential
    const smsConfig = config.getProperties().fast2sms_post_url
    const fast2smsConfig = await keyValueTo<PERSON><PERSON>(
      TenantCredential,
      [
        'APP_FAST2SMS_AUTHORIZATION_TOKEN',
        'APP_FAST2SMS_SENDER_ID',
        'APP_FAST2SMS_MESSAGE_ID'
      ],
      'tenantId', context.tenant.id
    )

    const data = JSON.stringify({
      sender_id: fast2smsConfig.APP_FAST2SMS_SENDER_ID,
      message: fast2smsConfig.APP_FAST2SMS_MESSAGE_ID,
      variables_values: otp,
      route: 'dlt',
      numbers: phone
    })

    var configs = {
      method: 'post',
      url: smsConfig,
      headers: { Authorization: fast2smsConfig.APP_FAST2SMS_AUTHORIZATION_TOKEN, 'Content-Type': 'application/json' },
      data: data
    }

    const response = await axios.request(configs)
    return {
      success: !!response
    }
  } catch (error) {
    throw new Error('Erros:' + error)
  }
}
