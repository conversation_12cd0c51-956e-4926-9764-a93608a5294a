import { Op, Sequelize } from 'sequelize'
import keyValue<PERSON><PERSON><PERSON><PERSON> from './keyValueToJSON'
import { TRANSACTION_TYPES } from '../common/constants'

export default async (context, userId, amount) => {
  const {
    databaseConnection: {
      Transaction: TransactionModel,
      Wallet: WalletModel,
      UserSetting: UserSettingModel,
      TenantSportsBetSetting: TenantSportsBetSettingModel
    },
    tenant: Tenant
  } = context

  let allowed = true
  const userSetting = await keyValueTo<PERSON>son(UserSettingModel, ['depositLimit'], 'userId', userId)

  const tenantSettings = await TenantSportsBetSettingModel.findOne({
    where: {
      tenantId: Tenant.id
    }
  })

  let depositLimit = null
  if (userSetting?.depositLimit) {
    depositLimit = Math.min(+userSetting.depositLimit, +tenantSettings?.depositLimit)
  } else {
    depositLimit = +tenantSettings?.depositLimit
  }

  const wallet = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' }, raw: true })

  const userTransaction = await TransactionModel.findOne({
    attributes: ['targetWalletId', [Sequelize.fn('sum', Sequelize.col('amount')), 'totalAmount']],
    group: ['targetWalletId'],
    raw: true,
    where: {
      targetWalletId: wallet.id,
      transactionType: TRANSACTION_TYPES.DEPOSIT,
      [Op.and]:
        [Sequelize.where(Sequelize.fn('date', Sequelize.col('created_at')), '=', Sequelize.literal('CURRENT_DATE'))]
    }
  })

  if (depositLimit) {
    allowed = depositLimit >= (((+userTransaction?.totalAmount) || 0) + amount)
  }

  return allowed
}
