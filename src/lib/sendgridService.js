import FormData from 'form-data'
import Mailgun from 'mailgun.js'
import * as nodemailer from 'nodemailer'
import { EMAIL_PROVIDERS, EMAIL_PROVIDERS_CREDENTIAL_KEYS } from '../common/constants'
import keyValueTo<PERSON><PERSON> from './keyValueToJSON'

/**
 * This function sends a fixed email to the given email address (For various verification)
 * @export
 * @param {*} id - User id
 * @param {*} token - Generated token (OTP)
 * @param {*} email - user email
 * @param {*} context - GraphQL context
 * @return {*} sent message id
 */
export async function sendEmailThroughSmtpRelay (id, token, email, context, tokenType, provider = EMAIL_PROVIDERS.SENDGRID) {
  const TenantCredential = context.databaseConnection.TenantCredential
  const frontendHost = context.req.headers.origin
  const sendgridConfig = await keyValueTo<PERSON><PERSON>(
    TenantCredential,
    [
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.SENDGRID],
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.MAILGUN],
    ],
    'tenantId', context.tenant.id
  )
  let transporter
  if(provider == EMAIL_PROVIDERS.SENDGRID) {

   transporter = nodemailer.createTransport({
    host: sendgridConfig.APP_SENDGRID_HOST,
    port: sendgridConfig.APP_SENDGRID_PORT,
    auth: {
      user: sendgridConfig.APP_SENDGRID_USERNAME,
      pass: sendgridConfig.APP_SENDGRID_RELAY_KEY
    }
  })
} else if (provider == EMAIL_PROVIDERS.MAILGUN) {
  const mailgun = new Mailgun(FormData)
  const mg = mailgun.client({
    username: sendgridConfig.MAILGUN_USERNAME || 'api',
    key: sendgridConfig.MAILGUN_API_KEY,
    // url: 'https://api.mailgun.net' // Use US region by default, change to 'https://api.eu.mailgun.net' for EU
  })

  transporter = {
    sendMail: async (emailOptions) => {
      const result = await mg.messages.create(sendgridConfig.MAILGUN_DOMAIN, {
        from: emailOptions.from,
        to: [emailOptions.to],
        subject: emailOptions.subject,
        html: emailOptions.html
      })
      return { messageId: result.id }
    }
  }

}

if (!transporter) {
  return Promise.reject(new Error('No transporter configured for sending emails'))
}

  const adminEmail = provider === EMAIL_PROVIDERS.SENDGRID ? sendgridConfig.APP_SENDGRID_EMAIL : sendgridConfig.MAILGUN_FROM_EMAIL

  const sendEmailObj = {
    from: `"Ezugi " <${adminEmail}>`,
    to: email
  }
  if (tokenType === 'email') {
    sendEmailObj.subject = 'Verify your email'
    sendEmailObj.text = 'Your Ezugi verification link'
    sendEmailObj.html = `<h1><a href=${frontendHost}/verify/${id}/${token}>Click to verify</a></h1>`
  } else if (tokenType === 'passwordReset') {
    sendEmailObj.subject = 'Password reset link'
    sendEmailObj.text = 'Your Ezugi password reset link'
    sendEmailObj.html = `<p>Hello ${email},</p></br><p> Someone has requested a link to change your password. You can do this through the link below.</p></br><h1><a href=${frontendHost}/reset-password/${id}/${token}>Change my password</a></h1></br><p>Your password won't change until you access the link above and create a new one.</p>`
  }


  // send mail with defined transport object
  try {
    const info = await transporter.sendMail(sendEmailObj)
    return info.messageId
  } catch (error) {
    console.error('Error sending email:', error)
    return true
  }
}

/**
 * This function sends a fixed email to the given email address to notify about the failed settlement
 * @export
 * @param {*} email - user email
 * @param {*} JobID - id of the failed job
 * @param {*} context - object contains database connections
 * @return {*} sent message id
 */
export async function sendFailedSettlementEmail (jobId, tenantId, context) {
  const TenantCredential = context.databaseConnection.TenantCredential
  const TenantThemeSettingModel = context.databaseConnection.TenantThemeSetting
  const tenantTheme = await TenantThemeSettingModel.findOne({
    where: {
      tenantId: tenantId
    },
    attributes: ['smsGateway', 'allowedModules', 'userLoginType', 'emailGateway']
  })

  const sendgridConfig = await keyValueToJson(
    TenantCredential,
    [
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.SENDGRID],
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.MAILGUN],
    ],
    'tenantId', tenantId
  )
const provider = tenantTheme.emailGateway || EMAIL_PROVIDERS.SENDGRID // Default to SendGrid if not specified
let transporter
if(provider == EMAIL_PROVIDERS.SENDGRID) {

   transporter = nodemailer.createTransport({
    host: sendgridConfig.APP_SENDGRID_HOST,
    port: sendgridConfig.APP_SENDGRID_PORT,
    secure: true,
    auth: {
      user: sendgridConfig.APP_SENDGRID_USERNAME,
      pass: sendgridConfig.APP_SENDGRID_RELAY_KEY
    }
  })
} else if (provider == EMAIL_PROVIDERS.MAILGUN) {
  const mailgun = new Mailgun(FormData)
  const mg = mailgun.client({
    username: sendgridConfig.MAILGUN_USERNAME || 'api',
    key: sendgridConfig.MAILGUN_API_KEY,
    // url: 'https://api.mailgun.net' // Use US region by default, change to 'https://api.eu.mailgun.net' for EU
  })

  transporter = {
    sendMail: async (emailOptions) => {
      const result = await mg.messages.create(sendgridConfig.MAILGUN_DOMAIN, {
        from: emailOptions.from,
        to: ['<EMAIL>'],
        subject: emailOptions.subject,
        html: emailOptions.html
      })
      return { messageId: result.id }
    }
  }

}

if (!transporter) {
  return Promise.reject(new Error('No transporter configured for sending emails'))
}

const adminEmail = provider === EMAIL_PROVIDERS.SENDGRID ? sendgridConfig.APP_SENDGRID_EMAIL : sendgridConfig.MAILGUN_FROM_EMAIL
  const sendEmailObj = {
    from: `"Ezugi " <${adminEmail}>`,
    to: `${adminEmail}, <EMAIL>`
  }

  sendEmailObj.subject = 'Bet settlement Failed'
  sendEmailObj.text = `Bet settlement is failed with betslip id: ${jobId}`
  sendEmailObj.html = `<p><b>Kindly contact the admin<br>bet slip id: ${jobId}</b></p>`

  // send mail with defined transport object
  const info = await transporter.sendMail(sendEmailObj)
  return info.messageId
}

/**
 * This function sends a OTP to the given email address (For various verification)
 * @export
 * @param {*} token - OTP
 * @param {*} email - user email
 * @param {*} context - GraphQL context
 * @return {*} sent message id
 */
export async function sendOtpThroughSmtpRelay (token, email, context, smsAlerts = false, provider = EMAIL_PROVIDERS.SENDGRID) {
  const TenantCredential = context.databaseConnection.TenantCredential
  const sendgridConfig = await keyValueToJson(
    TenantCredential,
    [
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.SENDGRID],
      ...EMAIL_PROVIDERS_CREDENTIAL_KEYS[EMAIL_PROVIDERS.MAILGUN],
    ],
    'tenantId', context.tenant.id
  )
let transporter
if(provider == EMAIL_PROVIDERS.SENDGRID) {

   transporter = nodemailer.createTransport({
    service: 'SendGrid',
    host: sendgridConfig.APP_SENDGRID_HOST,
    port: sendgridConfig.APP_SENDGRID_PORT,
    auth: {
      user: sendgridConfig.APP_SENDGRID_USERNAME,
      pass: sendgridConfig.APP_SENDGRID_RELAY_KEY
    }
  })
} else if (provider == EMAIL_PROVIDERS.MAILGUN) {
  const mailgun = new Mailgun(FormData)
  const mg = mailgun.client({
    username: sendgridConfig.MAILGUN_USERNAME || 'api',
    key: sendgridConfig.MAILGUN_API_KEY,
    // url: 'https://api.mailgun.net' // Use US region by default, change to 'https://api.eu.mailgun.net' for EU
  })

  transporter = {
    sendMail: async (emailOptions) => {
      const result = await mg.messages.create(sendgridConfig.MAILGUN_DOMAIN, {
        from: emailOptions.from,
        to: [emailOptions.to],
        subject: emailOptions.subject,
        html: emailOptions.html
      })
      return { messageId: result.id }
    }
  }

}

if (!transporter) {
  return Promise.reject(new Error('No transporter configured for sending emails'))
}


const adminEmail = provider === EMAIL_PROVIDERS.SENDGRID ? sendgridConfig.APP_SENDGRID_EMAIL : sendgridConfig.MAILGUN_FROM_EMAIL
  let sendEmailObj
  if (smsAlerts === 'registrationSms')
    sendEmailObj = {
      from: adminEmail,
      to: email,
      subject: 'Registration Successful',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Registration Successful</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                margin: 0;
                padding: 20px;
              }
              .container {
                max-width: 600px;
                background-color: #fff;
                margin: 0 auto;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
              }
              .header {
                text-align: center;
                padding: 10px 0;
                background-color: #007BFF;
                color: white;
                border-radius: 8px 8px 0 0;
              }
              .content {
                margin: 20px 0;
                text-align: center;
              }
             .username {
                font-size: 18px;
                font-weight: bold;
                color: #007BFF;
              }
              .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #888;
              }
            </style>
          </head>
       <body>
          <div class="container">
            <div class="header">
                <h1>Registration Successful</h1>
            </div>
            <div class="content">
                <p>We are pleased to inform you that your registration has been successfully completed.</p>
                <p>Your username is: <span class="username">${token}</span></p>
                <p>Please use this username for logging into your account.</p>
                <p>If you have any questions or need assistance, feel free to contact our support team.</p>
            </div>
            <div class="footer">
                <p>Thank you for joining!</p>
            </div>
        </div>
        </body>
      </html>
    `
    }
  else if (!smsAlerts)
    sendEmailObj = {
      from: adminEmail,
      to: email,
      subject: 'OTP for verification',
      html: `
      <!DOCTYPE html>
      <html>
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>OTP Verification</title>
          <style>
              body {
                  font-family: Arial, sans-serif;
                  background-color: #f4f4f4;
                  margin: 0;
                  padding: 20px;
              }
              .container {
                  max-width: 600px;
                  background-color: #fff;
                  margin: 0 auto;
                  padding: 20px;
                  border-radius: 8px;
                  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
              }
              .header {
                  text-align: center;
                  padding: 10px 0;
                  background-color: #4CAF50;
                  color: white;
                  border-radius: 8px 8px 0 0;
              }
              .content {
                  margin: 20px 0;
                  text-align: center;
              }
              .otp {
                  font-size: 24px;
                  font-weight: bold;
                  color: #4CAF50;
                  letter-spacing: 5px;
              }
              .footer {
                  margin-top: 30px;
                  text-align: center;
                  font-size: 12px;
                  color: #888;
              }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>OTP Verification</h1>
              </div>
              <div class="content">
                  <p>Dear User,</p>
                  <p>Your OTP (One-Time Password) for verification is:</p>
                  <p class="otp">${token}</p>
              </div>
              <div class="footer">
                  <p>If you did not request this OTP, please ignore this email.</p>
                  <p>Thank you!</p>
              </div>
          </div>
      </body>
      </html>
    `
    }
  else sendEmailObj = {
    from: adminEmail,
    to: email,
    subject: 'Withdraw Request Notification',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Withdrawal Request</title>
      <style>
          body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
          }
          .container {
            max-width: 600px;
            background-color: #fff;
            margin: 0 auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          }
          .content {
            margin: 20px 0;
            text-align: center;
          }
          .message {
            font-size: 18px;
            color: #333;
          }
      </style>
      </head>
      <body>
      <div class="container">
        <div class="content">
          <p class="message">${token}</p>
        </div>
      </div>
      </body>
      </html>
    `}
  // send mail with defined transport object
  try {
    const info = await transporter.sendMail(sendEmailObj)
    return info.messageId
  } catch (error) {
    throw new Error
  }
}
