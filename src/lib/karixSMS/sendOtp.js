import { UserInputError } from 'apollo-server-express';
import translate from '../../lib/languageTranslate';
import keyValueTo<PERSON>son from '../keyValueToJSON';
const axios = require('axios')

/**
 * This function sends a fixed message to a given phone number.
 * @export
 * @param {*} phone - User's phone number
 * @param {*} phoneCode - phone code
 * @param {*} otp - Generated OTP
 * @param {*} context - GraphQL context
 * @return {*} twilio object
 */

export async function sendKarixSMSPhoneVerificationOTP (phone, phoneCode ,otp, context, smsAlerts = false) {
  try {
    const TenantCredential = context.databaseConnection.TenantCredential

    const karixSMSConfig = await keyValueToJson(TenantCredential, ['KARIX_SMS_API_URL', 'KARIX_SMS_API_ACCESS_KEY'], 'tenantId', context.tenant.id)

    const url = karixSMSConfig.KARIX_SMS_API_URL

    let message
    if (!smsAlerts)
    message = `Dear user, ${otp} is your verification code. Please verify your identity. Do not share it with anyone.`
    else
    message = otp
    // Prepare JSON payload
    const payload = {
      "ver": "1.0",
      "key": karixSMSConfig.KARIX_SMS_API_ACCESS_KEY,
      "encrpt": "0",
      "messages": [
        {
          "dest": [
            phoneCode ? phoneCode + phone : phone
          ],
          "send": "PlayPM",
          "text": message
        }
      ]
    }

    // Make the request with the access token
    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response) {
      throw new Error('Unsupported OTP')
    }

    return {
      success: true,
      data: response.data
    }
  } catch (error) {
    const msg = error?.response?.data?.comment || error.message
    throw new UserInputError(translate('UNSUPPORTED_OTP', msg))
  }
}
