

export default (length = 8) => {
  const upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lower = 'abcdefghijklmnopqrstuvwxyz';
  const digits = '0123456789';
  const special = '@$!%*?&#';
  const all = upper + lower + digits + special;

  // Ensure at least one character from each required group
  let password = [
    upper[Math.floor(Math.random() * upper.length)],
    lower[Math.floor(Math.random() * lower.length)],
    digits[Math.floor(Math.random() * digits.length)],
    special[Math.floor(Math.random() * special.length)]
  ];

  // Fill the rest of the password length with random characters
  while (password.length < length) {
    password.push(all[Math.floor(Math.random() * all.length)]);
  }

  // Shuffle the result to randomize character positions
  return password
    .sort(() => Math.random() - 0.5)
    .join('');
}
