import { ApolloError } from 'apollo-server-express'
import { BONUS_TYPES } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for fetching Joining bonus
 * @export
 * @class JoiningBonus
 * @extends {ServiceBase}
 */
export default class JoiningBonus extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Bonus: BonusModel
      },
      tenant,
      req: { headers: { language } }
    } = this.context
    try {
    const losingBonus = await BonusModel.findOne({
      where: { kind: BONUS_TYPES.JOINING, tenantId: tenant.id, enabled: true }
    })

    return losingBonus
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
