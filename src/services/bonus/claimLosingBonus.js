import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import beforeAfterBalance from '../../common/beforeAfterBalance'
import calculateLosingBonusInterval from '../../common/calculateLosingBonusInterval'
import { BONUS_STATUS, BONUS_TYPES, ERORR_TYPE, QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import currencyConversionV3 from '../../common/currencyConversionV3'
import ErrorLogHelper from '../../common/errorLog'
import { ggrBasedLosingBonus } from '../../common/ggrBasedLosingBonus'
import { addLosingBonusAuditLog } from '../../common/losingBonus/addLosingBonusAuditLog'
import ServiceBase from '../../common/serviceBase'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import { Sequelize } from '../../db/models'
import generateUniqueTransactionId from '../../lib/generateUniqueTransactionId'
import { isEligibleForLosingBonus } from '../../lib/isEligibleForLosingBonus'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for claiming losing bonus
 * @export
 * @class ClaimLosingBonus
 * @extends {ServiceBase}
 */
export default class ClaimLosingBonus extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          Bonus: BonusModel,
          LosingBonusSetting: LosingBonusSettingModel,
          Wallet: WalletModel,
          Transaction: TransactionModel,
          BetsTransaction: BetsTransactionModel,
          LosingBonusTier: LosingBonusTierModel,
          UserBonus: UserBonusModel,
          Currency: CurrencyModel,
          QueueLog: QueueLogModel,
          UserLosingBonusClaimHistory: UserLosingBonusClaimHistoryModel,
          BurningBonus: BurningBonusModel,
          sequelize
        },
        req: { headers: { language } },
        auth: { id: userId },
        tenant: Tenant
      },
      args: { bonusId, ipAddress }
    } = this
    let auditErrorLog = {
      eventType: 'player',
      action: 'cashback validation',
      actioneeId: userId,
      tenantId: Tenant.id,
      actioneeIp: ipAddress,
      description: translate('NOT_ELIGIBLE_FOR_BONUS', language),
      eventId: userId,
      previousData: { bonusId },
      modifiedData: {}
    }
    let auditLogObj;
    this.context.sequelizeTransaction = await sequelize.transaction()
    try {
    const losingBonusUnparsed = await BonusModel.findOne({
      where: {
        kind: {
          [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
        },
        id: bonusId,
        enabled: true
      },
      include: {
        model: LosingBonusSettingModel,
        include: {
          model: LosingBonusTierModel
        }
      }
    })
    if (!losingBonusUnparsed) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('BONUS_NOT_FOUND', language),
      })
      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_FOUND', language), errorCode: 400 }
    }
    auditErrorLog.previousData.kind = losingBonusUnparsed.kind
    auditErrorLog.previousData.promotionTitle = losingBonusUnparsed.promotionTitle
    const userLosingBonus = await UserBonusModel.findOne({
      where: {
        bonusId,
        userId,
        kind: {
          [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
        }
      },
      transaction: this.context.sequelizeTransaction,
      lock: {
        level: this.context.sequelizeTransaction.LOCK.UPDATE,
        of: UserBonusModel
      },
      skipLocked: false
    })

    if (!userLosingBonus) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('BONUS_NOT_ACTIVE', language),
      })
      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_ACTIVE', language), errorCode: 400 }
    }

    await userLosingBonus.reload({
      lock: { level: this.context.sequelizeTransaction.LOCK.UPDATE, of: UserBonusModel },
      transaction: this.context.sequelizeTransaction
    })
    const bonusKind = userLosingBonus.kind

    if (userLosingBonus.status === BONUS_STATUS.CLAIMED) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('BONUS_ALREADY_CLAIMED', language),
      })
      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_ALREADY_CLAIMED', language), errorCode: 400 }
    }

    const losingBonus = JSON.parse(JSON.stringify(losingBonusUnparsed))

    if (new Date().getTime() > new Date(losingBonus.validUpto).getTime()) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('BONUS_EXPIRED', language),
      })
      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_EXPIRED', language), errorCode: 400 }
    }

    // calculating valid dates
    let dateFrom, dateTo
    if (losingBonus.LosingBonusSetting.claimDays) {
      const daysToSubtract = losingBonus.LosingBonusSetting.claimDays
      const claimDate = new Date().setDate(new Date().getDate() - daysToSubtract)
      dateFrom = Math.max(claimDate, new Date(losingBonus.validFrom).getTime())
      dateTo = Math.min(new Date().getTime(), new Date(losingBonus.validUpto).getTime())
    } else {
      const activationDate = new Date(userLosingBonus.createdAt)
      console.log('==========activationDate', activationDate)
      const currentDate = new Date(Date.now())
      console.log('==========currentDate', currentDate)
      const bonusType = losingBonus.LosingBonusSetting.claimIntervalType
      const lastClaimDate = userLosingBonus.claimedAt
      console.log('==========lastClaimDate', lastClaimDate)
      const validFrom = new Date(losingBonus.validFrom)
      console.log('==========validFrom', validFrom)
      const bonusInterval = calculateLosingBonusInterval(activationDate, currentDate, bonusType, lastClaimDate, validFrom)
      console.log('==========bonusInterval', bonusInterval)
      if (!bonusInterval) {
        auditLogObj = await addLosingBonusAuditLog({
          ...auditErrorLog,
          description: translate('BONUS_ALREADY_CLAIMED_IN_INTERVAL', language),
        })
        await QueueLogModel.create({
          type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [auditLogObj.id],
        })
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_ALREADY_CLAIMED_IN_INTERVAL', language), errorCode: 400 }
      }
      dateFrom = bonusInterval?.start ? new Date(bonusInterval.start).toISOString() : null
      dateTo = bonusInterval?.end ? new Date(bonusInterval.end).toISOString() : null
    }

    const dateObj = {
      createdAt: { [Op.gte]: new Date(dateFrom).toISOString(), [Op.lte]: (new Date(dateTo).toISOString()) }
    }

    const dates = { startDate: new Date(dateFrom).toISOString(), endDate: new Date(dateTo).toISOString() }
    // getting wallet details
    const userWallet = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' }, include: CurrencyModel })
    const tenantId = Tenant.id
    // ngr = only cash win and cash debit
    // ggr = both cash and non cash win and debit
    let betAmountSumLoss

    // find casino providers and gameIds
    let casinoProviderIds = []
    let gameIds = []
      if (losingBonus?.LosingBonusSetting?.providerDetails) {
        await Promise.all(
          losingBonus.LosingBonusSetting.providerDetails.map(async (detail) => {
            if (detail.game_id?.length > 0) {
              gameIds.push(...detail.game_id)
            } else {
              const games = await sequelize.query(
                `SELECT transactions_providers_list.seat_ids AS "seatIds"
                   FROM transactions_providers_list
                   WHERE title = (
                    SELECT title
                    FROM pages
                    JOIN aggregator  ON pages.id = aggregator.page_id AND aggregator.status = true
                    WHERE tenant_id = :tenantId
                      AND top_menu_id = :topMenuId
                      AND id = :id
                  )`,
                {
                  replacements: {
                    tenantId: Tenant.id,
                    topMenuId: detail.top_menu_id,
                    id: detail.id,
                  },
                  type: Sequelize.QueryTypes.SELECT,
                }
              )

              if (games.length > 0) {
                gameIds.push(...games[0]?.seatIds)
              } else {
                const provider = await sequelize.query(
                  `SELECT casino_providers.id
                    FROM casino_providers
                    INNER JOIN pages ON pages.title = casino_providers.name
                    WHERE tenant_id = :tenantId
                      AND pages.top_menu_id = :topMenuId
                      AND pages.id = :id`,
                  {
                    replacements: {
                      tenantId: Tenant.id,
                      topMenuId: detail.top_menu_id,
                      id: detail.id,
                    },
                    type: Sequelize.QueryTypes.SELECT,
                  }
                )
                if (provider.length > 0) {
                  casinoProviderIds.push(provider[0]?.id)
                }
              }
            }
          })
        )
      }

    if (losingBonus.LosingBonusSetting.claimDays) {
      if (!losingBonus.LosingBonusSetting.bonusCalculationType || losingBonus.LosingBonusSetting.bonusCalculationType === 'ngr') {
        [betAmountSumLoss, auditErrorLog] = await isEligibleForLosingBonus({
          TransactionModel,
          BetsTransactionModel,
          userWallet,
          userId,
          dateObj,
          auditErrorLog
        }, bonusKind, tenantId, dates, gameIds, casinoProviderIds)
      } else {
        [betAmountSumLoss, auditErrorLog] = await ggrBasedLosingBonus({
          TransactionModel,
          userWallet,
          dateObj,
          tenantId,
          auditErrorLog
        }, bonusKind, dates, gameIds, casinoProviderIds )
      }
    } else {
      if (losingBonus.LosingBonusSetting.bonusCalculationType === 'ngr') {
        [betAmountSumLoss, auditErrorLog] = await isEligibleForLosingBonus({
          TransactionModel,
          BetsTransactionModel,
          userWallet,
          userId,
          dateObj,
          auditErrorLog
        }, bonusKind, tenantId, dates, gameIds, casinoProviderIds)
      } else {
        [betAmountSumLoss, auditErrorLog] = await ggrBasedLosingBonus({
          TransactionModel,
          userWallet,
          dateObj,
          tenantId,
          auditErrorLog
        }, bonusKind, dates, gameIds, casinoProviderIds)
      }
    }

    // Step 1: Sort tiers by minLosingAmount and store in a variable
    const sortedTiers = [...losingBonus.LosingBonusSetting.LosingBonusTiers].sort(
      (a, b) => a.minLosingAmount - b.minLosingAmount
    );

    // Step 2: Initialize variables for the selected range
    let selectedMinLosingAmount = null;
    let selectedMaxLosingAmount = null;

    // Step 3: Iterate over the sorted tiers to find the correct range
    for (const tier of sortedTiers) {
      if (betAmountSumLoss >= tier.minLosingAmount && betAmountSumLoss <= tier.maxLosingAmount) {
        selectedMinLosingAmount = tier.minLosingAmount;
        selectedMaxLosingAmount = tier.maxLosingAmount;
        break;
      }
    }

    // Step 4: If no match, set the lowest range
    if (selectedMinLosingAmount === null) {
      const lowestTier = sortedTiers[0]; // Since sorted, the first tier has the lowest minLosingAmount
      selectedMinLosingAmount = lowestTier.minLosingAmount;
      selectedMaxLosingAmount = lowestTier.maxLosingAmount;
    }
    auditErrorLog.previousData.minLosingAmount = selectedMinLosingAmount
    auditErrorLog.previousData.maxLosingAmount = selectedMaxLosingAmount
    auditErrorLog.previousData.remainingLosingAmount = selectedMinLosingAmount - betAmountSumLoss

    if (!betAmountSumLoss) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('NOT_ELIGIBLE_FOR_BONUS', language),
      })

      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('NOT_ELIGIBLE_FOR_BONUS', language), errorCode: 400 }
    }


    // fetching the appropriate losing bonus tier
    let amountDiff = Infinity
    let bonusObj = null
    losingBonus.LosingBonusSetting.LosingBonusTiers.forEach(ele => {
      if (betAmountSumLoss >= ele.minLosingAmount && amountDiff > Math.abs(ele.minLosingAmount - betAmountSumLoss)) {
        amountDiff = Math.abs(ele.minLosingAmount - betAmountSumLoss)
        bonusObj = ele
      }
    })

    if (!bonusObj) {
      auditLogObj = await addLosingBonusAuditLog({
        ...auditErrorLog,
        description: translate('NOT_ELIGIBLE_FOR_BONUS', language),
      })
      await QueueLogModel.create({
        type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [auditLogObj.id],
      })
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('NOT_ELIGIBLE_FOR_BONUS', language), errorCode: 400 }
    }
    // calculating losing bonus to credit
    let bonusToCredit
    if (bonusObj?.maxLosingAmount && bonusObj?.maxLosingAmount < betAmountSumLoss) { bonusToCredit = bonusObj?.maxLosingAmount * (bonusObj?.percentage / 100) } else { bonusToCredit = betAmountSumLoss * (bonusObj?.percentage / 100) }
    if (bonusObj?.maxBonus && bonusToCredit > bonusObj.maxBonus) {
      bonusToCredit = bonusObj.maxBonus
    }
    auditErrorLog.previousData.minLosingAmount = bonusObj.minLosingAmount
    auditErrorLog.previousData.maxLosingAmount = bonusObj.maxLosingAmount
    const transactionId = await generateUniqueTransactionId(TransactionModel)

    let transactionObj = {
      targetWalletId: userWallet.id,
      targetCurrencyId: userWallet.currencyId,
      amount: bonusToCredit,
      comments: '',
      actioneeId: userId,
      transactionId,
      paymentMethod: 'manual',
      actioneeType: 'User',
      tenantId: Tenant.id,
      timestamp: new Date().getTime(),
      transactionType: TRANSACTION_TYPES.NON_CASH_BONUS_CLAIM,
      status: 'success'
    }

    userLosingBonus.bonusAmount = bonusToCredit
    userLosingBonus.userId = userId
    userLosingBonus.bonusId = bonusId
    // userLosingBonus.kind = BONUS_TYPES.LOSING
    if (losingBonus.LosingBonusSetting.claimDays) {
      userLosingBonus.status = BONUS_STATUS.CLAIMED
    }
    const claimedAt = Date.now()
    userLosingBonus.claimedAt = claimedAt

    const userLosingBonusClaimHistory = {}
    if (!losingBonus.LosingBonusSetting.claimDays) {
      userLosingBonusClaimHistory.bonusAmount = bonusToCredit
      userLosingBonusClaimHistory.userId = userId
      userLosingBonusClaimHistory.bonusId = bonusId
      userLosingBonusClaimHistory.claimedAt = claimedAt
    }

    try {
      const userWallet = await WalletModel.findOne({
        where: { ownerId: userId, ownerType: TABLES.USER },
        transaction: this.context.sequelizeTransaction,
        lock: {
          level: this.context.sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })
      await userWallet.reload({
        lock: { level: this.context.sequelizeTransaction.LOCK.UPDATE, of: WalletModel },
        transaction: this.context.sequelizeTransaction
      })

      userWallet.nonCashAmount += bonusToCredit
      const skipWalletHook = true
      await userWallet.save({ transaction: this.context.sequelizeTransaction, skipWalletHook })

      transactionObj.conversionRate = await userCurrencyExchange(this.context, userWallet.currencyId)
      transactionObj = await currencyConversionV3(this.context, transactionObj, userWallet, bonusToCredit)
      transactionObj = await beforeAfterBalance(transactionObj, userWallet.nonCashAmount, bonusToCredit)

      const skipTransactionHook = true
      const newTransaction = await TransactionModel.create(transactionObj, { transaction: this.context.sequelizeTransaction, skipTransactionHook })
      userLosingBonus.transactionId = newTransaction.id
      await userLosingBonus.save({ transaction: this.context.sequelizeTransaction })
      if (!losingBonus.LosingBonusSetting.claimDays) {
        userLosingBonusClaimHistory.transactionId = newTransaction.id
        var { id: claimIntervalBonusId } = await UserLosingBonusClaimHistoryModel.create(userLosingBonusClaimHistory, { transaction: this.context.sequelizeTransaction })
      }
      const txnIds = []
      if (newTransaction) {
        txnIds.push(newTransaction.id)
      }
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: this.context.sequelizeTransaction })

      if (losingBonus?.LosingBonusSetting?.burningDays && losingBonus?.LosingBonusSetting?.burnType === 0) {
        const burningBonusHistory = {
          userId: userId,
          bonusId: bonusId,
          userBonusId: userLosingBonus?.id,
          bonusType: 'losing_manual',
          bonusAmount: bonusToCredit,
          ...(!losingBonus?.LosingBonusSetting?.claimDays && { claimIntervalBonusId }) // Add claimIntervalBonusId only if claimDays is null
        }
        await BurningBonusModel.create(burningBonusHistory, { transaction: this.context.sequelizeTransaction })
      }
      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: queueLog?.id } }
        )
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
      }
    } catch (error) {
      throw error
    }
    await this.context.sequelizeTransaction.commit()
    return { bonusCredited: bonusToCredit }
  } catch (error) {
    await this.context.sequelizeTransaction.rollback()
    if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
      throw new ApolloError(error.errorMsg, error.errorCode)
    } else {

    auditLogObj = await addLosingBonusAuditLog({
      ...auditErrorLog,
      description: translate('INTERNAL_SERVER_ERROR', language),
    })
    await QueueLogModel.create({
      type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [auditLogObj.id],
    })
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
  }
}
