import { ApolloError } from 'apollo-server-express';
import { Op, Sequelize } from 'sequelize';
import { ALLOWED_PERMISSIONS, BONUS_STATUS, BONUS_TYPES, MULTI_BONUS_STATUS } from '../../common/constants';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import getUserFromToken from '../../lib/getUserFromToken';
import translate from '../../lib/languageTranslate';

/**
 * Provides service for fetching deposit bonus details
 * @export
 * @class DepositBonus
 * @extends {ServiceBase}
 */
export default class DepositBonus extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Bonus: BonusModel,
        BonusCategory: BonusCategoryModel,
        UserBonus: UserBonusModel,
        UserPromoCodeBonus: UserPromoCodeBonusModel,
        UserBonusQueue: UserBonusQueueModel,
        TenantThemeSetting: TenantThemeSettingModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context

    try {
    const { decodedToken, user: currentUser } = await getUserFromToken(this.context, ['id', 'parentId', 'vipLevel', 'affiliatedData', 'categoryType']);


    // This is the case where the user visits the promotion page without logging in
    if (!decodedToken) {
      const result = await BonusModel.findAll({
        where: {
          kind: {
            [Op.or]: [
              BONUS_TYPES.DEPOSIT,BONUS_TYPES.DEPOSIT_SPORTS,BONUS_TYPES.DEPOSIT_INSTANT,BONUS_TYPES.DEPOSIT_BOTH,
            ],
          },
          currencyId: {
            [Op.eq]: Sequelize.literal(`(
              SELECT c.id
              FROM "tenant_credentials" tc
              INNER JOIN "currencies" c ON c.code = tc.value
              WHERE tc."key" = 'TENANT_BASE_CURRENCY'
              AND tc."tenant_id" = ${Tenant.id}
            )`),
          },
          enabled: true,
          tenantId: Tenant.id,
          validUpto: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        },
        order: [['id', 'ASC']],
        raw: true
      });

        // Add empty recurring rollover
        const response = result.map(bonus => ({
          ...bonus,
          recurringRolloverHistory: []
        }))

        return response;
      }

     // Condition for validUpto based on current timestamp
     const validUptoCondition = {
      [Op.or]: [
        {
          validUpto: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') }
        },
        {
          validUpto: { [Op.lt]: Sequelize.literal('CURRENT_TIMESTAMP') },
          '$userBonus.status$': BONUS_STATUS.ACTIVE
        }
      ]
    }

    // Fetch promo code and tenant settings in parallel
    const [userPromoCode, tenantThemeSetting] = await Promise.all([
      UserPromoCodeBonusModel.findOne({
        where: { userId: currentUser.id },
        raw: true,
        attributes: ['id', 'promoCodeId']
      }),
      TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: Tenant.id },
        raw: true
      })
    ]);

    const promoCodeId = userPromoCode?.promoCodeId || null;

    // Check if the tenant has the playerCategory module enabled
    const hasPlayerCategory = tenantThemeSetting?.allowedModules
      ? tenantThemeSetting?.allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY)
      : false;

    // Base conditions for bonuses
    const baseConditions = {
      kind: { [Op.or]: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_BOTH] },
      currencyId: currentUser?.Wallet?.currencyId,
      enabled: true,
      tenantId: Tenant.id,
      vipLevels: { [Op.contains]: [`${currentUser.vipLevel}`] },
    }

    // Conditionally add category check
    const categoryCondition = hasPlayerCategory
    ? Sequelize.literal(`("bonusCategory"."category" = ${currentUser.categoryType} OR "bonusCategory"."category" IS NULL)`)
    : null;

    // Condition for promo codes (if promoCodeId is provided)
    let promoCodeCondition = {}; let referTypeCondition = {}

    if (promoCodeId) {
      promoCodeCondition = {
        [Op.and]: Sequelize.literal(`
          EXISTS (
            SELECT 1
            FROM jsonb_array_elements("Bonus"."promo_codes") AS "promoCodes"
            WHERE "promoCodes"::text = '${promoCodeId}'
          )
        `)
      };
    }

    if (currentUser.affiliatedData) {
      referTypeCondition = {
        referType: 'affiliate_code',
        referValue: {
          [Op.or]: [
            { [Op.eq]: currentUser.affiliatedData },
            { [Op.is]: null }
          ]
        }
      };
    } else {
      referTypeCondition = {
        referType: 'referral_link',
        [Op.and]: Sequelize.literal(`
        EXISTS (
          SELECT 1
          FROM admin_users
          WHERE affiliate_token = regexp_replace("Bonus"."refer_value", '^.*/', '')
          AND admin_users.id = ${currentUser.parentId}
        )
      `)
      }
    }

    const nullFallbackCondition = {
      promo_codes: { [Op.is]: null },
      referType: { [Op.is]: null }
    };

    const orConditions = [];

    // Push promoCodeCondition if promoCodeId is present
    if (promoCodeId) {
      orConditions.push({ ...promoCodeCondition });
    }

    // Push referTypeCondition always (based on whether affiliatedData exists or fallback)
    orConditions.push({ ...referTypeCondition });

    // Always push the fallback null condition
    orConditions.push(nullFallbackCondition);

    // Final combined condition
    const combinedCondition = {
      [Op.or]: orConditions
    };

    // Merge all conditions into bonusConditions
    const bonusConditions = {
      ...baseConditions,
      [Op.and]: [
        validUptoCondition,
        ...(categoryCondition ? [categoryCondition] : []),
        combinedCondition
      ]
    }

    const result = await BonusModel.findAll({
      attributes: {
        include: [
          [
            Sequelize.literal(`
              EXISTS (
                SELECT 1
                FROM user_bonus_queue AS ubq
                WHERE ubq.bonus_id = "Bonus"."id"
                  AND ubq.user_id = ${currentUser.id}
                  AND ubq.status = ${MULTI_BONUS_STATUS.PENDING}
              )
            `),
            'isInBonusQueue'
            ],
            [
              Sequelize.literal(`
              CASE 
                WHEN EXISTS (
                  SELECT 1
                  FROM deposit_bonus_settings AS dbs
                  WHERE dbs.bonus_id = "Bonus"."id"
                    AND dbs.deposit_bonus_type = 'recurring'
                ) THEN (
                  SELECT COALESCE(
                    json_agg(
                      json_build_object(
                        'id', ubrr.id,
                        'userBonusId', ubrr.user_bonus_id,
                        'bonusId', ubrr.bonus_id,
                        'bonusAmount', ubrr.bonus_amount,
                        'rolloverTarget', ubrr.rollover_target,
                        'remainingRollover', ubrr.remaining_rollover,
                        'status', ubrr.status,
                        'transactionId', ubrr.transaction_id,
                        'createdAt', ubrr.created_at,
                        'claimedAt', ubrr.claimed_at
                      ) ORDER BY ubrr.created_at ASC
                    ),
                    '[]'::json
                  )
                  FROM user_bonus_recurring_rollover ubrr
                  INNER JOIN user_bonus ub ON ub.id = ubrr.user_bonus_id
                  WHERE ub.user_id = ${currentUser.id} 
                    AND ub.bonus_id = "Bonus"."id"
                )
                ELSE '[]'::json
              END
            `),
              'recurringRolloverHistory'
          ]
        ]
      },
      where: bonusConditions,
      order: [['id', 'ASC']],
      include: [
        {
          required: false,
          model: UserBonusModel,
          as: 'userBonus',
          where: { userId: currentUser.id }
        },
        {
          model: UserBonusQueueModel,
          as: 'userBonusQueue',
          required: false,
          attributes: ['bonusAmount', 'rolloverTarget', 'remainingRollover', 'ordering'],
          where: {
            user_id: currentUser.id,
            status: MULTI_BONUS_STATUS.PENDING
          },
        },
        // Conditionally add BonusCategoryModel if hasPlayerCategory is true
        ...(hasPlayerCategory
          ? [
            {
              model: BonusCategoryModel,
              attributes: [
                'category',
                [Sequelize.literal(`
                  CASE
                    WHEN "bonusCategory"."category" = 1 THEN 'A'
                    WHEN "bonusCategory"."category" = 2 THEN 'B'
                    WHEN "bonusCategory"."category" = 3 THEN 'C'
                    WHEN "bonusCategory"."category" = 4 THEN 'D'
                    ELSE 'Unknown'
                  END
                `), 'categoryLabel']
              ],
              as: 'bonusCategory',
              required: false, // Ensures bonuses without a category are also included
            }
          ]
          : [])
      ]
    })

      const response = result.map(bonus => {
        const jsonBonus = bonus.toJSON()
        if (typeof jsonBonus.recurringRolloverHistory === 'string') {
          try {
            jsonBonus.recurringRolloverHistory = JSON.parse(jsonBonus.recurringRolloverHistory)
          } catch (e) {
            jsonBonus.recurringRolloverHistory = []
          }
        }
        return jsonBonus
      })

    return response
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
