import { ApolloError } from 'apollo-server-express';
import { Op, Sequelize } from 'sequelize';
import { ALLOWED_PERMISSIONS, BONUS_TYPES, MULTI_BONUS_STATUS } from '../../common/constants';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import getUserFromToken from '../../lib/getUserFromToken';
import translate from '../../lib/languageTranslate';

/**
 * Provides service for fetching losing bonus details
 * @export
 * @class LosingBonus
 * @extends {ServiceBase}
 */
export default class LosingBonus extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Bonus: BonusModel,
        BonusCategory: BonusCategoryModel,
        UserPromoCodeBonus: UserPromoCodeBonusModel,
        UserBonusQueue: UserBonusQueueModel,
        TenantThemeSetting: TenantThemeSettingModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context
    try {
    const { decodedToken, user: currentUser } = await getUserFromToken(this.context, ['id', 'vipLevel', 'categoryType']);

    // Base conditions for bonuses
    const baseConditions = {
      kind: { [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH] },
      enabled: true,
      tenantId: Tenant.id,
      validUpto: { [Op.gte]: Sequelize.literal('CURRENT_DATE') }
    };

    // This is the case where the user visits the promotion page without logging in
    if (!decodedToken) {
      const result = await BonusModel.findAll({
        where: {
          ...baseConditions,
          currencyId: {
            [Op.eq]: Sequelize.literal(`(
              SELECT c.id
              FROM "tenant_credentials" tc
              INNER JOIN "currencies" c ON c.code = tc.value
              WHERE tc."key" = 'TENANT_BASE_CURRENCY'
              AND tc."tenant_id" = ${Tenant.id}
            )`),
          },
        },
        order: [['id', 'ASC']],
        raw: true
      });

      return result;
    }

    // Fetch promo code and tenant settings in parallel
    const [userPromoCode, tenantThemeSetting] = await Promise.all([
      UserPromoCodeBonusModel.findOne({
        where: { userId: currentUser.id },
        raw: true,
        attributes: ['promoCodeId']
      }),
      TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: Tenant.id },
        raw: true
      })
    ]);

    const promoCodeId = userPromoCode?.promoCodeId || null;

    // Check if the tenant has the playerCategory module enabled
    const hasPlayerCategory = tenantThemeSetting?.allowedModules
      ?.split(',')
      .map(module => module.trim())
      .includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY);


    // Apply logged-in user conditions
    Object.assign(baseConditions, {
      currencyId: currentUser.Wallet.currencyId,
      vipLevels: { [Op.contains]: [`${currentUser.vipLevel}`] },
      // Conditionally apply category check using Sequelize.literal
      ...(hasPlayerCategory && {
        [Op.and]: [
          Sequelize.literal(`("bonusCategory"."category" = ${currentUser.categoryType} OR "bonusCategory"."category" IS NULL)`)
        ]
      })
    });

    // Condition for promo codes (if promoCodeId is provided)
    let promoCodeCondition = {};
    if (promoCodeId) {
      promoCodeCondition = {
        [Op.or]: [
          { promo_codes: { [Op.is]: null } },
          Sequelize.literal(`EXISTS (SELECT 1 FROM jsonb_array_elements("Bonus"."promo_codes") AS "promoCodes" WHERE "promoCodes"::text = '${promoCodeId}')`)
        ]
      };
    } else {
      promoCodeCondition = { promo_codes: { [Op.is]: null } };
    }

    // Merge all conditions into bonusConditions
    const bonusConditions = {
      ...baseConditions,
      ...promoCodeCondition,
    };

    const result = await BonusModel.findAll({
      attributes: {
        include: [
          [
            Sequelize.literal(`
              EXISTS (
                SELECT 1
                FROM user_bonus_queue AS ubq
                WHERE ubq.bonus_id = "Bonus"."id"
                  AND ubq.user_id = ${currentUser.id}
                  AND ubq.status = ${MULTI_BONUS_STATUS.PENDING}
              )
            `),
            'isInBonusQueue'
          ]
        ]
      },
      where: bonusConditions,
      include: [
        {
          model: UserBonusQueueModel,
          as: 'userBonusQueue',
          required: false,
          attributes: ['bonusAmount', 'rolloverTarget', 'remainingRollover', 'ordering'],
          where: {
            user_id: currentUser.id,
            status: MULTI_BONUS_STATUS.PENDING
          }
        },
        // Conditionally add BonusCategoryModel if hasPlayerCategory is true
        ...(hasPlayerCategory
          ? [
            {
              model: BonusCategoryModel,
              attributes: [
                'category',
                [Sequelize.literal(`
                  CASE
                    WHEN "bonusCategory"."category" = 1 THEN 'A'
                    WHEN "bonusCategory"."category" = 2 THEN 'B'
                    WHEN "bonusCategory"."category" = 3 THEN 'C'
                    WHEN "bonusCategory"."category" = 4 THEN 'D'
                    ELSE 'Unknown'
                  END
                `), 'categoryLabel']
              ],
              as: 'bonusCategory',
              required: false, // Ensures bonuses without a category are also included
            }
          ]
          : [])
      ],
    })

    const response = result.map(bonus => bonus.toJSON());
    return response
   } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
   }
  }
}
