import { ApolloError } from 'apollo-server-express'
import { ALLOWED_PARALLEL_BONUS, ALLOWED_PERMISSIONS, AUDIT_LOG_ACTIONEE_TYPE, BONUS_RECURRING_STATUS, BONUS_STATUS, BONUS_TYPES, DEPOSIT_BONUS_TYPE, ERORR_TYPE, EVENT, EVENT_TYPE, MULTI_BONUS_STATUS } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'
const { Op } = require('sequelize')

const constraints = {
  bonusId: {
    type: 'integer'
  }
}

/**
 * Provides service for adding deposit bonus
 * @export
 * @class AddDepositBonus
 * @extends {ServiceBase}
 */
export default class AddDepositBonus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          Bonus: BonusModel,
          Wallet: WalletModel,
          UserBonus: UserBonusModel,
          TenantThemeSetting: TenantThemeSettingModel,
          UserBonusQueue: UserBonusQueueModel,
          UserBonusRecurringRollover: UserBonusRecurringRolloverModel,
          AuditLog: AuditLogModel,
          sequelize
        },
        req: { headers: { language } },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { bonusId, bonusType }
    } = this

    try {
    await checkImpersonatedAccess(this.context)

    const bonusAdded = await UserBonusModel.findOne({
      where: {
        userId,
        bonusId: this.args.bonusId
      },
      raw: true
    })

    if (bonusAdded) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_ALREADY_ADDED', language), errorCode: 400 }
    }

    const userWallet = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' } })

    const depositBonus = await BonusModel.findOne({
      attributes: ['kind', 'validUpto', 'tenantId'],
      where: {
        kind: bonusType,
        id: bonusId,
        currencyId: userWallet.currencyId,
        enabled: true
      }
    })

    if (!depositBonus) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_FOUND', language), errorCode: 400 }
    }

    if (new Date().getTime() > new Date(depositBonus.validUpto).getTime()) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_EXPIRED', language), errorCode: 400 }
    }

    const userBonusObject = {
      status: BONUS_STATUS.ACTIVE,
      userId,
      bonusId: this.args.bonusId,
      kind: depositBonus.kind,
      expiresAt: depositBonus.validUpto
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      attributes: ['allowedModules'],
      where: {
        tenantId: depositBonus.tenantId
      },
      raw: true
    })

    // Parse allowed modules from the tenantTheme and check for specific permissions
    const allowedModules = tenantTheme?.allowedModules?.split(',').map((module) => module.trim()) || [];
    const hasParallelBonus = allowedModules.includes(ALLOWED_PARALLEL_BONUS);
    const allowsMultiBonus = allowedModules.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE);

    let bonusInQueue = false, bonusActivated = false;

    // Define conditions to find active bonuses for the user
    const bonusConditions = {
      userId,
      status: BONUS_STATUS.ACTIVE,
      ...(hasParallelBonus && { // Include specific bonus types if parallel bonuses are allowed
        kind: {
          [Op.or]: [
            BONUS_TYPES.DEPOSIT,
            BONUS_TYPES.DEPOSIT_SPORTS,
            BONUS_TYPES.DEPOSIT_INSTANT,
            BONUS_TYPES.DEPOSIT_BOTH,
          ],
        },
      }),
    };

    // Fetch user's active bonus if there's no pending bonus queue
    let userHasActiveBonus = await UserBonusModel.findOne({ where: bonusConditions });

    this.context.sequelizeTransaction = await sequelize.transaction();
    try {
      if (allowsMultiBonus) {
        if (userHasActiveBonus) {
          // Handle case when the user has an active bonus, Add new bonus to queue
          const bonusExistInQueue = await UserBonusQueueModel.findOne({
            attributes: ['userId'],
            where: { userId, bonusId: this.args?.bonusId, status: MULTI_BONUS_STATUS.PENDING },
          });

          // If user already has an active bonus and no existing queue, add the bonus to the queue
          if (!bonusExistInQueue) {
            const newQueueBonus = {
              userId,
              bonusId: this.args.bonusId,
              status: MULTI_BONUS_STATUS.PENDING,
            };

            await UserBonusQueueModel.create(newQueueBonus, { transaction: this.context.sequelizeTransaction });
            bonusInQueue = true;
          }
        }
      } else {
        // If multi-bonus is not allowed, cancel existing active bonuses
        await UserBonusModel.update(
          { status: BONUS_STATUS.CANCELLED }, // Set the status of existing active bonuses to CANCELLED
          { where: bonusConditions, transaction: this.context.sequelizeTransaction }
        );
          if (userHasActiveBonus) {
          const userBonusId = userHasActiveBonus.id;

          const bonusWithSettings = await BonusModel.findOne({
            where: { id: userHasActiveBonus.bonusId },
            include: {
              model: this.context.databaseConnection.DepositBonusSetting,
              attributes: ['depositBonusType']
            }
          });

          // Check if this is a recurring type bonus
          const isRecurringBonus = bonusWithSettings?.DepositBonusSetting?.depositBonusType === DEPOSIT_BONUS_TYPE.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES. DEPOSIT_BOTH].includes(depositBonus.kind);
          if (isRecurringBonus) {
            // Find child tier bonuses related to the cancelled userBonus record
            const childTierBonuses = await UserBonusRecurringRolloverModel.findAll({
              where: {
                userBonusId: userBonusId,
                status: BONUS_RECURRING_STATUS.ACTIVE
              }
            });

            if (childTierBonuses.length > 0) {
              const childTierBonusIds = childTierBonuses.map(tierBonus => tierBonus.id);

              // Update child tier bonuses to cancelled status
              await UserBonusRecurringRolloverModel.update(
                { status: BONUS_RECURRING_STATUS.CANCELLED },
                {
                  where: { id: { [Op.in]: childTierBonusIds } },
                  transaction: this.context.sequelizeTransaction
                }
              );

            }
          }

          const auditData = {
            tenantId: depositBonus.tenantId,
            actioneeId: userId,
            eventType: EVENT_TYPE.bonusCancelled,
            event: EVENT.update,
            eventId: userHasActiveBonus?.bonusId,
            actioneeIp: this.args?.ip || '',
            description: 'Bonus cancelled',
            action: 'Bonus cancelled',
            previousData: { userId, bonusId: userHasActiveBonus?.bonusId, status: BONUS_STATUS.ACTIVE },
            modifiedData: { userId, bonusId: userHasActiveBonus?.bonusId, status: BONUS_STATUS.CANCELLED },
            actioneeType: AUDIT_LOG_ACTIONEE_TYPE.USER
          }

          await AuditLogModel.create(auditData, { transaction: this.context.sequelizeTransaction });
        }
      }

      // Add the new bonus if multi-bonus is not allowed or the user does not have an active bonus
      if (!allowsMultiBonus || !userHasActiveBonus) {
        await UserBonusModel.create(userBonusObject, { transaction: this.context.sequelizeTransaction });
        bonusActivated = true
      }

      // Commit the transaction to finalize all changes
      await this.context.sequelizeTransaction.commit();
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      throw error
    }

    return { bonusAdded: bonusActivated, bonusInQueue }

  } catch (error) {
    if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
      throw new ApolloError(error.errorMsg, error.errorCode)
    } else {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
  }
}
