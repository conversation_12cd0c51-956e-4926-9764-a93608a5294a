import { ApolloError } from 'apollo-server-express'
import { AUDIT_LOG_ACTIONEE_TYPE, BONUS_STATUS, BONUS_TYPES, ERORR_TYPE, EVENT, EVENT_TYPE, QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'


const constraints = {
  bonusId: {
    type: 'integer'
  }
}

/**
 * Provides service for cancelling deposit bonus
 * @export
 * @class CancelDepositBonus
 * @extends {ServiceBase}
 */
export default class CancelDepositBonus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      databaseConnection: {
        Bonus: BonusModel,
        Wallet: WalletModel,
        UserBonus: UserBonusModel,
        Transaction: TransactionModel,
        QueueLog: QueueLogModel,
        AuditLog: AuditLogModel,
        sequelize
      },
      tenant: { id: tenantId },
      req: { headers: { language } },
      auth: { id: userId }
    } = this.context

    try {
    await checkImpersonatedAccess(this.context)

    const userWallet = await WalletModel.findOne({
      attributes: ['currencyId'],
      where: { ownerId: userId, ownerType: 'User' }
    })

    const depositBonus = await BonusModel.findOne({
      attributes: ['id'],
      where: { id: this.args.bonusId, currencyId: userWallet.currencyId, enabled: true }
    })

    if (!depositBonus) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_FOUND', language), errorCode: 400 }
    }

    const userDepositBonus = await UserBonusModel.findOne({
      attributes: ['id', 'status', 'transactionId', 'kind'],
      where: { userId, status: BONUS_STATUS.ACTIVE, bonusId: this.args.bonusId },
      useMaster: true
    })

    if (!userDepositBonus) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_FOUND', language), errorCode: 400 }
    }

    this.context.sequelizeTransaction = await sequelize.transaction()
    try {
      userDepositBonus.status = BONUS_STATUS.CANCELLED
      await userDepositBonus.save({ transaction: this.context.sequelizeTransaction })
      if (userDepositBonus.transactionId && userDepositBonus.kind !== BONUS_TYPES.DEPOSIT_INSTANT) {
        const transaction = await TransactionModel.findOne({
          attributes: ['id', 'comments', 'status'],
          where: {
            id: userDepositBonus.transactionId
          },
          useMaster: true
        })
        if (transaction) {
          transaction.comments = 'Transaction cancelled for deposit bonus'
          transaction.status = 'cancelled'
          const skipTransactionHook = true
          await transaction.save({ transaction: this.context.sequelizeTransaction, skipTransactionHook })
          const txnIds = []
          txnIds.push(transaction.id)
          const queueLogObject = {
            type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: txnIds
          }
          const queueLog = await QueueLogModel.create(queueLogObject, { transaction: this.context.sequelizeTransaction })
          try {
            this.context.pubSub.publish(
              SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
              { QueueLog: { queueLogId: queueLog?.id } }
            )
          } catch (e) {
            await ErrorLogHelper.logError(e, this.context, { id: userId, tenantId })
          }
        }
      }

      const auditData = {
        tenantId,
        actioneeId: userId,
        eventType: EVENT_TYPE.bonusCancelled,
        event: EVENT.update,
        eventId: userDepositBonus?.id,
        actioneeIp: this.args?.ip || '',
        description: 'Bonus cancelled',
        action: 'Bonus cancelled',
        previousData: { userId, bonusId: userDepositBonus?.bonusId, status: BONUS_STATUS.ACTIVE },
        modifiedData: { userId, bonusId: userDepositBonus?.bonusId, status: BONUS_STATUS.CANCELLED },
        actioneeType: AUDIT_LOG_ACTIONEE_TYPE.USER
      }

      await AuditLogModel.create(auditData, { transaction: this.context.sequelizeTransaction });

      await this.context.sequelizeTransaction.commit()
      return { bonusCanceled: true }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      throw error
    }
  } catch (error) {
    if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
      throw new ApolloError(error.errorMsg, error.errorCode)
    } else {
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  } }
}
