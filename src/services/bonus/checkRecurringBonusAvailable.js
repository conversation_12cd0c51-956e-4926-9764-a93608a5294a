import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, DEPOSIT_BONUS_TYPE, MULTI_BONUS_STATUS } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for the currency
 * @export
 * @class Currency
 * @extends {ServiceBase}
 */
export default class CheckRecurringBonusAvailable extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      auth: { id: userId },
      tenant: { id: tenantId },
      databaseConnection: {
        UserBonusQueue: UserBonusQueueModel,
        Bonus: BonusModel,
        TenantThemeSetting: TenantThemeSettingModel,
      }
    } = this.context

    try {
      const tenantTheme = await TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: { tenantId },
        raw: true
      })

      const allowedModules = tenantTheme?.allowedModules?.split(',').map((module) => module.trim()) || [];
      const allowsMultiBonus = allowedModules.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE);

      if (!allowsMultiBonus) {
        return { exists: false };
      }

      const hasRecurringBonusInQueue = await UserBonusQueueModel.findOne({
        attributes: ['id'],
        where: {
          userId,
          rolloverTarget: { [Op.is]: null },
          bonusAmount: { [Op.is]: null },
          status: MULTI_BONUS_STATUS.PENDING
        },
        include: [
          {
            model: BonusModel,
            as: 'Bonus',
            required: true,
            where: {
              kind: BONUS_TYPES.DEPOSIT_INSTANT,
              enabled: true,
              depositBonusType: DEPOSIT_BONUS_TYPE.RECURRING,
            },
          },
        ],
      });

      return { exists: !!hasRecurringBonusInQueue };

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId });
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
