import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ALLOWED_PARALLEL_BONUS, ALLOWED_PERMISSIONS, AUDIT_LOG_ACTIONEE_TYPE, BONUS_STATUS, BONUS_TYPES, ERORR_TYPE, EVENT, EVENT_TYPE, MULTI_BONUS_STATUS } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

const constraints = {
  bonusId: {
    type: 'integer'
  }
}

/**
 * Provides service for adding losing bonus
 * @export
 * @class AddLosingBonus
 * @extends {ServiceBase}
 */
export default class AddLosingBonus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          Bonus: BonusModel,
          Wallet: WalletModel,
          UserBonus: UserBonusModel,
          TenantThemeSetting: TenantThemeSettingModel,
          UserBonusQueue: UserBonusQueueModel,
          AuditLog: AuditLogModel,
          sequelize
        },
        req: { headers: { language } },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { bonusId }
    } = this

    try {
      const userBonus = await UserBonusModel.findOne({
        attributes: ['id'],
        where: { bonusId: this.args.bonusId, userId }
      })
      if (userBonus) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_ALREADY_USED', language), errorCode: 400 }
      }

      const userWallet = await WalletModel.findOne({
        attributes: ['currencyId'],
        where: { ownerId: userId, ownerType: 'User' }
      })

      const losingBonus = await BonusModel.findOne({
        attributes: ['validUpto', 'validFrom', 'tenantId', 'kind'],
        where: {
          kind: {
            [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
          },
          id: bonusId,
          currencyId: userWallet.currencyId,
          enabled: true
        }
      })

      if (!losingBonus) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_FOUND', language), errorCode: 400 }
      }

      if (new Date().getTime() > new Date(losingBonus.validUpto).getTime()) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_EXPIRED', language), errorCode: 400 }
      }

      if (new Date().getTime() < new Date(losingBonus.validFrom).getTime()) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('BONUS_NOT_AVAILABLE', language), errorCode: 400 }
      }

      const userBonusObject = {
        status: BONUS_STATUS.ACTIVE,
        userId,
        bonusId: this.args.bonusId,
        kind: losingBonus.kind,
        expiresAt: losingBonus.validUpto
      }
      const tenantTheme = await TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: {
          tenantId: losingBonus.tenantId
        },
        raw: true
      })

      // Parse allowed modules from the tenantTheme and check for specific permissions
      const allowedModules = tenantTheme?.allowedModules?.split(',').map((module) => module.trim()) || [];
      const hasParallelBonus = allowedModules.includes(ALLOWED_PARALLEL_BONUS);
      const allowsMultiBonus = allowedModules.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE);

      let bonusInQueue = false, bonusActivated = false;

      // Define conditions to find active bonuses for the user
      const bonusConditions = {
        userId,
        status: BONUS_STATUS.ACTIVE,
        ...(hasParallelBonus && { // Include specific bonus types if parallel bonuses are allowed
          kind: {
            [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH],
          },
        }),
      };

      // Fetch user's active bonus if there's no pending bonus queue
      let userHasActiveBonus = await UserBonusModel.findOne({ where: bonusConditions });

      this.context.sequelizeTransaction = await sequelize.transaction();

      try {
        if (allowsMultiBonus) {
          if (userHasActiveBonus) {
            // Handle case when the user has an active bonus, Add new bonus to queue
            const bonusExistInQueue = await UserBonusQueueModel.findOne({
              attributes: ['userId'],
              where: { userId, bonusId: this.args?.bonusId, status: MULTI_BONUS_STATUS.PENDING },
            });

            // If user already has an active bonus and no existing queue, add the bonus to the queue
            if (!bonusExistInQueue) {
              const newQueueBonus = {
                userId,
                bonusId: this.args.bonusId,
                status: MULTI_BONUS_STATUS.PENDING,
              };

              await UserBonusQueueModel.create(newQueueBonus, { transaction: this.context.sequelizeTransaction });
              bonusInQueue = true;
            }
          }
        } else {
          // If multi-bonus is not allowed, cancel existing active bonuses
          await UserBonusModel.update(
            { status: BONUS_STATUS.CANCELLED }, // Set the status of existing active bonuses to CANCELLED
            { where: bonusConditions, transaction: this.context.sequelizeTransaction }
          );

          if (userHasActiveBonus) {
            const auditData = {
              tenantId: losingBonus?.tenantId,
              actioneeId: userId,
              eventType: EVENT_TYPE.bonusCancelled,
              event: EVENT.update,
              eventId: userHasActiveBonus?.bonusId,
              actioneeIp: this.args?.ip || '',
              description: 'Bonus cancelled',
              action: 'Bonus cancelled',
              previousData: { userId, bonusId: userHasActiveBonus?.bonusId, status: BONUS_STATUS.ACTIVE },
              modifiedData: { userId, bonusId: userHasActiveBonus?.bonusId, status: BONUS_STATUS.CANCELLED },
              actioneeType: AUDIT_LOG_ACTIONEE_TYPE.USER
            }

            await AuditLogModel.create(auditData, { transaction: this.context.sequelizeTransaction });
          }
        }

        // Add the new bonus if multi-bonus is not allowed or the user does not have an active bonus
        if (!allowsMultiBonus || !userHasActiveBonus) {
          await UserBonusModel.create(userBonusObject, { transaction: this.context.sequelizeTransaction });
          bonusActivated = true
        }

        // Commit the transaction to finalize all changes
        await this.context.sequelizeTransaction.commit();
      } catch (error) {
        await this.context.sequelizeTransaction.rollback()
        throw error
      }

      return { bonusAdded: bonusActivated, bonusInQueue }

    } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
    }
  }
}
