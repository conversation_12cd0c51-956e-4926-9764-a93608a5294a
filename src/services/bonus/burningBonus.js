import { ApolloError } from 'apollo-server-express'
import { burningCashBonusAmountCheck } from '../../common/burningCashBonusCheck'
import { burningNonCashBonusAmountCheck } from '../../common/burningNonCashBonusCheck'
import { BURN_DEPOSIT_BONUS_MESSAGE, BURN_LOSING_BONUS_MESSAGE } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

/**
 * Verify if a user has any active bonuses with burning days remaining that they haven't utilized
 * @export
 * @class BurningBonus
 * @extends {ServiceBase}
 */
export default class BurningBonus extends ServiceBase {
  async run () {
    const {
      auth: currentUser,
      req: { headers: { language } },
      tenant,
      databaseConnection: {
        LosingBonusSetting: LosingBonusSettingModel
      }
    } = this.context
    try {
      const today = new Date()
      const flag = true
      const message = this.args.bonusType === 'deposit' ? BURN_DEPOSIT_BONUS_MESSAGE : BURN_LOSING_BONUS_MESSAGE
      let burningAmount

      // checking if there is any deposit bonus which is active and have burning days
      if (this.args.bonusType === 'deposit') {
        const hasActiveDepositBonus = await sequelize.query(`
      SELECT EXISTS (
        SELECT 1
        FROM "public"."user_bonus" AS "UserBonus"
        INNER JOIN "public"."deposit_bonus_settings" AS "DepositBonusSetting"
        ON "UserBonus"."bonus_id" = "DepositBonusSetting"."bonus_id"
        WHERE "DepositBonusSetting"."burning_days" IS NOT NULL
        AND "UserBonus"."kind" IN ('deposit', 'deposit_sport', 'deposit_both', 'deposit_instant')
        AND "UserBonus"."status" = 'active'
        AND "UserBonus"."user_id" = :userId
        AND "UserBonus"."expires_at" > :currentDate
      ) AS "exists";
      `, {
          replacements: {
            currentDate: new Date(),
            userId: currentUser.id
          },
          type: sequelize.QueryTypes.SELECT
        }
        )
        if (!hasActiveDepositBonus[0].exists) return { flag: false, message: '' }
      }

      // checking if the losing bonus does not have burning days
      if (this.args.bonusType !== 'deposit' && this.args.losingBonusId) {
        const losingBonus = await LosingBonusSettingModel.findOne({
          attributes: ['burningDays'],
          where: {
            bonusId: this.args.losingBonusId
          }
        })
        if (!losingBonus.burningDays) return { flag: false, message: '' }
      }

      // burning joining bonus check
      const userBurningJoiningBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_joining_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningJoiningBonusHistory && userBurningJoiningBonusHistory.length > 0) {
        burningAmount = await burningNonCashBonusAmountCheck(userBurningJoiningBonusHistory[0])
        if (burningAmount > 0) return { flag, message }
      }

      // burning promocode bonus check
      const userBurningPromocodeBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_promocode_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningPromocodeBonusHistory && userBurningPromocodeBonusHistory.length > 0) {
        burningAmount = await burningNonCashBonusAmountCheck(userBurningPromocodeBonusHistory[0]) // as burning option is there in non cash only
        if (burningAmount > 0) return { flag, message }
      }

      // casino, sports, both casino and sports deposit burning bonus check
      const userBurningDepositBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_deposit_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningDepositBonusHistory && userBurningDepositBonusHistory.length > 0) {
        for (let i = 0; i < userBurningDepositBonusHistory.length; i++) {
          if (userBurningDepositBonusHistory[i].walletType === 0) {
            burningAmount = await burningCashBonusAmountCheck(userBurningDepositBonusHistory[i])
          } else {
            burningAmount = await burningNonCashBonusAmountCheck(userBurningDepositBonusHistory[i])
          }
          if (burningAmount > 0) return { flag, message }
        }
      }

      // instant deposit burning bonus check
      const userBurningInstantDepositBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_instant_deposit_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningInstantDepositBonusHistory && userBurningInstantDepositBonusHistory.length > 0) {
        for (let i = 0; i < userBurningInstantDepositBonusHistory.length; i++) {
          if (userBurningInstantDepositBonusHistory[i].walletType === 0) {
            burningAmount = await burningCashBonusAmountCheck(userBurningInstantDepositBonusHistory[i])
          } else {
            burningAmount = await burningNonCashBonusAmountCheck(userBurningInstantDepositBonusHistory[i])
          }
          if (burningAmount > 0) return { flag, message }
        }
      }

      // automatic active losing burning bonus check
      const userBurningAutomaticActiveLosingBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_automatic_active_losing_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningAutomaticActiveLosingBonusHistory && userBurningAutomaticActiveLosingBonusHistory.length > 0) {
        for (let i = 0; i < userBurningAutomaticActiveLosingBonusHistory.length; i++) {
          burningAmount = await burningNonCashBonusAmountCheck(userBurningAutomaticActiveLosingBonusHistory[i])
          if (burningAmount > 0) return { flag, message }
        }
      }

      // burning claim days losing bonus check
      const userBurningClaimDaysLosingBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_claim_days_losing_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningClaimDaysLosingBonusHistory && userBurningClaimDaysLosingBonusHistory.length > 0) {
        for (let i = 0; i < userBurningClaimDaysLosingBonusHistory.length; i++) {
          burningAmount = await burningNonCashBonusAmountCheck(userBurningClaimDaysLosingBonusHistory[i])
          if (burningAmount > 0) return { flag, message }
        }
      }

      // burning claim interval losing bonus check
      const userBurningClaimIntervalLosingBonusHistory = await sequelize.query(
        'SELECT * FROM get_user_burning_claim_interval_losing_bonus_history(:user_id, :tenant_id, :current_date);',
        {
          replacements: {
            user_id: currentUser.id,
            tenant_id: tenant.id,
            current_date: today.toISOString()
          },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        }
      )

      if (userBurningClaimIntervalLosingBonusHistory && userBurningClaimIntervalLosingBonusHistory.length > 0) {
        for (let i = 0; i < userBurningClaimIntervalLosingBonusHistory.length; i++) {
          burningAmount = await burningNonCashBonusAmountCheck(userBurningClaimIntervalLosingBonusHistory[i])
          if (burningAmount > 0) return { flag, message }
        }
      }

      return { flag: false, message: '' }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId: tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
