import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import <PERSON><PERSON>r<PERSON>ogHel<PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for fetching bonus
 * @export
 * @class AllBonus
 * @extends {ServiceBase}
 */
export default class AllBonus extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Bonus: BonusModel
      },
      req: { headers: { language } },
      tenant
    } = this.context
    try {
    const allBonus = await BonusModel.findAll({
      attributes: ['id', 'kind', 'promotionTitle', 'image'],
      where: {
        tenantId: tenant.id,
        enabled: true,
        validFrom: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        validUpto: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') }
      },
      raw: true
    })

    return allBonus
  } catch (error) {
    await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.logError(error, this.context, { tenantId: tenant.id })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
