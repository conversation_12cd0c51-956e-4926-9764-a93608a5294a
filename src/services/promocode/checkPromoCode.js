import { ApolloError } from 'apollo-server-express'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import verifyPromoCode from '../../common/verifyPromoCode'
import translate from '../../lib/languageTranslate'

const constraints = {
  promoCode: {
    type: 'string',
    presence: { message: 'Promo Code is required' }
  },
  currencyId: {
    type: 'string',
    presence: { message: 'Currency Code is required' }
  }
}

/**
 * @export
 * @class CheckPromoCode
 * @extends {ServiceBase}
 */
export default class CheckPromoCode extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
      const promoCode = this.args.promoCode
      const currencyId = this.args.currencyId
      const result = await verifyPromoCode(this.context, promoCode, currencyId)

      return (result ? { isValid: true, message: translate('VALID_PROMOCODE', language) } : { isValid: false, message: translate('INVALID_PROMOCODE', language) })
    } catch (error) {
      await <PERSON>rror<PERSON>ogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
