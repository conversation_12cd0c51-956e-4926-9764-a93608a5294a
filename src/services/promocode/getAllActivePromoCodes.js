import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

export default class GetAllActivePromoCodes extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        TenantPromoCodes: TenantPromoCodesModel
      },
      req: {
        headers: { language }
      },
      tenant: { id: tenantId }
    } = this.context
    try {
      const result = await TenantPromoCodesModel.findAll({
        attributes: [
          'id',
          'code',
          'promoName',
          'description',
          'bonusAmount',
          'currencyId',
          'image',
          'termsAndConditions',
          'promoCodeBonusType',
          'walletType',
          [
            Sequelize.fn(
              'to_char',
              Sequelize.col('valid_from'),
              'YYYY-MM-DD HH24:MI:SS'
            ),
            'validFrom'
          ],
          [
            Sequelize.fn(
              'to_char',
              Sequelize.col('valid_till'),
              'YYYY-MM-DD HH24:MI:SS'
            ),
            'validTill'
          ]
        ],
        where: {
          tenantId,
          status: true,
          validFrom: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') },
          validTill: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') }
        },
        raw: true
      })
      return result
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
