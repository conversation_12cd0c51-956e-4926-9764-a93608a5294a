import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { REFERRAL_BLOCK_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

const constraints = {
  referralCode: {
    type: 'string',
    presence: { message: 'Referral Code is required' }
  },
}

/**
 * @export
 * @class CheckReferralCode
 * @extends {ServiceBase}
 */
export default class CheckReferralCode extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      databaseConnection: {
        UserReferralCode: UserReferralCodeModel,
        BlockedReferralUser: BlockedReferralUserModel,
        Blacklist: BlacklistModel,
        User: UserModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
      const { referralCode } = this.args

      const result = await UserReferralCodeModel.findOne({
        where: { tenantId, referralCode },
        attributes: ['id', 'userId', 'referralCode'],
        include: [{
          model: UserModel,
          attributes: ['parentId', 'active', 'email', 'phone']
        }],
      })

      if (!result) {
        return { isValid: false, message: translate('INVALID_REFERRAL_CODE', language) }
      }

      if (!result.User?.active) {
        return { isValid: false, message: translate('INVALID_REFERRAL_CODE', language) }
      }

      const isBlocked = await BlockedReferralUserModel.findOne({
        where: {
          tenantId,
          [Op.or]: [
            { blockType: REFERRAL_BLOCK_TYPE.USER, blockId: result.userId },
            { blockType: REFERRAL_BLOCK_TYPE.AGENT, blockId: result.User?.parentId }
          ]
        },
        attributes: ['blockType'],
      })

      if (isBlocked) {
        return { isValid: false, message: translate('INVALID_REFERRAL_CODE', language) }
      }

      const isBlacklisted = await BlacklistModel.findOne({
        where: {
          tenantId,
          status: true,
          [Op.or]: [
            { value: result.User?.email, type: 2 },
            { value: result.User?.phone, type: 1 }
          ]
        },
        attributes: ['type'],
      })

      if (isBlacklisted) {
        return { isValid: false, message: translate('INVALID_REFERRAL_CODE', language) }
      }

      return { isValid: true, message: translate('VALID_REFERRAL_CODE', language) }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
