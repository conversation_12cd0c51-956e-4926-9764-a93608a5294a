import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * @export
 * @class ReferralSetting
 * @extends {ServiceBase}
 */
export default class ReferralSetting extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        ReferralSettings: ReferralSettingsModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
      const referralSetting = await ReferralSettingsModel.findOne({
        attributes: ['id', 'title', 'description', 'bonusAmount', 'bonusType', 'walletType', 'event', 'applyTo', 'active', 'tenantId', 'minValue', 'maxValue', 'percentageValue',
          'referrerBonusAmount', 'refereeBonusAmount'
        ],
        where: { tenantId },
        raw: true
      })

      return referralSetting
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
