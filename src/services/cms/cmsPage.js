import { ApolloError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get cms pages
 * @export
 * @class CmsPages
 * @extends {ServiceBase}
 */
export default class CmsPages extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        CmsPage: CmsPagesModel,
        FooterLicense: FooterLicenseModel,
        FooterLicenseImage: FooterLicenseImageModel
      },
      tenant: { id: tenantId },
      req: { headers: { language } }
    } = this.context

    try {
    const cmsPages = await CmsPagesModel.findAll({
      where: {
        active: true,
        tenantId: tenantId
      },
      order: ['id'],
      raw: true
    })

    const footerContent = await FooterLicenseModel.findOne({
      where: { tenantId },
      attributes: ['heading', 'content_1', 'content_2','headerContent'],
      include: [{
        model: FooterLicenseImageModel,
        attributes: ['imageUrl', 'redirectUrl', 'id']
      }],
      require: true
    })

    if (!CmsPages) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('CMS_PAGE_NOT_FOUND', language), errorCode: 404 }
    }

    return { CmsPagesContent: cmsPages, FooterContent: footerContent && footerContent.dataValues ? footerContent.dataValues : null }
  } catch (error) {
    if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
      throw new ApolloError(error.errorMsg, error.errorCode)
    } else {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  } }
}
