import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import moment from 'moment'
import <PERSON><PERSON><PERSON><PERSON>ogHel<PERSON> from '../../common/errorLog'
import translate from '../../lib/languageTranslate'
import ServiceBase from '../../common/serviceBase'
import { GAME_LAUNCH } from '../../common/constants'
import { FIXTURE_STATUS } from '../../betting-integration/common/constant'
import { Sequelize } from '../../db/models'

/**
 * Get Sports with Active Leagues and Events
 * @export
 * @class SportsWithActiveLeaguesAndEventsService
 * @extends {ServiceBase}
 */
export default class SportsWithActiveLeaguesAndEvents extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        PullsSport: SportModel,
        PullsLeague: LeagueModel,
        PullsEvent: EventModel,
        CasinoProvider: CasinoProviderModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    const { sportId, filter } = this.args
    const providerName = GAME_LAUNCH.POWER_PLAY

    try {
      const provider = await CasinoProviderModel.findOne({ where: { name: providerName }, attributes: ['id'] })

      const sportFilter = sportId ? { id: sportId } : {}
      const leagueFilter = {
        isDeleted: false,
        tenantId,
        providerId: provider.id,
        status: true
      }
      const eventFilter = {
        isDeleted: false,
        tenantId,
        providerId: provider.id,
        status: true,
        isVirtual: false
        // marketType: 0
      }

      if (!filter || filter === 'inPlay') {
        eventFilter.fixtureStatus = FIXTURE_STATUS.IN_PROGRESS
        eventFilter.marketType = 0
      } else if (filter === 'upcoming') {
        eventFilter.fixtureStatus = FIXTURE_STATUS.NOT_STARTED
        eventFilter.startDate = {
          [Op.gte]: moment().toDate()
        }
        eventFilter[Op.or] = [
          { marketType: 10 },
          { marketType: 0 }
        ]
      } else {
        const now = moment()
        if (filter === 'today') {
          eventFilter.startDate = {
            [Op.between]: [now.startOf('day').toDate(), now.endOf('day').toDate()]
          }
          eventFilter[Op.or] = [
            {
              fixtureStatus: FIXTURE_STATUS.IN_PROGRESS,
              marketType: 0

            },
            {
              fixtureStatus: FIXTURE_STATUS.NOT_STARTED,
              [Op.or]: [
                { marketType: 10 },
                { marketType: 0 }
              ]
            }
          ]
        } else if (filter === 'tomorrow') {
          const tomorrow = now.add(1, 'day')
          eventFilter.startDate = {
            [Op.between]: [tomorrow.startOf('day').toDate(), tomorrow.endOf('day').toDate()]
          }
          eventFilter[Op.or] = [
            { marketType: 10 },
            { marketType: 0 }
          ]
        }
      }

      const sports = await SportModel.findAll({
        attributes: [
          [Sequelize.literal('DISTINCT ON ("leagues->events"."fixture_id") "PullsSport"."name_en"'), 'nameEn'],
          'id',
          'sportId'
        ],
        where: {
          isDeleted: false,
          tenantId,
          providerId: provider.id,
          status: true,
          ...sportFilter
        },
        include: [
          {
            model: LeagueModel,
            where: leagueFilter,
            as: 'leagues',
            attributes: ['id', 'nameEn', 'sportId', 'leagueId'],
            include: [
              {
                model: EventModel,
                as: 'events',
                attributes: ['id', 'fixtureId', 'nameEn', 'startDate'],
                where: eventFilter,
                required: true
              }
            ]
          }
        ]
      })

      return { sports, providerName }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
