import { ApolloError } from 'apollo-server-express'
import <PERSON><PERSON><PERSON><PERSON>ogHel<PERSON> from '../../common/errorLog'
import translate from '../../lib/languageTranslate'
import ServiceBase from '../../common/serviceBase'
import { FIXTURE_STATUS } from '../../betting-integration/common/constant'
import { GAME_LAUNCH } from '../../common/constants'
import { Sequelize } from '../../db/models'

/**
 * Get Live Events
 * @export
 * @class LiveEvents
 * @extends {ServiceBase}
*/

export default class LiveEvents extends ServiceBase {
  async run () {
    const {
      databaseConnection: { PullsEvent: EventModel, CasinoProvider: CasinoProviderModel, PullsSport: SportModel },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    const preferredSport = 'Cricket' // Will be made dynamic from BO.
    const providerName = GAME_LAUNCH.POWER_PLAY

    try {
      const provider = await CasinoProviderModel.findOne({ where: { name: providerName }, attributes: ['id'] })

      const preferredSportId = await SportModel.findOne({ where: { nameEn: preferredSport, tenantId, providerId: provider.id }, attributes: ['id'] })
      const whereClause = {
        isDeleted: false,
        sportId: preferredSportId.id,
        fixtureStatus: FIXTURE_STATUS.IN_PROGRESS,
        tenantId,
        providerId: provider.id,
        status: true,
        isVirtual: false,
        marketType: 0
      }

      const events = await EventModel.findAll({
        where: whereClause,
        attributes: [
          [Sequelize.literal('DISTINCT ON ("fixture_id") "name_en"'), 'nameEn'],
          'id',
          'fixtureId'
        ],
        order: ['fixtureId', ['startDate', 'DESC']],
        limit: 10,
        raw: true
      })

      events.map(event => {
        event.providerName = providerName
        event.sportNameEn = preferredSport
      })

      return events
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
