import { ApolloError } from 'apollo-server-express'
import Sequelize, { Op } from 'sequelize'
import { FIXTURE_STATUS } from '../../betting-integration/common/constant'
import { GAME_LAUNCH } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import moment from 'moment'

/**
 * Get Events of a Particular League
 * @export
 * @class LeagueEvents
 * @extends {ServiceBase}
 */

export default class LeagueEvents extends ServiceBase {
  async run () {
    const {
      databaseConnection: { PullsEvent: EventModel, PullsLeague: LeagueModel, CasinoProvider: CasinoProviderModel, PullsSport: SportModel },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    const leagueId = this.args?.input?.leagueId
    const providerName = GAME_LAUNCH.POWER_PLAY

    try {
      if (!leagueId) {
        throw new ApolloError(translate('LEAGUE_ID_REQUIRED', language), 400)
      }

      const provider = await CasinoProviderModel.findOne({
        where: { name: providerName },
        attributes: ['id']
      })

      const eventFilter = {
        isDeleted: false,
        [Op.or]: [
          {
            fixtureStatus: FIXTURE_STATUS.IN_PROGRESS,
            marketType: 0
          },
          {
            fixtureStatus: FIXTURE_STATUS.NOT_STARTED,
            startDate: { [Op.gte]: moment().tz('Asia/Kolkata').toDate() },
            [Op.or]: [
              { marketType: 10 },
              { marketType: 0 }
            ]
          }
        ],
        tenantId,
        providerId: provider.id,
        leagueId,
        status: true,
        isVirtual: false
        // marketType: 0
      }

      const leagueEvents = await LeagueModel.findAll({
        where: { id: leagueId },
        attributes: ['id', 'nameEn'],
        include: [
          {
            model: EventModel,
            as: 'events',
            where: eventFilter,
            attributes: [[Sequelize.literal('DISTINCT ON ("PullsEvent"."fixture_id") "PullsEvent"."name_en"'), 'nameEn'], 'id', 'fixtureId', 'startDate'],
            order: [['fixtureId', 'ASC'], ['startDate', 'DESC']],
            limit: 10,
            include: [
              {
                model: SportModel,
                as: 'sport',
                attributes: ['id', 'nameEn']
              }
            ]
          }
        ]
      })

      leagueEvents[0].events.map((event) => {
        event.providerName = providerName
      })

      return { league: leagueEvents }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
