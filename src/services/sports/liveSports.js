import { ApolloError } from 'apollo-server-express'
import moment from 'moment'
import { FIXTURE_STATUS } from '../../betting-integration/common/constant'
import { CASINO_PROVIDER } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'


/**
 * Get Sports with event counts
 * @export
 * @class GetLiveSports
 * @extends {ServiceBase}
 */

const constraints = {}

function buildEventFilterSQL(filter, now, tenantId, providerId) {
  const baseConditions = `
    pe.is_deleted = false
    AND pe.is_event_blacklisted = false
    AND pe.status = true
    AND pe.tenant_id = :tenantId
    AND pe.provider_id = :providerId
    AND pe.is_virtual = false
  `

  let filterConditions = ''
  const params = { tenantId, providerId }

  if (!filter || filter === 'all') {
    const threeDaysFromNow = now.clone().add(3, 'days').toDate()
    params.threeDaysFromNow = threeDaysFromNow

    filterConditions = `
      AND (
        (pe.fixture_status = :inProgressStatus AND pe.market_type = 0)
        OR (
          pe.fixture_status = :notStartedStatus
          AND pe.start_date <= :threeDaysFromNow
          AND pe.market_type IN (0, 10)
        )
      )
    `
    params.inProgressStatus = FIXTURE_STATUS.IN_PROGRESS
    params.notStartedStatus = FIXTURE_STATUS.NOT_STARTED

  } else if (filter === 'inPlay') {
    filterConditions = `
      AND pe.fixture_status = :inProgressStatus
      AND pe.market_type = 0
    `
    params.inProgressStatus = FIXTURE_STATUS.IN_PROGRESS

  } else if (filter === 'upcoming') {
    params.nowDate = now.toDate()
    filterConditions = `
      AND pe.fixture_status = :notStartedStatus
      AND pe.start_date >= :nowDate
      AND pe.market_type IN (0, 10)
    `
    params.notStartedStatus = FIXTURE_STATUS.NOT_STARTED

  } else if (filter === 'today') {
    params.todayStart = now.clone().startOf('day').toDate()
    params.todayEnd = now.clone().endOf('day').toDate()

    filterConditions = `
      AND pe.start_date BETWEEN :todayStart AND :todayEnd
      AND (
        (pe.fixture_status = :inProgressStatus AND pe.market_type = 0)
        OR (pe.fixture_status = :notStartedStatus AND pe.market_type IN (0, 10))
      )
    `
    params.inProgressStatus = FIXTURE_STATUS.IN_PROGRESS
    params.notStartedStatus = FIXTURE_STATUS.NOT_STARTED

  } else if (filter === 'tomorrow') {
    const tomorrow = now.clone().add(1, 'day')
    params.tomorrowStart = tomorrow.startOf('day').toDate()
    params.tomorrowEnd = tomorrow.endOf('day').toDate()

    filterConditions = `
      AND pe.start_date BETWEEN :tomorrowStart AND :tomorrowEnd
      AND pe.market_type IN (0, 10)
    `
  }

  return { baseConditions, filterConditions, params }
}

export default class GetLiveSports extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args,
      context: {
        databaseConnection: {
          CasinoProvider: CasinoProviderModel,
          sequelize
        },
        tenant: { id: tenantId },
        req: { headers: { language } }
      }
    } = this

    try {
      const filter = args?.filter || 'all'
      const now = moment()

      // Get provider ID from constants based on environment
      const providerId = config.get('env') === 'production' ? CASINO_PROVIDER.POWERPLAY.PROD : CASINO_PROVIDER.POWERPLAY.STAGE

      if (!providerId) {
        return { sports: [], totalSports: 0, totalEvents: 0 }
      }

      const { baseConditions, filterConditions, params } = buildEventFilterSQL(
        filter,
        now,
        tenantId,
        providerId
      )

      // Optimized raw SQL query
      const query = `
        SELECT
          ps.sport_id,
          ps.id,
          ps.name_en as "nameEn",
          COUNT(DISTINCT pe.fixture_id) as "eventCount"
        FROM pulls_sports ps
        INNER JOIN pulls_events pe ON ps.id = pe.sport_id
        WHERE ps.is_deleted = false
          AND ps.status = true
          AND ps.tenant_id = :tenantId
          AND ps.provider_id = :providerId
          ${baseConditions.replace('pe.', 'AND pe.')}
          ${filterConditions}
        GROUP BY ps.sport_id, ps.id, ps.name_en
        ORDER BY ps.name_en
      `

      const sports = await sequelize.query(query, {
        replacements: params,
        type: sequelize.QueryTypes.SELECT
      })

      // Convert eventCount to number, filter out null sportIds, and calculate total
      const processedSports = sports
        .filter(sport => sport.sport_id !== null && sport.sport_id !== undefined)
        .map(sport => ({
          ...sport,
          eventCount: Number(sport.eventCount),
          sportId: String(sport.sport_id)
        }))

      const totalEvents = processedSports.reduce((sum, sport) => sum + sport.eventCount, 0)

      return {
        sports: processedSports,
        totalSports: processedSports.length,
        totalEvents
      }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
