import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import { GAME_LAUNCH } from '../../common/constants'
import <PERSON>rror<PERSON><PERSON><PERSON>elper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { FIXTURE_STATUS } from '../../betting-integration/common/constant'
import moment from 'moment'

/**
 * Get Active Leagues
 * @export
 * @class ActiveLeagues
 * @extends {ServiceBase}
 */
export default class ActiveLeagues extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        PullsEvent: PullsEventModel,
        CasinoProvider: CasinoProviderModel,
        PullsLeague: PullsLeagueModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
      const sportId = this.args?.input?.sportId
      const provider = await CasinoProviderModel.findOne({
        where: { name: GAME_LAUNCH.POWER_PLAY },
        attributes: ['id']
      })

      if (!provider) {
        throw new ApolloError(translate('PROVIDER_NOT_FOUND', language), 404)
      }

      const leaugeFilter = {
        isDeleted: false,
        tenantId,
        providerId: provider.id,
        status: true
      }
      const eventFilter = {
        isDeleted: false,
        tenantId,
        providerId: provider.id,
        status: true,
        [Op.or]: [
          {
            fixtureStatus: FIXTURE_STATUS.IN_PROGRESS,
            marketType: 0
          },
          {
            fixtureStatus: FIXTURE_STATUS.NOT_STARTED,
            startDate: { [Op.gte]: moment().tz('Asia/Kolkata').toDate() },
            [Op.or]: [
              { marketType: 10 },
              { marketType: 0 }
            ]
          }
        ],
        isVirtual: false

      }

      if (sportId) {
        leaugeFilter.sportId = sportId
        eventFilter.sportId = sportId
      }

      const leagues = await PullsLeagueModel.findAll({
        where: leaugeFilter,
        attributes: ['id', 'leagueId', 'sportId', 'season', 'locationId', 'sportId', 'nameEn', 'isPopular',
          [Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('events.fixture_id'))), 'eventCount']
        ],
        include: {
          model: PullsEventModel,
          as: 'events',
          where: eventFilter,
          required: true,
          attributes: []
        },
        group: ['PullsLeague.id'],
        raw: true
      })
      return leagues
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
