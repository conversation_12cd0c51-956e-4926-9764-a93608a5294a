import { ApolloError } from 'apollo-server-express'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get notification
 * @export
 * @class Notification
 * @extends {ServiceBase}
 */
export default class Notification extends ServiceBase {
  async run () {
    const {
      NotificationReceiver: NotificationReceiverModel
    } = this.context.databaseConnection

    const {
      context: {
        tenant: { id: tenantId },
        req: { headers: { language } }
      }
    } = this

    try {
    const notification = await NotificationReceiverModel.findAll({
      where: {
        receiverId: this.context.auth.id,
        receiverType: 'User'
      },
      order: [['createdAt', 'DESC']]
    })
    return notification
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { id: this.context.auth.id, tenantId })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
