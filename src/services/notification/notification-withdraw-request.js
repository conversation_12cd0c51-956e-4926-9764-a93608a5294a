import { SUBSCRIPTION_CHANNEL } from '../../common/constants';
import <PERSON>rrorLogHelper from '../../common/errorLog';
/**
 *
 *
 * @export
 * @param {*} response
 * @param {*} context
 */
export default async function WithDrawRequestNotification (response, context) {
  const {
    databaseConnection: {
      Notification: NotificationModel,
      NotificationReceiver: NotificationReceiverModel,
      User: UserModel
    },
    auth: currentUser,
    pubSub
  } = context

  const userInfo = (await UserModel.findOne({ attributes: ['parentId', 'userName', 'id', 'tenantId'], where: { id: currentUser.id }, raw: true }))
  const userOwnerId = userInfo.parentId

  let notificationSender, notificationReference;

  if (response.depositType === 'manual') {
    notificationSender = { senderId: currentUser.id, senderType: 'User', referenceType: 'Manual Deposit' }
    notificationReference = {
      referenceId: response.id,
      message: `${userInfo.userName} made a manual deposit request`
    }
  } else {
    notificationSender = { senderId: currentUser.id, senderType: 'User', referenceType: 'Withdraw' }
    notificationReference = {
      referenceId: response.id,
      message: `${userInfo.userName} created withdraw request`
    }
  }

  const notifications = {
    ...notificationReference,
    ...notificationSender
  }
  try {
    const notificationInserted = await NotificationModel.create(notifications)

    const notificationReceiver = {
      receiverId: userOwnerId,
      receiverType: 'AdminUser',
      isRead: false
    }
    const notificationReference = { ...notificationReceiver, notificationId: notificationInserted.id }
    await NotificationReceiverModel.create(notificationReference)
    try {
      pubSub.publish(`${SUBSCRIPTION_CHANNEL.USER_WITHDRAW_NOTIFICATION}_${userOwnerId}`, { ...notificationSender, ...notificationReference })
    } catch (e) {}
  } catch (e) {
    await ErrorLogHelper.logError(e, context, userInfo)
    throw new Error(e)
  }
}
