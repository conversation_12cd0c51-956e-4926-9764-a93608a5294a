import { SUBSCRIPTION_CHANNEL } from '../../common/constants'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'

/**
 *
 *
 * @export
 * @param {*} response
 * @param {*} context
 */
export default async function DocumentNotification (response, context) {
  const {
    databaseConnection: {
      Notification: NotificationModel,
      NotificationReceiver: NotificationReceiverModel,
      User: UserModel
    },
    auth: currentUser,
    pubSub
  } = context

  const user = await UserModel.findOne({ where: { id: currentUser.id }, raw: true })

  const notificationSender = { senderId: currentUser.id, senderType: 'User', referenceType: 'UserDocument' }
  const notifications = response.map((item) => {
    const notificationReference = {
      referenceId: item.id,
      message: `${user.userName} uploaded document ${item.documentName}`
    }

    pubSub.publish(`${SUBSCRIPTION_CHANNEL.DOCUMENT_NOTIFICATION}_${user.parentId}`, { ...notificationSender, ...notificationReference })

    return ({ ...notificationSender, ...notificationReference })
  })
  try {
    const notificationInserted = await NotificationModel.bulkCreate(notifications, { returning: true })

    const notificationReceiver = {
      receiverId: user.parentId,
      receiverType: 'AdminUser',
      isRead: false
    }
    const notificationReference = notificationInserted.map(item => ({ ...notificationReceiver, notificationId: item.id }))
    await NotificationReceiverModel.bulkCreate(notificationReference)
  } catch (e) {
    await ErrorLogHelper.logError(e, context, user)
    throw new Error(e)
  }
}
