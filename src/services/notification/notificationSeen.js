import { ApolloError } from 'apollo-server-express'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * update Notification
 * @export
 * @class NotificationSeen
 * @extends {ServiceBase}
 */

const constraints = {
  notificationId: {
    type: 'string'
  }
}
export default class NotificationSeen extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const { databaseConnection: { NotificationReceiver: NotificationReceiverModel }, auth: currentUser, tenant: { id: tenantId }, req: { headers: { language } } } = this.context

    try {
    await NotificationReceiverModel.update({ isRead: true }, {
      where: {
        notificationId: this.args.notificationId,
        receiverId: currentUser.id,
        receiverType: 'User'
      },
      raw: true
    })
    return { ...this.args, isRead: true }
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
