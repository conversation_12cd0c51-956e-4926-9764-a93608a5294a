import ServiceBase from '../../common/serviceBase'
import redisConnection from '../../lib/redisConnection'
import ErrorLogHelper from '../../common/errorLog'

export default class UserLogout extends ServiceBase {
  async run () {
    try {
      const {
        auth: {
          id
        }
      } = this.context

      const data = await redisConnection.del(`user:${id}`)
      return { data: !!data }
    } catch (e) {
      await ErrorLogHelper.logError(e, this.context, null)
      return {
        data: false
      }
    }
  }
}
