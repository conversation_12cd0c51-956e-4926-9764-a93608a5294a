import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express';
import md5 from 'md5';
import { Op } from 'sequelize';
import { ERORR_TYPE, QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL, TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD } from '../../common/constants'; // Ensure the paths are correct
import <PERSON><PERSON><PERSON><PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import config from '../../config/app';
import { sequelize } from '../../db/models';
import translate from '../../lib/languageTranslate';

const constraints = {
  userName: {
    type: 'string',
    presence: { message: 'Username is required' }
  },
  newPassword: {
    type: 'string',
    presence: { message: 'New password is required' },
    length: {
      minimum: 5,
      message: 'Password must be at least 5 characters long'
    }
  },
  confirmPassword: {
    type: 'string',
    presence: { message: 'Confirm password is required' }
  }
};

export default class UpdatePassword extends ServiceBase {
  get constraints () {
    return constraints;
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        QueueLog: QueueLogModel,
        Marina888User: Marina888UserModel
      },
      tenant: Tenant,
    } = this.context;

    try {
    const { userName, newPassword, confirmPassword } = this.args;

    // Check if newPassword and confirmPassword match
    if (newPassword !== confirmPassword) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('PASSWORDS_DO_NOT_MATCH', language) }
    }

    const newPasswordHash = Buffer.from(newPassword, 'base64').toString('ascii');

      let regExp
      const allowExtraSpecialCharsTenant = config.get('env') === 'production' ? TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.PROD : TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.STAGE
      if (allowExtraSpecialCharsTenant && allowExtraSpecialCharsTenant.includes(Tenant.id))
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!"#$%&\'()*+,-./:;<=>?_@(){}])[A-Za-z\\d!"#$%&\'()*+,-./:;<=>?_@(){}]{5,}$')
      else
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*["\\!#$%&\'()*+,-./:;<=>?_@])[A-Za-z\\d"\\!#$%&\'()*+,-./:;<=>?_@]{5,}$')
    if (!regExp.test(newPasswordHash)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_PASSWORD_FORMAT', language) }
    }

    const user = await UserModel.findOne({
      attributes: ['id', 'userName', 'tenantId'],
      where: {
        tenantId: Tenant.id,
        [Op.and]: sequelize.where(sequelize.fn('LOWER', sequelize.col('user_name')), userName.toLowerCase())
      }
    });

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    const encryptedPassword = md5(newPasswordHash);

    await UserModel.update({
      encryptedPassword,
      passwordUpdatedAt: sequelize.fn('NOW')
    }, {
      where: {
        tenantId: Tenant.id,
        id: user.id
      }
    });

    const marinaUser = await Marina888UserModel.findOne({
      attributes: ['id', 'externalUserId'],
      where: { internalUserId: user.id, status: true }
    });

    if (marinaUser) {
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.MARINA888_USER_PASSWORD_CHANGE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            userId: marinaUser.externalUserId,
            userName: user.userName,
            password: newPasswordHash
          }
        ]
      };

      const queueLog = await QueueLogModel.create(queueLogObject);

      if (queueLog.id) {
        try {
          this.context.pubSub.publish(
            SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
            { QueueLog: { queueLogId: queueLog.id } }
          );
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
        }
      }
    }

    return { success: true, message: translate('PASSWORD_UPDATED_SUCCESSFULLY', language) };
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
