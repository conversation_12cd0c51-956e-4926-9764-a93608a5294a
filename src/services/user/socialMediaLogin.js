import { AuthenticationError, UserInputError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ERORR_TYPE, REFERRAL_BLOCK_TYPE, SOCIAL_MEDIA_AUTH_LINK, SOCIAL_MEDIA_LOGIN_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { loginBySocialMedia } from '../../common/loginBySocialMedia'
import ServiceBase from '../../common/serviceBase'
import { signupBySocialMedia } from '../../common/signupBySocialMedia'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
const axios = require('axios')

/**
 * social media login of user
 * @export
 * @class SocialMediaLogin
 * @extends {ServiceBase}
 */
export default class SocialMediaLogin extends ServiceBase {
  async run () {
    this.context.sequelizeTransaction = await sequelize.transaction()
    const {
      context: { databaseConnection:
        {
          User: UserModel,
          BotUser: BotUserModel,
          BlockedReferralUser: BlockedReferralUserModel
        },
        tenant: { id: tenantId },
        req: { headers: { language } },
      },
      args: {
        accessToken,
        socialMediaLoginType,
        isSignUp
      }
    } = this

    try {
      let userData, data, loginType
      switch (socialMediaLoginType) {
        case SOCIAL_MEDIA_LOGIN_TYPE.GOOGLE:
          data = (await axios.get(SOCIAL_MEDIA_AUTH_LINK.GOOGLE, {
            headers: { Authorization: `Bearer ${accessToken}` }
          })).data
          if (data) {
            const { sub: socialMediaUserId, email, picture: avatarImage, given_name: firstName, family_name: lastName } = data || {}
            userData = {
              socialMediaUserId,
              email,
              avatarImage,
              firstName,
              lastName
            }
            loginType = SOCIAL_MEDIA_LOGIN_TYPE.GOOGLE
          }
          break
        case SOCIAL_MEDIA_LOGIN_TYPE.FACEBOOK:
          data = this.args.data
          if (data) {
            userData = data
            loginType = SOCIAL_MEDIA_LOGIN_TYPE.FACEBOOK
          }
          break
        default:
          data = null
      }
      if (!userData && !userData?.email) throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_NOT_FOUND', language) }

      // check signup or login
      const ifUserExist = await UserModel.findOne({
        attributes: ['id', 'active', 'profileVerified', 'parentId'],
        where: {
          tenantId,
          email: userData?.email
        }
      })

      let result
      if (!ifUserExist && !isSignUp) { // trying to do login but user not signup
        await this.context.sequelizeTransaction.rollback()
        return { token: null, user: null, resToken: null, success: false }
      }

      if (isSignUp && !ifUserExist) { // signup
        result =  await signupBySocialMedia(this.context, this.args, userData, loginType)
      }
      else if ((!isSignUp && ifUserExist) || (ifUserExist && isSignUp)) { // login
        result = await loginBySocialMedia(this.context, this.args, userData, ifUserExist, loginType )
      }
      if (result) {
        if(result.user){
          const isBotUser = await BotUserModel.findOne({ where: { userId: result.user.id } })
          result.user.isBotUser = isBotUser ? true : false

          // Find a blocked referral user based on tenant ID and block conditions
          const blockedReferralCode = await BlockedReferralUserModel.findOne({
            where: {
              tenantId, // Ensure the record belongs to the correct tenant
              [Op.or]: [
                // Check if the referral user is blocked as a normal user
                { blockId: result.user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

                // Check if the referral user's parent (agent) is blocked
                { blockId: result?.user?.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
              ]
            }
          });

          result.user.isReferralCodeBlocked = !!blockedReferralCode;
        }
        await this.context.sequelizeTransaction.commit()
        return result
      }
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INTERNAL_SERVER_ERROR', language) }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId })
        throw new UserInputError(translate('INTERNAL_SERVER_ERROR', language))
      }
    }
  }
}
