import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
import { Op } from 'sequelize'
import { ALLOWED_PERMISSIONS, DEFAULT_OTP_VERIFY_ATTEMPT, DEFAULT_OTP_VERIFY_ATTEMPT_EXPIRY_TIME, ERORR_TYPE, LOGIN_TYPE, OTP_ATTEMPTS_TYPES, PROD_TENANTS_MESSAGE_CENTRAL_SMS, REFERRAL_BLOCK_TYPE, STAGE_TENANTS_MESSAGE_CENTRAL_SMS, SUBSCRIPTION_CHANNEL, TENANT_SETTINGS_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import mobileLogin from '../../common/mobileLogin'
import { MultipleOtpAttempts } from '../../common/mutlipleOtpAttempt'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const constraints = {
  phone: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone format'
    },
    length: {
      minimum: 5,
      maximum: 15
    }
  },
  phoneCode: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone code format'
    }
  },
  otp: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid OTP format'
    },
    length: {
      minimum: 4,
      maximum: 6
    }
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  },
  ip: {
    type: 'string'
  },
  email: {
    type: 'string'
  },
  otpPreference: {
    type: 'integer'
  }
}

/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class MobileOTPVerification extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        QueueLog: QueueLogModel,
        TenantThemeSetting: TenantThemeSettingModel,
        BotUser: BotUserModel,
        BlockedReferralUser: BlockedReferralUserModel,
        TenantSetting: TenantSettingModel
      },
      tenant: Tenant
    } = this.context

    const {
      otpAttempt,
      getOtpAttempt,
      clearOtpAttempt,
      getRemainigTime
    } = MultipleOtpAttempts()

    try {
    const isMobileLogin = this.args.otpPreference === LOGIN_TYPE.MOBILE
    let whereCondition = {
      tenantId: Tenant.id,
      [Op.or]: [
        isMobileLogin ? { phone: this.args.phone, phoneCode: this.args.phoneCode } : { email: this.args.email }
      ]
    }

    const joiningFlag = true // as it is given at the time of signup

    let user = await UserModel.findOne({
      attributes: ['id', 'active', 'encryptedPassword', 'phoneVerified', 'emailVerified','profileVerified','tenantId', 'parentId'],
      where: whereCondition
    })
    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(isMobileLogin ? 'PHONE_NOT_FOUND' : 'EMAIL_NOT_REGISTER', language) }
    }

    const otp = this.args.otp

    if (!user.active) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
    }

    let tenantSettings = await TenantSettingModel.findAll({
      where: {
        tenantId: Tenant.id,
        type: TENANT_SETTINGS_TYPE.AUTHENTICATION_SETTINGS,
      },
      attributes: ['key', 'value'],
      raw: true,
    });

      tenantSettings = tenantSettings.reduce((acc, curr) => {
        acc[curr.key] = curr.value
        return acc
      }, {})

    const maxOtpVerifyAttempts = tenantSettings?.OTP_VERIFY_ATTEMPTS ? tenantSettings.OTP_VERIFY_ATTEMPTS : DEFAULT_OTP_VERIFY_ATTEMPT
    const otpVerifyAttemptExpiryTime = tenantSettings?.OTP_VERIFY_ATTEMPT_EXPIRY_TIME ? tenantSettings.OTP_VERIFY_ATTEMPT_EXPIRY_TIME : DEFAULT_OTP_VERIFY_ATTEMPT_EXPIRY_TIME

    const userIpKey = `${user.id}-${this.args.ip}`

    // get login attempts for user and device
    const attempts = await getOtpAttempt(userIpKey, OTP_ATTEMPTS_TYPES.VERIFY_OTP)

    if (attempts && attempts >= maxOtpVerifyAttempts) {
      const remainingTime = await getRemainigTime(userIpKey, OTP_ATTEMPTS_TYPES.VERIFY_OTP)
      const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(`Please try again after ${Math.ceil(remainingTime / 60).toFixed(0)} mins`, language) }
      throw err
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: user.tenantId
      },
      attributes: [ 'allowedModules']
    })
    const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
    if (profileVerifiedPermissionCheck && !user.profileVerified) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('PROFILE_NOT_ACTIVE', language) }
    }

    if (this.args.password) {
      this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')
      if (md5(this.args.password) !== user.encryptedPassword) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('WRONG_PASSWORD', language) }
      }
    } else {
      const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS
      if (eligibleTenant.includes(Tenant.id)) {

        let otpAttemptRes;
        try {
          const response = await messageCentralVerifyOTP(this.context, this.args.otp, this.args.phone, this.args.phoneCode, user.id)
          if (!response) {
            otpAttemptRes = await otpAttempt(userIpKey, maxOtpVerifyAttempts, otpVerifyAttemptExpiryTime, OTP_ATTEMPTS_TYPES.VERIFY_OTP)
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate(otpAttemptRes?.message || 'VERIFICATION_FAILED', language) }
          }
        } catch (error) {
          if (!otpAttemptRes) {
            otpAttemptRes = await otpAttempt(userIpKey, maxOtpVerifyAttempts, otpVerifyAttemptExpiryTime, OTP_ATTEMPTS_TYPES.VERIFY_OTP)
          }

          await ErrorLogHelper.logError(error, this.context, user)
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate(otpAttemptRes?.message || 'VERIFICATION_FAILED', language) }
        }
      } else {
        whereCondition = {
          userId: user.id,
          tenantId: Tenant.id,
          tokenType: isMobileLogin ? 'phone' : 'email',
          ...(isMobileLogin
            ? { phone: this.args.phone, phoneCode: this.args.phoneCode }
            : { email: this.args.email }),
          token: md5(otp)
        }
        const verifyPhoneToken = await UserTokenModel.findOne({
          attributes: ['id'],
          where: whereCondition,
          raw: true
        })
        if (!verifyPhoneToken) {
          const otpAttemptRes = await otpAttempt(userIpKey, maxOtpVerifyAttempts, otpVerifyAttemptExpiryTime, OTP_ATTEMPTS_TYPES.VERIFY_OTP)
          throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(otpAttemptRes?.message || 'INVALID_OTP', language) }
        }
        UserTokenModel.destroy({ where: whereCondition })
      }
      if (isMobileLogin && !user.phoneVerified)
        await user.update({ phoneVerified: true })
      else if (!isMobileLogin && !user.emailVerified)
        await user.update({ emailVerified: true })
    }
    let bulkData = []
    const responseObj = await mobileLogin(this.context, user.id, joiningFlag, this.args, bulkData)
    const createdRecords = await QueueLogModel.bulkCreate(bulkData)
    if (createdRecords) {
      try {
        createdRecords.forEach(record => {
          this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
            QueueLog: { queueLogId: record.id }
          })
        })
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
      }
    }
    const token = responseObj.token
    const resToken = responseObj.resToken
    user = responseObj.user
    const isBotUser = await BotUserModel.findOne({ where: { userId: user.id } })
    user.isBotUser = isBotUser ? true : false

    // Find a blocked referral user based on tenant ID and block conditions
    const blockedReferralCode = await BlockedReferralUserModel.findOne({
      where: {
        tenantId: Tenant.id, // Ensure the record belongs to the correct tenant
        [Op.or]: [
          // Check if the referral user is blocked as a normal user
          { blockId: user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

          // Check if the referral user's parent (agent) is blocked
          { blockId: user?.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
        ]
      }
    });

    user.isReferralCodeBlocked = !!blockedReferralCode;

    await clearOtpAttempt(userIpKey, OTP_ATTEMPTS_TYPES.VERIFY_OTP)
    await clearOtpAttempt(userIpKey, OTP_ATTEMPTS_TYPES.GENERATE_OTP)

    return { token, user, resToken }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
