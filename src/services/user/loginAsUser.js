import { AuthenticationError } from 'apollo-server-express'
import crypto from 'crypto'
import * as jwt from 'jsonwebtoken'
import { Op } from 'sequelize'
import { ERORR_TYPE, IMPERSONATED_USER_EXPIRY_TIME, LOGIN_AS_USER_KEY, REFERRAL_BLOCK_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { getUserSmarticoId } from '../../common/getUserSmarticoId'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'


const constraints = {

  token: {
    type: 'string',
    presence: { message: 'Token is required' }
  },

}

/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class LoginAsUser extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    let user
    try {
      const {
        req: { headers: { language } },
        databaseConnection: {
          User: UserModel,
          TenantThemeSetting: TenantThemeSettingModel,
          Marina888User: Marina888UserModel,
          BotUser: BotUserModel,
          BlockedReferralUser: BlockedReferralUserModel
        },
        tenant: Tenant
      } = this.context



      const { token: adminToken } = this.args

      const encryptedText = Buffer.from(adminToken, 'base64');
      const keyID = LOGIN_AS_USER_KEY;

      // Split the encrypted text and IV
      const parts = encryptedText.toString('utf8').split('::');

      const encrypted = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');

      const method = 'aes-256-cbc';
      const key = Buffer.from(keyID, 'base64');

      // Create decipher object
      const decipher = crypto.createDecipheriv(method, key, iv);

      // Decrypt the data
      let decrypted = decipher.update(encrypted, 'base64', 'utf8');
      decrypted += decipher.final('utf8');

      const decoded = JSON.parse(decrypted)
      if (!decoded.id) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_TOKEN', language) }
        throw err
      }
      let now = Math.floor(Date.now() / 1000)
      if(parseInt(decoded.exp) < now) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('TOKEN_EXPIRED', language) }
        throw err
      }

      const whereCondition = {
        tenantId: Tenant.id,
        id: (decoded.id)

      }

       user = await UserModel.findOne({ where: whereCondition } , { attributes: ['id',
        'userName', 'email', 'phone', 'firstName',
        'lastName', 'phoneVerified', 'emailVerified',
        'createdAt', 'passwordUpdatedAt', 'forceResetPassword', 'active',
        'tenantId', 'parentType', 'parentId'
      ] })

      const environment = config.get('env')
    // get tenant theme settings
    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['userLoginType', 'maxAttempts', 'attemptExpiryTime', 'passwordResetDays', 'idleScreenLogoutTime']
    })

      if (user) {
        const smarticoUserId = await getUserSmarticoId(user?.id, Tenant.id, environment, Marina888UserModel)
        user.smarticoUserId = smarticoUserId
      }
      // if (((environment === ENVIORNMENT.STAGING && Tenant.id === TENANT_IDS.MARINA_888.STAGE) ||
      //   (environment === ENVIORNMENT.PRODUCTION && Tenant.id === TENANT_IDS.MARINA_888.PROD)) && user && !user.encryptedPassword) {
      //   return {
      //     user: {
      //       id: user.id,
      //       userName: user.userName,
      //       email: user.email,
      //       phone: user.phone,
      //       firstName: user.firstName,
      //       lastName: user.lastName,
      //       phoneVerified: user.phoneVerified,
      //       emailVerified: user.emailVerified,
      //       createdAt: user.createdAt,
      //       smarticoUserId: user.smarticoUserId
      //     },
      //     message: translate('CHANGE_PASSWORD_FORCEFULLY', language),
      //     success: false
      //   }
      // }


      const timeDiff = Math.abs(new Date() - user?.createdAt)
      const dayDiff = timeDiff && Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
      let lastPasswordChangeFlag = false
      /**
       * Check if user creation date in greater then reset days
       * Check if password hasnt been changed in past few days equal to reset days
       */

      if (user?.passwordUpdatedAt) {
        const lastPasswordUpdateTime = Math.abs(new Date() - user.passwordUpdatedAt)
        const lastPasswordUpdateDayDiff = Math.ceil(lastPasswordUpdateTime / (1000 * 60 * 60 * 24))
        lastPasswordChangeFlag = lastPasswordUpdateDayDiff < tenantTheme.passwordResetDays
      }

      const condition = (tenantTheme?.passwordResetDays && dayDiff >= tenantTheme.passwordResetDays && !lastPasswordChangeFlag) || user?.forceResetPassword



      if (!user) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_USERNAME', language) }
        throw err
      }

      if (!user.active) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
        throw err
      }
    // Impersonated user EMAIL_NOT_VERIFIED check not required
    // if ((tenantTheme.userLoginType !== LOGIN_TYPE.MOBILE) && (!user.emailVerified)) {
    //   const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('EMAIL_NOT_VERIFIED', language) }
    //   throw err
    // }

      const credentials = await this.context.databaseConnection.TenantCredential.findOne({
        attributes: ['key', 'value'],
        where: {
          key: 'APP_JWT_SECRET_KEY',
          tenantId: this.context.tenant.id
        },
        raw: true
      })

      const authConfig = config.getProperties().auth
      const resToken = await jwt.sign({ id: user.id }, credentials.value, { expiresIn: IMPERSONATED_USER_EXPIRY_TIME })


      const token = jwt.sign({
        id: user.id, impersonated: true

      }, authConfig.jwt_secret, {
        expiresIn: IMPERSONATED_USER_EXPIRY_TIME
      })
      user.isAdminUser = true
      const isBotUser = await BotUserModel.findOne({ where: { userId: user.id } })
      user.isBotUser = isBotUser ? true : false

      // Find a blocked referral user based on tenant ID and block conditions
      const blockedReferralCode = await BlockedReferralUserModel.findOne({
        where: {
          tenantId: Tenant.id, // Ensure the record belongs to the correct tenant
          [Op.or]: [
            // Check if the referral user is blocked as a normal user
            { blockId: user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

            // Check if the referral user's parent (agent) is blocked
            { blockId: user?.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
          ]
        }
      });

      user.isReferralCodeBlocked = !!blockedReferralCode;

      return { token, user, resToken, shouldPasswordUpdate: !!condition, idle_screen_logout_time: tenantTheme.idleScreenLogoutTime, success: true , isAdminUser: true}
    } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, user)
        throw new AuthenticationError(ERORR_TYPE.INVALID_REQUEST)
      }
    }
  }
}
