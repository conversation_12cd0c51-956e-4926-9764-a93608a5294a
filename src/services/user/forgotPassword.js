import { ApolloError, AuthenticationError, ForbiddenError, UserInputError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { v4 as uuid } from 'uuid'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, ERORR_TYPE, PHONE_CODE, SMS_GATEWAY } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
import { sendEmailThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'
const constraints = {}

/**
 * Provides service for the forgot password functionality
 * @export
 * @class ForgotPassword
 * @extends {ServiceBase}
 */
export default class ForgotPassword extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      tenant: Tenant,
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Wallet: WalletModel,
        Currency: CurrencyModel
      }
    } = this.context

    try {
    const whereObj = {
      tenantId: Tenant.id,
      [Op.or]: [
        {
          userName: this.args.userNameOrEmail
        },
        {
          email: this.args.userNameOrEmail
        },
        {
          phone: this.args.userNameOrEmail
        }
      ]
    }

    const user = await UserModel.findOne({
      where: whereObj,
      raw: true,
      include: {
        model: WalletModel,
        attributes: ['ownerId', 'currencyId'],
        include: {
          model: CurrencyModel,
          attributes: ['code']
        }
      }
    })

    if (!user) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    if (!user.emailVerified) {
      throw { errorType: ERORR_TYPE.FORBIDDEN_ERROR, errorMsg: translate('EMAIL_NOT_VERIFIED', language) }
    }

    if (!user.phoneVerified) {
      throw { errorType: ERORR_TYPE.FORBIDDEN_ERROR, errorMsg: translate('PHONE_NOT_VERIFIED', language) }
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['smsGateway', 'forgotPasswordOption', 'allowedModules', 'emailGateway'],
    })

    let otp
    if (tenantTheme.forgotPasswordOption === 'mobile' && tenantTheme.smsGateway === 'MessageCentralSMS') {
      try {
        const response = await messageCentralSendOTP(this.context, user.phone, user.phoneCode, user.id)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
      }
    } else {
      const userToken = {}
      userToken.token = uuid()
      userToken.userId = user.id
      userToken.tokenType = 'passwordReset'
      try {
        await UserTokenModel.create({ ...userToken })

        if (tenantTheme.forgotPasswordOption === 'email') {
          await sendEmailThroughSmtpRelay(
            user.id,
            userToken.token,
            user.email,
            this.context,
            userToken.tokenType,
            tenantTheme.emailGateway
          )
        }

        if (tenantTheme.forgotPasswordOption === 'mobile') {
          const environment = config.get('env')
          otp = (environment === 'production') ? Math.random().toString().substring(2, 8) : DEFAULT_OTP
          let result
          if (environment === 'production') {
            if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
              if (tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY)
                && user['Wallet.Currency.code'] === CURRENCY_CODE.LKR) {
                result = await sendKarixSMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
              } else {
                result = await sendFast2SMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
              }
            } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
              if (user.phoneCode !== PHONE_CODE.SRILANKA)
                throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
              result = await sendZMessengerSMSPhoneVerificationOTP(user.firstName, user.phone, otp, this.context)
            } else {
              result = await sendPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
            }
          }
          if (!result?.success) {
            // Handle the case when the operation fails
            await UserTokenModel.destroy({ where: { id: user.id, tokenType: 'passwordReset' } })
            throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_LOGIN_FAILED', language) }
          }
          // throw new Error(e)
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        if (error.message && error.message.includes('Unsupported OTP')) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
        }
        throw { errorType: ERORR_TYPE.FORBIDDEN_ERROR, errorMsg: translate('FORBIDDEN', language) }
      }
    }
    return true
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.FORBIDDEN_ERROR) {
        throw new ForbiddenError(error.errorMsg)
      }
      else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
