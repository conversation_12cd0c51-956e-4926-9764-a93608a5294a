import AuthenticationError, { ApolloError, UserInputError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ERORR_TYPE, QUEUE_WORKER_CONSTANT, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const Sequelize = require('sequelize')

const constraints = {
  firstName: {
    type: 'string',
    // format: {
    //   pattern: '^[a-zA-Z ]*$',
    //   message: 'Can only contains letters'
    // },
    length: {
      minimum: 2,
      maximum: 50
    }
  },
  lastName: {
    type: 'string',
    // format: {
    //   pattern: '^[a-zA-Z ]*$',
    //   message: 'Can only contains letters'
    // },
    length: {
      minimum: 2,
      maximum: 50
    }
  },
  dateOfBirth: {
    type: 'string'
  },
  signInIp: {
    type: 'string'
  },
  gender: {
    type: 'string',
    inclusion: ['male', 'female', 'other']
  },
  otpPreference: {
    type: 'integer'
  }
}

/**
 * Provides service for the updating profile functionality
 * @export
 * @class UpdateProfile
 * @extends {ServiceBase}
 */
export default class UpdateProfile extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        QueueLog: QueueLogModel,
        UserPreferenceType: UserPreferenceTypeModel
      },
      tenant: Tenant,
      auth: { id }
    } = this.context

    try {
    if (this.args.otpPreference && this.args.isOtpUpdate) {
      const [record, created] = await UserPreferenceTypeModel.findOrCreate({
        where: { tenantId: Tenant.id, userId: id, preferenceType: 'otp' },
        defaults: { value: this.args.otpPreference }
      })
      if (!created) {
        await record.update({ value: this.args.otpPreference })
      }
      return this.args // we are using same api for updating otp preference and profile
    }
    if (this.args?.userName)
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_NAME_NOT_MODIFIED', language) }

    const dateOfBirth = this.args?.dateOfBirth
    const nationalIdExist = (this.args.nationalId)?.toLowerCase()

    if (dateOfBirth && new Date(new Date().setUTCFullYear(new Date().getUTCFullYear() - 18)) < new Date(dateOfBirth)) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_ABOVE_18_YEARS', language) }
    }

    if (nationalIdExist) {
      const userExist = await UserModel.findOne({
        attributes: ['id', 'nationalId'],
        where: {
          nationalId: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col('national_id')), nationalIdExist),
          tenantId: Tenant.id,
          id: {
            [Op.ne]: id
          }
        }
      })

      if (userExist) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NATIONAL_ID_ALREADY_EXISTS', language) }
      }
    }

    const user = await UserModel.update(this.args, { where: { id }, individualHooks: true })

    // smartigo code
    let smartigoTenants
    let smartiGoQueueLog
    if (config.get('env') === 'production') {
      smartigoTenants = SMARTIGO_TENANTS.PROD
    } else {
      smartigoTenants = SMARTIGO_TENANTS.STAGE
    }

    if (smartigoTenants && smartigoTenants.includes(Tenant.id)) {
      const smartiGoObject = {
        type: QUEUE_WORKER_CONSTANT.SMARTICO_USER,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [parseInt(id)],
        tenantId: parseInt(Tenant.id)
      }

      smartiGoQueueLog = await QueueLogModel.create(smartiGoObject)
    }

    if (smartiGoQueueLog) {
      try {
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: smartiGoQueueLog.id } }
        )
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      }
    }

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    return this.args
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
