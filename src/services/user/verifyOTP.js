import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
// import bcrypt from 'bcrypt'
import md5 from 'md5'
import { Op } from 'sequelize'
import { ERORR_TYPE, LOGIN_TYPE, PROD_TENANTS_MESSAGE_CENTRAL_SMS, STAGE_TENANTS_MESSAGE_CENTRAL_SMS } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

const constraints = {
  token: {
    type: 'string'
  },
  newPassword: {
    type: 'string'
  },
  phone: {
    type: 'string'
  },
  phoneCode: {
    type: 'string'
  },
  email: {
    type: 'string'
  },
  otpPreference: {
    type: 'integer'
  },
  newLoginPin: {
    type: 'string'
  },
}

/**
 * Provides service for the verification of password token
 * @export
 * @class VerifyPasswordToken
 * @extends {ServiceBase}
 */
export default class VerifyOTP extends ServiceBase {
  get constraints() {
    return constraints
  }

  async run() {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          User: UserModel,
          UserToken: UserTokenModel,
          UserLoginPin: UserLoginPinModel
        },
        tenant: Tenant
      },
      args: { token }
    } = this

    try {
      const isMobileLogin = this.args.otpPreference === LOGIN_TYPE.MOBILE
      let whereCondition = {
        tenantId: Tenant.id,
        [Op.or]: [
          isMobileLogin ? { phone: this.args.phone, phoneCode: this.args.phoneCode } : { email: this.args.email }
        ]
      }
      const user = await UserModel.findOne({
        attributes: ['id', 'phone', 'phoneCode', 'tenantId'],
        where: whereCondition
      })

      if (!user) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
      }

      const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS
      const tokenType = this.args.loginType == 'resetLoginPin' ? 'resetLoginPin' : 'resetPassword'

      if (eligibleTenant.includes(Tenant.id)) {
        try {
          const response = await messageCentralVerifyOTP(this.context, this.args.token, user.phone, user.phoneCode, user.id)
          if (!response) {
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
          }
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
        }
      } else {
        const currentDate = new Date()
        const expirationDate = new Date(currentDate.setHours(currentDate.getHours() - 3))
        const validToken = await UserTokenModel.findOne({
          where: {
            [Op.and]: [
              { userId: user.id },
              { tokenType: tokenType },
              { token: md5(token) },
              { updatedAt: { [Op.gt]: expirationDate.toLocaleString() } }
            ]
          }
        })

        if (!validToken) {
          throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_OTP', language) }
        }
      }

      if (tokenType == 'resetLoginPin') {

        const loginPin = Buffer.from(this.args?.newLoginPin, 'base64').toString('ascii')

        const newHashedLoginPin = md5(loginPin)
        const userLoginPinDetails = await UserLoginPinModel.findOne({ where: { userId: user.id }, raw: true })
        if (userLoginPinDetails) {
          await UserLoginPinModel.update({ encryptedLoginPin: newHashedLoginPin }, {
            where: {
              userId: user.id
            }
          })
        }
        else {
          await UserLoginPinModel.create({
            userId: user.id,
            encryptedLoginPin: newHashedLoginPin
          })
        }

        return {
          status: true,
          data: translate('LOGIN_PIN_UPDATED_SUCCESSFULLY', language)
        }
      } else {
        const password = Buffer.from(this.args?.newPassword, 'base64').toString('ascii')
        const newHashedPassword = md5(password)
        await UserModel.update({ encryptedPassword: newHashedPassword, passwordUpdatedAt: sequelize.fn('NOW') }, {
          where: {
            id: user.id
          }
        })

        return {
          status: true,
          data: translate('PASSWORD_UPDATED_SUCCESSFULLY', language)
        }
      }

    } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
    }
  }
}
