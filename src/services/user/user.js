import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import { ALLOWED_PERMISSIONS, BONUS_STATUS, BONUS_TYPES, DEMO_LOGIN_USER_CREDENTIAL, REFERRAL_BLOCK_TYPE } from '../../common/constants'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per from '../../common/errorLog'
import { getUserSmarticoId } from '../../common/getUserSmarticoId'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'


/**
 * get user
 * @export
 * @class User
 * @extends {ServiceBase}
 */
export default class User extends ServiceBase {
  async run () {
    const {
      User: UserModel,
      UserBonus: UserBonusModel,
      Marina888User: Marina888UserModel,
      UserPreferenceType: UserPreferenceTypeModel,
      AdminUsersAdminRole: AdminUsersAdminRoleModel,
      TenantThemeSetting: TenantThemeSettingModel,
      UserReferralCode: UserReferralCodeModel,
      BotUser: BotUserModel,
      BlockedReferralUser: BlockedReferralUserModel

    } = this.context.databaseConnection
    const {
      tenant: { id: tenantId },
      req: { headers: { language } }
    } = this.context

    try {
    const environment = config.get('env')


    const user = await UserModel.findOne({
      where: {
        id: this.context.auth.id,
      },
      include: [
        {
          model: this.context.databaseConnection.UserDocument,
        },
        {
          model: UserReferralCodeModel,
          where: { tenantId: tenantId },
          attributes: ['referralCode'],
          required: false,
        },
      ],
    });

    user.referralCode = user?.UserReferralCode?.referralCode  || ''

    if (user?.email) {
      user.email = user.email?.split('@').map((item, idx) => {
        if (idx === 0) {
          return item[0] + '*'.repeat(item.length - 1)
        }
        if (idx === 1) {
          return item.split('.').map((piece, idx2) => idx2 === 0 ? '*'.repeat(piece.length) : piece).join('.')
        }
      }).join('@')
    }

    user.phone = user.phone ? '*'.repeat(8) + user.phone?.substring(8, user.phone?.length) : null


    const smarticoUserId = await getUserSmarticoId(user?.id, user.tenantId, environment, Marina888UserModel)
    user.smarticoUserId = smarticoUserId

    // check user document is verified or not
    user.isDocumentVerified = false
    if (user?.dataValues?.UserDocuments.length > 0) {
      const result = user.dataValues.UserDocuments.filter((item) => {
        return item.status !== 'approved'
      }).map((item) => {
        return item
      })
      user.isDocumentVerified = !(result.length > 0)
    }

    const userActiveDepositBonus = await UserBonusModel.findOne({
      where: {
        status: BONUS_STATUS.ACTIVE,
        kind: BONUS_TYPES.DEPOSIT,
        userId: user.id,
        expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        bonusAmount: { [Op.ne]: 0 },
        rolloverBalance: { [Op.ne]: 0 }
      }
    })

    user.activeDepositBonus = !!userActiveDepositBonus

    user.isDemoUser = parseInt(user?.tenantId) === DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID ? true : false;
    user.isAdminUser = this.context?.auth?.impersonated || false

    const isBotUser = await BotUserModel.findOne({ where: { userId: user.id } })
    user.isBotUser = isBotUser ? true : false

    // user otp preference
    const userOtpPreference = await UserPreferenceTypeModel.findOne({
      attributes : ['value'],
      where: {
        tenantId: user.tenantId,
        userId: user.id,
        preferenceType: 'otp'
      }
    })

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: user.tenantId
      },
      attributes: [ 'allowedModules']
    })
    const isFinancialActivityEnabledPermissionCheck = tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.DISABLEAGENTUSER) ? true : false
    user.isFinancialActivityEnabled = true
    if(isFinancialActivityEnabledPermissionCheck && user.parentId){
    const adminUserRole = await AdminUsersAdminRoleModel.findOne(
      {
        where: {
          adminUserId: user.parentId
        },
        attributes: ['adminRoleId'],
        raw:true
      }
    )
    user.isFinancialActivityEnabled = adminUserRole?.adminRoleId === '1' ? true : false  // 1 for admin role
  }
    if (userOtpPreference) user.otpPreference = userOtpPreference.value

      // Find a blocked referral user based on tenant ID and block conditions
      const blockedUser = await BlockedReferralUserModel.findOne({
        where: {
          tenantId: user.tenantId, // Ensure the record belongs to the correct tenant
          [Op.or]: [
            // Check if the referral user is blocked as a normal user
            { blockId: user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

            // Check if the referral user's parent (agent) is blocked
            { blockId: user.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
          ]
        }
      });

      user.isReferralCodeBlocked = !!blockedUser

    return user
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: this.context.auth.id, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
