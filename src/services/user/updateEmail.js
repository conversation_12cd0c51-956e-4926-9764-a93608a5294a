import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, ERORR_TYPE, LOGIN_TYPE, PHONE_CODE, SMS_GATEWAY } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'

const constraints = {
  newEmail: {
    type: 'string',
    presence: { message: 'New email is required' },
    email: {
      message: 'Invalid email'
    }
  }
}

/**
 * Provides service for the updating email functionality
 * @export
 * @class UpdateEmail
 * @extends {ServiceBase}
 */
export default class UpdateEmail extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Wallet: WalletModel,
        Currency: CurrencyModel,
        UserPreferenceType: UserPreferenceTypeModel
      },
      tenant: Tenant,
      auth: { id: userId }
    } = this.context

    try {
    await checkImpersonatedAccess(this.context)

    const user = await UserModel.findOne({
      where: { id: userId },
      include: {
        model: WalletModel,
        attributes: ['ownerId', 'currencyId'],
        include: {
          model: CurrencyModel,
          attributes: ['code']
        }
      }
    })

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    if (user.dataValues.email === this.args.newEmail) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('SAME_AS_PREVIOUS_EMAIL', language) }
    }

    // if (!user.emailVerified) {
    //   throw new AuthenticationError(translate('PREVIOUS_EMAIL_NOT_VERIFIED', language))
    // }

    const emailExist = await UserModel.findOne({ where: { email: this.args.newEmail, tenantId: Tenant.id }, raw: true })

    if (emailExist) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('EMAIL_ALREADY_EXIST', language) }
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['smsGateway', 'allowedModules', 'userLoginType', 'emailGateway']
    })

    // Determine OTP login preference:
    // - If login type is BOTH (email or phone), fetch the user's specific OTP preference from the database.
    // - If no preference is found, send to EMAIL.
    // - If login type is not BOTH, use the tenant-wise setting directly.

    let otpPreference = tenantTheme?.userLoginType || LOGIN_TYPE.EMAIL, otp;

    if (tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) {
      const userPreference = await UserPreferenceTypeModel.findOne({
        attributes: ['value'],
        where: {
          tenantId: Tenant.id,
          userId: user?.dataValues?.id,
          preferenceType: 'otp'
        },
        raw: true,
      });

      otpPreference = userPreference?.value
        ? +userPreference.value
        : LOGIN_TYPE.EMAIL;
    }

    if (tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) {
      try {
        const response = await messageCentralSendOTP(this.context, user?.phone, user?.phoneCode, userId)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
      }
    } else {
      const environment = config.get('env')
      otp = (environment === 'production') ? Math.random().toString().substring(2, 8) : DEFAULT_OTP

      const [record, created] = await UserTokenModel.findOrCreate({
        where: { tenantId: Tenant.id, userId: user.dataValues.id, tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email' },
        defaults: { token: md5(otp) }
      })
      if (!created) {
        await record.update({ token: md5(otp) })
      }

      const primaryEmail = this.args?.newEmail || user?.email;

      try {
        if (environment === 'production' && otpPreference === LOGIN_TYPE.MOBILE) {
          if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
            if ((tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY)) &&
              (user && user.Wallet && user.Wallet.Currency && user.Wallet.Currency.code === CURRENCY_CODE.LKR)) {
              await sendKarixSMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
            } else {
              await sendFast2SMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
            }
          } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
            if (user.phoneCode !== PHONE_CODE.SRILANKA)
              throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
            await sendZMessengerSMSPhoneVerificationOTP(user.firstName, user.phone, otp, this.context)
          } else {
            await sendPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
          }
        } else if (environment === 'production' && otpPreference === LOGIN_TYPE.EMAIL){
          await sendOtpThroughSmtpRelay(otp, primaryEmail, this.context, false, tenantTheme.emailGateway)
        }
      } catch (error) {
        await UserTokenModel.destroy({ where: { id: record?.id, tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId: Tenant.id } })
        await ErrorLogHelper.logError(error, this.context, user)
        // throw new AuthenticationError(translate('INVALID_PHONE_NUMBER', language))
        if (error.message && error.message.includes('Unsupported OTP')) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
        }
      }
    }

    return {
      data: translate(otpPreference === LOGIN_TYPE.MOBILE ? 'OTP_SENT' : 'EMAIL_OTP_SENT', language),
      user: user.dataValues
    }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
