import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
// import bcrypt from 'bcrypt'
import md5 from 'md5'
import { Op } from 'sequelize'
import { ERORR_TYPE, PROD_TENANTS_MESSAGE_CENTRAL_SMS, STAGE_TENANTS_MESSAGE_CENTRAL_SMS } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const constraints = {
  id: {
    type: 'string'
  },
  token: {
    type: 'string'
  }
}

/**
 * Provides service for the verification of password token
 * @export
 * @class VerifyPasswordToken
 * @extends {ServiceBase}
 */
export default class VerifyPasswordToken extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          User: UserModel,
          UserToken: UserTokenModel
        },
        tenant: { id: tenantId }
      },
      args: { id, password: newPassword, token }
    } = this

    try {
    const user = await UserModel.findOne({ where: { id: this.id, tenantId } })
    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS
    if (eligibleTenant.includes(tenantId)) {
      try {
        const response = await messageCentralVerifyOTP(this.context, this.args.token, user.phone, user.phoneCode, user.id)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
      }
    } else {
      const currentDate = new Date()
      const expirationDate = new Date(currentDate.setHours(currentDate.getHours() - 3))
      const validToken = await UserTokenModel.findOne({
        where: {
          [Op.and]: [
            { userId: id },
            { tokenType: 'passwordReset' },
            { token: token },
            { updatedAt: { [Op.gt]: expirationDate } }
          ]
        }
      })

      if (!validToken) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('TOKEN_EXPIRED', language) }
      }
      await UserTokenModel.destroy({
        where: {
          [Op.and]: [
            { userId: id },
            { token: token },
            { tokenType: 'passwordReset' }
          ]
        }
      })
    }

    const password = Buffer.from(newPassword, 'base64').toString('ascii')
    // const newHashedPassword = await bcrypt.hash(newPassword, 12)
    const newHashedPassword = md5(password)
    let dateNowTime = new Date();
     dateNowTime = dateNowTime.now;
    await UserModel.update({ encryptedPassword: newHashedPassword, passwordUpdatedAt: dateNowTime }, {
      where: {
        id: user.id
      }
    })

    return user.dataValues
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: this.id, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
