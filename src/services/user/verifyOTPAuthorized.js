import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
// import bcrypt from 'bcrypt'
import md5 from 'md5'
import { Op } from 'sequelize'
import { ERORR_TYPE, PROD_TENANTS_MESSAGE_CENTRAL_SMS, STAGE_TENANTS_MESSAGE_CENTRAL_SMS, VERIFICATION_TYPE } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const constraints = {
  token: {
    type: 'string'
  },
  type: {
    type: 'string'
  }
}

/**
 * Provides service for the verification of password token
 * @export
 * @class VerifyPasswordToken
 * @extends {ServiceBase}
 */
export default class VerifyOTPAuthorized extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          User: UserModel,
          UserToken: UserTokenModel,
          UserBankDetails: UserBankDetailsModel
        },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { token, type, userBankId }
    } = this

    try {
    const user = await UserModel.findOne({ attributes: ['id'], where: { id: userId } })

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS

    if (eligibleTenant.includes(tenantId)) {
      try {
        const response = await messageCentralVerifyOTP(this.context, this.args.token, user.phone, user.phoneCode, userId)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
      }
    } else {
      const currentDate = new Date()
      const expirationDate = new Date(currentDate.setHours(currentDate.getHours() - 3))
      const tokenType = type === VERIFICATION_TYPE.VERIFICATION ? 'resetPassword' : 'otpVarify'
      const validToken = await UserTokenModel.findOne({
        attributes: ['id'],
        where: {
          [Op.and]: [
            { userId: userId },
            { tokenType: tokenType },
            { token: md5(token) },
            { updatedAt: { [Op.gt]: expirationDate.toLocaleString() } }
          ]
        }
      })

      if (!validToken) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_OTP', language) }
      }

      UserTokenModel.destroy({ where: { userId, token: md5(token), tokenType } })
    }

    let userBankDetails = null
    if (userBankId) {
      userBankDetails = await UserBankDetailsModel.findOne({
        attributes: ['id', 'bankName', 'bankIfscCode', 'accountNumber', 'name', 'phoneNumber', 'phoneCode', 'status'],
        where: {
          id: userBankId,
          tenantId,
          userId
        }
      })
    }

    return {
      status: true,
      data: translate('OTP_VERIFIED_SUCCESSFULLY', language),
      userBankDetails
    }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
