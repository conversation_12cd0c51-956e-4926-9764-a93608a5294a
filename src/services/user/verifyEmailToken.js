import AuthenticationError, { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { BONUS_TYPES, ERORR_TYPE, QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

const constraints = {
  id: {
    type: 'string'
  },
  token: {
    type: 'string'
  }
}

/**
 * Provides service for the verification of email functionality
 * @export
 * @class VerifyEmailToken
 * @extends {ServiceBase}
 */
export default class VerifyEmailToken extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        req: { headers: { language } },
        tenant: { id: tenantId },
        databaseConnection: {
          User: UserModel,
          UserToken: UserTokenModel,
          QueueLog: QueueLogModel,
          UserReferralCode: UserReferralCodeModel
        }
      },
      args: { token, id }
    } = this

    try {
    const currentDate = new Date()
    const expirationDate = new Date(currentDate.setHours(currentDate.getHours() - 3))
    const validToken = await UserTokenModel.findOne({
      where: {
        [Op.and]: [
          { userId: id },
          { tokenType: 'email' },
          { token: token },
          { updatedAt: { [Op.gt]: expirationDate } }
        ]
      }
    })

    if (!validToken) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('TOKEN_EXPIRED', language) }
    }
    const user = await UserModel.findOne({ where: { id: this.id },include: [
      {
        model: UserReferralCodeModel,
        where: { tenantId: context.tenant.id },
        attributes: ['referralCode'],
        required: false
      },
    ], })

    await UserTokenModel.destroy({
      where: {
        [Op.and]: [
          { userId: id },
          { token: token },
          { tokenType: 'email' }
        ]
      }
    })
    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    user.referralCode = user?.UserReferralCode?.referralCode  || ''

    await UserModel.update({ emailVerified: true }, {
      where: {
        id: user.id
      },
      individualHooks: true
    })

    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.BONUS,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [
        {
          userId: user.id,
          tenantId: this.context.tenant.id,
          bonusType: BONUS_TYPES.JOINING
        }
      ]
    }

    const queueLog = await QueueLogModel.create(queueLogObject)

    if (queueLog.id) {
      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, { QueueLog: { queueLogId: queueLog.id } })
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
      }
    }

    return user.dataValues
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
