import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { sign, verify } from 'jsonwebtoken'
import { ERORR_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import keyValueTo<PERSON><PERSON> from '../../lib/keyValueToJSON'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for the refresh token api in the live-ezugi
 * @export
 * @class RefreshToken
 * @extends {ServiceBase}
 */

const constraints = {
  resToken: {
    type: 'string'
  },
  userId: {
    type: 'string'
  }
}

/**
 *
 *
 * @export
 * @class RefreshToken
 * @extends {ServiceBase}
 */
export default class RefreshToken extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    try {
    const { TenantCredential: TenantCredentialModel } = this.context.databaseConnection

    const ezugiCredentials = await keyValueTo<PERSON><PERSON>(TenantCredentialModel, ['APP_JWT_SECRET_KEY'], 'tenantId', this.context.tenant.id)

    try {
      await verify(this.args.resToken, ezugiCredentials.APP_JWT_SECRET_KEY)
    } catch (e) {
      await ErrorLogHelper.logError(e, this.context, { tenantId: this.context.tenant.id })
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: 'Expired token' }
    }

    const authConfig = config.getProperties().auth
    const newToken = await sign({ id: this.args.userId }, authConfig.jwt_secret, { expiresIn: authConfig.expiry_time })

    const resToken = await sign({ id: this.args.userId }, ezugiCredentials.APP_JWT_SECRET_KEY, { expiresIn: authConfig.res_token_expiry_time })
    return { token: newToken, resToken }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
