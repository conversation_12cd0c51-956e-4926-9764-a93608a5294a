import { ApolloError, AuthenticationError } from 'apollo-server-express'
import md5 from 'md5'
// import bcrypt from 'bcrypt'
import { ERORR_TYPE, TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
import UserLogout from './userLogout'

const constraints = {
  oldPassword: {
    type: 'string',
    presence: { message: 'Old password is required' }
  },
  newPassword: {
    type: 'string',
    presence: { message: 'New password is required' },
    length: {
      minimum: 5
    }
  }
}

/**
 * Provides service for the changePassword functionality
 * @export
 * @class ChangePassword
 * @extends {ServiceBase}
 */
export default class ChangePassword extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel
      },
      auth: { id: currentUserId },
      tenant: { id: tenantId }
    } = this.context

    try {
    const currentUser = await UserModel.findOne({ where: { id: currentUserId }, raw: true })
    this.oldPassword = Buffer.from(this.oldPassword, 'base64').toString('ascii')
    this.newPassword = Buffer.from(this.newPassword, 'base64').toString('ascii')

      let regExp
      const allowExtraSpecialCharsTenant = config.get('env') === 'production' ? TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.PROD : TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.STAGE
      if (allowExtraSpecialCharsTenant && allowExtraSpecialCharsTenant.includes(tenantId))
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!"#$%&\'()*+,-./:;<=>?_@(){}])[A-Za-z\\d!"#$%&\'()*+,-./:;<=>?_@(){}]{5,}$')
      else
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*["\\!#$%&\'()*+,-./:;<=>?_@])[A-Za-z\\d"\\!#$%&\'()*+,-./:;<=>?_@]{5,}$')
    if (!regExp.test(this.newPassword)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_PASSWORD_FORMAT', language) }
    }

    if (md5(this.oldPassword) !== currentUser.encryptedPassword) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('OLD_PASSWORD_INCORRECT', language) }
 }

    const newEncryptedPassword = md5(this.newPassword)
    await UserModel.update({ encryptedPassword: newEncryptedPassword, passwordUpdatedAt: sequelize.fn('NOW'), forceResetPassword: false }, { where: { id: currentUser.id } })
    UserLogout.execute(null, this.context)
    return currentUser
  } catch ( error ) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUserId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
