import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
// import bcrypt from 'bcrypt'
import md5 from 'md5'
import { Op } from 'sequelize'
import { checkBlackList } from '../../common/checkBlackList'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, DEFAULT_OTP_GENERATE_ATTEMPT, DEFAULT_OTP_GENERATE_ATTEMPT_EXPIRY_TIME, ERORR_TYPE, LOGIN_TYPE, OTP_ATTEMPTS_TYPES, OTP_TYPES, PHONE_CODE, SMS_GATEWAY, TENANT_SETTINGS_TYPE, USER_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import { MultipleOtpAttempts } from '../../common/mutlipleOtpAttempt'
import ServiceBase from '../../common/serviceBase'
import verifyRecaptcha from '../../common/verifyRecaptcha'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'

const constraints = {
  phone: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone format'
    },
    length: {
      minimum: 5,
      maximum: 15
    }
  },
  phoneCode: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone code format'
    }
  },
  resendOtp: {
    type: 'boolean'
  },
  loginType: {
    type: 'string'
  },
  email: {
    type: 'string'
  },
  otpPreference: {
    type: 'integer'
  },
  isKioskMachine: {
    type: 'boolean'
  },
  ip:{
    type: 'string'
  }
}

/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class MobileLogin extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {

    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Wallet: WalletModel,
        Currency: CurrencyModel,
        Blacklist: BlacklistModel,
        TenantSetting: TenantSettingModel
      },
      tenant: Tenant
    } = this.context

    const {
      otpAttempt,
      getOtpAttempt,
      getRemainigTime
    } = MultipleOtpAttempts()

    try {
      if (!(this.args.resendOtp) && !await verifyRecaptcha(this.context, grcptv3)) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SIGNATURE', language) }
      }

      const isMobileLogin = this.args.otpPreference === LOGIN_TYPE.MOBILE
      let whereCondition = {
        tenantId: Tenant.id,
        [Op.or]: [
          isMobileLogin ? { phone: this.args.phone, phoneCode: this.args.phoneCode } : { email: this.args.email }
        ]
      }

      const user = await UserModel.findOne({
        where: whereCondition,
        attributes: ['id', 'active', 'firstName', 'profileVerified', 'userType'],
        include: {
          model: WalletModel,
          attributes: ['ownerId', 'currencyId'],
          include: {
            model: CurrencyModel,
            attributes: ['code']
          }
        }
      })

      if (!user) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(translate(isMobileLogin ? 'PHONE_NOT_FOUND' : 'EMAIL_NOT_REGISTER', language)) }
      }
      if (!user.active) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
      }

      if (this.args.isKioskMachine && user.userType !== USER_TYPES.KIOSK && user.userType !== USER_TYPES.KIOSK_AND_ONLINE) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NOT_ALLOWED_PLEASE_LOGIN_ONLINE', language) }
      }

      if (!this.args.isKioskMachine && user.userType == USER_TYPES.KIOSK) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NOT_ALLOWED_PLEASE_LOGIN_AT_KIOSK', language) }
      }

      const checkBlackListUser = await checkBlackList(this.args.phone ? this.args.phone : '', this.args.phoneCode ? this.args.phoneCode : '', this.args.email ? this.args.email : '', '', Tenant.id, BlacklistModel, this.args.ip)
      if (checkBlackListUser) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('BLACK_LISTED', language) }
      }

      let tenantSettings = await TenantSettingModel.findAll({
        where: {
          tenantId: Tenant.id,
          type: TENANT_SETTINGS_TYPE.AUTHENTICATION_SETTINGS,
        },
        attributes: ['key', 'value'],
        raw: true,
      });

      tenantSettings = tenantSettings.reduce((acc, curr) => {
        acc[curr.key] = curr.value
        return acc
      }, {})

      const maxOtpGenerateAttempts = tenantSettings?.OTP_GENERATE_ATTEMPTS ? tenantSettings.OTP_GENERATE_ATTEMPTS : DEFAULT_OTP_GENERATE_ATTEMPT
      const otpGenerateAttemptExpiryTime = tenantSettings?.OTP_GENERATE_ATTEMPT_EXPIRY_TIME ? tenantSettings.OTP_GENERATE_ATTEMPT_EXPIRY_TIME : DEFAULT_OTP_GENERATE_ATTEMPT_EXPIRY_TIME

      const userIpKey = `${user.id}-${this.args.ip}`

      // get login attempts for user and device
      const attempts = await getOtpAttempt(userIpKey, OTP_ATTEMPTS_TYPES.GENERATE_OTP)

      if (attempts && attempts >= maxOtpGenerateAttempts) {
        const remainingTime = await getRemainigTime(userIpKey, OTP_ATTEMPTS_TYPES.GENERATE_OTP)
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(`Maxim attempts reached,please try again after ${Math.ceil(remainingTime / 60).toFixed(0)} mins`, language) }
        throw err
      }

      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: Tenant.id
        },
        attributes: ['userLoginType', 'smsGateway', 'allowedModules', 'emailGateway']
      })

      const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
      if (profileVerifiedPermissionCheck && !user.profileVerified) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('PROFILE_NOT_ACTIVE', language) }
      }

      let otp
      if (tenantTheme.smsGateway === 'MessageCentralSMS' && isMobileLogin) {
        try {
          const response = await messageCentralSendOTP(this.context, this.args.phone, this.args.phoneCode, user.id)
          if (!response) {
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
          }
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
        }
      } else {
        const environment = config.get('env');

        // Temporary adding for client testing on stage
        let sendBetshopSMSOTP = false
        if (Tenant.id == 185 && this.args.phoneCode == PHONE_CODE.SRILANKA) {
          sendBetshopSMSOTP =true
        } // For Temporary Basis to check SMS OTP

        otp = (environment === 'production') || sendBetshopSMSOTP ? Math.random().toString().substring(2, 8) : DEFAULT_OTP

        let tokenType = ''
        if (this?.args?.loginType === OTP_TYPES.RESET_PASSOWRD || this?.args?.loginType === OTP_TYPES.RESET_LOGIN_PIN) {
          tokenType = this?.args?.loginType
        }
        else if (isMobileLogin) {
          tokenType = OTP_TYPES.PHONE
        }
        else {
          tokenType = OTP_TYPES.EMAIL
        }
        try {
          whereCondition = {
            userId: user.id,
            tenantId: Tenant.id,
            tokenType: tokenType,
            ...(isMobileLogin
              ? { phone: this.args.phone, phoneCode: this.args.phoneCode }
              : { email: this.args.email })
          }
          if (tokenType === 'resetPassword' || tokenType === 'resetLoginPin') {
            await UserTokenModel.create({
              token: md5(otp),
              ...whereCondition
            })
          } else {
            const userToken = await UserTokenModel.findOne({
              attributes: ['id', 'token'],
              where: whereCondition
            })
            if (userToken) {
              userToken.token = md5(otp)
              await userToken.save()
            } else {
              await UserTokenModel.create({
                token: md5(otp),
                ...whereCondition
              })
            }
          }

          if ((environment === 'production' && isMobileLogin) || sendBetshopSMSOTP) {
            if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
              if ((tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY)) &&
                (user && user.Wallet && user.Wallet.Currency && user.Wallet.Currency.code === CURRENCY_CODE.LKR)) {
                await sendKarixSMSPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
              } else {
                await sendFast2SMSPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
              }
            } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
              if (this.args.phoneCode !== PHONE_CODE.SRILANKA)
                throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
              await sendZMessengerSMSPhoneVerificationOTP(user.firstName, this.args.phone, otp, this.context)
            } else {
              await sendPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
            }
          }
          else if (environment === 'production' && !isMobileLogin) {
            await sendOtpThroughSmtpRelay(otp, this.args.email, this.context, false, tenantTheme.emailGateway)
          }
        } catch (e) {
          console.error('Error in sending OTP:', e)
          await UserTokenModel.destroy({ where: whereCondition })
          await ErrorLogHelper.logError(e, this.context, user)
          if (e?.message && e?.message.includes('Unsupported OTP')) {
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
          } else {
            throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_LOGIN_FAILED', language) }
          }
        }
      }

      const success = true
      const phone = this.args.phone
      const phoneCode = this.args.phoneCode
      const email = this.args.email
      const otpPreference = this.args.otpPreference

      await otpAttempt(userIpKey, maxOtpGenerateAttempts, otpGenerateAttemptExpiryTime, OTP_ATTEMPTS_TYPES.GENERATE_OTP)

      return { success, phone, phoneCode, email, otpPreference }
    } catch (error) {
      console.log(error);
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
    }
  }
}
