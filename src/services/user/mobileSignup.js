import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
import { Op } from 'sequelize'
import { checkBlackList } from '../../common/checkBlackList'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, ERORR_TYPE, LOGIN_TYPE, PHONE_CODE, SMS_GATEWAY } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import ServiceBase from '../../common/serviceBase'
import verifyRecaptcha from '../../common/verifyRecaptcha'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'
const Sequelize = require('sequelize')

const constraints = {
  phone: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone format'
    },
    length: {
      minimum: 5,
      maximum: 15
    }
  },
  phoneCode: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid phone code format'
    }
  },
  userName: {
    type: 'string',
    presence: { message: 'User Name is required' }
  },
  email: {
    type: 'string'
  },
  currencyId: {
    type: 'string'
  },
  otpPreference: {
    type: 'integer'
  }
}

/**
 * Provides service for Mobile Signup
 * @export
 * @class MobileSignup
 * @extends {ServiceBase}
 */
export default class MobileSignup extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Currency: CurrencyModel,
        Blacklist: BlacklistModel
      },
      tenant: Tenant
    } = this.context
    try {
    this.args.userName = (this.args.userName).toLowerCase()
    if (!await verifyRecaptcha(this.context, grcptv3)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SIGNATURE', language) }
    }

    const emailExist = (this.args.email)?.toLowerCase?.()
    const nationalIdExist = (this.args.nationalId)?.toLowerCase()
    const phoneExist = this.args.phone

    let whereCondition = {
      [Op.or]: [
        {
          tenantId: Tenant.id,
          userName: Sequelize.where(
            Sequelize.fn('LOWER', Sequelize.col('user_name')),
            (this.args.userName).toLowerCase()
          )
        },
        phoneExist ? {
          tenantId: Tenant.id,
          phone: phoneExist
        } : null,
        emailExist ? {
          tenantId: Tenant.id,
          email: emailExist
        } : null,
        nationalIdExist ? {
          tenantId: Tenant.id,
          nationalId: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col('national_id')), nationalIdExist.toLowerCase())
        } : null
      ].filter(condition => condition !== null)
    }

    const user = await UserModel.findOne({
      attributes: ['id', 'nationalId', 'phone', 'email', 'tenantId'],
      where: whereCondition
    })

    if (user) {
      if (nationalIdExist && nationalIdExist === (user.nationalId)?.toLowerCase()) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NATIONAL_ID_ALREADY_EXISTS', language) }
      } else {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USERNAME_OR_EMAIL_OR_PHONE_ALREADY_EXIST', language) }
      }
    }

    const userNamePattern = new RegExp('^[a-zA-Z][a-zA-Z0-9]*$')
    if (!userNamePattern.test(this.args.userName)) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_USERNAME_FORMAT', language) }
    }

    const currencyExist = await CurrencyModel.findOne({ attributes: ['code'], where: { id: this.args.currencyId } })

    if (!currencyExist) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('CURRENCY_NOT_FOUND', language) }
    }

    const checkBlackListUser = await checkBlackList(this.args.phone ? this.args.phone : '', this.args.phoneCode ? this.args.phoneCode : '', this.args.email ? this.args.email : '', this.args.userName, Tenant.id, BlacklistModel, this.args.ip)
    if (checkBlackListUser) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('BLACK_LISTED', language) }
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['smsGateway', 'allowedModules', 'userLoginType', 'emailGateway']
    })

    let otp
    const otpPreference = tenantTheme?.userLoginType === LOGIN_TYPE.BOTH ? this.args.otpPreference : tenantTheme?.userLoginType
    if (tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) {
      try {
        const type = 'signup'
        const response = await messageCentralSendOTP(this.context, this.args.phone, this.args.phoneCode, null, type)
        if (!response)
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
      }
    } else {
      const environment = config.get('env')
      otp = (environment === 'production') ? Math.random().toString().substring(2, 8) : DEFAULT_OTP

      try {
        whereCondition = otpPreference === LOGIN_TYPE.MOBILE
          ? { tokenType: 'mobile_signup', phone: this.args.phone, phoneCode: this.args.phoneCode, tenantId: Tenant.id }
          : { tokenType: 'email_signup', email: this.args.email, tenantId: Tenant.id }

        const userToken = await UserTokenModel.findOne({
          attributes: ['id', 'token'],
          where: whereCondition
        })
        if (userToken) {
          userToken.token = md5(otp)
          await userToken.save()
        } else {
          await UserTokenModel.create({
            token: md5(otp),
            ...whereCondition
          })
        }

        if (environment === 'production' && otpPreference === LOGIN_TYPE.MOBILE) {
          if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
            if ((tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY)) &&
              (currencyExist && currencyExist.code === CURRENCY_CODE.LKR)) {
              await sendKarixSMSPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
            } else {
              await sendFast2SMSPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
            }
          } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
            if (this.args.phoneCode !== PHONE_CODE.SRILANKA)
              throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
            await sendZMessengerSMSPhoneVerificationOTP(this.args.userName, this.args.phone, otp, this.context)
          } else {
            await sendPhoneVerificationOTP(this.args.phone, this.args.phoneCode, otp, this.context)
          }
        }
        else if (environment === 'production' && otpPreference === LOGIN_TYPE.EMAIL) {
          await sendOtpThroughSmtpRelay(otp, this.args.email, this.context, false, tenantTheme.emailGateway)

        }
      } catch (e) {
        await ErrorLogHelper.logError(e, this.context, user)
        if (e.message && e.message.includes('Unsupported OTP')) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
        }
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_SIGN_UP_FAILED', language) }
      }
    }

    const success = true
    const phone = this.args.phone
    const phoneCode = this.args.phoneCode
    const userName = this.args.userName

    return { success, phone, phoneCode, userName }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
