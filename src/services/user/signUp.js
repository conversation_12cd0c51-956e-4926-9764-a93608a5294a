import { AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
// import bcrypt from 'bcrypt'
import { Op } from 'sequelize'
// import { v4 as uuid } from 'uuid'
import { checkBlackList } from '../../common/checkBlackList'
import { AFFILIATE, ALANBASE_EVENT_TYPES, ALLOWED_PERMISSIONS, BONUS_TYPES, ERORR_TYPE, LOGIN_TYPE, QUEUE_WORKER_CONSTANT, REFERRAL_EVENT, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL, TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD, VIP_CODE } from '../../common/constants'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import mobileLogin from '../../common/mobileLogin'
import ServiceBase from '../../common/serviceBase'
import verifyRecaptcha from '../../common/verifyRecaptcha'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
// import { sendEmailThroughSmtpRelay } from '../../lib/sendgridService'
import { v4 as uuidv4 } from 'uuid'
import ErrorLogHelper from '../../common/errorLog'
import { smsEmailUponSignup } from '../../common/smsEmailUponSignup'
import verifyPromoCode from '../../common/verifyPromoCode'
import verifyReferralCode from '../../common/verifyReferralCode'
const Sequelize = require('sequelize')

const constraints = {
  password: {
    type: 'string',
    presence: { message: 'Password is required' }
  },
  userName: {
    type: 'string'
  },
  currencyId: {
    type: 'string'
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  },
  ip: {
    type: 'string'
  },
  avatarImage: {
    type: 'string',
    presence: false
  },
  otpPreference: {
    type: 'integer'
  }
}

/**
 * Provides service for the sign up functionality
 * @export
 * @class SignUp
 * @extends {ServiceBase}
 */
export default class SignUp extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    this.context.sequelizeTransaction = await sequelize.transaction()
    try {
      const {
        req: { headers: { language, grcptv3 } },
        databaseConnection: {
          User: UserModel,
          UserToken: UserTokenModel,
          AdminUser,
          Wallet: WalletModel,
          AdminRole: AdminRoleModel,
          TenantThemeSetting: TenantThemeSettingModel,
          QueueLog: QueueLogModel,
          PlayerCategory: PlayerCategoryModel,
          PlayerCategoryLevel: PlayerCategoryLevelModel,
          Blacklist: BlacklistModel,
          UserPreferenceType: UserPreferenceTypeModel,
          AdminUsersAdminRole: AdminUsersAdminRoleModel,
          UserReferralCode: UserReferralCodeModel,
          UsersAffiliate: UsersAffiliateModel
        },
        tenant: Tenant,
        sequelizeTransaction
      } = this.context
      this.args.userName = (this.args.userName).toLowerCase()

      if (this.args.otp === '' && !await verifyRecaptcha(this.context, grcptv3)) {
        const t = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SIGNATURE', language) }
        throw t
      }

      const emailExist = (this.args.email)?.toLowerCase?.()
      const phoneExist = this.args.phone
      const dateOfBirth = this.args.dateOfBirth
      const nationalIdExist = (this.args.nationalId)?.toLowerCase()

      let parent = await AdminUser.findAll({
        attributes: ['id', 'kycRegulated', 'timezone'],
        where: {
          tenantId: Tenant.id
        },
        order: ['createdAt'],
        include: {
          model: AdminRoleModel,
          attributes: [],
          where: {
            name: 'owner'
          }
        },
        raw: true,
        nest: true
      })

      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: Tenant.id
        },
        attributes: ['userLoginType', 'smsGateway', 'allowedModules', 'emailGateway']
      })

      if (parent.length === 0) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('ADMIN_NOT_FOUND', language) }
        throw err
      }

      parent = parent[0]
      let whereCondition = {}

      // default value for otp preference is mobile when user login type is null
      const otpPreference = tenantTheme?.userLoginType === LOGIN_TYPE.BOTH
        ? this.args.otpPreference
        : tenantTheme?.userLoginType || LOGIN_TYPE.MOBILE
      const primaryIdentifierExist = otpPreference === LOGIN_TYPE.MOBILE ? phoneExist : emailExist
      if (!primaryIdentifierExist) {
        const primaryIdentifierExistThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate(otpPreference === LOGIN_TYPE.MOBILE ? 'PHONE_REQUIRED' : 'EMAIL_NOT_FOUND', language) }
        throw primaryIdentifierExistThrow
      }
      whereCondition = {
        [Op.or]: [
          {
            tenantId: Tenant.id,
            userName: Sequelize.where(
              Sequelize.fn('LOWER', Sequelize.col('user_name')),
              this.args.userName.toLowerCase()
            )
          },
          {
            tenantId: Tenant.id,
            [otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email']: primaryIdentifierExist
          },
          otpPreference === LOGIN_TYPE.MOBILE && emailExist ? {
            tenantId: Tenant.id,
            email: emailExist
          } : otpPreference !== LOGIN_TYPE.MOBILE && phoneExist ? {
            tenantId: Tenant.id,
            phone: phoneExist
          } : null
        ].filter(Boolean)
      }

      if (nationalIdExist) {
        whereCondition[Op.or].push({
          nationalId: Sequelize.where(Sequelize.fn('LOWER', Sequelize.col('national_id')), (this.args.nationalId).toLowerCase()),
          tenantId: Tenant.id
        })
      }

      const checkBlackListUser = await checkBlackList(this.args.phone ? this.args.phone : '', this.args.phoneCode ? this.args.phoneCode : '', this.args.email ? this.args.email : '', this.args.userName, Tenant.id, BlacklistModel, this.args.ip)
      if (checkBlackListUser) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('BLACK_LISTED', language) }
        throw err
      }

      if (new Date(new Date().setUTCFullYear(new Date().getUTCFullYear() - 18)) < new Date(dateOfBirth)) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_ABOVE_18_YEARS', language) }
        throw err
      }

      const user = await UserModel.findOne({ attributes: ['id', 'nationalId', 'phone', 'email'], where: whereCondition, paranoid: false })
      if (user) {
        if (nationalIdExist && nationalIdExist === (user.nationalId)?.toLowerCase()) {
          const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NATIONAL_ID_ALREADY_EXISTS', language) }
          throw err
        } else {
          const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USERNAME_OR_EMAIL_OR_PHONE_ALREADY_EXIST', language) }
          throw err
        }
      }

      if (this.args.userName === (this.args.nickName).toLowerCase()) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USERNAME_NICKNAME_MUST_BE_DIFFERENT', language) }
        throw err
      }
      this.args.parentId = parent.id
      this.args.dateOfBirth = this.args.dateOfBirth || null
      this.args.parentType = 'AdminUser'
      this.args.tenantId = Tenant.id
      this.args.kycDone = !parent.kycRegulated
      this.password = Buffer.from(this.password, 'base64').toString('ascii')

      let regExp
      const allowExtraSpecialCharsTenant = config.get('env') === 'production' ? TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.PROD : TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.STAGE
      if (allowExtraSpecialCharsTenant && allowExtraSpecialCharsTenant.includes(Tenant.id))
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!"#$%&\'()*+,-./:;<=>?_@(){}])[A-Za-z\\d!"#$%&\'()*+,-./:;<=>?_@(){}]{5,}$')
      else
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*["\\!#$%&\'()*+,-./:;<=>?_@])[A-Za-z\\d"\\!#$%&\'()*+,-./:;<=>?_@]{5,}$')
      if (!regExp.test(this.password)) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_PASSWORD_FORMAT', language) }
        throw err
      }

      const userNamePattern = new RegExp('^[a-zA-Z][a-zA-Z0-9]*$')
      if (!userNamePattern.test(this.args.userName)) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_USERNAME_FORMAT', language) }
        throw err
      }

      this.args.encryptedPassword = md5(this.password)

      // The base64 encoded affiliateToken string
      const { affiliateToken } = this.args
      if (affiliateToken) {
        const agentData = await AdminUser.findOne({
          attributes: ['id', 'affiliateStatus'],
          where: { affiliateToken }
        })
        if (!agentData) {
          const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NO_AFFILIATE_FOUND', language) }
          throw err
        }

        if (agentData.affiliateStatus) this.args.parentId = agentData.id
      }
      const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
      if (profileVerifiedPermissionCheck) {
        this.args.profileVerified = false
      }

      const skipOtpVerification = (tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SKIP_OTP_VERIFICATION)) || tenantTheme.userLoginType === null

      if (!skipOtpVerification) {
        //  OTP verification
        const { otp, phone, phoneCode, email } = this.args
        if (otp) {
          if (tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) {
            try {
              const type = 'signup'
              const response = await messageCentralVerifyOTP(this.context, otp, phone, phoneCode, null, type)
              if (!response) {
                const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('VERIFICATION_FAILED', language) }
                throw err
              }
            } catch (error) {
              await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
              const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('VERIFICATION_FAILED', language) }
              throw err
            }
          } else {
            const whereCondition = otpPreference === LOGIN_TYPE.MOBILE
              ? { token: md5(otp), tokenType: 'mobile_signup', phone, phoneCode, tenantId: Tenant.id }
              : { token: md5(otp), tokenType: 'email_signup', email, tenantId: Tenant.id }
            const verifyOtpToken = await UserTokenModel.findOne({
              attributes: ['id'],
              where: whereCondition,
              raw: true
            })

            if (!verifyOtpToken) {
              const errverifyOtpToken = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_OTP', language) }
              throw errverifyOtpToken
            }
            UserTokenModel.destroy({ where: whereCondition }, { transaction: sequelizeTransaction })
          }
        } else {
          const errThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('NO_OTP_FOUND', language) }
          throw errThrow
        }
      }

      if (this.args.vipKey) {
        const vipData = VIP_CODE[this.args?.vipKey] ? VIP_CODE[this.args?.vipKey] : null
        this.args.vipLevel = vipData ? vipData?.vipLevel : 0
      }


      const hasPlayerCategory = tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PLAYER_CATEGORIZATION)
      let subcategoryLevelData = null

      if (hasPlayerCategory) {
        subcategoryLevelData = await PlayerCategoryLevelModel.findOne({
          include: [{
            model: PlayerCategoryModel,
            where: {
              status: true,
              tenantId: Tenant.id
            },
            attributes: []
          }],
          order: [[PlayerCategoryModel, 'id', 'ASC'], ['id', 'ASC']],
          attributes: ['id']
        })

        // If no category or subcategory is found, subcategoryLevelData will be null
        this.args.playerCategoryLevel = subcategoryLevelData ? subcategoryLevelData?.id : null
      }

      const skipUserHook = true
      const newUser = await UserModel.create(this.args, { transaction: sequelizeTransaction, skipUserHook })
      await WalletModel.create({ ownerId: newUser.dataValues.id, ownerType: 'User', amount: '0', currencyId: this.args.currencyId }, { transaction: sequelizeTransaction })

      // entry in user preference table
      if (this.args.otpPreference && tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) {
        await UserPreferenceTypeModel.create({ tenantId: Tenant.id, userId: newUser.id, preferenceType: 'otp', value: this.args.otpPreference }, { transaction: sequelizeTransaction })
      }

      let referralCode = (this.args.userName).toUpperCase().substring(0, 4) + uuidv4().replace(/-/g, '').substring(0, 4).toUpperCase()

      const checkReferralCode = await UserReferralCodeModel.findOne({
        where: {
          referralCode,
          tenantId: Tenant.id
         },
        attributes: ['id'],
      })
      if (checkReferralCode?.id) {
        referralCode = (this.args.userName).toUpperCase().substring(0, 4) + uuidv4().replace(/-/g, '').substring(0, 6).toUpperCase()
      }

      await UserReferralCodeModel.create({ userId: newUser.id, tenantId: Tenant.id, referralCode }, { transaction: sequelizeTransaction })
      const bulkData = []

      if (this.args.promoCode ) {
        const isValidPromo = await verifyPromoCode(this.context, this.args.promoCode, this.args.currencyId)
        if (!isValidPromo) {
          const errThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_PROMOCODE', language) }
          throw errThrow
        }

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.BONUS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{
            bonusType: BONUS_TYPES.PROMO_CODE,
            userId: newUser.id,
            promoCode: this.args.promoCode,
            tenantId: Tenant.id
          }]
        }
        bulkData.push(queueLogObject)
      }

      // update only those where otp is verified
      if (!skipOtpVerification) {
        if (otpPreference === LOGIN_TYPE.MOBILE) { await newUser.update({ phoneVerified: true }, { transaction: sequelizeTransaction }) } else { await newUser.update({ emailVerified: true }, { transaction: sequelizeTransaction }) }
      }
      const joiningFlag = !!this.args.promoCode // If both the joining bonus and promo bonus are available, only the promo bonus will be awarded to the user.
      const responseObj = await mobileLogin(this.context, newUser.id, joiningFlag, this.args, bulkData)
      let token = responseObj.token
      let resToken = responseObj.resToken
      responseObj.user.referralCode = referralCode


      // smartigo code
      const smartigoTenants = config.get('env') === 'production' ? SMARTIGO_TENANTS.PROD : SMARTIGO_TENANTS.STAGE
      if (smartigoTenants && smartigoTenants.includes(Tenant.id)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.SMARTICO_USER,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [newUser.id],
          tenantId: parseInt(Tenant.id)
        }
        bulkData.push(smartiGoObject)
      }

      // alanbase entry
      const hasAlanBase = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ALANBASE)
      if (hasAlanBase && this.args.alanbaseClickId) {
        await UsersAffiliateModel.create({ userId: newUser.id, clickId: this.args?.alanbaseClickId, tenantId: Tenant?.id, affiliate : AFFILIATE.ALANBASE }, { transaction: sequelizeTransaction })
        const alanbaseObject = {
          type: ALANBASE_EVENT_TYPES.REGISTRATION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [newUser.id],
          tenantId: parseInt(Tenant.id)
        }
        bulkData.push(alanbaseObject)
      }

      // wynta entry
      const hasWynta = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WYNTA)
      if (hasWynta && this.args.wyntaClickId) {
        await UsersAffiliateModel.create({ userId: newUser.id, clickId: this.args?.wyntaClickId, tenantId: Tenant?.id, affiliate : AFFILIATE.WYNTA }, { transaction: sequelizeTransaction })
      }

      // referral code verification and Pushing to queue
      if (this.args.referralCode && tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REFERRAL_CODE)) {
        const referralDetails = await verifyReferralCode(this.context, newUser.id, this.args)

        if (!referralDetails.status) {
          const errThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_REFERRAL_CODE', language) }
          throw errThrow
        }

        if (referralDetails.status &&  referralDetails?.event == REFERRAL_EVENT.SIGNUP ) {
          const queueLogObject = {
            type: QUEUE_WORKER_CONSTANT.BONUS,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [{
              bonusType: BONUS_TYPES.REFERRAL_CODE,
              referralId: referralDetails.referral.id,
              referralSettingsId: referralDetails.referralSettingsId
            }]
          }
          bulkData.push(queueLogObject)
        }
      }

      // Add auto-activate bonus to queue if allowed.
      const isAutoActivateBonusModuleAlllowed = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.AUTO_ACTIVATE_BONUS);
      if (isAutoActivateBonusModuleAlllowed) {
        const autoActivateQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.BONUS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{
            bonusType: BONUS_TYPES.AUTO_BONUS_ACTIVATE,
            userId: newUser.id,
            skipKycCheck: true,
            timezone: parent?.timezone
          }]
        }
        bulkData.push(autoActivateQueueLogObject)
      }

      // referal code creation for the user
      const createdRecords = await QueueLogModel.bulkCreate(bulkData, { transaction: sequelizeTransaction })


      await this.context.sequelizeTransaction.commit()

      smsEmailUponSignup(tenantTheme, newUser, this.context) // here no need for waiting for sending thank you message
      if (createdRecords) {
        try {
          createdRecords.forEach(record => {
            this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
              QueueLog: { queueLogId: record.id }
            })
          })
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
        }
      }

      const isFinancialActivityEnabledPermissionCheck = !!tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.DISABLEAGENTUSER)
      responseObj.user.isFinancialActivityEnabled = true
      if (isFinancialActivityEnabledPermissionCheck && responseObj.user.parentId) {
        const adminUserRole = await AdminUsersAdminRoleModel.findOne(
          {
            where: {
              adminUserId: responseObj.user.parentId
            },
            attributes: ['adminRoleId'],
            raw: true
          }
        )
        responseObj.user.isFinancialActivityEnabled = adminUserRole?.adminRoleId === '1' // 1 for admin role
      }
      if (!responseObj.user.profileVerified) {
        token = null
        resToken = null
      }
      return { token, user: responseObj.user, resToken }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
        throw new UserInputError(translate('USER_SIGN_UP_FAILED'))
      }
    }
  }
}
