import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
import { ERORR_TYPE, LOGIN_TYPE, PROD_TENANTS_MESSAGE_CENTRAL_SMS, STAGE_TENANTS_MESSAGE_CENTRAL_SMS } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const constraints = {
  newEmail: {
    type: 'string',
    presence: { message: 'New email is required' },
    email: {
      message: 'Invalid email'
    }
  },
  otp: {
    type: 'string',
    format: {
      pattern: '^[0-9-]+$',
      message: 'Invalid OTP format'
    },
    presence: { message: 'Otp is required' },
    length: {
      minimum: 6,
      maximum: 6
    }
  }
}

/**
 * Provides service for changing mail of user by verifying otp
 * @export
 * @class emailOtpVerification
 * @extends {ServiceBase}
 */
export default class emailOtpVerification extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        UserPreferenceType: UserPreferenceTypeModel
      },
      auth: { id: userId },
      tenant: Tenant
    } = this.context

    try {
    const tenantTheme = await TenantThemeSettingModel.findOne({
      attributes: ['userLoginType', 'smsGateway'],
      where: { tenantId: Tenant.id }
    })

    const user = await UserModel.findOne({
      where: { id: userId, tenantId: Tenant.id }
    })

    // Determine OTP login preference:
    // - If login type is BOTH (email or phone), fetch the user's specific OTP preference from the database.
    // - If no preference is found, send to EMAIL.
    // - If login type is not BOTH, use the tenant-wise setting directly.

    let otpPreference = tenantTheme?.userLoginType || LOGIN_TYPE.EMAIL;

    if (tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) {
      const userPreference = await UserPreferenceTypeModel.findOne({
        attributes: ['value'],
        where: {
          tenantId: Tenant.id,
          userId: user?.id,
          preferenceType: 'otp'
        },
        raw: true,
      });

      otpPreference = userPreference?.value ? +userPreference.value : LOGIN_TYPE.EMAIL;
    }

    const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS

    if (eligibleTenant.includes(Tenant.id) && tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) {
      try {
        const response = await messageCentralVerifyOTP(this.context, this.args.otp, user.phone, user.phoneCode, userId)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
      }
    } else {
      const verifyPhoneToken = await UserTokenModel.findOne({
        where: { userId: user.id, token: md5(this.args.otp), tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId: Tenant.id },
        raw: true
      })

      if (!verifyPhoneToken) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_OTP', language) }
      }
      UserTokenModel.destroy({ where: { userId: user.id, token: md5(this.args.otp), tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId: Tenant.id } })
    }

    user.email = this.args.newEmail
    await user.save()
    user.dataValues.email = user.dataValues.email.split('@').map((item, idx) => {
      if (idx === 0) {
        return item[0] + '*'.repeat(item.length - 1)
      }
      if (idx === 1) {
        return item.split('.').map((piece, idx2) => idx2 === 0 ? '*'.repeat(piece.length) : piece).join('.')
      }
    }).join('@')

    return user.dataValues
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      }
      else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
