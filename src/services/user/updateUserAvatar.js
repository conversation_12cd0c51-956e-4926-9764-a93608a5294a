import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { v4 as uuid } from 'uuid'
import { ALLOWED_FILE_TYPES, ERORR_TYPE, FILE_UPLOAD_ERROR_CODES, HTTP_STATUS, MAX_IMAGE_SIZE } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { s3 } from '../../lib/aws-s3.config'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'

const constraints = {
  avatarImage: {
    type: 'string',
    presence: { allowEmpty: true }
  },
  file: {
    type: 'object',
    presence: false
  }
}

export default class UpdateUserAvatar extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const { avatarImage, file } = this.args
    const {
      req: { headers: { language } },
      databaseConnection: { User: UserModel },
      auth: { id: userId },
      sequelizeTransaction,
      tenant: Tenant
    } = this.context

    try {
    // Fetch the user
    const user = await UserModel.findByPk(userId)

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    // Check impersonated access
    await checkImpersonatedAccess(this.context)

    let avatarImageToSave = null

    // Check if either avatarImage or file is provided
    if (!avatarImage && !file) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('INVALID_FILE_TYPE', language), errorCode: HTTP_STATUS.BAD_REQUEST }
    }

    // Handle file upload if file is provided
    if (file) {
      const ele = await this.args.file
      const stream = ele.createReadStream()
      const fileType = ele.mimetype.split('/')[1]

      // Validate the file type
      if (!ALLOWED_FILE_TYPES.includes(fileType)) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('INVALID_FILE_TYPE', language), errorCode: HTTP_STATUS.BAD_REQUEST }
      }

      const fileSizeMB = (ele.capacitor?.bytesWritten) / (1024 * 1024)
      if (fileSizeMB > MAX_IMAGE_SIZE.SIZE_IN_MB) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('FILE_SIZE_LIMIT_EXCEEDED', language), errorCode: HTTP_STATUS.BAD_REQUEST }
      }

      const s3Config = config.getProperties().s3
      const key = `tenants/${user.tenantId}/user/${userId}/${uuid()}.${fileType}`

      const s3Params = {
        ACL: 'public-read',
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream
      }

      try {
        await s3.upload(s3Params).promise()
      } catch (error) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate((FILE_UPLOAD_ERROR_CODES[error.message] || 'FILE_UPLOAD_FAILED', language)), errorCode: HTTP_STATUS.INTERNAL_SERVER_ERROR }
      }

      avatarImageToSave = key
    }

    const newAvatarImage = avatarImageToSave || avatarImage

    const [recordUpdated] = await UserModel.update(
      { avatarImage: newAvatarImage },
      { where: { id: userId }, transaction: sequelizeTransaction }
    )

    if (recordUpdated === 1) {
      return {
        success: true,
        message: translate('AVATAR_UPDATED_SUCCESSFULLY', language),
        avatarImage: newAvatarImage
      }
    } else {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('UPDATE_ERROR', language) }
    }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
