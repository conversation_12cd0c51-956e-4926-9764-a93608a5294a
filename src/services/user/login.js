import { AuthenticationError } from 'apollo-server-express'
// import bcrypt from 'bcrypt'
import * as jwt from 'jsonwebtoken'
import md5 from 'md5'
import { Op } from 'sequelize'
import { checkBlackList } from '../../common/checkBlackList'
import { ALLOWED_PERMISSIONS, ENVIORNMENT, ERORR_TYPE, LOGIN_TYPE, QUEUE_WORKER_CONSTANT, REFERRAL_BLOCK_TYPE, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL, TENANT_IDS } from '../../common/constants'
import { getUserSmarticoId } from '../../common/getUserSmarticoId'
import ServiceBase from '../../common/serviceBase'
import verifyRecaptcha from '../../common/verifyRecaptcha'
import config from '../../config/app'
// import { updateUserLastLogin } from '../../elastic-search'
import ErrorLogHelper from '../../common/errorLog'
import { MultipleLoginAttempts } from '../../common/mutlipleLoginAttempt'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
import redisConnection from '../../lib/redisConnection'

const constraints = {
  userNameOrEmail: {
    type: 'string'
  },
  password: {
    type: 'string',
    presence: { message: 'Password is required' }
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  },
  ip: {
    type: 'string'
  }
}

/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class Login extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    this.context.sequelizeTransaction = await sequelize.transaction()
    try {
      const {
        req: { headers: { language, grcptv3 } },
        databaseConnection: {
          User: UserModel,
          TenantThemeSetting: TenantThemeSettingModel,
          QueueLog: QueueLogModel,
          UserLoginHistory: UserLoginHistoryModel,
          Marina888User: Marina888UserModel,
          Blacklist: BlacklistModel,
          AdminUsersAdminRole: AdminUsersAdminRoleModel,
          UserReferralCode: UserReferralCodeModel,
          BotUser: BotUserModel,
          BlockedReferralUser: BlockedReferralUserModel,
          UserPreferenceType: UserPreferenceTypeModel,
        },
        tenant: Tenant,
        sequelizeTransaction
      } = this.context

      const {
        attempt,
        getLoginAttempt,
        clearLoginAttempt,
        getRemainigTime
      } = MultipleLoginAttempts()

      const environment = config.get('env')
      if (!await verifyRecaptcha(this.context, grcptv3)) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SIGNATURE', language) }
        throw err
      }

      const checkBlackListUser = await checkBlackList('', '', '', this.userNameOrEmail, Tenant.id, BlacklistModel, this.args.ip)
      if (checkBlackListUser) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('BLACK_LISTED', language) }
        throw err
      }
      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: Tenant.id
        },
        attributes: ['userLoginType', 'maxAttempts', 'attemptExpiryTime', 'passwordResetDays', 'idleScreenLogoutTime', 'allowedModules']
      })

      const whereCondition = {
        tenantId: Tenant.id,
        [Op.or]: [
          {
            userName:
             sequelize.where(
               sequelize.fn('LOWER', sequelize.col('user_name')),
               (this.userNameOrEmail).toLowerCase()
             )
          },
          {
            email: (this.userNameOrEmail)?.toLowerCase?.()
          }
        ]
      }

      const user = await UserModel.findOne({ where: whereCondition , include: [
        {
          model: this.context.databaseConnection.UserDocument,
        },
        {
          model: UserReferralCodeModel,
          where: { tenantId: Tenant.id },
          attributes: ['referralCode'],
          required: false,
        },
        {
          model: UserPreferenceTypeModel,
          where: { tenantId: Tenant.id, preferenceType: 'otp' },
          attributes: ['value'],
          required: false,
          limit: 1
        }
      ],
     })

      if(user.UserPreferenceTypes && user.UserPreferenceTypes.length > 0) {
        user.otpPreference = user.UserPreferenceTypes[0].value
      } else {
        user.otpPreference = null
      }

      if (user) {
        const smarticoUserId = await getUserSmarticoId(user?.id, Tenant.id, environment, Marina888UserModel)
        user.smarticoUserId = smarticoUserId
        user.referralCode = user?.UserReferralCode?.referralCode  || ''
      }

      if (((environment === ENVIORNMENT.STAGING && Tenant.id === TENANT_IDS.MARINA_888.STAGE) ||
        (environment === ENVIORNMENT.PRODUCTION && Tenant.id === TENANT_IDS.MARINA_888.PROD)) && user && !user.encryptedPassword) {
        return {
          user: {
            id: user.id,
            userName: user.userName,
            email: user.email,
            phone: user.phone,
            firstName: user.firstName,
            lastName: user.lastName,
            phoneVerified: user.phoneVerified,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt,
            smarticoUserId: user.smarticoUserId
          },
          message: translate('CHANGE_PASSWORD_FORCEFULLY', language),
          success: false
        }
      }

      const timeDiff = Math.abs(new Date() - user?.createdAt)
      const dayDiff = timeDiff && Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
      let lastPasswordChangeFlag = false
      /**
       * Check if user creation date in greater then reset days
       * Check if password hasnt been changed in past few days equal to reset days
       */

      if (user?.passwordUpdatedAt) {
        const lastPasswordUpdateTime = Math.abs(new Date() - user.passwordUpdatedAt)
        const lastPasswordUpdateDayDiff = Math.ceil(lastPasswordUpdateTime / (1000 * 60 * 60 * 24))
        lastPasswordChangeFlag = lastPasswordUpdateDayDiff < tenantTheme.passwordResetDays
      }

      const condition = (tenantTheme?.passwordResetDays && dayDiff >= tenantTheme.passwordResetDays && !lastPasswordChangeFlag) || user?.forceResetPassword

      if (!user && tenantTheme?.userLoginType !== LOGIN_TYPE.MOBILE) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_USERNAME_OR_EMAIL', language) }
        throw err
      } else if (!user) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_USERNAME', language) }
        throw err
      }

      if (!user.active) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
        throw err
      }

      const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
      if (profileVerifiedPermissionCheck && !user.profileVerified) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('PROFILE_NOT_ACTIVE', language) }
        throw err
      }

      // const verify = (isVerified, errorKey) => {
      //   if (!isVerified) {
      //     throw {
      //       errorType: ERORR_TYPE.AUTHRNTICATION,
      //       errorMsg: translate(errorKey, language)
      //     }
      //   }
      // }

      // const otpPreference = tenantTheme.userLoginType === LOGIN_TYPE.BOTH
      //   ? +(await UserPreferenceTypeModel.findOne({
      //     attributes: ['value'],
      //     where: { tenantId: Tenant.id, userId: user.id, preferenceType: 'otp' }
      //   }))?.value || LOGIN_TYPE.MOBILE
      //   : tenantTheme.userLoginType

      // if (otpPreference === LOGIN_TYPE.MOBILE) {
      //   verify(user.phoneVerified, 'PHONE_NOT_VERIFIED')
      // } else if (otpPreference === LOGIN_TYPE.EMAIL) {
      //   verify(user.emailVerified, 'EMAIL_NOT_VERIFIED')
      // }
      // key for redis to check login attempts
      const userIpKey = `${user.id}-${this.args.ip}`

      if (tenantTheme?.maxAttempts) {
        // get login attempts for user and device
        const attempts = await getLoginAttempt(userIpKey)
        if (attempts && attempts >= tenantTheme?.maxAttempts) {
          const ttl = await getRemainigTime(userIpKey)
          const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(`Please try again after ${Math.ceil(ttl / 60).toFixed(0)} mins`, language) }
          throw err
        }
      }

      // if (!(await bcrypt.compare(this.args.password, user.encryptedPassword))) {
      //   throw new AuthenticationError(translate('WRONG_PASSWORD', language))
      // }
      this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')
      if (md5(this.args.password) !== user.encryptedPassword) {
        const resp = tenantTheme?.maxAttempts && (+tenantTheme?.maxAttempts) > 0 &&
          (+tenantTheme?.attemptExpiryTime) > 0 &&
          await attempt(
            userIpKey,
            tenantTheme.maxAttempts,
            tenantTheme.attemptExpiryTime
          )

        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(resp?.message || 'WRONG_PASSWORD', language) }
        throw err
      }

      const isFinancialActivityEnabledPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.DISABLEAGENTUSER)
      user.isFinancialActivityEnabled = true
      if (isFinancialActivityEnabledPermissionCheck && user.parentId) {
        const adminUserRole = await AdminUsersAdminRoleModel.findOne(
          {
            where: {
              adminUserId: user.parentId
            },
            attributes: ['adminRoleId'],
            raw: true
          }
        )
        user.isFinancialActivityEnabled = adminUserRole?.adminRoleId === '1' // 1 for admin role
      }

      await clearLoginAttempt(userIpKey)

      const credentials = await this.context.databaseConnection.TenantCredential.findOne({
        attributes: ['key', 'value'],
        where: {
          key: 'APP_JWT_SECRET_KEY',
          tenantId: this.context.tenant.id
        },
        raw: true
      })

      const authConfig = config.getProperties().auth
      const resToken = await jwt.sign({ id: user.id }, credentials.value, { expiresIn: authConfig.res_token_expiry_time })

      user.signInCount += 1
      user.lastLoginDate = new Date()

      if (this.args.ip) {
        const loginHistoryCondition = {
          tenantId: Tenant.id,
          userId: user.id,
          ip: this.args.ip,
          loginType: 0
        }

        const userLoginInfo = await UserLoginHistoryModel.findOne({
          attributes: ['id', 'signInCount'],
          where: loginHistoryCondition
        })

        const userInfo = {
          userId: user.id,
          tenantId: Tenant.id,
          ip: this.args.ip,
          network: this.args.network,
          version: this.args.version,
          deviceId: this.args.deviceId,
          deviceType: this.args.deviceType,
          deviceModel: this.args.deviceModel,
          data: {
            city: this.args.city,
            region: this.args.region,
            countryName: this.args.countryName
            // postal: this.args.postal,
            // latitude: this.args.latitude,
            // longitude: this.args.longitude
          },
          lastLoginDate: new Date(),
          signInCount: userLoginInfo ? userLoginInfo.signInCount + 1 : 1,
          loginType: 0,
        }

        userLoginInfo ? await UserLoginHistoryModel.update(userInfo, { where: loginHistoryCondition }) : await UserLoginHistoryModel.create(userInfo)
      }

      const isBotUser = await BotUserModel.findOne({ where: { userId: user.id } })
      user.isBotUser = isBotUser ? true : false

      // Find a blocked referral user based on tenant ID and block conditions
      const blockedReferralCode = await BlockedReferralUserModel.findOne({
        where: {
          tenantId: Tenant.id, // Ensure the record belongs to the correct tenant
          [Op.or]: [
            // Check if the referral user is blocked as a normal user
            { blockId: user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

            // Check if the referral user's parent (agent) is blocked
            { blockId: user?.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
          ]
        }
      });

      user.isReferralCodeBlocked = !!blockedReferralCode;

      // await updateUserLastLogin(user)

      const token = jwt.sign({ id: user.id }, authConfig.jwt_secret, {
        expiresIn: authConfig.expiry_time
      })

      const storedToken = await redisConnection.get(`user:${user.id}`)
      if (storedToken && (storedToken !== token)) {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.MULTIPLE_LOGIN_NOTIFICATION, { MultipleLoginNotification: { userId: user.id, multipleLogin: true, token: storedToken } })
        await redisConnection.del(`user:${user.id}`)
      }
      await redisConnection.set(`user:${user.id}`, token, { EX: authConfig.expiry_time })
      await user.save({ transaction: sequelizeTransaction })

      await this.context.sequelizeTransaction.commit()

      const userIds = []
      if (user) {
        userIds.push(user.id)
      }
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: userIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject)

      if (queueLog.id) {
        try {
          this.context.pubSub.publish(
            SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
            { QueueLog: { queueLogId: queueLog.id } }
          )
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
        }
      }

      // smartigo code
      const smartigoTenants = config.get('env') === 'production' ? SMARTIGO_TENANTS.PROD : SMARTIGO_TENANTS.STAGE
      if (smartigoTenants && smartigoTenants.includes(Tenant.id)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.LOGIN_STATS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [+user.id],
          tenantId: parseInt(Tenant.id)
        }
        await QueueLogModel.create(smartiGoObject)
      }

      return { token, user, resToken, shouldPasswordUpdate: !!condition, idle_screen_logout_time: tenantTheme.idleScreenLogoutTime, success: true }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
        throw new AuthenticationError(ERORR_TYPE.INVALID_REQUEST)
      }
    }
  }
}
