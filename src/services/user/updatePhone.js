import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, ERORR_TYPE, LOGIN_TYPE, PHONE_CODE, SMS_GATEWAY } from '../../common/constants'
import <PERSON>rror<PERSON>ogHel<PERSON> from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'

const constraints = {
  countryCode: {
    type: 'string',
    presence: { message: 'Country code is required' },
    format: {
      pattern: '^[A-Za-z]+$',
      message: 'Can only contain characters (min 1, max 3)'
    },
    length: {
      minimum: 1,
      maximum: 3
    }
  },
  newPhone: {
    type: 'string',
    presence: { message: 'New phone is required' },
    format: {
      pattern: '^[0-9-]+$',
      message: 'Can only contain numbers (min 5, max 15)'
    },
    length: {
      minimum: 5,
      maximum: 15
    }
  }
}

/**
 * Provides service for the updating phone functionality
 * @export
 * @class UpdatePhone
 * @extends {ServiceBase}
 */
export default class UpdatePhone extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
       UserPreferenceType: UserPreferenceTypeModel,
        Wallet: WalletModel,
        Currency: CurrencyModel
      },
      tenant: Tenant,
      auth: { id: currentUserId }
    } = this.context

    try {
    const currentUser = await UserModel.findOne({
      attributes: ['userName', 'email', 'firstName', 'id', 'tenantId'],
      where: { id: currentUserId },
      include: {
        model: WalletModel,
        attributes: ['ownerId', 'currencyId'],
        include: {
          model: CurrencyModel,
          attributes: ['code']
        }
      }
    })

    const phoneExist = await UserModel.findOne({ where: { phone: this.args.newPhone, countryCode: this.args.countryCode, tenantId: Tenant.id, phoneCode: this.args.phoneCode }, raw: true })

    if (phoneExist) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('PHONE_ALREADY_EXIST', language) }
    }

    let otpPreference, otp;
    if (currentUser) {
      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: Tenant.id
        },
        attributes: ['smsGateway', 'allowedModules', 'userLoginType', 'emailGateway'],
      })

    // Determine OTP login preference:
    // - If login type is BOTH (email or phone), fetch the user's specific OTP preference from the database.
    // - If no preference is found, send to PHONE.
    // - If login type is not BOTH, use the tenant-wise setting directly.

    otpPreference = tenantTheme?.userLoginType || LOGIN_TYPE.MOBILE;

    if (tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) {
      const userPreference = await UserPreferenceTypeModel.findOne({
        attributes: ['value'],
        where: {
          tenantId: Tenant.id,
          userId: currentUserId,
          preferenceType: 'otp'
        },
        raw: true,
      });

      otpPreference = userPreference?.value
        ? +userPreference.value
        : LOGIN_TYPE.MOBILE;
    }

      if (tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE ) {
        try {
          const response = await messageCentralSendOTP(this.context, this.args.newPhone, this.args.phoneCode, this.context.auth.id)
          if (!response) {
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
          }
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, currentUser)
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
        }
      } else {
        const environment = config.get('env')
        otp = (environment === 'production') ? Math.random().toString().substring(2, 8) : DEFAULT_OTP

        const [record, created] = await UserTokenModel.findOrCreate({
          where: { tenantId: Tenant.id, userId: this.context.auth.id, tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email' },
          defaults: { token: md5(otp) }
        })
        if (!created) {
          await record.update({ token: md5(otp) })
        }

        try {
          if (environment === 'production' && otpPreference === LOGIN_TYPE.MOBILE) {
            if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
              if ((tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY)) &&
                (currentUser && currentUser.Wallet && currentUser.Wallet.Currency && currentUser.Wallet.Currency.code === CURRENCY_CODE.LKR)) {
                await sendKarixSMSPhoneVerificationOTP(this.args.newPhone, this.args.phoneCode, otp, this.context)
              } else {
                await sendFast2SMSPhoneVerificationOTP(this.args.newPhone, this.args.phoneCode, otp, this.context)
              }
            } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
              if (this.args.phoneCode !== PHONE_CODE.SRILANKA)
                throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
              await sendZMessengerSMSPhoneVerificationOTP(currentUser.firstName, this.args.newPhone, otp, this.context)
            } else {
              await sendPhoneVerificationOTP(this.args.newPhone, this.args.phoneCode, otp, this.context)
            }
          } else if (environment === 'production' && otpPreference === LOGIN_TYPE.EMAIL) {
            await sendOtpThroughSmtpRelay(otp, currentUser.email, this.context, false, tenantTheme.emailGateway)
          }
        } catch (error) {
          await UserTokenModel.destroy({ where: { id: record.id, tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId: Tenant.id } })
          await ErrorLogHelper.logError(error, this.context, currentUser)
          if (error.message && error.message.includes('Unsupported OTP')) {
            throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
          }

          // throw new UserInputError(translate('INVALID_PHONE_NUMBER', language))
        }
      }
    } else {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }
    this.args.data = translate(otpPreference === LOGIN_TYPE.MOBILE ? 'OTP_SENT' : 'EMAIL_OTP_SENT', language)
    return this.args
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUserId, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
