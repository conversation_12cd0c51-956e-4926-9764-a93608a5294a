import { ApolloError, AuthenticationError, ForbiddenError, UserInputError } from 'apollo-server-express'
import md5 from 'md5'
// import { v4 as uuid } from 'uuid'
import { ALLOWED_PERMISSIONS, CURRENCY_CODE, DEFAULT_OTP, ERORR_TYPE, LOGIN_TYPE, PHONE_CODE, SMS_GATEWAY } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../../lib/karixSMS/sendOtp'
import translate from '../../lib/languageTranslate'
// import { sendEmailThroughSmtpRelay } from '../../lib/sendgridService'
import ErrorLogHelper from '../../common/errorLog'
import { messageCentralSendOTP } from '../../common/messageCentralSendOTP'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import { sendOtpThroughSmtpRelay } from '../../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../../lib/zMessengerSms/sendOtp'

const constraints = {}

/**
 * This api is to generate OTP for authorization,
 * It will only be used when user is logged in and wants to verify the mobile or email
 * @exports
 * @class GenerateOTP
 * @extends ServiceBase
 */

export default class GenerateOTP extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      tenant: Tenant,
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Wallet: WalletModel,
        Currency: CurrencyModel,
        UserPreferenceType: UserPreferenceTypeModel,
        UserLoginPin: UserLoginPinModel
      },
      auth: { id: userId }
    } = this.context

    try {
    // get users details
    const whereObj = {
      id: userId
    }


    await checkImpersonatedAccess(this.context)

    const user = await UserModel.findOne({
      include: {
        model: WalletModel,
        attributes: ['ownerId', 'currencyId'],
        include: {
          model: CurrencyModel,
          attributes: ['code']
        }
      },
      where: whereObj,
      raw: true
    })

    if (this.args?.oldPassword) {
      const oldPassword = Buffer.from(this.args.oldPassword, 'base64').toString('ascii')
      if (md5(oldPassword) !== user.encryptedPassword) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('OLD_PASSWORD_INCORRECT', language) }
      }
    }

    if (this.args?.oldLoginPin) {
      const userLoginPinDetails = await UserLoginPinModel.findOne({ where: { userId: user.id }, raw: true })
      const oldLoginPin = Buffer.from(this.args.oldLoginPin, 'base64').toString('ascii')
      if (md5(oldLoginPin) !== userLoginPinDetails?.encryptedLoginPin) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('OLD_LOGIN_PIN_INCORRECT', language) }
      }
    }

    // get tenant's sms gateway and forgotpassword option
    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['smsGateway', 'allowedModules', 'userLoginType', 'emailGateway']
    })

    // finding otp preference
    let otpPreference = tenantTheme?.userLoginType === LOGIN_TYPE.BOTH
      ? +(await UserPreferenceTypeModel.findOne({
        attributes: ['value'],
        where: { tenantId: Tenant.id, userId: user.id, preferenceType: 'otp' }
      }))?.value
      : tenantTheme?.userLoginType

    if (!otpPreference) {
      otpPreference = LOGIN_TYPE.MOBILE
    }
    // if user isnt present
    if (!user) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    let otp
    if (tenantTheme.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) {
      try {
        const response = await messageCentralSendOTP(this.context, user.phone, user.phoneCode, user.id)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, user)
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTP_NOT_SEND', language) }
      }
    } else {
      // creation of user token
      const userToken = {}
      userToken.userId = user.id
      userToken.tokenType = 'otpVarify'

      try {
        // if email is the option
        // if (tenantTheme.forgotPasswordOption === FORGOT_PASSWORD_OPTIONS.email) {
        //   // generate token
        //   userToken.token = uuid()

        //   // create token and update in the db
        //   await UserTokenModel.create({ ...userToken })

        //   // send otp on the mail
        //   await sendEmailThroughSmtpRelay(
        //     user.id,
        //     userToken.token,
        //     user.email,
        //     this.context,
        //     userToken.tokenType
        //   )
        // }

        // if mobile is the option
        // if (tenantTheme.forgotPasswordOption === 'mobile') {
        const environment = config.get('env')
        // generate OTP based on environment
        otp = (environment === 'production') ? Math.random().toString().substring(2, 8) : DEFAULT_OTP
        userToken.token = md5(otp)

        await UserTokenModel.create({ ...userToken })

        // sent the OTP through SMS
        let result
        if (environment === 'production' && otpPreference === LOGIN_TYPE.MOBILE) {
          if (tenantTheme.smsGateway === SMS_GATEWAY.FAST2_SMS) {
            if (tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY) &&
              (user['Wallet.Currency.code'] === CURRENCY_CODE.LKR)) {
              result = await sendKarixSMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
            } else {
              result = await sendFast2SMSPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
            }
          } else if (tenantTheme.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
            if (user.phoneCode !== PHONE_CODE.SRILANKA)
              throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
            result = await sendZMessengerSMSPhoneVerificationOTP(user.firstName, user.phone, otp, this.context)
          } else {
            result = await sendPhoneVerificationOTP(user.phone, user.phoneCode, otp, this.context)
          }
          if (!result?.success) {
            // Handle the case when the operation fails
            await UserTokenModel.destroy({ where: { id: user.id, tokenType: 'otpVarify' } })
            throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('GENERATE_OTP_FAILED', language) }
          }
        } else if (environment === 'production' && otpPreference === LOGIN_TYPE.EMAIL) {
          await sendOtpThroughSmtpRelay(otp, user.email, this.context, false, tenantTheme.emailGateway)
        }
        // }
      } catch (error) {
        // throw new Error(e)
        await ErrorLogHelper.logError(error, this.context, user)
        if (error.message && error.message.includes('Unsupported OTP')) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('UNSUPPORTED_OTP', language) }
        }
        throw { errorType: ERORR_TYPE.FORBIDDEN_ERROR, errorMsg: translate('FORBIDDEN', language) }
      }
    }
    return {
      status: true,
      data: translate(otpPreference === LOGIN_TYPE.MOBILE ? 'OTP_SENT' : 'EMAIL_OTP_SENT', language)
    }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.FORBIDDEN_ERROR) {
        throw new ForbiddenError(error.errorMsg)
      }
      else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
