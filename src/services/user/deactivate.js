import { ApolloError, AuthenticationError } from 'apollo-server-express'
import md5 from 'md5'
import { AUDIT_LOG_ACTIONEE_TYPE, ERORR_TYPE, EVENT, EVENT_TYPE } from '../../common/constants'
import <PERSON>rro<PERSON><PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * it provides service for soft deleting a user
 * @export
 * @class Deactivate
 * @extends {ServiceBase}
 */
export default class Deactivate extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        AuditLog: AuditLogModel,
      },
      auth: { id: currentUser },
      tenant: { id: tenantId }
    } = this.context

    try {
    const user = await UserModel.findOne({ where: { id: currentUser } })

    if (!user) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
    }

    this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')

    if (!(md5(this.args.password) === user.encryptedPassword)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('WRONG_PASSWORD', language) }
    }

    user.disabledAt = new Date()
    user.disabledByType = 'User'
    user.disabledById = user.id,
    user.active = false
    user.disabledRemarks = this.args?.remarks || null
    await user.save()

    const auditData = {
      tenantId,
      actioneeId: currentUser,
      eventType: EVENT_TYPE.userAccountDetactivated,
      event: EVENT.update,
      eventId: currentUser,
      actioneeIp: '',
      description: this.args?.remarks || 'User deactivates their account.',
      action: 'Account Deactivation',
      previousData: { active: true, },
      modifiedData: { active: false },
      actioneeType: AUDIT_LOG_ACTIONEE_TYPE.USER
    }

    await AuditLogModel.create(auditData);

    return true
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUser, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
