import { ApolloError, AuthenticationError } from 'apollo-server-express'
import md5 from 'md5'
// import bcrypt from 'bcrypt'
import { ERORR_TYPE, TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'
import UserLogout from './userLogout'

const constraints = {
  oldLoginPin: {
    type: 'string',
    presence: { message: 'Old Login PIN is required' }
  },
  newLoginPin: {
    type: 'string',
    presence: { message: 'New Login PIN is required' },
    length: {
      minimum: 5
    }
  }
}

/**
 * Provides service for the change Login PIN functionality
 * @export
 * @class ChangeLoginPin
 * @extends {ServiceBase}
 */
export default class ChangeLoginPin extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserLoginPin: UserLoginPinModel
      },
      auth: { id: currentUserId },
      tenant: { id: tenantId }
    } = this.context

    try {
    const currentUser = await UserModel.findOne({ where: { id: currentUserId }, raw: true })
    const userLoginPinDetails = await UserLoginPinModel.findOne({ where: { userId: currentUserId }, raw: true })
    this.oldLoginPin = Buffer.from(this.oldLoginPin, 'base64').toString('ascii')
    this.newLoginPin = Buffer.from(this.newLoginPin, 'base64').toString('ascii')

      let regExp
      const allowExtraSpecialCharsTenant = config.get('env') === 'production' ? TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.PROD : TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD.STAGE
      if (allowExtraSpecialCharsTenant && allowExtraSpecialCharsTenant.includes(tenantId))
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!"#$%&\'()*+,-./:;<=>?_@(){}])[A-Za-z\\d!"#$%&\'()*+,-./:;<=>?_@(){}]{5,}$')
      else
        regExp = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*["\\!#$%&\'()*+,-./:;<=>?_@])[A-Za-z\\d"\\!#$%&\'()*+,-./:;<=>?_@]{5,}$')
    if (!regExp.test(this.newLoginPin)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_PIN_FORMAT', language) }
    }

    if (md5(this.oldLoginPin) !== userLoginPinDetails?.encryptedLoginPin) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('OLD_PIN_INCORRECT', language) }
 }

    const newEncryptedLoginPin = md5(this.newLoginPin)
    await UserLoginPinModel.update({ encryptedLoginPin: newEncryptedLoginPin }, { where: { userId: currentUser.id } })
    UserLogout.execute(null, this.context)
    return currentUser
  } catch ( error ) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUserId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
