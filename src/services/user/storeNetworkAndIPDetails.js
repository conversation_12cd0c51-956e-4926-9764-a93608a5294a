import { AuthenticationError } from "apollo-server-express";
import { Op } from "sequelize";
import {
  ERORR_TYPE,
  USER_LOGIN_TYPES,
} from "../../common/constants";
import <PERSON>rror<PERSON><PERSON><PERSON><PERSON>per from "../../common/errorLog";
import ServiceBase from "../../common/serviceBase";
import { sequelize } from "../../db/models";
import languageTranslate from "../../lib/languageTranslate";

const IP_AND_NETWORK_STORE_EVENTS = ["APP_LAUNCH", "GAME_LAUNCH", "RECONNECT"];

const constraints = {
  network: {
    type: "string",
    presence: { message: "Network is required" },
  },
  ip: {
    type: "string",
    presence: { message: "IP is required" },
  },
  event: {
    type: "string",
    inclusion: {
      within: IP_AND_NETWORK_STORE_EVENTS,
      message: `^Event must be one of: ${IP_AND_NETWORK_STORE_EVENTS.join(", ")}, but got %{value}`,
    },
  },
  deviceId: {
    type: "string",
  },
  deviceType: {
    type: "string",
  },
  deviceModel: {
    type: "string",
  },
  data: {
    type: "object"
  },
  version: {
    type: "string"
  },
};

/**
 * Provides service for the Maintain user Ip and Network history storing functionality
 * @export
 * @class StoreNetworkAndIPDetails
 * @extends {ServiceBase}
 */
export default class StoreNetworkAndIPDetails extends ServiceBase {
  get constraints() {
    return constraints;
  }

  async run() {
    this.context.sequelizeTransaction = await sequelize.transaction();
    try {
      const {
        req: {
          headers: { language },
        },
        databaseConnection: {
          User: UserModel,
          UserLoginHistory: UserLoginHistoryModel,
        },
        tenant: Tenant,
        auth: { id: userId },
      } = this.context;

      const user = await UserModel.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw {
          errorType: ERORR_TYPE.AUTHRNTICATION,
          errorMsg: languageTranslate("USER_NOT_FOUND", language),
        };
      }

      const loginHistoryCondition = {
        tenantId: Tenant.id,
        userId: user.id,
        loginType: { [Op.notIn]: [USER_LOGIN_TYPES.GAME_LAUNCH,USER_LOGIN_TYPES.APP_LAUNCH, USER_LOGIN_TYPES.RECONNECT, USER_LOGIN_TYPES.WITHDRAW] },
      };

      const userLoginInfo = await UserLoginHistoryModel.findOne({
        attributes: ["id", "signInCount", "lastLoginDate"],
        where: loginHistoryCondition,
        raw: true,
        order: [["createdAt", "DESC"]],
      });

      const userIpAndNetworkInfo = {
        userId: user.id,
        tenantId: Tenant.id,
        ip: this.args.ip,
        network: this.args.network,
        version: this.args.version,
        deviceId: this.args.deviceId,
        deviceType: this.args.deviceType,
        deviceModel: this.args.deviceModel,
        data: this.args.data,
        loginType: USER_LOGIN_TYPES[this.args.event],
        lastLoginDate: userLoginInfo?.lastLoginDate || null,
      };

      await UserLoginHistoryModel.create(userIpAndNetworkInfo, {
        transaction: this.context.sequelizeTransaction,
      });

      this.context.sequelizeTransaction.commit()

      return { success: true, message: "User IP and network details stored successfully." };
    } catch (error) {
      await this.context.sequelizeTransaction.rollback();

      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg);
      } else {
        await ErrorLogHelper.logError(error, this.context, {
          tenantId: this.context.tenant.id,
        });
        throw new AuthenticationError(ERORR_TYPE.INVALID_REQUEST);
      }
    }
  }
}
