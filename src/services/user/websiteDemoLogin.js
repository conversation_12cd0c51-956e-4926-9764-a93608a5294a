import { AuthenticationError } from 'apollo-server-express'
import * as jwt from 'jsonwebtoken'
import md5 from 'md5'
import { Op } from 'sequelize'
import { ALLOWED_PERMISSIONS, DEMO_LOGIN_USER_CREDENTIAL, ERORR_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'


/**
 * Provides service for the Login functionality
 * @export
 * @class Login
 * @extends {ServiceBase}
 */
export default class WebsiteDemoLogin extends ServiceBase {
  async run () {
    this.context.sequelizeTransaction = await sequelize.transaction()
    try {
      const {
        req: { headers: { language } },
        databaseConnection: {
          User: UserModel,
          TenantThemeSetting: TenantThemeSettingModel
        },
        tenant: Tenant
      } = this.context

      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: Tenant.id
        },
        attributes: ['allowedModules']
      })

      let hasDemoLoginPermission = tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WEBSITE_DEMO_LOGIN);

      if (!hasDemoLoginPermission) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('FORBIDDEN', language) }
        throw err
      }

      const user = await UserModel.findOne({
        attributes: ['id', 'countryCode', 'createdAt', 'dateOfBirth', 'email', 'emailVerified', 'firstName', 'gender', 'lastName', 'phone', 'phoneVerified',
          'signInCount', 'updatedAt', 'userName', 'nickName', 'selfExclusion', 'phoneCode', 'city', 'zipCode', 'kycDone',
          'vipLevel', 'active', 'encryptedPassword'
        ],
        where: {
          tenantId: DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID,
          email: DEMO_LOGIN_USER_CREDENTIAL.EMAIL
        },
      })

      if (!user) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
        throw err
      }

      if (!user.active) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
        throw err
      }

      let password = Buffer.from(DEMO_LOGIN_USER_CREDENTIAL.PASSWORD, 'base64').toString('ascii')

      if (md5(password) !== user.encryptedPassword) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('WRONG_PASSWORD', language) }
        throw err
      }

      const credentials = await this.context.databaseConnection.TenantCredential.findOne({
        attributes: ['key', 'value'],
        where: {
          key: {
            [Op.in]: ['APP_JWT_SECRET_KEY']
          },
          tenantId: Tenant.id
        },
        raw: true
      });

      const authConfig = config.getProperties().auth
      const resToken = jwt.sign({ id: user.id, tenantId: DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID }, credentials.value, { expiresIn: authConfig.res_token_expiry_time })
      const token = jwt.sign({ id: user.id, tenantId: DEMO_LOGIN_USER_CREDENTIAL.TENANT_ID }, authConfig.jwt_secret, {
        expiresIn: authConfig.expiry_time
      })

      let userInfo = {
        signInCount: user.signInCount + 1,
        lastLoginDate: new Date()
      }

      await UserModel.update(userInfo, { where: { id: user.id } })

      await this.context.sequelizeTransaction.commit()

      user.isDemoUser = true;

      return { token, user, resToken, shouldPasswordUpdate: false, idle_screen_logout_time: null }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
        throw new AuthenticationError(ERORR_TYPE.INVALID_REQUEST)
      }
    }
  }
}
