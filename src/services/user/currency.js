import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ERORR_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for the currency
 * @export
 * @class Currency
 * @extends {ServiceBase}
 */
export default class Currency extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      tenant: { id: tenantId },
      databaseConnection: {
        Currency: CurrencyModel,
        TenantConfiguration: TenantConfigurationModel
      }
    } = this.context

    try {
    const tenantCurrencies = await TenantConfigurationModel.findOne({ where: { tenantId: this.context.tenant.id }, raw: true, attributes: ['allowedCurrencies'] })
    // const arr = tenantCurrencies.allowedCurrencies.filter(Number)
    const arr = tenantCurrencies.allowedCurrencies.split(',')

    const currency = await CurrencyModel.findAll({ where: { id: { [Op.in]: arr } }, raw: true })

    if (!currency) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('CURRENCY_NOT_FOUND', language) }
    }

    return currency
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
