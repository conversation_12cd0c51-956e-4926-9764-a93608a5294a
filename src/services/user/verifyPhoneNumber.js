import { AuthenticationError, UserIn<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rror } from 'apollo-server-express'
import md5 from 'md5'
import { LOGIN_TYPE, PROD_TENANTS_MESSAGE_CENTRAL_SMS, STAGE_TENANTS_MESSAGE_CENTRAL_SMS, ERORR_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { messageCentralVerifyOTP } from '../../common/messageCentralVerifyOTP'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

const constraints = {
  otp: {
    type: 'string'
  }
}

/**
 * Provides service for the verification of phone functionality
 * @export
 * @class VerifyPhoneNumber
 * @extends {ServiceBase}
 */
export default class VerifyPhoneNumber extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        User: UserModel,
        UserToken: UserTokenModel,
        TenantThemeSetting: TenantThemeSettingModel,
        UserPreferenceType: UserPreferenceTypeModel
      },
      auth: { id: userId },
      tenant: { id: tenantId }
    } = this.context
    try {
    const otp = this.args.otp

    const tenantTheme = await TenantThemeSettingModel.findOne({
      attributes: ['userLoginType'],
      where: { tenantId }
    })

    let otpPreference = tenantTheme?.userLoginType || LOGIN_TYPE.MOBILE;

    if (tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) {
      const userPreference = await UserPreferenceTypeModel.findOne({
        attributes: ['value'],
        where: {
          tenantId,
          userId,
          preferenceType: 'otp'
        },
        raw: true,
      });

      otpPreference = userPreference?.value ? +userPreference.value : LOGIN_TYPE.MOBILE;
    }

    const eligibleTenant = config.get('env') === 'production' ? PROD_TENANTS_MESSAGE_CENTRAL_SMS : STAGE_TENANTS_MESSAGE_CENTRAL_SMS

    if (eligibleTenant.includes(tenantId)) {
      try {
        const user = await UserModel.findOne({
          attributes: ['phone', 'phoneCode'],
          where: { id: userId, tenantId },
          raw: true
        })
        const response = await messageCentralVerifyOTP(this.context, this.args.otp, user.phone, user.phoneCode, userId)
        if (!response) {
          throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
        }
      } catch (error) {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('VERIFICATION_FAILED', language) }
      }
    } else {
      const verifyPhoneToken = await UserTokenModel.findOne({
        where: { userId, token: md5(otp), tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId },
        raw: true
      })
      if (!verifyPhoneToken) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_TOKEN', language) }
      }
      UserTokenModel.destroy({ where: { userId, token: md5(otp), tokenType: otpPreference === LOGIN_TYPE.MOBILE ? 'phone' : 'email', tenantId } })
    }

    await UserModel.update({
      phone: this.args.newPhone,
      phoneVerified: false,
      countryCode: this.args.countryCode,
      phoneCode: this.args.phoneCode
    }, {
      where: {
        id: userId
      }
    })

    const phoneChanged = true

    return { phoneChanged }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(error.errorMsg)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
