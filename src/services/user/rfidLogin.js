import { AuthenticationError } from 'apollo-server-express'
import * as jwt from 'jsonwebtoken'
import { Op } from 'sequelize'
import { checkBlackList } from '../../common/checkBlackList'
import { ALLOWED_PERMISSIONS, ERORR_TYPE, QUEUE_WORKER_CONSTANT, REFERRAL_BLOCK_TYPE, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL, USER_LOGIN_TYPES, USER_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { getUserSmarticoId } from '../../common/getUserSmarticoId'
import { MultipleLoginAttempts } from '../../common/mutlipleLoginAttempt'
import ServiceBase from '../../common/serviceBase'
import verifyRecaptcha from '../../common/verifyRecaptcha'
import config from '../../config/app'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
import redisConnection from '../../lib/redisConnection'

const constraints = {
  rfidToken: {
    type: 'string'
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  }
}

/**
 * Provides service for the RFID Login functionality
 * @export
 * @class RfidLogin
 * @extends {ServiceBase}
 */
export default class RfidLogin extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        User: UserModel,
        TenantThemeSetting: TenantThemeSettingModel,
        QueueLog: QueueLogModel,
        UserLoginHistory: UserLoginHistoryModel,
        Marina888User: Marina888UserModel,
        Blacklist: BlacklistModel,
        AdminUsersAdminRole: AdminUsersAdminRoleModel,
        UserReferralCode: UserReferralCodeModel,
        BotUser: BotUserModel,
        BlockedReferralUser: BlockedReferralUserModel,
        UserDocument: UserDocumentModel,
        TenantCredential: TenantCredentialModel,
        UserRfidQrcodeDetails: UserRfidQrcodeDetailsModel
      },
      tenant: Tenant,
      sequelizeTransaction
    } = this.context

    this.context.sequelizeTransaction = await sequelize.transaction()

    try {
      const {
        getLoginAttempt,
        clearLoginAttempt,
        getRemainigTime
      } = MultipleLoginAttempts()

      const tenantId = Tenant.id
      const environment = config.get('env')

      if (!await verifyRecaptcha(this.context, grcptv3)) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SIGNATURE', language) }
        throw err
      }

      const rfidDetails = await UserRfidQrcodeDetailsModel.findOne({
        where: {
          rfidToken: this.args.rfidToken,
          tenantId: tenantId // RFID can repeat across tenants , Same as userName.
        }
      })

      if (!rfidDetails) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('RFID_TOKEN_NOT_FOUND', language) }
        throw err
      }

      const userId = rfidDetails.userId

      const whereCondition = {
        id: userId,
        tenantId: tenantId
      }

      const user = await UserModel.findOne({
        where: whereCondition,
        include: [
          {
            model: UserDocumentModel
          },
          {
            model: UserReferralCodeModel,
            where: { tenantId: tenantId },
            attributes: ['referralCode'],
            required: false
          }
        ]
      })

      if (!user) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_NOT_FOUND', language) }
        throw err
      }

      if (user.userType !== USER_TYPES.KIOSK && user.userType !== USER_TYPES.KIOSK_AND_ONLINE) {
        throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('NOT_ALLOWED_PLEASE_LOGIN_ONLINE', language) }
      }

      if (user) {
        const smarticoUserId = await getUserSmarticoId(user?.id, tenantId, environment, Marina888UserModel)
        user.smarticoUserId = smarticoUserId
        user.referralCode = user?.UserReferralCode?.referralCode || ''
      }

      const checkBlackListUser = await checkBlackList(user.phone, user.phoneCode, user.email, user.userName, tenantId, BlacklistModel, this.args.ip)
      if (checkBlackListUser) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('BLACK_LISTED', language) }
        throw err
      }

      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: tenantId
        },
        attributes: ['userLoginType', 'maxAttempts', 'attemptExpiryTime', 'passwordResetDays', 'idleScreenLogoutTime', 'allowedModules']
      })

      if (!user.active) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
        throw err
      }

      const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
      if (profileVerifiedPermissionCheck && !user.profileVerified) {
        const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('PROFILE_NOT_ACTIVE', language) }
        throw err
      }

      // key for redis to check login attempts
      const userIpKey = `${user.id}-${this.args.ip}`

      if (tenantTheme?.maxAttempts) {
        // get login attempts for user and device
        const attempts = await getLoginAttempt(userIpKey)
        if (attempts && attempts >= tenantTheme?.maxAttempts) {
          const ttl = await getRemainigTime(userIpKey)
          const err = { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate(`Please try again after ${Math.ceil(ttl / 60).toFixed(0)} mins`, language) }
          throw err
        }
      }

      const isFinancialActivityEnabledPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.DISABLEAGENTUSER)
      user.isFinancialActivityEnabled = true
      if (isFinancialActivityEnabledPermissionCheck && user.parentId) {
        const adminUserRole = await AdminUsersAdminRoleModel.findOne(
          {
            where: {
              adminUserId: user.parentId
            },
            attributes: ['adminRoleId'],
            raw: true
          }
        )
        user.isFinancialActivityEnabled = adminUserRole?.adminRoleId === '1' // 1 for admin role
      }

      await clearLoginAttempt(userIpKey)

      const credentials = await TenantCredentialModel.findOne({
        attributes: ['key', 'value'],
        where: {
          key: 'APP_JWT_SECRET_KEY',
          tenantId: tenantId
        },
        raw: true
      })

      const authConfig = config.getProperties().auth
      const resToken = await jwt.sign({ id: user.id }, credentials.value, { expiresIn: authConfig.res_token_expiry_time })

      user.signInCount += 1
      user.lastLoginDate = new Date()

      if (this.args.ip) {
        const loginHistoryCondition = {
          tenantId: tenantId,
          userId: user.id,
          ip: this.args.ip,
          loginType: USER_LOGIN_TYPES.RFID
        }

        const userLoginInfo = await UserLoginHistoryModel.findOne({
          attributes: ['id', 'signInCount'],
          where: loginHistoryCondition
        })

        const userInfo = {
          userId: user.id,
          tenantId: tenantId,
          ip: this.args.ip,
          network: this.args.network,
          version: this.args.version,
          deviceId: this.args.deviceId,
          deviceType: this.args.deviceType,
          deviceModel: this.args.deviceModel,
          data: {
            city: this.args.city,
            region: this.args.region,
            countryName: this.args.countryName
            // postal: this.args.postal,
            // latitude: this.args.latitude,
            // longitude: this.args.longitude
          },
          lastLoginDate: new Date(),
          signInCount: userLoginInfo ? userLoginInfo.signInCount + 1 : 1,
          loginType: USER_LOGIN_TYPES.RFID
        }

        userLoginInfo ? await UserLoginHistoryModel.update(userInfo, { where: loginHistoryCondition }) : await UserLoginHistoryModel.create(userInfo)
      }

      const isBotUser = await BotUserModel.findOne({ where: { userId: user.id } })
      user.isBotUser = !!isBotUser

      // Find a blocked referral user based on tenant ID and block conditions
      const blockedReferralCode = await BlockedReferralUserModel.findOne({
        where: {
          tenantId: tenantId, // Ensure the record belongs to the correct tenant
          [Op.or]: [
            // Check if the referral user is blocked as a normal user
            { blockId: user.id, blockType: REFERRAL_BLOCK_TYPE.USER },

            // Check if the referral user's parent (agent) is blocked
            { blockId: user?.parentId, blockType: REFERRAL_BLOCK_TYPE.AGENT }
          ]
        }
      })

      user.isReferralCodeBlocked = !!blockedReferralCode

      const token = jwt.sign({ id: user.id }, authConfig.jwt_secret, {
        expiresIn: authConfig.expiry_time
      })

      const storedToken = await redisConnection.get(`user:${user.id}`)

      if (storedToken && (storedToken !== token)) {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.MULTIPLE_LOGIN_NOTIFICATION, { MultipleLoginNotification: { userId: user.id, multipleLogin: true, token: storedToken } })
        await redisConnection.del(`user:${user.id}`)
      }

      await redisConnection.set(`user:${user.id}`, token, { EX: authConfig.expiry_time })
      await user.save({ transaction: sequelizeTransaction })

      await this.context.sequelizeTransaction.commit()

      const userIds = []
      if (user) {
        userIds.push(user.id)
      }
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: userIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject)

      if (queueLog.id) {
        try {
          this.context.pubSub.publish(
            SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
            { QueueLog: { queueLogId: queueLog.id } }
          )
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
        }
      }

      // smartigo code
      const smartigoTenants = config.get('env') === 'production' ? SMARTIGO_TENANTS.PROD : SMARTIGO_TENANTS.STAGE
      if (smartigoTenants && smartigoTenants.includes(tenantId)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.LOGIN_STATS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [+user.id],
          tenantId: parseInt(tenantId)
        }
        await QueueLogModel.create(smartiGoObject)
      }

      return { token, user, resToken, idle_screen_logout_time: tenantTheme.idleScreenLogoutTime, success: true }
    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        throw new AuthenticationError(ERORR_TYPE.INVALID_REQUEST)
      }
    }
  }
}
