import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { v4 as uuid } from 'uuid'
import { checkLimit } from '../../common/checkLimit'
import { ALLOWED_FILE_TYPES, AMOUNT_LIMIT, EXCEPTIONS, FILE_UPLOAD_ERROR_CODES, HTTP_STATUS, MANUAL_DEPOSIT_TYPE, QUEUE_WORKER_CONSTANT, RESPONSIBLE_GAMING_CONSTANT, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES, UTR } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { s3 } from '../../lib/aws-s3.config'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'
import WithDrawRequestNotification from '../notification/notification-withdraw-request'
import checkUserPermission from '../../common/checkUserPermission'

const constraints = {
}

export default class createManualDepositRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        TenantBankConfiguration: TenantBankConfigurationModel,
        DepositRequest: DepositRequestModel,
        User: UserModel,
        UtrHistory: UtrHistoryModel,
        DuplicateUtrHistory: DuplicateUtrHistoryModel,
        QueueLog: QueueLogModel
      },
      tenant: { id: tenantId },
      auth: currentUser
    } = this.context

    await checkImpersonatedAccess(this.context)

    // demo user check
    const user = await UserModel.findOne({
      attributes: ['id', 'tenantId'],
      where: {
        id: currentUser.id,
        demo: false,
        active: true,
        tenantId
      }
    })
    if (!user) {
      throw new ApolloError(translate('INVALID_ACTION', language), HTTP_STATUS.INTERNAL_SERVER_ERROR)
    }

    const hasPermission = await checkUserPermission( tenantId, currentUser.id, this.args.manualDepositType === MANUAL_DEPOSIT_TYPE.BANK ? USER_SPECIFIC_PERMISSIONS_MODULES.MANUAL_DEPOSIT : (this.args.manualDepositType === MANUAL_DEPOSIT_TYPE.VIRTUAL ? USER_SPECIFIC_PERMISSIONS_MODULES.VIRTUAL_DEPOSIT : USER_SPECIFIC_PERMISSIONS_MODULES.QRCODE_DEPOSIT), USER_SPECIFIC_PERMISSIONS_ACTION.CREATE)
    if (!hasPermission) {
      throw new ApolloError(translate(this.args.manualDepositType === MANUAL_DEPOSIT_TYPE.BANK ? 'MANUAL_DEPOSIT_PERMISSION_DENIED' : ( this.args.manualDepositType === MANUAL_DEPOSIT_TYPE.VIRTUAL ? 'VIRTUAL_DEPOSIT_PERMISSION_DENIED' : 'QR_CODE_DEPOSIT_PERMISSION_DENIED'), language), HTTP_STATUS.FORBIDDEN)
    }
    let normalizedUtrNumber = this.args?.utrNumber?.trim()

    const utrExist = await UtrHistoryModel.findOne({
      attributes: ['id'],
      where: {
        utrNumber: normalizedUtrNumber,
        tenantId: tenantId,
        status: { [Op.not]: UTR.STATUS.REJECTED }
      },
      order: [['id', 'DESC']],
    });


    const s3Config = config.getProperties().s3

    // const regex = /^[A-Z0-9]{11}(?!.*[.:_-]{2})[A-Z0-9.:_-]{0,30}[A-Z0-9]$/
    // if (!regex.test(this.args.utrNumber)) {
    //   throw new ApolloError('Please Enter Valid UTR Number', 500)
    // }
    if (!this.args.amount) {
      throw new ApolloError(translate('INVALID_AMOUNT', language), 500);
    }

    const amount = parseFloat(this.args.amount)

    if (amount >= AMOUNT_LIMIT.DEPOSIT_AMOUNT_LIMIT) {
      throw new ApolloError(translate('AMOUNT_ERROR', language), HTTP_STATUS.BAD_REQUEST)
    }
    if (!Number.isInteger(amount)) {
      const decimalDigit = amount.toString().split('.')[1].length || 0
      if (decimalDigit > 4) {
        throw new ApolloError(translate('AMOUNT_ERROR', language), HTTP_STATUS.BAD_REQUEST)
      }
    }
    const allowed = await checkLimit.depositLimitCheck(this.context, currentUser.id, this.args.amount)
    if (allowed === RESPONSIBLE_GAMING_CONSTANT.DAILY_ERROR || allowed === RESPONSIBLE_GAMING_CONSTANT.WEEKLY_ERROR || allowed === RESPONSIBLE_GAMING_CONSTANT.MONTHLY_ERROR) {
      throw new ApolloError(translate(`${allowed.toUpperCase()}_DEPOSIT_LIMIT_EXCEEDED`, language), HTTP_STATUS.INTERNAL_SERVER_ERROR)
    }

    try {
      const ele = await this.args.files
      const stream = ele.createReadStream()

      if (ALLOWED_FILE_TYPES.indexOf(ele.mimetype.split('/')[1]) === -1) {
        throw new Error(1)
      }
      const key = `tenants/${tenantId}/user/${currentUser.id}/${uuid()}.${ele.mimetype.split('/')[1]}`
      const s3Params = {
        ACL: 'public-read',
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream
      }

      await s3.upload(s3Params).promise()

      if (utrExist) {
        const duplicateUtrHistoryObj = {
          userId: currentUser.id,
          tenantId,
          amount,
          receipt: key,
          utrNumber: normalizedUtrNumber,
          depositRequestId: utrExist.id,
        };

        await DuplicateUtrHistoryModel.create(duplicateUtrHistoryObj);
        throw { errorType: EXCEPTIONS.DUPLICATE_UTR_REQUEST, errorMsg: translate('UTR_DUPLICATE', language) };
      }

      if (utrExist) {
        const duplicateUtrHistoryObj = {
          userId: currentUser.id,
          tenantId,
          amount,
          receipt: key,
          utrNumber: normalizedUtrNumber,
          depositRequestId: utrExist.id,
        };

        await DuplicateUtrHistoryModel.create(duplicateUtrHistoryObj);

        throw { errorType: EXCEPTIONS.DUPLICATE_UTR_REQUEST, errorMsg: translate('UTR_DUPLICATE', language) };
      }

      const depositRequestObj = {
        userId: currentUser.id,
        tenantId,
        amount: this.args.amount,
        utrNumber: normalizedUtrNumber,
        depositType: 'manual',
        transactionReceipt: key,
        countryCode: this.args.countryCode,
        manualDepositType: this.args.manualDepositType,
        requestedAmount: this.args.amount,
        userRemark: this.args.userRemark || null,
        requestedCurrencyCode: this.args?.requestedCurrencyCode || null
      }

      if (this.args.manualDepositType === MANUAL_DEPOSIT_TYPE.VIRTUAL) {
        const tenantVirtualWalletInfo = await TenantBankConfigurationModel.findOne({
          attributes: ['cryptoExchangeRate'],
          where: { tenantId: tenantId, type: MANUAL_DEPOSIT_TYPE.VIRTUAL },
          order: [['id', 'ASC']]
        })

        if (tenantVirtualWalletInfo) {
          depositRequestObj.currencyConversion = tenantVirtualWalletInfo.cryptoExchangeRate
          depositRequestObj.amount = tenantVirtualWalletInfo.cryptoExchangeRate * depositRequestObj.amount
        }
      }

      const newDepositRequest = await DepositRequestModel.create(depositRequestObj)

      // Insert a new record into UtrHistory if the UTR number is processed, marking it as an approved UTR.
      let utrHistoryData = {
        utrNumber: normalizedUtrNumber,
        tenantId,
        actioneeId: currentUser.id,
        actioneeType: 'User',
        amount: depositRequestObj.amount,
        remarks: this.args.userRemark || null,
        utrType: UTR.TYPE.DEPOSIT,
        status: UTR.STATUS.OPENED
      };

      await UtrHistoryModel.create(utrHistoryData);

      await WithDrawRequestNotification(newDepositRequest, this.context)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.OCR_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [newDepositRequest.id]
      }

      await QueueLogModel.create(queueLogObject)

      return newDepositRequest
    } catch (e) {
      if (e.errorType === EXCEPTIONS.DUPLICATE_UTR_REQUEST) {
        throw new ApolloError(translate('UTR_NUMBER_ERROR', language), HTTP_STATUS.INTERNAL_SERVER_ERROR)
      } else {
        await ErrorLogHelper.logError(e, this.context, user)
        throw new ApolloError(translate(FILE_UPLOAD_ERROR_CODES[e.message] || 'FILE_UPLOAD_FAILED', language), HTTP_STATUS.INTERNAL_SERVER_ERROR)
      }
    }
  }
}
