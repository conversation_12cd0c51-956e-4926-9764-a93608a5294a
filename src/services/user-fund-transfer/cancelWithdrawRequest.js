import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { v4 as uuidv4 } from 'uuid'
import {
  CANCEL_WITHDRAW_REQUEST,
  ERORR_TYPE, HTTP_STATUS,
  QUEUE_WORKER_CONSTANT, <PERSON>ARTIGO_TENANTS,
  SUBSCRIPTION_CHANNEL,
  TRANSACTION_TYPES, WITHDRAW_REQUEST_STATUS, WITHDRAW_REQUEST_VERIFY_STATUS, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES
} from '../../common/constants'
import currencyConversion from '../../common/currencyConversionV3'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import { walletLocking } from '../../common/walletLocking'
import config from '../../config/app'
import { checkAuthUserIdWithPassword } from '../../lib/checkAuthUserIdWithPassword'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'
import checkUserPermission from '../../common/checkUserPermission'
const constraints = {}

/**
 * Provides service for cancel the withdraw wallet money request
 * @export
 * @class CancelWithdrawRequest
 * @extends {ServiceBase}
 */
export default class CancelWithdrawRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: { requestId },
      context: {
        req: { headers: { language } },
        databaseConnection: {
          Wallet: WalletModel,
          WithdrawRequest: WithdrawRequestModel,
          Transaction: TransactionModel,
          QueueLog: QueueLogModel,
          User: UserModel,
          sequelize
        },
        auth: { id: userId },
        tenant: { id: tenantId }
      }
    } = this

    await checkImpersonatedAccess(this.context)

    const password = Buffer.from(this.args.password, 'base64').toString('ascii')
    if (!await checkAuthUserIdWithPassword(userId, password, UserModel)) { throw new AuthenticationError(translate('WRONG_PASSWORD', language)) }
    let response
    try {
      this.context.sequelizeTransaction = await sequelize.transaction()

      const hasPermission = await checkUserPermission(tenantId, userId, USER_SPECIFIC_PERMISSIONS_MODULES.MANUAL_WITHDRAW, USER_SPECIFIC_PERMISSIONS_ACTION.CANCEL)
      if (!hasPermission) {
        return new ApolloError(translate('CANCEL_WITHDRAW_PERMISSION_DENIED', language), HTTP_STATUS.FORBIDDEN)
      }

      const withdrawRequest = await WithdrawRequestModel.findOne({
        where: { userId, id: requestId, status: WITHDRAW_REQUEST_STATUS.PENDING, tenantId },
        attributes: ['id', 'verify_status', 'amount', 'transactionId', 'status', 'actionableType', 'actionableId', 'actionedAt'],
        transaction: this.context.sequelizeTransaction,
        lock: {
          level: this.context.sequelizeTransaction.LOCK.UPDATE,
          of: WithdrawRequestModel
        },
        skipLocked: true
      })

      if (!withdrawRequest) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('WITHDRAW_REQUEST_LOCKED_OR_NOT_FOUND', language), statusCode: HTTP_STATUS.NOT_FOUND }
      }

      await withdrawRequest.reload({
        lock: { level: this.context.sequelizeTransaction.LOCK.UPDATE, of: WithdrawRequestModel },
        transaction: this.context.sequelizeTransaction
      })

      if (withdrawRequest.verify_status === WITHDRAW_REQUEST_VERIFY_STATUS.VERIFIED) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('WITHDRAW_REQUEST_ALREADY_VERIFIED', language), statusCode: HTTP_STATUS.NOT_FOUND }
      }

      withdrawRequest.status = WITHDRAW_REQUEST_STATUS.CANCELLED
      withdrawRequest.actionableType = 'User'
      withdrawRequest.actionableId = userId
      withdrawRequest.actionedAt = new Date().toISOString()
      response = await withdrawRequest.save({ transaction: this.context.sequelizeTransaction })

      const transaction = await TransactionModel.findOne({
        where: { id: withdrawRequest.transactionId, tenantId },
        attributes: ['id', 'status', 'comments'],
        useMaster: true
      })
      if (!transaction) {
        throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('TRANSACTION_NOT_FOUND', language), statusCode: HTTP_STATUS.NOT_FOUND }
      }
      transaction.status = CANCEL_WITHDRAW_REQUEST.TRANSACTION_STATUS
      transaction.comments = CANCEL_WITHDRAW_REQUEST.TRANSACTION_COMMENTS
      await transaction.save({ transaction: this.context.sequelizeTransaction })

      const user = { id: userId }
      const userWallet = await walletLocking(this.context, user, true)

      if (!userWallet) {
          const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTHER_TRANSACTION_IN_PROGRESS', language) }
          throw err
      }

      await userWallet.reload({
        lock: { level: this.context.sequelizeTransaction.LOCK.UPDATE, of: WalletModel },
        transaction: this.context.sequelizeTransaction
      })

      userWallet.amount += withdrawRequest.amount
      await userWallet.save({ transaction: this.context.sequelizeTransaction })

      const conversionRate = await userCurrencyExchange(this.context, userWallet.currencyId)
      const transactionId = uuidv4()

      let transactionObject = {
        targetWalletId: userWallet.id,
        targetCurrencyId: userWallet.currencyId,
        amount: withdrawRequest.amount,
        conversionRate,
        targetBeforeBalance: userWallet.amount - withdrawRequest.amount,
        targetAfterBalance: userWallet.amount,
        actioneeId: userId,
        actioneeType: 'User',
        status: 'success',
        tenantId,
        timestamp: new Date().getTime(),
        transactionType: TRANSACTION_TYPES.WITHDRAW_CANCEL,
        transactionId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        success: true,
        cancelTransactionId: transaction.id
      }

      transactionObject = await currencyConversion(this.context, transactionObject, userWallet, withdrawRequest.amount)
      const newTransaction = await TransactionModel.create(transactionObject, { transaction: this.context.sequelizeTransaction })

      const txnIds = []
      if (newTransaction) {
        txnIds.push(newTransaction.id)
        txnIds.push(transaction.id)
      }

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: this.context.sequelizeTransaction })

      // smartigo code
      let smartiCoTenants
      let smartiCoQueueLog
      if (config.get('env') === 'production') {
        smartiCoTenants = SMARTIGO_TENANTS.PROD
      } else {
        smartiCoTenants = SMARTIGO_TENANTS.STAGE
      }

      if (smartiCoTenants && smartiCoTenants.includes(this.context.tenant.id)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.SMARTICO_WITHDRAW_CANCELLED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [newTransaction.id],
          tenantId: this.context.tenant.id
        }
        smartiCoQueueLog = await QueueLogModel.create(smartiGoObject, { transaction: this.context.sequelizeTransaction })
      }
        await this.context.sequelizeTransaction.commit()
      if (smartiCoQueueLog) {
        try {
          this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, { QueueLog: { queueLogId: smartiCoQueueLog.id } })
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        }
      }
      try {
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: queueLog.id } }
        )
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
      } catch (e) {
        await ErrorLogHelper.logError(e, this.context, { id: userId, tenantId })
      }

      return response
    } catch (e) {
      await this.context.sequelizeTransaction.rollback()

      // Handle custom errors with specific error type
      if (e.errorType === ERORR_TYPE.CUSTOM) {
        throw new ApolloError(e.errorMsg, e.statusCode);
      }
      await ErrorLogHelper.logError(e, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
