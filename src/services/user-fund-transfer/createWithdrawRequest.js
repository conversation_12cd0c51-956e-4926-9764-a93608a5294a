import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { checkLimit } from '../../common/checkLimit'
import validateWithdrawalByBetHistory from '../../common/checkPlayerPlaceBet'
import {
  ALLOWED_PERMISSIONS, AMOUNT_LIMIT,
  ERORR_TYPE,
  HTTP_STATUS,
  PLAYER_CATEGORY,
  QUEUE_WORKER_CONSTANT,
  RESPONSIBLE_GAMING_CONSTANT,
  SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL, TABLES,
  TENANT_SETTINGS_TYPE,
  TRANSACTION_TYPES, USER_LOGIN_TYPES, USER_MIN_DEPOSIT_WITHDRAWAL_CHECK, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES, WITHDRAW_REQUEST_STATUS
} from '../../common/constants'
import currencyConversionV3 from '../../common/currencyConversionV3'
import <PERSON>rror<PERSON><PERSON><PERSON><PERSON><PERSON> from '../../common/errorLog'
import { filterValidMultipliers } from '../../common/helper'
import kycCheck from '../../common/kycCheck'
import ServiceBase from '../../common/serviceBase'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import config from '../../config/app'
import { checkAuthUserIdWithPassword } from '../../lib/checkAuthUserIdWithPassword'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import generateUniqueTransactionId from '../../lib/generateUniqueTransactionId'
import translate from '../../lib/languageTranslate'
import WithDrawRequestNotification from '../notification/notification-withdraw-request'
import checkUserPermission from '../../common/checkUserPermission'

const constraints = {
  amount: {
    numericality: {
      greaterThan: 0
    }
  },
  network: {
    type: 'string'
  },
  ip: {
    type: 'string'
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  },
  version: {
    type: 'string'
  },
  data: {
    type: 'object'
  }
}

/**
 * Provides service for fetch wallet money request ot the user
 * @export
 * @class CreateWithdrawRequest
 * @extends {ServiceBase}
 */
export default class CreateWithdrawRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          Wallet: WalletModel,
          User: UserModel,
          WithdrawRequest: WithdrawRequestModel,
          Transaction: TransactionModel,
          QueueLog: QueueLogModel,
          UserBankDetails: UserBankDetailsModel,
          TenantThemeSetting: TenantThemeSettingModel,
          TenantCredential: TenantCredentialModel,
          DepositWager: DepositWagerModel,
          WithdrawalSettings: WithdrawalSettingsModel,
          TenantSetting: TenantSettingModel,
          PlayerSummaryProviderWise: PlayerSummaryProviderWiseModel,
          UserLoginHistory: UserLoginHistoryModel,
          sequelize
        },
        tenant: { id: tenantId },
        req: { headers: { language } },
        auth: { id: userId }
      },
      args: { input: { amount, bankId, ip, network, deviceType, deviceModel, version, deviceId, data } }
    } = this

    await checkImpersonatedAccess(this.context)

    let responseWithdrawRequest

    const user = await UserModel.findOne({
      attributes: ['id', 'enableWithdrawRequests', 'vipLevel', 'tenantId', 'parentType', 'withdrawWagerAllowed', 'categoryType', 'wagerMultiplier', 'createdAt'],
      where: { id: userId, demo: false, tenantId }
    })

    if (!user) {
      throw new ApolloError(translate('INVALID_ACTION', language), 400)
    }

    if (!user.enableWithdrawRequests) {
      throw new Error(translate('WITHDRAWAL_DISABLE', language))
    }

    const hasPermission = await checkUserPermission(tenantId, userId, USER_SPECIFIC_PERMISSIONS_MODULES.MANUAL_WITHDRAW, USER_SPECIFIC_PERMISSIONS_ACTION.CREATE)
    if (!hasPermission) {
      throw new ApolloError(translate('CREATE_WITHDRAW_PERMISSION_DENIED', language), HTTP_STATUS.FORBIDDEN)
    }

    const allowed = await checkLimit.withdrawLimitCheck(this.context, user.id, amount)
    if (allowed === RESPONSIBLE_GAMING_CONSTANT.DAILY_ERROR || allowed === RESPONSIBLE_GAMING_CONSTANT.WEEKLY_ERROR || allowed === RESPONSIBLE_GAMING_CONSTANT.MONTHLY_ERROR) {
      throw new ApolloError(translate(`${allowed.toUpperCase()}_WITHDRAW_LIMIT_EXCEEDED`, language), HTTP_STATUS.INTERNAL_SERVER_ERROR)
    }

    const providers = await TenantThemeSettingModel.findOne({ attributes: ['allowedModules', 'smsEnable'], where: { tenantId: user.tenantId }, raw: true })

    if (providers.allowedModules && providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WITHDRAWAL_SETTINGS) &&
      providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY)) {
      // Fetch the user-specific withdrawal limit for their category
      const userPerDataLimit = await WithdrawalSettingsModel.findOne({
        attributes: ['maxWithdrawalCount'],
        where: { category: PLAYER_CATEGORY[user?.categoryType], isEnabled: true, tenantId: user.tenantId },
        raw: true
      })

      if (userPerDataLimit && userPerDataLimit.maxWithdrawalCount) {
        // Calculate the current count of pending or approved withdrawal requests
        const startOfToday = new Date()
        startOfToday.setHours(0, 0, 0, 0)

        const endOfToday = new Date()
        endOfToday.setHours(23, 59, 59, 999)

        // Count only today's pending or approved withdrawal requests
        const currentCount = await WithdrawRequestModel.count({
          where: {
            userId,
            tenantId,
            status: [WITHDRAW_REQUEST_STATUS.PENDING, WITHDRAW_REQUEST_STATUS.APPROVED],
            createdAt: {
              [Op.between]: [startOfToday, endOfToday]
            }
          }
        })

        // If current withdrawals exceed the allowed limit, throw an error
        if (currentCount >= userPerDataLimit.maxWithdrawalCount) {
          throw new Error(translate('WITHDRAWAL_PER_DAY_LIMIT_EXCEEDED', language))
        }
      }
    }

    if (!user.withdrawWagerAllowed && providers?.allowedModules && providers?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION)) {
      const wagerExists = await DepositWagerModel.findOne({
        attributes: ['id', 'wagerAmount', 'wagerAchieved'],
        where: {
          tenantId,
          userId,
          wageringCompleted: false
        },
        raw: true
      })

      if (wagerExists) {
        let remainingAmount = wagerExists?.wagerAmount - wagerExists?.wagerAchieved
        let wagerMultiplier = 1

        // Fetch global and VIP level wager multipliers
        const tenantSettings = await TenantSettingModel.findAll({
          where: {
            tenantId,
            [Op.or]: [
              { type: TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER },
              { type: TENANT_SETTINGS_TYPE.MINIMUM_WAGERING_PERCENTAGE },
              { type: TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER, key: user?.vipLevel?.toString() }
            ]
          },
          attributes: ['key', 'value', 'type'],
          raw: true
        })

        // Map the settings for easier access
        const settingsMap = tenantSettings.reduce((map, setting) => {
          map[setting.type] = setting.value
          return map
        }, {})

        const multipliers = filterValidMultipliers([
          user.wagerMultiplier,
          settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER],
          settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER]
        ])

        // Step 1: Check if any multiplier (User, VIP, global) is zero
        if (multipliers.includes(0)) {
          wagerMultiplier = 0
        } else {
          // Step 2: Determine the wager multiplier priority
          if (user.wagerMultiplier) {
            wagerMultiplier = Number(user.wagerMultiplier)
          } else if (settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER]) {
            wagerMultiplier = Number(settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER])
          } else if (settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER]) {
            wagerMultiplier = Number(settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER])
          }
        }

        if (wagerMultiplier) {
          const minWageringPercent = settingsMap[TENANT_SETTINGS_TYPE.MINIMUM_WAGERING_PERCENTAGE]

          // Check if wagering threshold logic should be applied
          const shouldCheckWageringThreshold = minWageringPercent && minWageringPercent > 0

          if (shouldCheckWageringThreshold) {
            remainingAmount = (wagerExists.wagerAmount * minWageringPercent / 100) - wagerExists.wagerAchieved
          }

          // Ensure remainingAmount is non-negative and format to two decimal places
          remainingAmount = Math.max(remainingAmount, 0).toFixed(2)

          if (parseFloat(remainingAmount) > 0) {
            const translatedErrorMessage = translate('DEPOSIT_WAGERING_AMOUNT_NOT_ACHIEVED', language)
              .replace('#amount', remainingAmount)
              .replace('#wagerMultiplier', `${wagerMultiplier}x`)

            throw new Error(translatedErrorMessage)
          }
        }
      }
    }

    if (providers.allowedModules && providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REQUIRED_KYC_FOR_WITHDRAWAL)) {
      const isVerifiedDocument = await kycCheck(this.context, userId)
      if (!isVerifiedDocument) {
        throw new UserInputError(translate('KYC_NOT_DONE', language))
      }
    }

    if (providers.allowedModules && providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WITHDRAWAL_REQUEST_LIMITATION)) {
      const pendingRequestExist = await WithdrawRequestModel.findOne({
        where: {
          userId,
          tenantId,
          status: WITHDRAW_REQUEST_STATUS.PENDING
        },
        attributes: ['id'],
        raw: true
      })

      if (pendingRequestExist) {
        throw new Error(translate('PENDING_WITHDRAWAL_REQUEST', language))
      }
    }

    const password = Buffer.from(this.args.input.password, 'base64').toString('ascii')
    if (!await checkAuthUserIdWithPassword(userId, password, UserModel)) { throw new AuthenticationError(translate('WRONG_PASSWORD', language)) }

    const isUserWalletExist = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' }, attributes: ['id', 'amount', 'currencyId', 'nonCashAmount'] })

    const bankDetail = await UserBankDetailsModel.findOne({ where: { id: bankId }, attributes: ['id', 'bankName', 'name', 'accountNumber', 'bankIfscCode', 'phoneNumber'] })

    if (!bankDetail) {
      throw new ApolloError(translate('BANK_DETAILS_NOT_FOUND', language), 400)
    }
    if (amount >= AMOUNT_LIMIT.WITHDRAW_AMOUNT_LIMIT) {
      throw new ApolloError(translate('AMOUNT_ERROR', language), 400)
    }
    if (!Number.isInteger(amount)) {
      const decimalDigit = amount.toString().split('.')[1].length || 0
      if (decimalDigit > 4) {
        throw new ApolloError(translate('AMOUNT_ERROR', language), 400)
      }
    }

    if (amount <= 0) {
      throw new UserInputError(translate('INVALID_AMOUNT', language))
    }

    if (amount > isUserWalletExist.amount) {
      throw new UserInputError(translate('No_SUFFICIENT_AMOUNT', language))
    }

    const userMinDepositWithdrawalCheck = await TenantCredentialModel.findOne({
      where: {
        tenantId: tenantId,
        key: 'USER_MIN_DEPOSIT_WITHDRAWAL_CHECK'
      },
      attributes: ['value']
    })

    const totalDepositAmount = await TransactionModel.sum('amount', {
      where: {
        targetWalletId: isUserWalletExist.id,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        createdAt: {
          [Op.and]: {
            [Op.gte]: `${(new Date(user?.createdAt)).toISOString().substring(0, 10)} 00:00:00.000+00`
          }
        }
      }
    })

    const minDepositAmount = (userMinDepositWithdrawalCheck?.value ? userMinDepositWithdrawalCheck.value : USER_MIN_DEPOSIT_WITHDRAWAL_CHECK)

    if (totalDepositAmount < minDepositAmount) {
      throw new UserInputError(translate('INVALID_DEPOSIT_AMOUNT', language) + minDepositAmount)
    }

    const transactionId = await generateUniqueTransactionId(TransactionModel)

    let transactionObject = {
      sourceWalletId: isUserWalletExist.id,
      sourceCurrencyId: isUserWalletExist.currencyId,
      amount: amount,
      comments: 'Pending confirmation from admin',
      actioneeId: userId,
      actioneeType: 'User',
      status: 'success',
      tenantId: this.context.tenant.id,
      timestamp: new Date().getTime(),
      transactionType: TRANSACTION_TYPES.WITHDRAW,
      transactionId,
      // errorDescription: 'Completed Successfully',
      errorCode: 0,
      success: true
    }

    const withdrawRequestObj = {
      userId,
      bankId: bankDetail.id,
      bankName: bankDetail.bankName,
      name: bankDetail.name,
      accountNumber: bankDetail.accountNumber,
      ifscCode: bankDetail.bankIfscCode,
      amount,
      phoneNumber: bankDetail.phoneNumber,
      tenantId
    }

    if (providers.allowedModules && providers.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.CHECK_WITHDRAW_IS_SUSPICIOUS)) {
      const isBankAccountAlreadyLinked = await UserBankDetailsModel.findOne({
        where: {
          accountNumber: bankDetail.accountNumber,
          tenantId,
          userId: {
            [Op.ne]: userId
          },
          isDeleted: false
        },
        attributes: ['id']
      })
      const playerHasBettingActivity = await validateWithdrawalByBetHistory(userId, tenantId, PlayerSummaryProviderWiseModel)

      const oldestIPRecord = await UserLoginHistoryModel.findOne({
        where: {
          ip,
          tenantId,
          loginType: [USER_LOGIN_TYPES.WITHDRAW]
        },
        order: [['createdAt', 'ASC']],
        raw: true
      })

      const isIPAlreadyUsedByAnotherUserBefore = oldestIPRecord && oldestIPRecord.userId !== userId

      // Determine the remark based on the conditions
      if (isBankAccountAlreadyLinked && !playerHasBettingActivity) {
        withdrawRequestObj.suspiciousReason = 'No betting activity and attempt to withdraw to an already linked bank account.'
      } else if (!playerHasBettingActivity) {
        withdrawRequestObj.suspiciousReason = 'No betting activity detected for this user.'
      } else if (isBankAccountAlreadyLinked) {
        withdrawRequestObj.suspiciousReason = 'Attempt to withdraw to an already linked bank account.'
      } else if (isIPAlreadyUsedByAnotherUserBefore) {
        withdrawRequestObj.suspiciousReason = 'IP address has been used by another user before.'
      }

      // Determine if the withdrawal request is suspicious
      withdrawRequestObj.isSuspicious = Boolean(isBankAccountAlreadyLinked || !playerHasBettingActivity || isIPAlreadyUsedByAnotherUserBefore)

      if (withdrawRequestObj.isSuspicious && !withdrawRequestObj?.suspiciousReason?.toLowerCase().includes('ip')) {
        const lastWithdrawReq = await WithdrawRequestModel.findOne({
          where: {
            accountNumber: bankDetail.accountNumber,
            tenantId: tenantId,
            userId: {
              [Op.ne]: userId
            }
          },
          order: [['id', 'DESC']]
        })

        if (lastWithdrawReq) withdrawRequestObj.suspiciousWithdrawId = parseInt(lastWithdrawReq.id)
      }
    }

    this.context.sequelizeTransaction = await sequelize.transaction()

    try {
      const userWallet = await WalletModel.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: this.context.sequelizeTransaction,
        lock: {
          level: this.context.sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: true
      })

      if (!userWallet) {
        const err = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('OTHER_TRANSACTION_IN_PROGRESS', language) }
        throw err
      }

      await userWallet.reload({
        lock: {
          level: this.context.sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        transaction: this.context.sequelizeTransaction
      })

      if (amount > userWallet.amount) {
        throw new UserInputError(translate('No_SUFFICIENT_AMOUNT', language))
      }

      userWallet.amount -= amount
      const skipWalletHook = true
      await userWallet.save({ transaction: this.context.sequelizeTransaction, skipWalletHook })

      transactionObject.conversionRate = await userCurrencyExchange(this.context, userWallet.currencyId)
      transactionObject = await currencyConversionV3(this.context, transactionObject, userWallet, amount)
      transactionObject.sourceBeforeBalance = (userWallet.amount + amount)
      transactionObject.sourceAfterBalance = userWallet.amount
      const skipTransactionHook = true
      const transactionCreated = await TransactionModel.create(transactionObject, { transaction: this.context.sequelizeTransaction, skipTransactionHook })
      withdrawRequestObj.transactionId = transactionCreated.id

      responseWithdrawRequest = await WithdrawRequestModel.create(withdrawRequestObj, { transaction: this.context.sequelizeTransaction })

      const loginHistoryCondition = {
        tenantId: tenantId,
        userId: userId,
        loginType: { [Op.notIn]: [USER_LOGIN_TYPES.GAME_LAUNCH, USER_LOGIN_TYPES.APP_LAUNCH, USER_LOGIN_TYPES.RECONNECT, USER_LOGIN_TYPES.WITHDRAW] }
      }

      const userLoginInfo = await UserLoginHistoryModel.findOne({
        attributes: ['id', 'lastLoginDate'],
        where: loginHistoryCondition,
        raw: true,
        order: [['createdAt', 'DESC']]
      })

      const loginHistoryObj = {
        userId: user.id,
        tenantId,
        ip,
        network,
        deviceId,
        deviceType,
        deviceModel,
        version,
        data: {
          ...data,
          withdrawId: responseWithdrawRequest.id,
          withdrawAmount: responseWithdrawRequest.amount
        },
        loginType: USER_LOGIN_TYPES.WITHDRAW,
        lastLoginDate: userLoginInfo?.lastLoginDate || null
      }

      await UserLoginHistoryModel.create(loginHistoryObj, { transaction: this.context.sequelizeTransaction })

      const txnIds = []
      if (transactionCreated) {
        txnIds.push(transactionCreated.id)
      }
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: this.context.sequelizeTransaction })
      try {
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: queueLog?.id } }
        )
      } catch (e) {
        await ErrorLogHelper.logError(e, this.context, user)
      }
      await WithDrawRequestNotification(responseWithdrawRequest, this.context)

      // smartigo code
      let smartigoTenants
      let smartiGoQueueLog
      if (config.get('env') === 'production') {
        smartigoTenants = SMARTIGO_TENANTS.PROD
      } else {
        smartigoTenants = SMARTIGO_TENANTS.STAGE
      }

      if (smartigoTenants && smartigoTenants.includes(this.context.tenant.id)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.SMARTICO_WITHDRAW_REQUESTED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds,
          tenantId: this.context.tenant.id
        }
        smartiGoQueueLog = await QueueLogModel.create(smartiGoObject)
      }

      // SMS when a player Initiate a withdrawal request
      if (providers?.allowedModules?.includes('sendSmsAlert') && providers?.smsEnable?.includes('initiateByUser')) {
        await QueueLogModel.create({
          type: QUEUE_WORKER_CONSTANT.DEP_WITHDRAW_SMS_OTP,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ id: +(responseWithdrawRequest.id) }],
          tenantId: responseWithdrawRequest.tenantId
        }, { transaction: this.context.sequelizeTransaction })
      }

      await this.context.sequelizeTransaction.commit()
      if (smartiGoQueueLog) {
        try {
          this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, { QueueLog: { queueLogId: smartiGoQueueLog.id } })
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, user)
        }
      }

      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WITHDRAW_NOTIFICATION, { userWithdrawAmount: amount, UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
      } catch (e) {
        await ErrorLogHelper.logError(e, this.context, user)
      }

      return responseWithdrawRequest
    } catch (e) {
      await this.context.sequelizeTransaction.rollback()
      await ErrorLogHelper.logError(e, this.context, user)
      if (e.errorType === ERORR_TYPE.CUSTOM) {
        throw new UserInputError(e.errorMsg)
      }
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR'), 500)
    }
  }
}
