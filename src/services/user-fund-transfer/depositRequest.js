import { ApolloError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import depositLimitCheck from '../../lib/depositLimitCheck'
import generateRandomOrderId from '../../lib/generateRandomOrderId'
import translate from '../../lib/languageTranslate'

/**
 * Generate unique order id for user's deposit request
 * @export
 * @class DepositRequest
 * @extends {ServiceBase}
 */
export default class DepositRequest extends ServiceBase {
  async run () {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          DepositRequest: DepositRequestModel
        },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { amount }
    } = this

    try {
    const allowed = await depositLimitCheck(this.context, userId, amount)

    if (!allowed) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('DEPOSIT_LIMIT_EXCEEDED', language), errorCode: 400 }
    }

    const orderId = await generateRandomOrderId(this.context)

    const newDepositRequest = await DepositRequestModel.create({ orderId, userId })

    return newDepositRequest
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
