import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import checkUserPermission from '../../common/checkUserPermission'
import { HTTP_STATUS, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES } from '../../common/constants'

const constraints = {
}

/**
 * Provides service for fetch all the withdraw request user made till date
 * @export
 * @class WithdrawRequest
 * @extends {ServiceBase}
 */
export default class WithdrawRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    let {
      args: { input: { page, limit, dateFrom, dateTo, orderBy } = {} },
      context: {
        auth: { id: userId },
        databaseConnection: {
          WithdrawRequest: WithdrawRequestModel
        },
        req: { headers: { language } },
        tenant: { id: tenantId }
      }
    } = this
    try {
    const hasPermission = await checkUserPermission( tenantId, userId, USER_SPECIFIC_PERMISSIONS_MODULES.MANUAL_WITHDRAW, USER_SPECIFIC_PERMISSIONS_ACTION.READ)
    if (!hasPermission) {
      return new ApolloError(translate('WITHDRAW_PERMISSION_DENIED', language), HTTP_STATUS.FORBIDDEN)
    }
      
    const todayDateSec = new Date(dateTo || new Date()).setDate(new Date(dateTo || new Date()).getDate())
    dateTo = new Date(todayDateSec)
    let sortOrder = [['createdAt', 'desc'], ['id', 'asc']]

    if (orderBy) {
      sortOrder = Object.entries(orderBy)
      sortOrder.push(['id', 'asc'])
    }

    let offset = 0
    let dateObj = { createdAt: { [Op.lte]: (dateTo.toISOString()) } }

    if (page && limit) {
      offset = (page - 1) * limit
    }

    // date object creation
    if (dateFrom) {
      if (new Date(dateFrom).toISOString() < dateTo.toISOString()) {
        dateObj = {
          createdAt: { [Op.gte]: (dateFrom), [Op.lte]: (dateTo.toISOString()) }
        }
      } else if (new Date(dateFrom).toISOString() === dateTo.toISOString()) {
        dateTo = new Date(dateTo.setDate(dateTo.getDate() + 1))
        dateObj = {
          createdAt: { [Op.gte]: (dateFrom), [Op.lte]: dateTo.toISOString() }
        }
      }
    }

    const { count, rows: withdrawRequests } = await WithdrawRequestModel.findAndCountAll({
      where: {
        userId,
        ...dateObj
      },
      raw: true,
      limit: limit || 10,
      offset: offset || 0,
      order: sortOrder
    })

    return { count, requests: withdrawRequests }
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
