import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import { BONUS_STATUS, TRANSACTION_TYPES } from '../../common/constants'
import currencyConversion from '../../common/currencyConversion'
import <PERSON>rror<PERSON><PERSON><PERSON><PERSON>per from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import translate from '../../lib/languageTranslate'

const constraints = {
  amount: {
    numericality: {
      greaterThan: 0
    }
  }
}

/**
 * Provides service for adding money in the wallet
 * @export
 * @class DepositFund
 * @extends {ServiceBase}
 */
export default class DepositFund extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      Bonus: BonusModel,
      UserBonus: UserBonusModel,
      Wallet: WalletModel,
      DepositBonusSetting: DepositBonusSettingModel,
      Transaction: TransactionModel
    } = this.context.databaseConnection
    const { amount } = this.args
    const { id: userId } = this.context.auth
    const {
      tenant: { id: tenantId },
      headers: { language }
    } = this.context

    try {
    const userWallet = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' } })

    let transactionObject = {
      targetWalletId: userWallet.id,
      targetCurrencyId: userWallet.currencyId,
      amount: amount,
      conversionRate: await userCurrencyExchange(this.context, userWallet.currencyId),
      targetBeforeBalance: userWallet.amount,
      targetAfterBalance: userWallet.amount + amount,
      comments: '',
      actioneeId: userId,
      actioneeType: 'User',
      tenantId: this.context.tenant.id,
      timestamp: new Date().getTime(),
      transactionType: TRANSACTION_TYPES.DEPOSIT,
      //errorDescription: 'Completed Successfully',
      errorCode: 0,
      success: true
    }

    transactionObject = await currencyConversion(this.context, transactionObject, userWallet, amount)

    await TransactionModel.create(transactionObject)
    userWallet.amount += amount

    const userActiveDepositBonus = await UserBonusModel.findOne({
      where: {
        status: BONUS_STATUS.ACTIVE,
        kind: 'deposit',
        expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        bonusAmount: 0
      }
    })

    // handling deposit bonus cases
    if (userActiveDepositBonus) {
      const bonus = await BonusModel.findOne({
        where: {
          id: userActiveDepositBonus.bonusId,
          enabled: true
        },
        include: {
          model: DepositBonusSettingModel
        }
      })

      // bonus not found check
      if (!bonus) {
        userActiveDepositBonus.status = BONUS_STATUS.EXPIRED
        await userActiveDepositBonus.save()
        return userWallet.save()
      }

      // deposit amount less than min deposit amount check
      if (amount < bonus.DepositBonusSetting.minDeposit) {
        return userWallet.save()
      }

      const bonusToBeGiven = (amount * (bonus.percentage / 100)) > bonus.DepositBonusSetting.maxBonus ? bonus.DepositBonusSetting.maxBonus : amount * (bonus.percentage / 100)

      userActiveDepositBonus.bonusAmount = bonusToBeGiven
      userActiveDepositBonus.rolloverBalance = bonusToBeGiven * bonus.DepositBonusSetting.rolloverMultiplier
      userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString()
      await userActiveDepositBonus.save()
    }

    return userWallet.save()
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
