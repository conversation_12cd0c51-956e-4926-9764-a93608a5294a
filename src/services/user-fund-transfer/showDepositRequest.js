import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import <PERSON>rro<PERSON><PERSON>ogHel<PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import checkUserPermission from '../../common/checkUserPermission'
import { USER_SPECIFIC_PERMISSIONS_MODULES, USER_SPECIFIC_PERMISSIONS_ACTION, HTTP_STATUS } from '../../common/constants'

const constraints = {
}
/**
 * Provides service for fetch all the withdraw request user made till date
 * @export
 * @class ShowDepositRequest
 * @extends {ServiceBase}
 */
export default class ShowDepositRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    let {
      args: { input: { page, limit, dateFrom, dateTo, orderBy, depositType, searchKey } = {} },
      context: {
        auth: { id: userId },
        databaseConnection: {
          DepositRequest: DepositRequestModel,
          paymentProviders: paymentProvidersModel,
          tenantPaymentConfiguration: tenantPaymentConfigurationModel
        },
        req: { headers: { language } },
        tenant: { id: tenantId }
      }
    } = this

    try {
      const todayDateSec = new Date(dateTo || new Date()).setDate(new Date(dateTo || new Date()).getDate())
      dateTo = new Date(todayDateSec)
      let sortOrder = [['createdAt', 'desc'], ['id', 'asc']]

      if (orderBy) {
        sortOrder = Object.entries(orderBy)
        sortOrder.push(['id', 'asc'])
      }

      const hasPermission = await checkUserPermission( tenantId, userId, USER_SPECIFIC_PERMISSIONS_MODULES.DEPOSIT_REQUEST_LIST, USER_SPECIFIC_PERMISSIONS_ACTION.READ)
      if (!hasPermission) {
        return new ApolloError(translate('DEPOSIT_REQUEST_LIST_PERMISSION_DENIED', language), HTTP_STATUS.FORBIDDEN)
      }

      let offset = 0
      let dateObj = { createdAt: { [Op.lte]: (dateTo.toISOString()) } }
      const DEFAULT_LIMIT = 10;
      const parsedLimit = parseInt(limit, 10) || DEFAULT_LIMIT;

      if (page) {
        offset = (parseInt(page, 10) - 1) * parsedLimit;
      }

      // date object creation
      if (dateFrom) {
        if (new Date(dateFrom).toISOString() < dateTo.toISOString()) {
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: (dateTo.toISOString()) }
          }
        } else if (new Date(dateFrom).toISOString() === dateTo.toISOString()) {
          dateTo = new Date(dateTo.setDate(dateTo.getDate() + 1))
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: dateTo.toISOString() }
          }
        }
      }
      const searchAmount = isNaN(searchKey) ? null : parseFloat(searchKey)

      // Create search conditions based on 'searchKey'.
      const customSearchConditions = []
      if (searchKey && searchKey.trim()) {
        customSearchConditions.push({
          [Op.or]: [
            {
              utrNumber: {
                [Op.like]: `%${searchKey}%`
              }
            },
            {
              orderId: {
                [Op.like]: `%${searchKey}%`
              }
            },
          ]
        });
      }
      if (searchAmount) {
        customSearchConditions.push({
          amount: searchAmount
        })
      }

      let whereStatement = {}
      if (depositType === 'all') {
        whereStatement = {
          userId,
          ...dateObj,
          ...(customSearchConditions.length && { [Op.or]: customSearchConditions })
        }
      } else {
        whereStatement = {
          userId,
          depositType,
          ...dateObj,
          ...(customSearchConditions.length && { [Op.or]: customSearchConditions })
        }
      }

      const queryOptions = {
        subQuery: false,
        where: whereStatement,
        limit: parsedLimit,
        order: sortOrder,
        include: [
          {
            model: paymentProvidersModel,
            attributes: ['id', 'providerName'],
            include: {
              model: tenantPaymentConfigurationModel,
              attributes: ['description'],
              where: { tenantId },
              required: false
            }
          }
        ]
      };

      // Only apply offset when there's no custom search (e.g. UTR search)
      if (!customSearchConditions.length && offset > 0) {
        queryOptions.offset = offset;
      }

      // Then call the query
      const { count, rows: depositRequests } = await DepositRequestModel.findAndCountAll(queryOptions);

      return { count, requests: depositRequests };

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
