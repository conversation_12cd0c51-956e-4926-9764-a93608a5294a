import { ApolloError } from 'apollo-server-express';
import moment from 'moment';
import { Op, Sequelize } from 'sequelize';
import { ALLOWED_PERMISSIONS, ERORR_TYPE } from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

const constraints = {
}
/**
 * Provides service for fetch all the withdraw request user made till date and sum the amount
 * @export
 * @class CheckWithdrawalLimit
 * @extends {ServiceBase}
 */
export default class CheckWithdrawalLimit extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    let {
      context: {
        tenant: Tenant,
        auth: { id: userId },
        databaseConnection: {
          User: UserModel,
          WithdrawRequest: WithdrawRequestModel,
          PlayerCategory: PlayerCategoryModel,
          PlayerCategoryLevel: PlayerCategoryLevelModel,
          TenantThemeSetting: TenantThemeSettingModel
        },
        req: { headers: { language } }
      }
    } = this

    try {
    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId: Tenant.id
      },
      attributes: ['allowedModules']
    })

    const hasPlayerCategory = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PLAYER_CATEGORIZATION)

    if(!hasPlayerCategory)
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: 'Permission Denied' }

    // Fetch the user's subcategory, category, and withdrawal limit
    const userSubcategory = await UserModel.findOne({
      where: { id: userId },
      attributes: ['playerCategoryLevel'],
      include: [{
        model: PlayerCategoryLevelModel,
        attributes: ['categoryId'],
        include: {
          model: PlayerCategoryModel,
          attributes: ['withdrawalLimit']
        }
      }],
    });

    let totalWithdrawalLimit = 0, usedWithdrawalLimit = 0, remainingWithdrawalLimit = 0, isWithdrawalEnabled;

    // Check if category or subcategory found
    if (!userSubcategory || !userSubcategory?.PlayerCategoryLevel?.categoryId) {
      isWithdrawalEnabled = 2;
    } else {
      totalWithdrawalLimit = userSubcategory?.PlayerCategoryLevel?.PlayerCategory?.withdrawalLimit;

      // Calculate today's used withdrawal amount
      const todayWithdrawals = await WithdrawRequestModel.findOne({
        where: {
          userId,
          status: { [Op.in]: ['pending', 'approved'] },
          createdAt: { [Op.between]: [moment().startOf('day').toDate(), moment().endOf('day').toDate()] }
        },
        attributes: [[Sequelize.fn('SUM', Sequelize.col('amount')), 'totalAmount']],
        raw: true
      });

      usedWithdrawalLimit = todayWithdrawals && todayWithdrawals.totalAmount ? parseFloat(todayWithdrawals.totalAmount) : 0;
      remainingWithdrawalLimit = totalWithdrawalLimit - usedWithdrawalLimit;
      isWithdrawalEnabled = remainingWithdrawalLimit <= 0 ? 0 : 1;
    }

    return {
      totalWithdrawalLimit,
      usedWithdrawalLimit,
      remainingWithdrawalLimit,
      isWithdrawalEnabled,
    };

  } catch (error) {
      if (error.errorType === ERORR_TYPE.CUSTOM) {
        throw new Error(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId: Tenant.id })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
