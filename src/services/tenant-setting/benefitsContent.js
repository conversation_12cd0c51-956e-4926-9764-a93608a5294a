import ServiceBase from '../../common/serviceBase';
import { ApolloError } from 'apollo-server-express'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import translate from '../../lib/languageTranslate'

/**
 * get BenefitsContent
 * @export
 * @class benefitsContent
 * @extends {ServiceBase}
 */
export default class BenefitsContent extends ServiceBase {
  async run () {

    const {
      databaseConnection: {
        BenefitsContent: BenefitsContentModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const benefitsContent = await BenefitsContentModel.findAll({
      attributes: ['id', 'title', 'buttonText', 'buttonRedirection', 'content', 'image'],
      where: {
        tenantId,
        isActive: true
      },
      order: [['createdAt', 'DESC']],
      raw: true
    })

    return benefitsContent
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
