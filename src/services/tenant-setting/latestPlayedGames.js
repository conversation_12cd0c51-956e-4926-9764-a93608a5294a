import { ApolloError } from 'apollo-server-express'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

export default class LatestPlayedGames extends ServiceBase {
  async run () {
    const {
      tenant: { id: tenantId },
      req: { headers: { language } },
      auth: { id: currentUser }
    } = this.context

    try {
    const sql = `
      SELECT * FROM most_played_games where tenant_id = ${tenantId} AND user_id = ${currentUser} ORDER BY play_count DESC;
    `

    const result = await sequelize.query(sql, {
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    })

    return { count: result.length, data: result.map(({ id, uuid, name, image, provider }) => ({ pageMenuTitle: provider, name, CasinoItem: { hasLobby: null, id, image, uuid } })) }
  } catch (error) {
      await <PERSON><PERSON>r<PERSON>ogHelper.logError(error, this.context, { id: currentUser, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
