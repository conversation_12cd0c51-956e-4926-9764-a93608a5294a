import { ApolloError } from 'apollo-server-express';
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

/**
 * get promotional videos
 * @export
 * @class PromotionVideos
 * @extends {ServiceBase}
 */
export default class PromotionVideo extends ServiceBase {
  async run () {

    const {
      context: {
        databaseConnection: {
          PromotionVideo: PromotionVideoModel
        },
        tenant: Tenant,
        req: { headers: { language } }
      },
      args: { input: { limit,offset, orderBy } = {} },
    } = this

    try {
    const sortOrder = orderBy ? [[orderBy, 'desc']] : [['videoOrder', 'desc']];

    const promotionVideo = await PromotionVideoModel.findAll({
      attributes: ['id', 'title', 'thumbnailUrl', 'videoUrl'],
      where: {
        tenantId: Tenant.id,
        status: true,
      },
      limit: limit || 10,
      offset: offset || 0,
      order: sortOrder
    })

    return promotionVideo
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
