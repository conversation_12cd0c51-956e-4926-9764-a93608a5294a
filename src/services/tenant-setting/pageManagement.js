import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
/**
 *
 * get tenant pages, page menus and menu items
 * @export
 * @class PageManagement
 * @extends {ServiceBase}
 */
export default class PageManagement extends ServiceBase {
  async run () {
    const {
      Page: PageModel,
      TenantBanner: TenantBannerModel,
      WebsiteContent: WebsiteContentModel
    } = this.context.databaseConnection

    const {
      req: { headers: { language } }
    } = this.context
    try {
    const banners = await TenantBannerModel.findAll({
      where: {
        tenantId: this.context.tenant.id,
        enabled: true
      },
      order: ['order'],
      raw: true
    })

    const pageDetails = await PageModel.findAll({
      where: {
        tenantId: this.context.tenant.id,
        topMenuId: this.args.topMenuId,
        enabled: true
      },
      order: ['order'],
      attributes: ['id', 'title', 'order'],
      raw: true
    })

    const websiteDetails = await WebsiteContentModel.findAll({
      attributes: ['id', 'heading', 'content', 'casinoPageId', 'casinoPageMenuId'],
      where: {
        isActive: true,
        tenantId: this.context.tenant.id,
        casinoTopMenuId: this.args.topMenuId,
        [Op.and]: [
          { casinoPageId: { [Op.not]: null } },
          { casinoPageId: { [Op.not]: '0' } }
        ],
        [Op.or]: [
          { casinoPageMenuId: null },
          { casinoPageMenuId: '0' }
        ]
      },
      raw: true
    }).reduce((acc, cur) => {
      if (Object.prototype.hasOwnProperty.call(acc, cur.casinoPageId)) {
        acc[cur.casinoPageId].push(cur)
      } else {
        acc[cur.casinoPageId] = [cur]
      }
      return acc
    }, {})

    pageDetails.forEach(item => {
      item.cms = websiteDetails[item.id] || []
    })

    return { pages: pageDetails, banners }
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
