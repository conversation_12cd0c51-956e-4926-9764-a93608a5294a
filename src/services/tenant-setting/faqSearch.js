import { ApolloError } from 'apollo-server-express';
import { Op, literal } from 'sequelize';
import ErrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

/**
 * get FAQs
 * @export
 * @class FaqSearch
 * @extends {ServiceBase}
 */
export default class FaqSearch extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Faq: FaqModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const { searchKey, limit, offset } = this.args

    const { count, rows: faqs } = await FaqModel.findAndCountAll({
      attributes: ['question', 'answer', 'featured'],
      where: {
        tenantId,
        active: true,
        [Op.and]: [
          literal(`EXISTS (
            SELECT 1 FROM jsonb_each_text(question) AS kv
            WHERE kv.value ILIKE '%${searchKey}%'
          )`)
        ]

      },
      order: [['featured', 'DESC'], ['updatedAt', 'DESC']],
      limit,
      offset,
      raw: true
    })

    return { count, faqs }
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
