import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get FAQs
 * @export
 * @class faqs
 * @extends {ServiceBase}
 */
export default class Faqs extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Faq: FaqModel,
        FaqCategories: FaqCategoriesModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context;

    try {

    const whereClause = {
      tenantId,
      active: true
    };

    const faqCategories = await FaqCategoriesModel.findAll({
      attributes: ["id", "name", "image", "slug"],
      where: {
        tenantId,
        active: true,
      },
      include: [{
          model: FaqModel,
          attributes: ["question", "answer", "featured"],
          where: whereClause,
          required: false,
        }],
      order: [
        [FaqModel, "featured", "DESC"],
        [FaqModel, "updatedAt", "DESC"],
      ],
    });

    const formattedCategories = faqCategories.map((category) => ({
      id: category.id,
      name: category.name,
      image: category.image,
      slug: category.slug,
      faqs: category.Faqs || [],
    }));

    return formattedCategories;
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
