import { Op } from 'sequelize';
import ServiceBase from '../../common/serviceBase';
import { ApolloError } from 'apollo-server-express'
import Error<PERSON>ogHelper from '../../common/errorLog'
import translate from '../../lib/languageTranslate'

export default class SeoPageMeta extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        TenantPageMeta: TenantPageMetaModel,
        MenuTenantSetting: MenuTenantSettingModel,
        Page: PageModel,
        PageMenu: PageMenuModel,
        MenuMaster: MenuMasterModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context

    try {
    const whereCondition = {
      tenantId: Tenant.id,
      active: true
    };

    if (this.args.topMenuId) {
      whereCondition.menuId = this.args.topMenuId;
    } else {
      whereCondition.casinoPageId = {
        [Op.or]: [0, null]
      };
    }

    let tenantSeoPage = await TenantPageMetaModel.findAll({
      where: whereCondition,
      attributes: ['id', 'menuId', 'metaTitle', 'metaDescription', 'metaKeyword', 'casinoPageId', 'casinoPageMenuId'],
      include: [
        {
          model: PageModel,
          attributes: ['title']
        },
        {
          model: PageMenuModel,
          attributes: ['name']
        }
      ],
      raw: true
    });

    if(!this.args.topMenuId) return tenantSeoPage

    // Step 1: Create the root object for casinoPageId 0
    const rootObject = { pages: [] };

    // Step 2: Iterate over the array and group by Page.title
    tenantSeoPage.forEach(item => {
      if (item.casinoPageId === '0') {
        // Populate root object metadata
        rootObject.id = item.id;
        rootObject.metaTitle = item.metaTitle;
        rootObject.metaDescription = item.metaDescription;
        rootObject.metaKeyword = item.metaKeyword
        rootObject.casinoPageId =  item.casinoPageId
        rootObject.casinoPageMenuId = item.casinoPageMenuId
        rootObject.menuId = item.menuId
      } else {
        const pageTitle = item["Page.title"];
        const pageMenuTitle = item["PageMenu.name"];
        let existingPage = rootObject.pages.find(page => page.title === pageTitle);

        if (!existingPage) {
          existingPage = {
            title: pageTitle,
            // id: item.id,
            menus: []
          };
          rootObject.pages.push(existingPage);
        }

        // If casinoPageMenuId is 0, add metaTitle and metaDescription to root
        if (item.casinoPageMenuId === '0') {
          existingPage.metaTitle = item.metaTitle
          existingPage.metaDescription = item.metaDescription
          existingPage.metaKeyword = item.metaKeyword
          existingPage.casinoPageId =  item.casinoPageId
          existingPage.casinoPageMenuId = item.casinoPageMenuId
        } else {
          // Add items without casinoPageMenuId 0 to the menus array
          existingPage.menus.push({
            // id: item.id,
            name: pageMenuTitle,
            metaTitle: item.metaTitle,
            metaDescription: item.metaDescription,
            metaKeyword: item.metaKeyword,
            casinoPageId: item.casinoPageId,
            casinoPageMenuId: item.casinoPageMenuId
          });
        }
      }
    });

    return [rootObject]
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
