
import { ApolloError } from 'apollo-server-express'
import <PERSON><PERSON>r<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get footer information
 * @export
 * @class FooterContent
 * @extends {ServiceBase}
 */
export default class FooterContent extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        FooterLicense: FooterLicenseModel,
        FooterLicenseImage: FooterLicenseImageModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const footerContents = await FooterLicenseModel.findOne({
      where: { tenantId },
      attributes: ['heading', 'content_1', 'content_2'],
      include: [{
        model: FooterLicenseImageModel,
        attributes: ['imageUrl', 'redirectUrl', 'id']
      }]
    })

    return footerContents
  } catch (error) {
      await <PERSON>rrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
