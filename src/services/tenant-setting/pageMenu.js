import ServiceBase from '../../common/serviceBase'
import { ApolloError } from 'apollo-server-express'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import translate from '../../lib/languageTranslate'

/**
 * get page menus
 * @export
 * @class PageMenu
 * @extends {ServiceBase}
 */
export default class PageMenu extends ServiceBase {
  async run () {
    const { PageMenu: PageMenuModel } = this.context.databaseConnection

    const {
      req: { headers: { language } }
    } = this.context
    try {
    const pageMenus = await PageMenuModel.findAll({
      where: {
        pageId: this.args.pageId
      },
      raw: true
    })

    return pageMenus
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
