import { ApolloError } from 'apollo-server-express'
import { QueryTypes } from 'sequelize'
import <PERSON>rror<PERSON>ogHel<PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

/**
 * Fetch pages based on custom category games and other criteria
 * @export
 * @class PageProvider
 * @extends {ServiceBase}
 */
export default class PageProvider extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        Wallet: WalletModel
      }
    } = this.context
    const { topMenuId, pageId } = this.args

    try {
      // Currency check for whitecliff : (<PERSON> has games currency wise.)
      let currencyId = null
      if (this.context.authUser) {
        const wallet = await WalletModel.findOne({ where: { ownerId: this.context.authUser.id, ownerType: 'User' }, attributes: ['currencyId'], raw: true })
        currencyId = wallet.currencyId
      }

      const providerDetailsPayload = {
        tenantId: this.context.tenant.id,
        enabled: true,
      };

      if (topMenuId) {
        providerDetailsPayload.topMenuId = topMenuId;
      }



      const query = `
        SELECT p.id, p.title, p."order", COUNT(ccg.id) AS category_count, p.image
        FROM pages p
          INNER JOIN (
          SELECT DISTINCT ccg.page_id, ccg.id
          FROM custom_category_games ccg
          join custom_category cc on  ccg.category_id = cc.id
          INNER JOIN casino_items ci ON ccg.uuid = ci.uuid AND ccg.tenant_id = ci.tenant_id AND ccg.provider_id = NULLIF(ci.provider, '')::INT
          JOIN public.pages p ON p.id = ccg.page_id
            JOIN aggregator a ON a.page_id = p.id AND a.status = true
          JOIN menu_tenant_setting mtg ON p.top_menu_id = mtg.id
          JOIN menu_master mm ON mtg.menu_id = mm.id
          JOIN menu_items mt ON ci.id = mt.casino_item_id
          JOIN page_menus pm ON mt.page_menu_id = pm.id
          JOIN casino_menus cmi ON pm.casino_menu_id = cmi.id
          WHERE  cc.status = true and ccg.is_deleted = false and cc.is_deleted = false  and a.status = true
            AND ci.active = true
            AND p.enabled = true
            AND p.tenant_id = ccg.tenant_id
            AND mtg.status = true
            AND mm.active = true
            AND mt.active = true
            AND (cmi.tenant_id = ccg.tenant_id OR cmi.tenant_id = 0)
            AND cmi.enabled = true
            AND p.tenant_id = :tenantId
            ${topMenuId ? 'AND p.top_menu_id = :topMenuId' : ''}
            ${currencyId ? 'AND (:currencyId = ANY(p.allowed_currencies) OR p.allowed_currencies = \'{}\'::INTEGER[])' : ''}
          ) ccg ON p.id = ccg.page_id
           JOIN aggregator ag ON ag.page_id = p.id AND ag.status = true and p.enabled = true
        WHERE p.tenant_id = :tenantId

          GROUP BY p.id, p.title, p."order"
          ORDER BY p."order" ASC
      `
      const replacements = { tenantId: this.context.tenant.id };
      if (topMenuId) {
        replacements.topMenuId = topMenuId;
      }

      if (currencyId) {
        replacements.currencyId = currencyId;
      }

      if (pageId) {
        replacements.pageId = pageId;
      }

      const pages = await sequelize.query(query, {
        type: QueryTypes.SELECT,
        replacements,
        useMaster: false,
        raw: true
      });
      const queryTogetAllProviders = `
      SELECT
        p.id,
        p.title,
        p.image,
        p.top_menu_id AS "topMenuId",
        COALESCE(
          json_agg(
            jsonb_build_object(
              'heading', wc.heading,
              'content', wc.content
            )
          ) FILTER (WHERE wc.id IS NOT NULL), '[]'
        ) AS cms
      FROM public.pages p
      JOIN aggregator a ON p.id = a.page_id AND a.status = true
      LEFT JOIN website_content wc
        ON p.id = wc.casino_page_id AND wc.casino_page_menu_id = 0
        ${topMenuId ? 'AND p.top_menu_id = wc.casino_top_menu_id' : ''}
      WHERE p.id in (:pageIds)
      ORDER BY p."order";
        `
      let providerDetails = [];
      if (pages && pages.length > 0) {
        providerDetails = await sequelize.query(queryTogetAllProviders, {
          type: QueryTypes.SELECT,
          replacements: { pageIds: pages.map(page => page.id) , 
          useMaster: false,
          raw: true
        });
      }

      const newDetails = providerDetails.reduce((acc, curr) => {
        // Get the current topMenuId value
        const currentTopMenuId = pages.find((page) => page.id === curr.id)
          ? curr.topMenuId
          : null;

        // Check if an item with the same title already exists in the accumulator
        const existingItem = acc.find((item) => item.title === curr.title);

        if (existingItem) {
          if (!existingItem.topMenuIds) {
            existingItem.topMenuIds = [];
          }

          // Add the current topMenuId to topMenuIds if not already there
          if (
            currentTopMenuId &&
            !existingItem.topMenuIds.includes(currentTopMenuId) &&
            pages.find((page) => page.id === curr.id)
          ) {
            existingItem.topMenuIds.push(currentTopMenuId);
          }
          delete existingItem.topMenuId;
        } else {
          // Create a new item with topMenuIds array containing the current topMenuId

          const currentPage = pages.find((page) => page.id === curr.id);
          const newItem = {
            ...curr,
            topMenuIds: currentTopMenuId ? [currentTopMenuId] : [],
            category_count: currentPage?.category_count,
            order: currentPage?.order,
            cms: curr.cms
          };

          delete newItem.topMenuId;
          acc.push(newItem);
        }

        return acc;
      }, []);
      // remove records where category_count is null or 0
      const filteredDetails = newDetails.filter((detail) => detail.category_count > 0);

      return {
        pages: filteredDetails.map((detail) => {
          if (detail.topMenuIds) {
            const flattenedArray = detail.topMenuIds.flat().map((id) => {
              return !isNaN(id) ? Number(id) : id;
            });
            // Remove duplicates
            detail.topMenuIds = [...new Set(flattenedArray)];
          }
          return detail;
        }),
      };

    } catch (error) {
      console.error('Error in PageProvider:', error);
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
