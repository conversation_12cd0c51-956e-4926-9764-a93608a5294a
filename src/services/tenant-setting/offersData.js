import { ApolloError } from 'apollo-server-express'
import { Sequelize } from 'sequelize'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

export default class OffersData extends ServiceBase {
  async run () {
    const {
      tenant: { id: tenantId },
      req: { headers: { language } },
      databaseConnection: {
        Offer: OfferModel,
        Prize: PrizeModel,
        OfferProvider: OfferProviderModel
      }
    } = this.context

    try {
      // Fetch offers for the tenant, including associated prizes
      const offersData = await OfferModel.findAll({
        where: { tenantId, status: true },
        order: [['createdAt', 'DESC']], // Orders by creation date (most recent first)
        raw: false, // Keep raw: false to include associations
        include: [
          {
            model: PrizeModel,
            as: 'prizes', // Defined alias in the Offer model
            attributes: ['id', 'title', 'description'] // Specify required fields from prizes
          },
          {
            model: OfferProviderModel,
            as: 'providers',
            attributes: ['providerId',
              [Sequelize.literal(`(
                SELECT name
                FROM casino_providers
                WHERE casino_providers.id = providers.provider_id
              )`), 'providerName']
            ]
          }
        ],
        attributes: [
          'id',
          'promotionTitle',
          'offerDescription',
          'validFrom',
          'validTo',
          'frequency',
          'dayOfWeek',
          'winningType',
          'image',
          'status'
        ]
      })

      return offersData
    } catch (error) {
      // Log error and return ApolloError
      await ErrorLogHelper.logError(error, this.context)
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
