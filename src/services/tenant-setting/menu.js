import { ApolloError } from 'apollo-server-express'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
/**
 * get tenant menu data
 * @export
 * @class Menu
 * @extends {ServiceBase}
 */
export default class Menu extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        MenuTenantSetting: MenuTenantSettingModel,
        MenuMaster: MenuMasterModel,
        WebsiteContent: WebsiteContentModel

      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
      const menu = await MenuTenantSettingModel.findAll({
        where: {
          tenant_id: tenantId,
          status: true
        },
        attributes: ['ordering', 'id', 'flash', 'loginRedirect', 'slug'],
        include: [
          {
            model: MenuMasterModel,
            attributes: ['name', 'path', 'component', 'componentName', 'image']
          },
          {
            model: WebsiteContentModel,
            attributes: ['heading', 'content'],
            where: {
              isActive: true,
              casinoPageId: 0,
              casinoPageMenuId: null
            },
            required: false
          }
        ]
      })

      const response = menu.map(menuItem => ({
        id: menuItem.id,
        name: menuItem.MenuMaster.name,
        ordering: menuItem.ordering,
        flash: menuItem.flash,
        loginRedirect: menuItem.loginRedirect,
        path: menuItem.MenuMaster.path,
        component: menuItem.MenuMaster.component,
        componentName: menuItem.MenuMaster.componentName,
        image: menuItem.MenuMaster.image,
        slug: menuItem.slug,
        cms: menuItem.WebsiteContents
      }))

      return response
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
