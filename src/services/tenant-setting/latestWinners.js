import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { literal } from 'sequelize'

export default class LatestWinners extends ServiceBase {
  async run () {
    const {
      tenant: { id: tenantId },
      req: { headers: { language } },
      databaseConnection: {
        LatestWinner: LatestWinnerModel
      }
    } = this.context

    try {
      const latestWinnerData = await LatestWinnerModel.findAll({
        where: { tenantId },
        order: [['betCount', 'DESC']],
        raw: true,
        attributes: ['id', 'gameId', 'tableId', 'tableName', 'image', 'winningAmount', 'providerName',
          [literal('LEFT(email, 3) || \'***\' || RIGHT(email, 3)'), 'email'],
          [literal('other_currency_amount::jsonb'), 'otherCurrencyAmount']
        ]
      })

      return latestWinnerData
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context)
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
