import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { literal, Op } from 'sequelize'

export default class OfferLeaderBoard extends ServiceBase {
  async run () {
    const {
      tenant: { id: tenantId },
      req: { headers: { language } },
      databaseConnection: { OfferLeaderboard: OfferLeaderboardModel }
    } = this.context

    const { offerId } = this.args

    try {
      const leaderboard = await OfferLeaderboardModel.findAll({
        where: {
          offerId,
          tenantId: { [Op.in]: literal(`(select tenant_id from tenant_theme_settings where tenant_id = ${tenantId} and 'offerModule' = ANY(string_to_array(allowed_modules, ',')))`) }
        },
        order: [['winningAmount', 'DESC']],
        raw: true,
        attributes: [
          'id', 'winningAmount',
          [literal('LEFT(user_name, 3) || \'***\' || RIGHT(user_name, 3)'), 'username']
        ]
      })

      return leaderboard
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context)
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
