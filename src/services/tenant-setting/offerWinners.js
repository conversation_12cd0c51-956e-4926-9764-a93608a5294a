import { ApolloError } from 'apollo-server-express';
import { literal } from 'sequelize';
import <PERSON>rrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

export default class OfferWinners extends ServiceBase {
  async run() {
    const { offerId, winningDate } = this.args;
    const { tenant: { id: tenantId },
    req: { headers: { language } },
    databaseConnection: {
      OfferWinner: OfferWinnerModel
    } } = this.context;

    try {
      const winners = await OfferWinnerModel.findAll({
        where: {
          offerId,
          tenantId,
          winnerAnnounceDate: winningDate
        },
        attributes: [
          'id',
          [literal('LEFT(user_name, 3) || \'***\' || RIGHT(user_name, 3)'), 'userName'],
          'winningType',
          'winningValue',
          'prizeTitle'
        ],
        order: [['id', 'asc']],
        raw: true
      });

      return winners;
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context);
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
