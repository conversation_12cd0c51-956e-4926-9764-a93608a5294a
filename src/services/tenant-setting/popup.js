
import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get popup information
 * @export
 * @class Popup
 * @extends {ServiceBase}
 */
export default class Popup extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        Popup: PopupModel
      },
      tenant: { id: tenantId },
      req: { headers: { language } }
    } = this.context

    try {
    const popup = await PopupModel.findOne({
      attributes: ['id', 'title', 'popupCode', 'popupType', 'content', 'banner', 'closePopupType', 'duration', 'visiblityStatus', 'displayCondition', 'startDate', 'endDate', 'isActive'],
      where: {
        tenantId,
        isActive: true,
        startDate: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        endDate: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') }
      },
      raw: true
    })

    return popup
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
