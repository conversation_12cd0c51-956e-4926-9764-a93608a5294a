
import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { DEFAULT_TRANSLATION_LANGUAGE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

/**
 * get language keys information
 * @export
 * @class Popup
 * @extends {ServiceBase}
 */
export default class LanguageKey extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        Language: LanguageModel,
        TenantConfiguration: TenantConfigurationModel
      },
      tenant: { id: tenantId }
    } = this.context

    try {
    let languageLet = language
    // Check if the language code is not null or empty
    if (!language) {
      languageLet = DEFAULT_TRANSLATION_LANGUAGE
    }

    const languageRecords = await LanguageModel.findAll({
      where: {
        code: sequelize.where(
          sequelize.fn('LOWER', sequelize.col('code')),
          {
            [Op.in]: [languageLet.toLowerCase(), DEFAULT_TRANSLATION_LANGUAGE.toLowerCase()]
          }
        )
      },
      attributes: ['id', 'code'],
      raw: true
    })

    // Determine the appropriate language record
    const providedLanguageRecord = languageRecords.find(record => record.code.toLowerCase() === languageLet.toLowerCase())
    const defaultLanguageRecord = languageRecords.find(record => record.code.toLowerCase() === DEFAULT_TRANSLATION_LANGUAGE.toLowerCase())

    // Check if the language is allowed for the tenant
    const isLanguageAllowed = await TenantConfigurationModel.findOne({
      where: {
        tenantId
      },
      attributes: ['allowedLanguages'],
      raw: true
    })

    const allowedLanguages = isLanguageAllowed
      ? isLanguageAllowed.allowedLanguages.split(',').map(item => item.trim())
      : []

    // Determine if the provided or default language is allowed
    let isAllowed = providedLanguageRecord
      ? allowedLanguages.includes(String(providedLanguageRecord.id))
      : false

    if (!isAllowed && defaultLanguageRecord) {
      isAllowed = allowedLanguages.includes(String(defaultLanguageRecord.id))
    }

    const languageRecord = providedLanguageRecord || defaultLanguageRecord

      // Run optimized raw SQL with JSON aggregation
      const result = await sequelize.query(`
        SELECT
          lk.page,
          json_object_agg(lk.key, ls.string) AS keys
        FROM language_keys lk
        LEFT JOIN language_strings ls
          ON lk.id = ls.language_key_id
          AND ls.language_id = :langId
          AND ls.tenant_id = :tenantId
        WHERE lk.is_deleted = false
        GROUP BY lk.page
        `,
        {
          replacements: { langId: languageRecord.id, tenantId },
          type: sequelize.QueryTypes.SELECT
        }
      )

      return result
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
