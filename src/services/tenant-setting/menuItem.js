import { ApolloError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 *
 * get tenant menu items
 * @export
 * @class MenuItem
 * @extends {ServiceBase}
 */
export default class MenuItem extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        MenuItem: MenuItemModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const itemDetails = await MenuItemModel.findAll({
      where: {
        active: true,
        pageMenuId: this.args.menuId
      },
      order: ['order'],
      raw: true
    })

    if (!itemDetails) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('ITEM_DETAILS_NOT_FOUND', language), errorCode: 404 }
    }

    return itemDetails
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
