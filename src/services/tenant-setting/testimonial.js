import { ApolloError } from 'apollo-server-express';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

/**
 * get Testimonials
 * @export
 * @class testimonials
 * @extends {ServiceBase}
 */
export default class Testimonial extends ServiceBase {
  async run () {

    const {
      databaseConnection: {
        Testimonial: TestimonialModel
      },

      tenant: { id: tenantId },
      req: { headers: { language } }
    } = this.context

    try {
    const testimonials = await TestimonialModel.findAll({
      attributes: ['id', 'title', 'subTitle', 'content', 'image'],
      where: {
        tenantId,
        isActive: true
      },
      order: [['createdAt', 'DESC']],
      raw: true
    })

    return testimonials
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
