import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { ALLOWED_PERMISSIONS, CHATBOT_TYPE, ERORR_TYPE, TENANT_SETTINGS_TYPE } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get tenant page layout setting
 * @export
 * @class TenantPageLayout
 * @extends {ServiceBase}
 */
export default class TenantPageLayout extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        TenantThemeSetting: TenantThemeSettingModel,
        Layout: LayoutModel,
        Language: LanguageModel,
        Tenant: TenantModel,
        TenantUserRegistrationFields: TenantUserRegistrationFieldsModel,
        TenantCredential: TenantCredentialModel,
        TenantSocialMediaSettings: TenantSocialMediaSettingsModel,
        WebsiteContent: WebsiteContentModel,
        PageBanner: PageBannerModel,
        SportsContent: SportsContentModel,
        TenantConfiguration: TenantConfigurationModel,
        TenantSetting: TenantSettingModel
      },
      tenant: { id: tenantId },
      req: { headers: { language } }
    } = this.context

    try {
      const themeDetails = await TenantThemeSettingModel.findOne({
        where: {
          tenantId
        },
        attributes: ['theme', 'logoUrl', 'userLoginType', 'fabIconUrl', 'signupPopupImageUrl', 'whatsappNumber', 'callingNumber', 'callingNumbers', 'poweredBy', 'chatbotToken', 'googleAnalyticsScriptsCode', 'googleRecaptchaKeys', 'allowedModules', 'socialMedia', 'forgotPasswordOption', 'googleTagManagerCode', 'googleSearchConsoleCode', 'otpEnable', 'maxBankAccountLimit', 'chatbotType', 'enableCustomCss', 'socialMediaLoginType'],
        include: [
          {
            model: LayoutModel,
            attributes: ['value'],
            required: true
          },
          {
            model: LanguageModel,
            attributes: ['code', 'name'],
            required: true
          }
        ]
      })

      const tenantSettings = await TenantSettingModel.findAll({
        where: {
          tenantId,
          type: {
            [Op.in]: [TENANT_SETTINGS_TYPE.CHATBOT_SETTINGS, TENANT_SETTINGS_TYPE.LOADER_TYPE, TENANT_SETTINGS_TYPE.BET_SHOP_SETTINGS]
          }
        },
        attributes: ['key', 'value'],
        raw: true
      });

      const settingsMap = tenantSettings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {});

      const cms = await WebsiteContentModel.findAll({
        attributes: ['heading', 'content'],
        where: {
          casinoTopMenuId: '0',
          tenantId,
          isActive: true
        },
        raw: true
      })

    let ezugiGamesLiveUpdates = false
    if (themeDetails?.allowedModules && themeDetails?.allowedModules.split(',').includes('ezugiGamesLiveUpdates')) {
      ezugiGamesLiveUpdates = true
    }

    let paymentGatewayCategoriesEnable = false
    if (themeDetails?.allowedModules && themeDetails?.allowedModules.split(',').includes('paymentGatewayCategoriesEnable')) {
      paymentGatewayCategoriesEnable = true
    }

    const fields = await TenantUserRegistrationFieldsModel.findOne({
      where: {
        tenantId
      }
    })

    const tenantDetails = await TenantModel.findOne({
      where: {
        id: tenantId
      },
      attributes: ['domain', 'name']
    })

    if (!themeDetails) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('THEME_DETAILS_NOT_FOUND', language), errorCode: 404 }
    }

    const keys = {
      minWithdraw: 'USER_MIN_WITHDRAWAL_LIMIT',
      maxWithdraw: 'USER_MAX_WITHDRAWAL_LIMIT',
      minDeposit: 'USER_MIN_DEPOSIT_AMOUNT',
      maxDeposit: 'USER_MAX_DEPOSIT_AMOUNT',
      socketUrl: 'APP_EZUGI_SOCKET_URL',
      tenantBaseCurrency: 'TENANT_BASE_CURRENCY',
      evolutionGameSecondaryCurrency: 'EVOLUTION_GAME_SECONDARY_CURRENCY',
      googleLoginCLientId: 'GOOGLE_LOGIN_CLIENT_ID',
      facebookLoginAppId: 'FACEBOOK_LOGIN_APP_ID',
      minimumWalletAmount: 'MINIMUM_WALLET_AMOUNT'
    }
    const credentials = await TenantCredentialModel.findAll({
      where: {
        key: Object.values(keys),
        tenantId: tenantId
      },
      raw: true
    })
    const tenantCredentialkey = {}
    Object.keys(keys).map((creds) => {
      const val = credentials.find(obj => obj.key === keys[creds])
      if (val) {
        tenantCredentialkey[creds] = val.value
      }
    })

    const registrationFields = {
      firstName: fields.firstName,
      lastName: fields.lastName,
      email: fields.email,
      username: fields.username,
      nickName: fields.nickName,
      dob: fields.dob,
      currency: fields.currency,
      phone: fields.phone,
      city: fields.city,
      zipCode: fields.zipCode,
      password: fields.password,
      confirmPassword: fields.confirmPassword,
      eighteenYearCheck: fields.eighteenYearCheck,
      promoCode: fields.promoCode,
      nationalId: fields.nationalId
    }

    const socialMediaEntries = await TenantSocialMediaSettingsModel.findAll({
      where: {
        tenantId,
        status: true
      }
    })

    const socialMediaDetails = socialMediaEntries.map(entry => ({
      id: entry.id,
      name: entry.name || null,
      image: entry.image || null,
      redirectUrl: entry.redirectUrl || null
    }))

    let customCssUrl = null
    if (themeDetails?.allowedModules && themeDetails?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.CUSTOM_CSS_ENABLE)) {
      customCssUrl = themeDetails.enableCustomCss ? 'custom-css/custom-css-' + tenantId + '.css?ver=' + new Date().getTime() : null
    }

    const banners = await PageBannerModel.findAll({
      where: {
        tenantId,
        enabled: true
      },
      order: ['order'],
      raw: true
    })

    const sportsContent = await SportsContentModel.findAll({
      attributes: ['id', 'title', 'buttonText', 'buttonRedirection', 'content', 'image', 'imageMobile'],
      where: {
        tenantId
      },
      order: [['createdAt', 'DESC']],
      raw: true
    })

    // Fetch the allowed languages for the tenant and their corresponding language details
    const tenantLanguageList = await TenantConfigurationModel.findOne({
      where: {
        tenantId
      },
      attributes: ['allowedLanguages'],
      raw: true
    })

    // Parse the allowed languages into an array of IDs
    const tenantLanguagesIds = tenantLanguageList.allowedLanguages.split(',').map(id => parseInt(id, 10)).filter(Boolean)

    let tenantLanguages = []

    if (tenantLanguagesIds && Array.isArray(tenantLanguagesIds) && tenantLanguagesIds.length > 0) {
      tenantLanguages = await LanguageModel.findAll({
        where: {
          id: {
            [Op.in]: tenantLanguagesIds
          },
          status: true
        },
        attributes: ['id', 'code', 'name', 'abbreviation'],
        raw: true
      })
    }

    const response = {
      language: themeDetails.Language.code,
      languageTitle: themeDetails.Language.name,
      theme: JSON.stringify(themeDetails.theme),
      layout: themeDetails.Layout.value,
      logoUrl: themeDetails.logoUrl,
      userLoginType: themeDetails.userLoginType,
      socialMediaLoginType: themeDetails.socialMediaLoginType,
      fabIconUrl: themeDetails.fabIconUrl,
      domain: tenantDetails.domain,
      tenantId: tenantId,
      signupPopupImageUrl: themeDetails.signupPopupImageUrl,
      whatsappNumber: themeDetails.whatsappNumber,
      callingNumber: themeDetails.callingNumber,
      callingNumbers: themeDetails.callingNumbers,
      customCssUrl,
      poweredBy: themeDetails.poweredBy,
      tenantName: tenantDetails.name,
      chatbotToken: settingsMap.CHATBOT_ACCESS_TOKEN || null,
      chatbotMobileToken: settingsMap.MOBILE_CHATBOT_ACCESS_TOKEN || null,
      chatbotMobileNumber:
        themeDetails?.chatbotType === CHATBOT_TYPE.WATI || themeDetails?.chatbotType?.split(',').includes(CHATBOT_TYPE.WATI)
          ? settingsMap.CHATBOT_MOBILE_NUMBER || null
          : null,
      loaderType: settingsMap.LOADER_TYPE || null,
      googleAnalyticsScriptsCode: themeDetails.googleAnalyticsScriptsCode,
      googleRecaptchaKeys: JSON.stringify(themeDetails.googleRecaptchaKeys),
      registrationFields: registrationFields,
      userMinWithdrawalLimit: tenantCredentialkey?.minWithdraw,
      userMaxWithdrawalLimit: tenantCredentialkey?.maxWithdraw,
      userMinDepositLimit: tenantCredentialkey?.minDeposit,
      userMaxDepositLimit: tenantCredentialkey?.maxDeposit,
      socketUrl: tenantCredentialkey?.socketUrl,
      ezugiGamesLiveUpdates: ezugiGamesLiveUpdates,
      socialMediaDetails,
      forgotPasswordOption: themeDetails.forgotPasswordOption,
      paymentGatewayCategoriesEnable,
      googleSearchConsoleCode: themeDetails.googleSearchConsoleCode,
      googleTagManagerCode: themeDetails.googleTagManagerCode,
      otpEnable: themeDetails.otpEnable,
      maxBankAccountLimit: themeDetails.maxBankAccountLimit,
      tenantBaseCurrency: tenantCredentialkey?.tenantBaseCurrency,
      evolutionGameSecondaryCurrency: tenantCredentialkey?.evolutionGameSecondaryCurrency,
      chatbotType: themeDetails.chatbotType,
      cms,
      allowedModules: themeDetails.allowedModules,
      banners,
      allowedLanguages: tenantLanguages,
      sportsContent,
      googleLoginCLientId: tenantCredentialkey?.googleLoginCLientId,
      facebookLoginAppId: tenantCredentialkey?.facebookLoginAppId,
      minimumWalletAmount: tenantCredentialkey?.minimumWalletAmount,
      kioskAppAutoLogout: settingsMap.KIOSK_APP_AUTO_LOGOUT || null
    }

    return response
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
    }
  }
}
