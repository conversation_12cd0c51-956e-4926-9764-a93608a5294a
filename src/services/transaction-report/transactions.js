import { ApolloError } from 'apollo-server-express'
import moment from 'moment'
import { Op, Sequelize } from 'sequelize'
import { SPORT_CASINO_TXN_TYPE, TRANSACTION_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get user transaction list
 * @export
 * @class Transactions
 * @extends {ServiceBase}
 */
export default class Transactions extends ServiceBase {
  async run () {
    let {
      args: { page, limit, dateFrom, dateTo, orderBy, paymentProviderId, paymentStatus, userCountryCode } = {},
      context: {
        tenant: Tenant,
        req: { headers: { language } },
        auth: currentUser,
        databaseConnection: {
          Wallet: WalletModel,
          Transaction: TransactionModel,
          paymentProviders: PaymentProvidersModel
        }
      }
    } = this

    try {
      const wallet = await WalletModel.findOne({ where: { ownerId: currentUser.id, ownerType: 'User' }, raw: true })

      // finding timezone based on user country code
      const timezone = userCountryCode && moment.tz.zonesForCountry(userCountryCode)[0]
      // adjusting dateTo and dateFrom based on timezone
      if (timezone) {
        dateTo = moment.tz(dateTo, timezone).utc().format()
        dateFrom = moment.tz(dateFrom, timezone).utc().format()
      }

      dateTo = new Date((dateTo || Date.now()))
      let sortOrder = [['createdAt', 'desc'], ['id', 'asc']]

      if (orderBy) {
        sortOrder = Object.entries(orderBy)
        sortOrder.push(['id', 'asc'])
      }

      let offset = 0
      let dateObj = { createdAt: { [Op.lte]: (dateTo.toISOString()) } }

      if (page && limit) {
        offset = (page - 1) * limit
      }

      let providerObj = null
      let statusObj = null

      if (paymentProviderId) {
        providerObj = { payment_provider_id: paymentProviderId }
      }

      if (paymentStatus) {
        statusObj = { status: paymentStatus }
      }

      // date object creation
      if (dateFrom) {
        if (new Date(dateFrom).toISOString() < dateTo.toISOString()) {
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: (dateTo.toISOString()) }
          }
        } else if (new Date(dateFrom).toISOString() === dateTo.toISOString()) {
          dateTo = new Date(dateTo.setDate(dateTo.getDate() + 1))
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: dateTo.toISOString() }
          }
        }
      }

      const { count, rows: transactionData } = await TransactionModel.findAndCountAll(
        {
          attributes: ['id', 'createdAt', 'transactionType', 'sourceBeforeBalance',
            'sourceAfterBalance', 'targetBeforeBalance', 'targetAfterBalance', 'gameId',
            'amount', 'status', 'comments', 'roundId', 'seatId',
            [Sequelize.literal(
          `CASE WHEN
            "Transaction".seat_id IS NOT NULL THEN
              (SELECT casino_items.name FROM casino_items
                WHERE casino_items.uuid = "Transaction".seat_id
                AND casino_items.tenant_id = "Transaction".tenant_id
                ${'AND ("Transaction"."provider_id" IS NULL OR "casino_items"."provider" = CAST("Transaction"."provider_id" as varchar))'}
                 ORDER BY id DESC LIMIT 1 )
            ELSE (SELECT casino_games.name from casino_games where casino_games.game_id = "Transaction".game_id ORDER BY id DESC LIMIT 1)
            END`),
            'gameName']],
          where: {
            tenantId: Tenant.id,
            [Op.or]: [
              { sourceWalletId: wallet.id },
              { targetWalletId: wallet.id }
            ],
            ...dateObj,
            ...providerObj,
            ...statusObj,
            transactionType: { [Op.notIn]: [20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37] }
          },
          include: [{
            model: PaymentProvidersModel,
            as: 'paymentProvider'
          }],
          limit: limit || 10,
          offset: offset || 0,
          order: sortOrder
        })

      let transactionList
      if (transactionData) {
        transactionList = await Promise.all(
          transactionData.map(async object => {
          // Formatting createdAt date with optional user timezone
            const date = timezone ? moment.tz(object.dataValues.createdAt, timezone) : moment(object.dataValues.createdAt)
            object.dataValues.createdAt = date.format('DD/MM/YYYY HH:mm')

            object.dataValues.amount = object.dataValues.amount ? object.dataValues.amount.toFixed(5) : 0

            if (object.dataValues.transactionType === TRANSACTION_TYPES.DEPOSIT || object.dataValues.transactionType === TRANSACTION_TYPES.ADMIN_ADD_NON_CASH || object.dataValues.transactionType === TRANSACTION_TYPES.JOINING_BONUS_CLAIMED || object.dataValues.transactionType === TRANSACTION_TYPES.PROMO_CODE_BONUS_CLAIMED || object.dataValues.transactionType === TRANSACTION_TYPES.CREDIT || object.dataValues.transactionType === TRANSACTION_TYPES.CREDIT_NO_CASH || object.dataValues.transactionType === TRANSACTION_TYPES.WITHDRAW_CANCEL || object.dataValues.transactionType === TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM || object.dataValues.transactionType === TRANSACTION_TYPES.NON_CASH_BONUS_CLAIM || object.dataValues.transactionType === TRANSACTION_TYPES.CANCELLED_BY_ADMIN || object.dataValues.transactionType === TRANSACTION_TYPES.REFERRAL_BONUS || object.dataValues.transactionType === SPORT_CASINO_TXN_TYPE.EXCHANGE_DEPOSIT_BONUS_CLAIM) {
              object.dataValues.sourceBeforeBalance = object.dataValues.targetBeforeBalance ? object.dataValues.targetBeforeBalance.toFixed(5) : 0
              object.dataValues.sourceAfterBalance = object.dataValues.targetAfterBalance ? object.dataValues.targetAfterBalance.toFixed(5) : 0
            } else {
              object.dataValues.sourceBeforeBalance = object.dataValues.sourceBeforeBalance ? object.dataValues.sourceBeforeBalance.toFixed(5) : 0
              object.dataValues.sourceAfterBalance = object.dataValues.sourceAfterBalance ? object.dataValues.sourceAfterBalance.toFixed(5) : 0
            }
            return object.dataValues
          })
        )
      }

      return { count, transactionList }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
