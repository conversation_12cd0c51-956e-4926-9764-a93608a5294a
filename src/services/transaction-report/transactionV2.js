import { ApolloError } from 'apollo-server-express'
import moment from 'moment'
import { Op, Sequelize } from 'sequelize'
import { CASINO_PROVIDER, ENVIORNMENT, SPORT_CASINO_TXN_TYPE, TRANSACTION_TYPES } from '../../common/constants'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'

/**
 * get user transaction list
 * @export
 * @class Transactions
 * @extends {ServiceBase}
 */
export default class Transactions extends ServiceBase {
  async run () {
    let {
      args: { page, limit, dateFrom, dateTo, orderBy, paymentProviderId, paymentStatus, userCountryCode, reportType, transactionType, roundId } = {},
      context: {
        tenant: Tenant,
        auth: currentUser,
        req: { headers: { language } },
        databaseConnection: {
          Wallet: WalletModel,
          Transaction: TransactionModel,
          paymentProviders: PaymentProvidersModel
        }
      }
    } = this

    try {
      const wallet = await WalletModel.findOne({ where: { ownerId: currentUser.id, ownerType: 'User' }, raw: true })

      // finding timezone based on user country code
      const timezone = userCountryCode && dateFrom !== '' && dateTo !== '' && moment.tz.zonesForCountry(userCountryCode)[0]
      // adjusting dateTo and dateFrom based on timezone
      if (timezone) {
        dateTo = moment.tz(dateTo, timezone).utc().format()
        dateFrom = moment.tz(dateFrom, timezone).utc().format()
      }

      dateTo = new Date((dateTo || Date.now()))
      let sortOrder = [['createdAt', 'desc'], ['id', 'desc']]

      if (orderBy) {
        sortOrder = Object.entries(orderBy)
        sortOrder.push(['id', Object.values(orderBy)[0]])
      }

      let offset = 0
      let dateObj = { createdAt: { [Op.lte]: (dateTo.toISOString()) } }

      if (page && limit) {
        offset = (page - 1) * limit
      }

      let providerObj = null
      let statusObj = null
      let transactionTypeArr = null
      let transactionTypeFilter = null
      let roundIdObj = null
      let gameTypeObj = null
      const gameTypeMap = {
        'saba-sportbooks': { game_id: 'sbs_sportsbook' },
        'sap-exchange': { game_id: 'sap_lobby' },
        'bti-sportsbook': { game_id: 'bti_sportsbook' }
      }

      if (paymentProviderId) {
        providerObj = { payment_provider_id: paymentProviderId }
      }

      if (paymentStatus) {
        statusObj = { status: paymentStatus }
      }

      if (roundId) {
        roundIdObj = { roundId: roundId }
      }

      if (['casino', 'saba-sportbooks', 'sap-exchange', 'bti-sportsbook'].includes(reportType)) {
        const defaultTransactionTypes = [0, 1, 2, 7, 8, 9, 10, 13, 46, 47]
        transactionTypeArr = defaultTransactionTypes

        if (gameTypeMap?.hasOwnProperty(reportType)) {
          gameTypeObj = gameTypeMap[reportType]
        } else {
          const gameIds = Object.values(gameTypeMap).map(game => game.game_id)
          gameTypeObj = {
            game_id: {
              [Op.notIn]: gameIds
            }
          }
        }
      } else if (reportType === 'financial') {
        transactionTypeArr = [3, 4, 5, 6, 11, 12, 14, 15, 16, 17, 19, 37, 38, 39, 40, 41, 42, 43, 44, 45, 50, 48, 49, 51, 52, 59, 60, 68, 69, 70, 71, 72, 74, 75, 76]
      }

      if (transactionType || transactionType === 0) {
        transactionTypeFilter = [+transactionType]
      } else {
        transactionTypeFilter = transactionTypeArr
      }

      // date object creation
      if (dateFrom) {
        if (new Date(dateFrom).toISOString() < dateTo.toISOString()) {
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: (dateTo.toISOString()) }
          }
        } else if (new Date(dateFrom).toISOString() === dateTo.toISOString()) {
          dateTo = new Date(dateTo.setDate(dateTo.getDate() + 1))
          dateObj = {
            createdAt: { [Op.gte]: (dateFrom), [Op.lte]: dateTo.toISOString() }
          }
        }
      }

      const isProduction = config.get('env') === ENVIORNMENT.PRODUCTION
      const st8ProviderId = isProduction ? CASINO_PROVIDER.ST8.PROD : CASINO_PROVIDER.ST8.STAGE
      const ezugiProviderId = isProduction ? CASINO_PROVIDER.EZUGI.PROD : CASINO_PROVIDER.EZUGI.STAGE
      const evolutionProviderId = isProduction ? CASINO_PROVIDER.EVOLUTION.PROD : CASINO_PROVIDER.EVOLUTION.STAGE
      const arcadeProviderId = isProduction ? CASINO_PROVIDER.ARCADE.PROD : CASINO_PROVIDER.ARCADE.STAGE
      const whiteCliffProviderId = isProduction ? CASINO_PROVIDER.WHITECLIFF.PROD : CASINO_PROVIDER.WHITECLIFF.STAGE

      const { count, rows: transactionData } = await TransactionModel.findAndCountAll(
        {
          attributes: ['id', 'createdAt', 'transactionType', 'sourceBeforeBalance',
            'sourceAfterBalance', 'targetBeforeBalance', 'targetAfterBalance', 'gameId',
            'amount', 'status', 'comments', 'roundId', 'seatId', 'transactionId', 'providerId', 'tableId',
            [Sequelize.literal(`
          CASE
            WHEN "Transaction".provider_id = ${st8ProviderId} THEN
              (SELECT title FROM get_provider_name("Transaction".seat_id, "Transaction".tenant_id))
            WHEN "Transaction".provider_id = ${ezugiProviderId} THEN
              'Ezugi'
            WHEN "Transaction".provider_id = ${evolutionProviderId} THEN
              'Evolution'
            WHEN "Transaction".provider_id = ${whiteCliffProviderId} THEN
              (SELECT title FROM get_provider_name("Transaction".seat_id, "Transaction".tenant_id))
            ELSE
              ''
          END
        `), 'custom_3'],
            [Sequelize.literal(
          `CASE WHEN
            "Transaction".seat_id IS NOT NULL THEN
              (SELECT casino_items.name FROM casino_items
                WHERE casino_items.uuid = "Transaction".seat_id
                AND casino_items.tenant_id = "Transaction".tenant_id
                ${'AND ("Transaction"."provider_id" IS NULL OR "casino_items"."provider" = CAST("Transaction"."provider_id" as varchar))'}
                 ORDER BY id DESC LIMIT 1 )
            WHEN "Transaction".provider_id IN (${ezugiProviderId}, ${evolutionProviderId}, ${arcadeProviderId})
              AND "Transaction"."table_id" IS NOT NULL THEN
              (SELECT casino_items.name
              FROM casino_items
              WHERE casino_items.uuid = CAST("Transaction"."table_id" AS varchar)
              AND casino_items.tenant_id = "Transaction".tenant_id
              AND casino_items.provider = CAST("Transaction".provider_id AS varchar)
              ORDER BY id DESC LIMIT 1)
            ELSE (SELECT casino_games.name from casino_games where casino_games.game_id = "Transaction".game_id AND casino_games.casino_provider_id = "Transaction".provider_id ORDER BY id DESC LIMIT 1)
            END`),
            'gameName']],
          where: {
            tenantId: Tenant.id,
            [Op.or]: [
              { sourceWalletId: wallet.id },
              { targetWalletId: wallet.id }
            ],
            ...dateObj,
            ...providerObj,
            ...statusObj,
            ...roundIdObj,
            ...gameTypeObj,
            transactionType: transactionTypeFilter
          },
          include: [{
            model: PaymentProvidersModel,
            as: 'paymentProvider'
          }],
          limit: limit || 10,
          offset: offset || 0,
          order: sortOrder
        })

      let transactionList
      if (transactionData) {
        transactionList = await Promise.all(
          transactionData.map(async object => {
          // Formatting createdAt date with optional user timezone
            const date = timezone ? moment.tz(object.dataValues.createdAt, timezone) : moment(object.dataValues.createdAt)
            object.dataValues.createdAt = date.format('DD/MM/YYYY HH:mm')

            object.dataValues.amount = object.dataValues.amount ? object.dataValues.amount.toFixed(2) : 0

            if (object.dataValues.transactionType === TRANSACTION_TYPES.DEPOSIT || object.dataValues.transactionType === TRANSACTION_TYPES.ADMIN_ADD_NON_CASH ||
              object.dataValues.transactionType === TRANSACTION_TYPES.DEPOSIT_OTB_CASH || object.dataValues.transactionType === TRANSACTION_TYPES.JOINING_BONUS_CLAIMED ||
              object.dataValues.transactionType === TRANSACTION_TYPES.PROMO_CODE_BONUS_CLAIMED || object.dataValues.transactionType === TRANSACTION_TYPES.CREDIT ||
              object.dataValues.transactionType === TRANSACTION_TYPES.CREDIT_NO_CASH || object.dataValues.transactionType === TRANSACTION_TYPES.WITHDRAW_CANCEL ||
              object.dataValues.transactionType === TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM || object.dataValues.transactionType === TRANSACTION_TYPES.NON_CASH_BONUS_CLAIM ||
              object.dataValues.transactionType === TRANSACTION_TYPES.CANCELLED_BY_ADMIN || object.dataValues.transactionType === TRANSACTION_TYPES.ROYALTY_BONUS_CASH ||
              object.dataValues.transactionType === TRANSACTION_TYPES.ROYALTY_BONUS_NON_CASH || object.dataValues.transactionType === TRANSACTION_TYPES.REFERRAL_BONUS ||
              object.dataValues.transactionType === TRANSACTION_TYPES.ROLLBACK || object.dataValues.transactionType === TRANSACTION_TYPES.ROLLBACK_NO_CASH ||
              object.dataValues.transactionType === SPORT_CASINO_TXN_TYPE.EXCHANGE_DEPOSIT_BONUS_CLAIM || object.dataValues.transactionType === TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM ||
              object.dataValues.transactionType === TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM || object.dataValues.transactionType === TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIMED ||
              object.dataValues.transactionType === TRANSACTION_TYPES.NON_CASH_PROMO_CODE_BONUS_CLAIMED  || object.dataValues.transactionType === TRANSACTION_TYPES.FREE_BETS_PROMO_CODE_BONUS_CLAIMED ||
              object.dataValues.transactionType === TRANSACTION_TYPES.SPORTS_FREE_BETS_PROMO_CODE_BONUS_CLAIMED || object.dataValues.transactionType === SPORT_CASINO_TXN_TYPE.EXCHANGE_NON_CASH_DEPOSIT_BONUS_CLAIM ||
              object.dataValues.transactionType === TRANSACTION_TYPES.SPORTS_FREE_BET_DEPOSIT || object.dataValues.transactionType === TRANSACTION_TYPES.NON_CASH_REFERRAL_BONUS_CLAIM
            ) {

              object.dataValues.sourceBeforeBalance = object.dataValues.targetBeforeBalance ? object.dataValues.targetBeforeBalance.toFixed(2) : 0
              object.dataValues.sourceAfterBalance = object.dataValues.targetAfterBalance ? object.dataValues.targetAfterBalance.toFixed(2) : 0

            } else {
              object.dataValues.sourceBeforeBalance = object.dataValues.sourceBeforeBalance ? object.dataValues.sourceBeforeBalance.toFixed(2) : 0
              object.dataValues.sourceAfterBalance = object.dataValues.sourceAfterBalance ? object.dataValues.sourceAfterBalance.toFixed(2) : 0
            }
            return object.dataValues
          })
        )
      }

      return { count, transactionList }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
