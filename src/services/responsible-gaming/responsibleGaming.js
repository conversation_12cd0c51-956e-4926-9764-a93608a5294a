import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { RESPONSIBLE_GAMING_CONSTANT } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * get responsible gaming Setting for the user
 * @export
 * @class ResponsibleGaming
 * @extends {ServiceBase}
 */
export default class ResponsibleGaming extends ServiceBase {
  async run () {
    const {
      auth: currentUser,
      databaseConnection: {
        UserSetting: UserSettingModel,
        User: UserModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const user = await UserModel.findOne(
      {
        where: { id: currentUser.id },
        attributes: ['selfExclusion'],
        raw: true
      }
    )
    const responsibleGaming = await UserSettingModel.findAll(
      {
        where: { userId: currentUser.id },
        key: {
          [Op.in]: [RESPONSIBLE_GAMING_CONSTANT.DAILY_BETTING_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_BETTING_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_BETTING_LIMIT, RESPONSIBLE_GAMING_CONSTANT.DAILY_DEPOSIT_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_DEPOSIT_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_DEPOSIT_LIMIT, RESPONSIBLE_GAMING_CONSTANT.DAILY_WITHDRAW_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_WITHDRAW_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_WITHDRAW_LIMIT]
        }
      }
    )

    const resObj = {
      selfExclusion: user.selfExclusion ? new Date(user.selfExclusion).toISOString() : null,
      setting: responsibleGaming
    }
    return resObj
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
