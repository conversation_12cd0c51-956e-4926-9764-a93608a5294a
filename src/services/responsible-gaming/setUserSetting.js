import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { checkAuthUserIdWithPassword } from '../../lib/checkAuthUserIdWithPassword'
import translate from '../../lib/languageTranslate'

const constraints = {
  maxBetAmount: {
    numericality: true
  }
}

/**
 * Provides service for the applying user setting functionality
 * @export
 * @class SetUserSetting
 * @extends {ServiceBase}
 */
export default class SetUserSetting extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      tenant: { id: tenantId },
      databaseConnection: {
        User: UserModel,
        UserSetting: UserSettingModel
      },
      auth: currentUser
    } = this.context

    try {
    this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')

    if (!await checkAuthUserIdWithPassword(currentUser.id, this.args.password, UserModel)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('WRONG_PASSWORD', language) }
    }

    let [userSetting, created] = await UserSettingModel.findOrCreate({
      where: {
        key: this.args.key,
        userId: currentUser.id
      },
      defaults: {
        value: this.args.value
      }
    })

    if (!created) {
      const lastUpdated = await UserSettingModel.findOne({
        where: {
          userId: currentUser.id,
          key: this.args.key
        }
      })
      const currentTime = new Date()
      const newUpdateTime = lastUpdated.updatedAt
      newUpdateTime.setDate(newUpdateTime.getDate() + 1)
      const errorDate = newUpdateTime.getDate() + '-' + (newUpdateTime.getMonth() + 1) + '-' + newUpdateTime.getFullYear() + ' ' + newUpdateTime.getHours() + ':' + newUpdateTime.getMinutes() + ':' + newUpdateTime.getSeconds()
      if (currentTime <= newUpdateTime) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('NEXT_UPDATION_TIME', language) + errorDate, errorCode: 500 }
      }
      userSetting.value = this.args.value
      userSetting = await userSetting.save()
      return userSetting
    }

    return userSetting
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
