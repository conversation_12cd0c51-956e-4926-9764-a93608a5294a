import { AuthenticationError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { checkAuthUserIdWithPassword } from '../../lib/checkAuthUserIdWithPassword'
import translate from '../../lib/languageTranslate'

const constraints = {
  date: {
    dateTime: {
      dateOnly: true,
      message: 'Enter the correct date formate'
    }
  }
}

/**
 * Provides service for the applying or updating self exclusion functionality
 * @export
 * @class UpdateSelfExclusion
 * @extends {ServiceBase}
 */
export default class UpdateSelfExclusion extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      tenant: { id: tenantId },
      auth: currentUser,
      databaseConnection: {
        User: UserModel
      }
    } = this.context

    try {
    let user = await UserModel.findOne({ where: { id: currentUser.id } })

    this.args.password = Buffer.from(this.args.password, 'base64').toString('ascii')

    if (!await checkAuthUserIdWithPassword(currentUser.id, this.args.password, UserModel)) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('WRONG_PASSWORD', language) }
    }

    const currentDate = new Date().toISOString().split('T')[0]

    if ((!user.selfExclusion && (currentDate <= this.date)) || (this.date >= user.selfExclusion && this.date >= currentDate)) {
      user.selfExclusion = this.date
      user = await user.save()
    } else {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('INVALID_SELF_EXCLUSION_DATE', language) }
    }

    return { date: new Date(user.selfExclusion).toISOString() }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId })
        throw new AuthenticationError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
