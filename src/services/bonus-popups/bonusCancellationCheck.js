import { ApolloError } from 'apollo-server-express';
import Sequelize, { Op } from 'sequelize';
import { ALLOWED_PARALLEL_BONUS, ALLOWED_PERMISSIONS, BONUS_STATUS, BONUS_TYPES } from '../../common/constants';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import getUserFromToken from '../../lib/getUserFromToken';
import translate from '../../lib/languageTranslate';

/**
 * Service for checking if bonus cancellation warning popup should be shown
 * @export
 * @class BonusCancellationCheck
 * @extends {ServiceBase}
 */
export default class BonusCancellationCheck extends ServiceBase {
  async run() {
    const {
      databaseConnection: {
        UserBonus: UserBonusModel,
        Bonus: BonusModel,
        TenantThemeSetting: TenantThemeSettingModel,
        DepositBonusSetting: DepositBonusSettingModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context;

    let currentUser = null;
    let actionType = null;

    try {
      // Get actionType from args
      actionType = this.args?.input?.actionType;

      // Get user from token
      const { decodedToken, user } = await getUserFromToken(this.context, ['id']);
      currentUser = user;

      if (!decodedToken) {
        throw new ApolloError(translate('UNAUTHORIZED', language), 401);
      }

      // Base response structure
      const baseResponse = {
        showPopup: false,
        popupData: ''
      };

      // Define bonus types to search for based on action type
      let bonusTypesToSearch = [];

      if (actionType === 'withdraw' || actionType === 'newBonus') {
        // Include both deposit and losing bonus types for withdraw and newBonus actions
        bonusTypesToSearch = [
          { kind: BONUS_TYPES.DEPOSIT },
          { kind: BONUS_TYPES.DEPOSIT_SPORTS },
          { kind: BONUS_TYPES.DEPOSIT_BOTH },
          { kind: BONUS_TYPES.DEPOSIT_INSTANT},
          { kind: BONUS_TYPES.LOSING },
          { kind: BONUS_TYPES.LOSING_SPORT },
          { kind: BONUS_TYPES.LOSING_BOTH }
        ];
      } else {
        // For other actions (deposit), only include deposit bonus types
        bonusTypesToSearch = [
          { kind: BONUS_TYPES.DEPOSIT },
          { kind: BONUS_TYPES.DEPOSIT_SPORTS },
          { kind: BONUS_TYPES.DEPOSIT_BOTH },
          { kind: BONUS_TYPES.DEPOSIT_INSTANT},
        ];
      }

      // Find active bonuses for the user based on action type
      const userActiveBonuses = await UserBonusModel.findAll({
        attributes: ['id', 'bonusId', 'bonusAmount'],
        where: {
          status: BONUS_STATUS.ACTIVE,
          [Op.or]: bonusTypesToSearch,
          expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
          userId: currentUser.id
        }
      });

      if (!userActiveBonuses || userActiveBonuses.length === 0) {
        return baseResponse;
      }

      // Get bonus details with cancellation settings for all active bonuses
      const bonusIds = userActiveBonuses.map(userBonus => userBonus.bonusId);
      const bonuses = await BonusModel.findAll({
        attributes: ['id', 'percentage', 'kind', 'bonusCancellationType'],
        where: {
          id: { [Op.in]: bonusIds },
          enabled: true
        },
        include: {
          model: DepositBonusSettingModel,
          attributes: ['id', 'minDeposit', 'maxDeposit', 'maxBonus', 'rolloverMultiplier', 'validForDays']
        }
      });

      if (!bonuses || bonuses.length === 0) {
        return baseResponse;
      }

      const cancellationType = bonuses[0].bonusCancellationType || [];

      // Check if current action requires cancellation warning
      let shouldShowPopup = false;
      let popupMessage = '';

      switch (actionType) {
        case 'deposit':
          if (cancellationType === 'multiple_deposit' || cancellationType === 'both') {
            shouldShowPopup = true;
            // Use translate with dynamic replacement similar to your withdraw reference
            popupMessage = translate('BONUS_CANCEL_DEPOSIT_WARNING', language)
              .replace('#bonusAmount', userActiveBonuses[0].bonusAmount);
          }
          break;

        case 'withdraw':
          if (cancellationType === 'multiple_withdraw' || cancellationType === 'both') {
            shouldShowPopup = true;
            // Check if it's a losing bonus type
            const isLosingBonus = [
              BONUS_TYPES.LOSING,
              BONUS_TYPES.LOSING_SPORT,
              BONUS_TYPES.LOSING_BOTH
            ].includes(bonuses[0].kind);
            if(userActiveBonuses.length >1 && (bonuses[1].cancellationType === 'multiple_withdraw' || bonuses[1].cancellationType === 'both')){
              popupMessage = translate('BONUS_CANCEL_WITHDRAW_AND_DEPOSIT_WARNING', language)
            }
            else if (isLosingBonus) {
              // For losing bonuses, use cashback message without amount
              popupMessage = translate('BONUS_CANCEL_CASHBACK_WITHDRAW_WARNING', language);
            } else {
              // For deposit bonuses, use regular message with amount
              popupMessage = translate('BONUS_CANCEL_WITHDRAW_WARNING', language)
                .replace('#bonusAmount', userActiveBonuses[0].bonusAmount);
            }
          }
          break;

        case 'newBonus':
          // Check tenant settings for multi-bonus functionality
          const tenantThemeSetting = await TenantThemeSettingModel.findOne({
            attributes: ['allowedModules'],
            where: { tenantId: Tenant.id },
            raw: true
          });

        const allowedModules = tenantThemeSetting?.allowedModules?.split(',').map(m => m.trim()) || [];
        const hasMultiBonusEnabled = allowedModules.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE);
        const hasParallelBonusEnabled = allowedModules.includes(ALLOWED_PARALLEL_BONUS);
          // Show popup if multi-bonus is disabled
          if (!hasMultiBonusEnabled) {
            shouldShowPopup = true;

            // Get the new bonus details from args
            const newBonusId = this.args?.input?.bonusId;

            if (newBonusId) {
              // Fetch the new bonus details to check its type
              const newBonus = await BonusModel.findOne({
                attributes: ['kind'],
                where: {
                  id: newBonusId,
                  enabled: true
                },
                raw: true
              });

              if (newBonus) {
                // Check if new bonus is a losing bonus type
                const isNewBonusLosing = [
                  BONUS_TYPES.LOSING,
                  BONUS_TYPES.LOSING_SPORT,
                  BONUS_TYPES.LOSING_BOTH
                ].includes(newBonus.kind);

                // Check if existing active bonus is a losing bonus type
                const isExistingLosingBonus = [
                  BONUS_TYPES.LOSING,
                  BONUS_TYPES.LOSING_SPORT,
                  BONUS_TYPES.LOSING_BOTH
                ].includes(bonuses[0].kind);

                if (hasParallelBonusEnabled) {
                  // Parallel bonus allows 1 deposit + 1 cashback
                  // Only show popup if trying to activate same type of bonus
                  if(userActiveBonuses.length > 1){
                    shouldShowPopup = true;
                    popupMessage = translate('BOTH_BONUS_CANCEL_NEW_BONUS_WARNING', language)
                  }
                  else if (isNewBonusLosing === isExistingLosingBonus) {
                    shouldShowPopup = true;
                    if (isNewBonusLosing) {
                      // Both are losing bonuses - replacing cashback with cashback
                      popupMessage = translate('BONUS_CANCEL_CASHBACK_NEW_CASHBACK_WARNING', language);
                    } else {
                      // Both are deposit bonuses - replacing deposit with deposit
                      popupMessage = translate('BONUS_CANCEL_NEW_BONUS_WARNING', language)
                        .replace('#bonusAmount', userActiveBonuses[0].bonusAmount);
                    }
                  }
                  // If different types (one deposit, one cashback), no popup needed - parallel allowed
                } else {
                  if (isNewBonusLosing) {
                    // Activating a losing bonus
                    if (isExistingLosingBonus) {
                      // Existing is losing, new is losing - use cashback message without amount
                      popupMessage = translate('BONUS_CANCEL_CASHBACK_NEW_CASHBACK_WARNING', language);
                    } else {
                      // Existing is deposit, new is losing - use deposit message with amount
                      popupMessage = translate('BONUS_CANCEL_NEW_BONUS_WARNING', language)
                        .replace('#bonusAmount', userActiveBonuses[0].bonusAmount);
                    }
                  } else {
                    // Activating a deposit bonus
                    if (isExistingLosingBonus) {
                      // Existing is losing, new is deposit - use cashback message without amount
                      popupMessage = translate('BONUS_CANCEL_CASHBACK_NEW_CASHBACK_WARNING', language);
                    } else {
                      // Both are deposit bonuses - use regular message with amount
                      popupMessage = translate('BONUS_CANCEL_NEW_BONUS_WARNING', language)
                        .replace('#bonusAmount', userActiveBonuses[0].bonusAmount);
                    }
                  }
                }
              }
            }
          }
          break;

        default:
          return baseResponse;
      }

      // Return response based on whether popup should be shown
      return {
        showPopup: shouldShowPopup,
        popupData: popupMessage
      };

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, {
        tenantId: Tenant.id,
        userId: currentUser?.id,
        actionType: actionType
      });
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
