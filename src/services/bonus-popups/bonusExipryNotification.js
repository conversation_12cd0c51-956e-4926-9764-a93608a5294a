import { ApolloError } from 'apollo-server-express';
import Sequelize, { Op } from 'sequelize';
import { BONUS_STATUS, BONUS_TYPES } from '../../common/constants';
import <PERSON>rrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import getUserFromToken from '../../lib/getUserFromToken';
import translate from '../../lib/languageTranslate';

/**
 * Service for checking bonus expiry notifications that should be shown at login
 * @export
 * @class BonusExpiryNotification
 * @extends {ServiceBase}
 */
export default class BonusExpiryNotification extends ServiceBase {
  async run() {
    const {
      databaseConnection: {
        UserBonus: UserBonusModel,
        Bonus: BonusModel,
        TenantSetting: TenantSettingModel,
        DepositBonusSetting: DepositBonusSettingModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context;

    let currentUser = null;

    try {
      // Get user from token
      const { decodedToken, user } = await getUserFromToken(this.context, ['id']);
      currentUser = user;

      if (!decodedToken) {
        throw new ApolloError(translate('UNAUTHORIZED', language), 401);
      }

      // Base response structure
      const baseResponse = {
        showPopup: false,
        popupData: ''
      };

      // Get tenant expiry warning hours setting
      const tenantSetting = await TenantSettingModel.findOne({
        attributes: ['value'],
        where: {
          tenantId: Tenant.id,
          key: 'BONUS_EXPIRY_REMINDER_HOURS'
        },
        raw: true
      });

      // Default to 24 hours if not set
      const expiryWarningHours = parseInt(tenantSetting?.value || '24');

      // Find the earliest expiring active bonus (both deposit and losing types)
      const userActiveBonus = await UserBonusModel.findOne({
        attributes: ['id', 'bonusId', 'bonusAmount', 'expiresAt', 'kind'],
        where: {
          status: BONUS_STATUS.ACTIVE,
          [Op.or]: [
            { kind: BONUS_TYPES.DEPOSIT },
            { kind: BONUS_TYPES.DEPOSIT_SPORTS },
            {kind: BONUS_TYPES.DEPOSIT_BOTH },
            { kind: BONUS_TYPES.DEPOSIT_INSTANT},
            { kind: BONUS_TYPES.LOSING },
            { kind: BONUS_TYPES.LOSING_SPORT },
            {kind: BONUS_TYPES.LOSING_BOTH }
          ],
          expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
          userId: currentUser.id
        },
        order: [['expiresAt', 'ASC']] // Get the earliest expiring bonus first
      });

      if (!userActiveBonus) {
        return baseResponse;
      }

      // Get bonus details
      const bonus = await BonusModel.findOne({
        attributes: ['id'],
        where: {
          id: userActiveBonus.bonusId,
          enabled: true
        },
        include: {
          model: DepositBonusSettingModel,
          attributes: ['id', 'minDeposit', 'maxDeposit', 'maxBonus', 'rolloverMultiplier', 'validForDays'],
          required: false // Make it optional since losing bonuses might not have deposit settings
        }
      });

      if (!bonus) {
        return baseResponse;
      }

      const currentTime = new Date();
      const expiryTime = new Date(userActiveBonus.expiresAt);
      const hoursUntilExpiry = Math.ceil((expiryTime - currentTime) / (1000 * 60 * 60));


      // Check if bonus is expiring within the warning period
      if (hoursUntilExpiry <= expiryWarningHours && hoursUntilExpiry > 0) {
        // Determine the appropriate translation key based on time remaining
        let translationKey;
        let timeDisplay;

        if (hoursUntilExpiry === 1) {
          translationKey = 'BONUS_EXPIRY_WARNING_1_HOUR';
          timeDisplay = translate('1_HOUR', language) || '1 hour';
        } else {
          translationKey = 'BONUS_EXPIRY_WARNING_MULTIPLE_HOURS';
          timeDisplay = translate('HOURS', language)?.replace('#hours', hoursUntilExpiry) || `${hoursUntilExpiry} hours`;
        }

        const popupMessage = translate(translationKey, language, {
          bonusAmount: userActiveBonus.bonusAmount,
          timeDisplay: timeDisplay
        })
          ?.replace('#bonusAmount', userActiveBonus.bonusAmount)
          ?.replace('#timeDisplay', timeDisplay)
          || `⏳ Hurry! Your ₹${userActiveBonus.bonusAmount} bonus will expire in ${timeDisplay}. Use it before it's gone!`;

        return {
          showPopup: true,
          popupData: popupMessage
        };
      }

      return baseResponse;

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, {
        tenantId: Tenant.id,
        userId: currentUser?.id,
        action: 'bonus_expiry_check'
      });
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
