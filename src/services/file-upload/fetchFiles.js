import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Provides service for fetching file details
 * @export
 * @class FetchFiles
 * @extends {ServiceBase}
 */
export default class FetchFiles extends ServiceBase {
  async run () {
    const {
      auth: currentUser,
      databaseConnection: {
        UserDocument: UserDocumentModel
      },
      tenant: Tenant,
      req: { headers: { language } }
    } = this.context
    try {
    const result = await UserDocumentModel.findAll({ where: { userId: currentUser.id }, raw: true })
    return result
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: currentUser.id, tenantId: Tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
