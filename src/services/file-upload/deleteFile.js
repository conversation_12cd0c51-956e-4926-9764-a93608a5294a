import { ApolloError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { s3 } from '../../lib/aws-s3.config'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'


/**
 * Provides service for file deleting
 * @export
 * @class UploadFiles
 * @extends {ServiceBase}
 */
export default class DeleteFile extends ServiceBase {
  async run () {
    const {
      context: {
        req: { headers: { language } },
        tenant: { id: tenantId },
        databaseConnection: {
          UserDocument: UserDocumentModel
        },
        auth: { id: userId }
      },
      args: { id, documentUrl }
    } = this


    try {
    await checkImpersonatedAccess(this.context)


    const s3Config = config.getProperties().s3

    const file = await UserDocumentModel.findOne({ where: { id, documentUrl, userId } })

    if (!file) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('FILE_NOT_FOUND', language), errorCode: 404 }
    }
    const key = file.documentUrl

    const params = {
      Bucket: s3Config.bucket,
      Key: key.slice(key.indexOf('.com') + 5)
    }

    await s3.deleteObject(params).promise()

    await file.destroy()
    return true
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
