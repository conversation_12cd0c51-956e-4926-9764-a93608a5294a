import { ApolloError } from 'apollo-server-express'
import { v4 as uuid } from 'uuid'
import { ALLOWED_FILE_TYPES, FILE_UPLOAD_ERROR_CODES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import { s3 } from '../../lib/aws-s3.config'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'
import DocumentNotification from '../notification/notification-document'


/**
 * Provides service for file uploading
 * @export
 * @class UploadFiles
 * @extends {ServiceBase}
 */
export default class UploadFiles extends ServiceBase {
  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        UserDocument: UserDocumentModel
      },
      tenant: { id: tenantId },
      auth: currentUser
    } = this.context

    await checkImpersonatedAccess(this.context)

    const s3Config = config.getProperties().s3

    try {
      const awaitedFiles = await Promise.all(this.args.files)
      const dataToInsert = []

      for (const ele of awaitedFiles) {
        const element = {}
        const stream = ele.createReadStream()

        if (ALLOWED_FILE_TYPES.indexOf(ele.mimetype.split('/')[1]) === -1) {
          throw new Error(1)
        }

        const originalName = ele.filename.split('.')[0]
        const key = `tenants/${tenantId}/user/${currentUser.id}/${uuid()}.${ele.mimetype.split('/')[1]}`

        const s3Params = {
          ACL: 'public-read',
          Bucket: s3Config.bucket,
          Key: key,
          Body: stream
        }
        await s3.upload(s3Params).promise()

        element.documentName = originalName
        element.documentUrl = key
        element.userId = currentUser.id
        element.isVerified = false
        element.status = 'pending'
        dataToInsert.push(element)
      }

      const response = await UserDocumentModel.bulkCreate(dataToInsert, { returning: true })
      await DocumentNotification(response, this.context)

      return response
    } catch (e) {
      await ErrorLogHelper.logError(e, this.context, { id: currentUser.id, tenantId })
      throw new ApolloError(translate(FILE_UPLOAD_ERROR_CODES[e.message] || 'FILE_UPLOAD_FAILED', language), 500)
    }
  }
}
