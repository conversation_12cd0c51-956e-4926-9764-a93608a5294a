import { ApolloError } from 'apollo-server-express';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

/**
 * mark thread message as read
 * @export
 * @class MarkThreadAsRead
 * @extends {ServiceBase}
 */
export default class MarkThreadAsRead extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        ThreadUserDetail: ThreadUserDetailModel
      },
      auth: { id: userId },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    // Check if the thread is already marked as read for the user
    const threadUserDetail = await ThreadUserDetailModel.findOne({
      attributes: ['id','isRead'],
      where: {
        threadId: this.args.id,
        userId,
      }
    });

    if (!threadUserDetail) {
      // If no record exists, create a new record and mark as read
      await ThreadUserDetailModel.create({
        threadId: this.args.id,
        userId,
        isRead: true
      });
    } else if (!threadUserDetail.isRead) {
      // If record exists but is not marked as read, update it to mark as read
      await ThreadUserDetailModel.update(
        { isRead: true },
        {
          where: {
            threadId: this.args.id,
            userId,
          }
        }
      )
    }

    return true
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
