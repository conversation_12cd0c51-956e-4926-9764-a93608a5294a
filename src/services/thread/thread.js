import { ApolloError } from 'apollo-server-express';
import moment from 'moment';
import { Op, Sequelize } from 'sequelize';
import { THREAD_MESSAGE } from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';

/**
 * get Threads
 * @export
 * @class Thread
 * @extends {ServiceBase}
 */
export default class Thread extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        User: UserModel,
        Thread: ThreadModel,
        ThreadUserDetail: ThreadUserDetailModel

      },
      tenant: { id: tenantId },
      auth: { id: userId },
      req: { headers: { language } }
    } = this.context

    try {
    const { limit, offset, read } = this.args

    const user = await UserModel.findOne({
      attributes: ['parentId', 'parentType'],
      where: {
        id: userId
      }
    })

    let whereCondition = {
      tenantId,
      active: true,
      [Op.or]: [
        {
          sentType: THREAD_MESSAGE.SENT_TYPE.ALL,
          ownerType: THREAD_MESSAGE.OWNER_TYPE.OWNER
        },
        {
          sentType: THREAD_MESSAGE.SENT_TYPE.SELECTED_USERS,
          ownerType: THREAD_MESSAGE.OWNER_TYPE.OWNER,
          [Op.and]: [
            Sequelize.literal(`EXISTS (SELECT 1 FROM jsonb_array_elements("Thread"."sent_ids") AS "sentId" WHERE "sentId"::text = '${userId}')`)
          ]
        },
        {
          sentType: THREAD_MESSAGE.SENT_TYPE.ALL,
          ownerType: THREAD_MESSAGE.OWNER_TYPE.AGENT,
          parent_id: user?.parentId
        },
        {
          sentType: THREAD_MESSAGE.SENT_TYPE.SELECTED_USERS,
          ownerType: THREAD_MESSAGE.OWNER_TYPE.AGENT,
          parent_id: user?.parentId,
          [Op.and]: [
            Sequelize.literal(`EXISTS (SELECT 1 FROM jsonb_array_elements("Thread"."sent_ids") AS "sentId" WHERE "sentId"::text = '${userId}')`)
          ]
        }
      ]
    }

    if (read !== undefined && read !== null) {
      whereCondition[Op.and] = [
        Sequelize.literal(`COALESCE("ThreadUserDetails"."is_read", false) = ${read}`)
      ];
    }

    const { count, rows: threads } = await ThreadModel.findAndCountAll({
      attributes: [
        'id',
        'title',
        'messageBody',
        [Sequelize.literal(`COALESCE("ThreadUserDetails"."is_read", false)`), 'isRead'],
        "createdAt"
      ],
      where: whereCondition,
      include: {
          model: ThreadUserDetailModel,
          attributes: [],
          required: false,
          where: {
            user_id: userId,
          },
      },
      order: [['createdAt', 'DESC']],
      limit: limit || 10,
      offset: offset || 0,
      subQuery: false,
      raw: true,
    });

    const finalThreadsList = threads.map(async object => {
      // Formatting createdAt date with optional user timezone
      const date = moment(object.createdAt)
      object.createdAt = date.format('DD/MM/YYYY HH:mm')
      return object
    })

    return { count, threads: finalThreadsList };
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
