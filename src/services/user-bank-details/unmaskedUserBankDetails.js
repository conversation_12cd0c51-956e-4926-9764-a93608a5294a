import { ApolloError, AuthenticationError } from 'apollo-server-express'
import { ERORR_TYPE } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'

const constraints = {
  userBankId: {
    type: 'integer'
  }
}

/**
 * Provides unmasked user bank details
 * @export
 * @class UnmaskedUserBankDetails
 * @extends {ServiceBase}
 */
export default class UnmaskedUserBankDetails extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          UserBankDetails: UserBankDetailsModel
        },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { userBankId }
    } = this

    try {
    const userBankDetails = await UserBankDetailsModel.findOne({
      attributes: ['id', 'bankName', 'bankIfscCode', 'accountNumber', 'name', 'phoneNumber', 'phoneCode', 'status'],
      where: {
        id: userBankId,
        tenantId,
        userId
      },
      raw: true
    })

    if (!userBankDetails) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_BANK_DETAIL_NOT_FOUND', language) }
    }

    return userBankDetails
  } catch (error) {
      if (error.errorType === ERORR_TYPE.AUTHRNTICATION) {
        throw new AuthenticationError(error.errorMsg)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
