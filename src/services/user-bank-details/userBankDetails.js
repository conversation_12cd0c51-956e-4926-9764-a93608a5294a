import { ApolloError } from 'apollo-server-express'
import { ALLOWED_PERMISSIONS, WITHDRAW_REQUEST_STATUS } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { Sequelize, sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'
const constraints = {
}
/**
 * Provides service for fetch all the withdraw request user made till date
 * @export
 * @class ShowDepositRequest
 * @extends {ServiceBase}
 */
export default class ShowDepositRequest extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: { input: { page, limit } = {} },
      context: {
        auth: { id: userId },
        databaseConnection: {
          UserBankDetails: UserBankDetailsModel,
          TenantThemeSetting: TenantThemeSettingModel,
          WithdrawRequest: WithdrawRequestModel
        },
        tenant: { id: tenantId },
        req: { headers: { language } }
      }
    } = this

    try {
      let offset = 0
      if (page && limit) {
        offset = (page - 1) * limit
      }
      const sortOrder = [['createdAt', 'desc'], ['id', 'asc']]
      if (limit === 0) {
        const { count, rows: bankDetails } = await UserBankDetailsModel.findAndCountAll({
          where: { userId: userId, isDeleted: false },
          order: sortOrder
        })

        const tenantTheme = await TenantThemeSettingModel.findOne({
          where: {
            tenantId
          },
          attributes: ['allowedModules']
        })

        let isWithdrawalRequestPending = false

        const withdrawalRequestLimitation = tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WITHDRAWAL_REQUEST_LIMITATION)

        if (withdrawalRequestLimitation) {
          const pendingRequestExist = await WithdrawRequestModel.findOne({
            where: {
              userId,
              tenantId,
              status: WITHDRAW_REQUEST_STATUS.PENDING
            },
            attributes: ['id'],
            raw: true
          })

          if (pendingRequestExist) isWithdrawalRequestPending = true
        }

        return { count, requests: bankDetails, isWithdrawalRequestPending }
      }
      const { count, rows: bankDetails } = await UserBankDetailsModel.findAndCountAll({
        where: { userId: userId, isDeleted: false },
        limit: limit || 10,
        offset: offset || 0,
        order: sortOrder
      })

      const updatedBankDetails = bankDetails?.map(detail => {
        if (detail.accountNumber && detail.accountNumber?.length >= 4) {
          detail.accountNumber = '*'.repeat('' + detail.accountNumber.length - 4) + ('' + detail.accountNumber).slice(-4)
        }

        if (detail.bankIfscCode && detail.bankIfscCode?.length >= 4) {
          detail.bankIfscCode = '*'.repeat('' + detail.bankIfscCode.length - 4) + ('' + detail.bankIfscCode).slice(-4)
        }
        return detail
      })

      const [details] = await sequelize.query(`
      SELECT
        "add_bank_details",
        CASE
          WHEN "TenantThemeSetting"."add_bank_details" = FALSE
          THEN
          (
            SELECT id from withdraw_requests
            WHERE
            (
              status='approved'
              OR verify_status = 'verified'
            )
            AND user_id = ${userId}
            LIMIT 1
          )
          ELSE NULL
        END as id
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting"
      WHERE
        "TenantThemeSetting"."tenant_id" = ${tenantId} LIMIT 1
    `,
      { type: Sequelize.QueryTypes.SELECT, useMaster: false })

      if (!details.id) {
        details.add_bank_details = true
      }

      return { count, requests: updatedBankDetails, addBankDetails: details.add_bank_details }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
