import { ApolloError } from 'apollo-server-express'
import { ERORR_TYPE, UNIQUE_BANK_ACCOUNT } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import ErrorLogHelper from '../../common/errorLog'
/**
 * get Providers
 * @export
 * @class UserBankDetails
 * @extends {ServiceBase}
 */
const constraints = {
  bankId: {
    type: 'string'
  },
  accountNumber: {
    type: 'string'
  },
  bankIfscCode: {
    type: 'string'
  },
  phoneNumber: {
    type: 'string'
  },
  name: {
    type: 'string'
  },
  bankName: {
    type: 'string'
  },
  phoneCode: {
    type: 'string'
  }

}
export default class UpdateUserBankDetails extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language } },
      databaseConnection: {
        UserBankDetails: UserBankDetailsModel,
        TenantThemeSetting: TenantThemeSettingModel,
        User: UserModel
      },
      tenant: { id: tenantId },
      auth: { id: userId }
    } = this.context

    try {
    const whereStatement = {
      userId: userId
    }

    const bankId = this.args.bankId

    // if (bankId) {
    //   whereStatement.id = { [Op.not]: this.args.bankId }
    // }

    const existingBankDetail = await UserBankDetailsModel.findAll({
      attributes: ['id', 'accountNumber'],
      where: whereStatement
    })

    const maxBankAccountDetails = await TenantThemeSettingModel.findOne({
      where: {
        tenantId
      },
      attributes: ['maxBankAccountLimit', 'allowedModules']
    })

    const hasUniqueBankAccount = maxBankAccountDetails?.allowedModules
      ? maxBankAccountDetails.allowedModules.split(',').map(module => module.trim()).includes(UNIQUE_BANK_ACCOUNT)
      : false

    if (hasUniqueBankAccount) {
      if (this.args?.accountNumber) {
        const existingBankAccount = await UserBankDetailsModel.findOne({
          attributes: ['id'],
          where: {
            accountNumber: this.args?.accountNumber,
            tenantId
          }
        })
        if (existingBankAccount) {
          throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('ACCOUNT_ALREADY_EXIST', language), errorCode: 500 }
          }
      }
    } else {
      if (this.args?.accountNumber && existingBankDetail.find(bank => bank.accountNumber === this.args.accountNumber)) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('ACCOUNT_ALREADY_EXIST', language), errorCode: 500 }
      }
    }

    const details = {
      userId: userId,
      tenantId: tenantId
    }

    if (this.args?.accountNumber) {
      details.accountNumber = this.args?.accountNumber
    }
    if (this.args?.bankIfscCode) {
      details.bankIfscCode = this.args?.bankIfscCode
    }
    if (this.args?.phoneNumber) {
      details.phoneNumber = this.args?.phoneNumber
    } else {
      const userPhoneNumber = await UserModel.findOne({
        attributes: ['phone'],
        where: {
          id: userId,
          tenantId
        },
        raw: true
      })
      details.phoneNumber = userPhoneNumber?.phone
    }
    if (this.args?.phoneCode) {
      details.phoneCode = this.args?.phoneCode
    }
    if (this.args?.name) {
      details.name = this.args?.name
    }
    if (this.args?.bankName) {
      details.bankName = this.args?.bankName
    }

    if (bankId) {
      const bankDetail = await UserBankDetailsModel.findOne({
        attributes: ['id'],
        where: {
          id: bankId,
          userId: userId
        }
      })
      if (!bankDetail) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('NO_DATA', language), errorCode: 400 }
      }
      await UserBankDetailsModel.update(
        details, {
          where: {
            id: bankId
          }
        })

      this.args.id = bankId
      this.args.status = 'active'

      return this.args
    } else {
      if (maxBankAccountDetails?.maxBankAccountLimit && maxBankAccountDetails?.maxBankAccountLimit <= existingBankDetail.length) {
        throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('Max account limit reached', language), errorCode: 500 }
      }

      const newBankDetails = await UserBankDetailsModel.create(details)

      return newBankDetails
    }
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId })
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
