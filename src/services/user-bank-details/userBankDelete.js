import { ApolloError } from 'apollo-server-express'
import ServiceBase from '../../common/serviceBase'
import checkImpersonatedAccess from '../../lib/checkImpersonatedAccess'
import translate from '../../lib/languageTranslate'
import <PERSON>rror<PERSON><PERSON><PERSON>elper from '../../common/errorLog'
import { ERORR_TYPE } from '../../common/constants'

/**
 * Provides service for file deleting
 * @export
 * @class userBankDelete
 * @extends {ServiceBase}
 */
export default class userBankDelete extends ServiceBase {
  async run () {
    const {
      context: {
        req: { headers: { language } },
        databaseConnection: {
          UserBankDetails: UserBankDetailsModel
        },
        auth: { id: userId },
        tenant: { id: tenantId }
      },
      args: { id, status, action }
    } = this

    try {
    await checkImpersonatedAccess(this.context)

    const bank = await UserBankDetailsModel.findOne({ where: { id, userId } })

    if (!bank) {
      throw { errorType: ERORR_TYPE.APOLLO_ERROR, errorMsg: translate('NO_DATA', language), errorCode: 404 }
    }

    const updateQuery = (action !== undefined && action === 'delete') ? { isDeleted: true } : { status: status }

    await UserBankDetailsModel.update(updateQuery, {
      where: {
        id: id
      }
    })

    return true
  } catch (error) {
      if (error.errorType === ERORR_TYPE.APOLLO_ERROR) {
        throw new ApolloError(error.errorMsg, error.errorCode)
      } else {
        await ErrorLogHelper.logError(error, this.context, { id: userId, tenantId})
        throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
      }
  } }
}
