import { ApolloError } from 'apollo-server-express';
import * as jwt from 'jsonwebtoken';
import { ENABLE_PLAYER_CATEGORY } from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import userCategory from '../../common/userCategory';
import config from '../../config/app';
import translate from '../../lib/languageTranslate'

/**
 * get BankLists
 * @export
 * @class BankLists
 * @extends {ServiceBase}
 */
export default class BankLists extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        TenantBankConfiguration: TenantBankConfigurationModel,
        TenantThemeSetting: TenantThemeSettingModel,
        User: UserModel,
        Wallet: WalletModel,
        Currency: CurrencyModel,
      },
      req: { headers: { authorization, language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    let decodedToken = null, user = null, category, enablePlayerCategory

    if (authorization) {
      const token = authorization
      const authConfig = config.getProperties().auth
      const splitToken = token.replace('Bearer ', '')
      const secretKey = authConfig.jwt_secret

      decodedToken = jwt.verify(splitToken, secretKey)

      user = await UserModel.findOne({
        where: { id: decodedToken.id },
        attributes: ['id','vipLevel'],
        include: [{
          model: WalletModel,
          attributes: ['id'],
          include: [{
            model: CurrencyModel,
            attributes: ['id'],
          }]
        }]
      })
    }

    const banks = await TenantBankConfigurationModel.findAll({
      attributes: ['id', 'bankName', 'accountHolderName', 'bankIfscCode', 'accountNumber', 'countryCode', 'upiId',
        'type', 'cryptoExchangeRate', 'image', 'playerCategory'],
      where: {
        status: true,
        tenantId: tenantId
      }
    })

    const tenantThemeData = await TenantThemeSettingModel.findOne({
      attributes: ['allowedModules'],
      where: {
        tenantId: tenantId
      }
    })

    if (!!user) {
      enablePlayerCategory = tenantThemeData?.allowedModules ? tenantThemeData?.allowedModules.split(',').map(module => module.trim()).includes(ENABLE_PLAYER_CATEGORY) : false
      if (enablePlayerCategory) category = await userCategory(user?.id, tenantId, user?.Wallet?.id)
    }

    const updatedBankList = !!user && banks.filter(bank => (
      !enablePlayerCategory || !bank?.playerCategory || (category && bank?.playerCategory?.includes(category))
    ))

    return updatedBankList || banks
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
