import { ApolloError } from 'apollo-server-express'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
/**
 * get PaymentProviderImages
 * @export
 * @class PaymentProviderImages
 * @extends {ServiceBase}
 */
export default class PaymentSupportImages extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        PaymentSupportImage: PaymentSupportImageModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    const paymentSupportImages = await PaymentSupportImageModel.findAll({
      where: {
        tenantId: tenantId,
        status: true
      },
      attributes: ['id', 'imageUrl']
    })

    return paymentSupportImages
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
