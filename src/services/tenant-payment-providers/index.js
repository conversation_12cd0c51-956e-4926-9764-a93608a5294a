import * as jwt from 'jsonwebtoken'
import { Op } from 'sequelize'
import { ENABLE_PLAYER_CATEGORY, PLAYER_CATEGORY } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import userCategory from '../../common/userCategory'
import config from '../../config/app'
import { ApolloError } from 'apollo-server-express'
import translate from '../../lib/languageTranslate'

/**
 * get Providers
 * @export
 * @class PaymentProviders
 * @extends {ServiceBase}
 */
export default class PaymentProviders extends ServiceBase {
  async run () {
    const {
      databaseConnection: {
        tenantPaymentConfiguration: tenantPaymentConfigurationModel,
        paymentProviders: paymentProvidersModel,
        User: UserModel,
        TenantThemeSetting: TenantThemeSettingModel,
        Wallet: WalletModel,
        Currency: CurrencyModel
      },
      req: { headers: { language } },
      tenant: { id: tenantId }
    } = this.context

    try {
    let decodedToken
    let user
    if (this.context.req?.headers?.authorization) {
      const token = this.context.req?.headers?.authorization
      const authConfig = config.getProperties().auth
      const splitToken = token.replace('Bearer ', '')
      const secretKey = authConfig.jwt_secret

      try {
        decodedToken = await jwt.verify(splitToken, secretKey)
        user = await UserModel.findOne({
          where: { id: decodedToken.id },
          // attributes: ['vipLevel'],
          include: [{
            model: WalletModel,
            include: [{
              model: CurrencyModel
            }]
          }]
        })
      } catch (e) {
        // await ErrorLogHelper.logError(e, this.context, { tenantId })
        throw new Error(e)
      }
    }

    // check if payment provider is available for this tenant
    const { dataValues: { payment_providers: allowedPaymentProviders, allowedModules } } = await TenantThemeSettingModel.findOne({
      attributes: ['payment_providers', 'allowedModules'],
      where: {
        tenantId: tenantId
      }
    })

    const PaymentProviders = await tenantPaymentConfigurationModel.findAll({
      include: {
        model: paymentProvidersModel,
        where: {
          providerType: 'deposit'
        }
      },
      where: {
        active: true,
        tenantId: tenantId,
        providerId: {
          [Op.in]: !!allowedPaymentProviders && allowedPaymentProviders.split(',')
        }
      },
      order: [
        ['order', 'ASC']
      ]
    })

    let enablePlayerCategory, category
    if (!!user) {
      enablePlayerCategory = allowedModules ? allowedModules.split(',').map(module => module.trim()).includes(ENABLE_PLAYER_CATEGORY) : false
      category = PLAYER_CATEGORY[user?.categoryType]
    }

    const updatedPaymentProviders = !!user && PaymentProviders.filter(provider => (
      provider.vipLevel &&
      provider.vipLevel?.includes(user?.vipLevel?.toString()) &&
      provider.paymentProvider?.currencies?.includes(user.Wallet?.Currency?.id) &&
      (
        !enablePlayerCategory || !provider?.playerCategory || // Check if enablePlayerCategory is false and player category is not null
        (
          category && // Check if category is not null
          provider?.playerCategory?.includes(category) // Check if user's category is included
        )
      )
    ))

    return updatedPaymentProviders || PaymentProviders
  } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  } }
}
