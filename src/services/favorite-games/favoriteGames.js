import { ApolloError } from 'apollo-server-express'
import { PAGINATION_CONSTANT } from '../../common/constants'
import <PERSON>rror<PERSON><PERSON><PERSON><PERSON><PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import translate from '../../lib/languageTranslate'

/**
 * get favorite games details
 * @export
 * @class GetFavoriteGames
 * @extends {ServiceBase}
 */
export default class GetFavoriteGames extends ServiceBase {
  async run () {
    const {
      context: {
        tenant: { id: tenantId },
        req: { headers: { language } },
        auth: { id: userId } = { id: null }
      },
      args: {
        pageId,
        topMenuId,
        categoryId
      }
    } = this

    const limit = this.args.limit || PAGINATION_CONSTANT.LIMIT
    const offset = this.args.offset || PAGINATION_CONSTANT.OFFSET

    try {
      if (!userId) {
        return { id: 0, title: 'Favorites', cms: [], menus: [] }
      }

      const favoriteGames = await sequelize.models.FavoriteGames.findAll({
        where: { userId, tenantId },
        attributes: ['gameId', 'casinoProviderId']
      })

      if (!favoriteGames.length) {
        return { id: 0, title: 'Favorites', cms: [], menus: [] }
      }

      const gameIds = favoriteGames.map(fg => fg.gameId)
      const providerIds = favoriteGames.map(fg => fg.casinoProviderId)

      const uniqueGameAssignmentQuery = `
        WITH game_categories AS (
          SELECT
            ccg.uuid,
            ccg.provider_id,
            ccg.category_id,
            ROW_NUMBER() OVER (PARTITION BY ccg.uuid, ccg.provider_id ORDER BY ccg.category_id) as rank
          FROM custom_category_games ccg
          JOIN custom_category cc ON ccg.category_id = cc.id
          WHERE
            cc.status = true
            AND cc.is_deleted = false
            AND ccg.is_deleted = false
            AND ccg.tenant_id = :tenantId
            AND ccg.uuid IN (:gameIds)
            AND ccg.provider_id IN (:providerIds)
            ${pageId ? 'AND ccg.page_id = :pageId' : ''}
            ${topMenuId ? 'AND cc.top_menu_id = :topMenuId' : ''}
            ${categoryId ? 'AND cc.id = :categoryId' : ''}
        )
        SELECT uuid, provider_id, category_id
        FROM game_categories
        WHERE rank = 1
      `

      const uniqueGameAssignments = await sequelize.query(uniqueGameAssignmentQuery, {
        replacements: {
          tenantId,
          gameIds,
          providerIds,
          pageId: pageId || null,
          topMenuId: topMenuId || null,
          categoryId: categoryId || null
        },
        type: sequelize.QueryTypes.SELECT
      })

      const assignedCategoryIds = [...new Set(uniqueGameAssignments.map(g => g.category_id))]

      const categoriesQuery = `
        SELECT
          cc.id,
          cc.title AS name,
          cc.top_menu_id,
          cm.image_url AS icon_url,
          cc.ordering
        FROM
          custom_category cc
          JOIN casino_menus cm ON cc.menu_icon_id = cm.id
        WHERE
          cc.status = true
          AND cc.is_deleted = false
          AND cc.id IN (:assignedCategoryIds)
          ${topMenuId ? 'AND cc.top_menu_id = :topMenuId' : ''}
          ${categoryId ? 'AND cc.id = :categoryId' : ''}
        ORDER BY
          cc.ordering ASC
      `

      const categories = await sequelize.query(categoriesQuery, {
        replacements: {
          assignedCategoryIds,
          topMenuId: topMenuId || null,
          categoryId: categoryId || null
        },
        type: sequelize.QueryTypes.SELECT
      })

      const favoriteMap = new Map()
      favoriteGames.forEach(fav => {
        favoriteMap.set(`${fav.gameId}_${fav.casinoProviderId}`, true)
      })

      const result = await Promise.all(categories.map(async (category) => {
        const categoryGames = uniqueGameAssignments.filter(g => g.category_id === category.id)
        const categoryGameIds = categoryGames.map(g => g.uuid)
        const categoryProviderIds = categoryGames.map(g => g.provider_id)

        if (categoryGameIds.length === 0) {
          return {
            id: category.id,
            name: category.name,
            top_menu_id: category.top_menu_id,
            topMenuId: category.top_menu_id,
            icon_url: category.icon_url,
            ordering: category.ordering,
            games: [],
            totalgamecount: 0
          }
        }

        const gamesQuery = `
          SELECT DISTINCT ON (ccg.uuid, ccg.provider_id)
            ccg.uuid,
            CASE
              WHEN ci.is_image_modified THEN ci.image
              WHEN ci2.image IS NOT NULL THEN ci2.image
              ELSE ci.image
            END AS image,
            ci.featured,
            ci.has_lobby,
            ccg.page_id,
            p.title,
            ccg.provider_id,
            ccg.ordering,
            ci.is_image_modified,
            mt.name as name,
            cp.name as provider_name
          FROM
            custom_category_games ccg
            JOIN custom_category cc ON ccg.category_id = cc.id
            INNER JOIN casino_items ci ON ccg.uuid = ci.uuid AND ccg.tenant_id = ci.tenant_id AND ccg.provider_id = NULLIF(ci.provider, '')::INT
            LEFT JOIN casino_items ci2 ON ccg.uuid = ci2.uuid AND ci2.tenant_id = 0 AND NULLIF(ci2.provider, '')::INT = ccg.provider_id
            JOIN public.pages p ON p.id = ccg.page_id
             JOIN aggregator a ON a.page_id = p.id AND a.status = true
            JOIN menu_tenant_setting mtg ON p.top_menu_id = mtg.id
            JOIN menu_master mm ON mtg.menu_id = mm.id
            JOIN menu_items mt ON ci.id = mt.casino_item_id
            JOIN page_menus pm ON mt.page_menu_id = pm.id
            JOIN casino_menus cmi ON pm.casino_menu_id = cmi.id
            JOIN casino_providers cp ON ccg.provider_id = cp.id
          WHERE cc.status = true and  cc.is_deleted = false and a.status = true and
            ccg.category_id = :categoryId
            AND ccg.is_deleted = false
            AND ci.active = true
            AND p.enabled = true
            AND p.tenant_id = ccg.tenant_id
            AND mtg.status = true
            AND mm.active = true
            AND mt.active = true
            AND (cmi.tenant_id = ccg.tenant_id OR cmi.tenant_id = 0)
            AND cmi.enabled = true
            AND ccg.tenant_id = :tenantId
            AND ccg.uuid IN (:categoryGameIds)
            AND ccg.provider_id IN (:categoryProviderIds)
            ${pageId ? 'AND ccg.page_id = :pageId' : ''}
          ORDER BY
            ccg.uuid, ccg.provider_id, ccg.ordering ASC
          OFFSET :offset LIMIT :limit
        `

        const games = await sequelize.query(gamesQuery, {
          replacements: {
            categoryId: category.id,
            tenantId,
            categoryGameIds,
            categoryProviderIds,
            pageId: pageId || null,
            offset,
            limit
          },
          type: sequelize.QueryTypes.SELECT
        })

        const formattedGames = games.map(game => ({
          uuid: game.uuid,
          image: game.image,
          featured: game.featured,
          has_lobby: game.has_lobby,
          page_id: game.page_id,
          title: game.title,
          provider_id: game.provider_id,
          ordering: game.ordering,
          is_image_modified: game.is_image_modified,
          provider_name: game.provider_name,
          name: game.name,
          isFavorite: favoriteMap.has(`${game.uuid}_${game.provider_id}`) || false
        }))

        return {
          id: category.id,
          name: category.name,
          top_menu_id: category.top_menu_id,
          topMenuId: category.top_menu_id,
          icon_url: category.icon_url,
          ordering: category.ordering,
          games: formattedGames.length > 0 ? formattedGames : [],
          totalgamecount: categoryGames.length
        }
      }))

      const filteredResult = result.filter(item => item.games && item.games.length > 0)

      return { id: 0, title: 'Favorites', cms: [], menus: filteredResult }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
