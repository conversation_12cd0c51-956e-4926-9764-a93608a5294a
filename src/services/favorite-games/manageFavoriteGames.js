import { ApolloError } from 'apollo-server-express'
import ServiceBase from '../../common/serviceBase'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import translate from '../../lib/languageTranslate'

/**
 * Manage Favorite Game (Add/Remove)
 * @export
 * @class ManageFavoriteGame
 * @extends {ServiceBase}
 */
export default class ManageFavoriteGame extends ServiceBase {
  async run () {
    const {
      args: { gameId, casinoProviderId, request },
      context: {
        databaseConnection: {
          FavoriteGames: FavoriteGameModel,
          CustomCategoryGame: CustomCategoryGameModel
        },
        tenant: { id: tenantId },
        auth: { id: userId },
        req: { headers: { language } }
      }
    } = this

    try {
      const gameExist = await CustomCategoryGameModel.findOne({
        where: {
          uuid: gameId,
          providerId: casinoProviderId,
          tenantId,
          isDeleted: false
        },
        attributes: ['id', 'uuid']
      })

      if (!gameExist) {
        return {
          success: false,
          message: translate('GAME_NOT_FOUND', language)
        }
      }
      if (request) {
        const existingFavorite = await FavoriteGameModel.findOne({
          where: { userId, gameId, casinoProviderId, tenantId },
          attributes: ['id', 'casinoProviderId', 'tenantId', 'gameId', 'userId', 'createdAt', 'updatedAt']
        })

        if (existingFavorite) {
          return {
            success: true,
            message: translate('ALREADY_FAVORITED', language),
            favoriteGame: existingFavorite
          }
        }

        const favoriteGame = await FavoriteGameModel.create({
          userId,
          gameId,
          tenantId,
          casinoProviderId
        })

        return {
          success: true,
          message: translate('GAME_FAVORITED', language),
          favoriteGame
        }
      } else {
        const deleted = await FavoriteGameModel.destroy({
          where: { userId, gameId, casinoProviderId, tenantId }
        })

        return {
          success: !!deleted,
          message: translate(deleted ? 'GAME_UNFAVORITED' : 'GAME_ALREADY_UNFAVORITED', language)
        }
      }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { userId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
