import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { isUserActive } from '../../lib/isUserActive'

/**
 * Get popular games details
 * @export
 * @class PopularGamesSliders
 * @extends {ServiceBase}
 */
export default class PopularGamesSliders extends ServiceBase {
  async run () {
    const {
      MenuItem: MenuItemModel,
      CasinoItem: CasinoItemModel,
      PageMenu: PageMenuModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel,
      PopularGames: PopularGamesModel,
      PopularGamesDetails: PopularGamesDetailsModel,
      FavoriteGames: FavoriteGamesModel
    } = this.context.databaseConnection

    const tenantId = this.context.tenant.id

    const {
      context: {
        req: { headers: { language, authorization } }
      }
    } = this

    try {
    // Check if user is authenticated and active
      let userId
      if (authorization) {
        const activeUser = await isUserActive(authorization, tenantId, this.context.databaseConnection, false)
        userId = activeUser.id
      }

      // Fetch popular game sliders
      const sliders = await PopularGamesModel.findAll({
        attributes: ['id', 'sliderTitle', 'isPrimary'],
        where: { tenantId, status: true },
        order: [['ordering', 'ASC']]
      })

      const sliderIds = sliders.map(slider => slider.id)

      // Fetch game details based on slider IDs
      const gameList = await PopularGamesDetailsModel.findAll({
        attributes: ['gameId', 'popularGameId'],
        where: { popularGameId: { [Op.in]: sliderIds } }
      })

      const allGameIDs = gameList.map(game => game.gameId)

      // Fetch active menu items with their casino items
      const menuItems = await MenuItemModel.findAll({
        include: [{
          model: CasinoItemModel,
          where: { tenantId }
        }],
        where: {
          active: true,
          id: { [Op.in]: allGameIDs }
        },
        order: [['updatedAt', 'DESC']]
      })

      const pageMenuIds = menuItems.map(item => item.pageMenuId)

      // Fetch page menus with associated pages and casino menus
      const pageMenus = await PageMenuModel.findAll({
        attributes: ['id'],
        include: [
          {
            model: PageModel,
            attributes: ['title', 'id'],
            where: {
              enabled: true,
              tenantId,
              topMenuId: {
                [Op.in]: Sequelize.literal(
                `(SELECT id FROM menu_tenant_setting WHERE tenant_id = ${tenantId})`
                )
              }
            }
          },
          {
            model: CasinoMenuModel,
            as: 'casinoMenu',
            attributes: ['imageUrl'],
            where: { enabled: true, tenantId }
          }
        ],
        where: { id: { [Op.in]: pageMenuIds } }
      })

      const pageMenuMap = Object.fromEntries(
        pageMenus.map(pm => [
          pm.id,
          {
            Page: pm.Page?.title,
            PageId: pm.Page?.id,
            casinoMenu: pm.casinoMenu?.imageUrl
          }
        ])
      )

      let favoriteGames = []
      if (userId) {
        favoriteGames = await FavoriteGamesModel.findAll({
          where: { userId, tenantId },
          attributes: ['gameId', 'casinoProviderId']
        })
      }

      const favoriteMap = new Map()
      favoriteGames.forEach(fav => {
        favoriteMap.set(`${fav.gameId}_${fav.casinoProviderId}`, true)
      })

      // Map menu items with page and casino menu data
      const menuItemsWithData = menuItems.map(menuItem => {
        const casinoItem = menuItem.CasinoItem || {}
        const pageInfo = pageMenuMap[menuItem.pageMenuId] || {}
        const providerId = casinoItem.provider

        return {
          ...menuItem.get(),
          pageMenuTitle: pageInfo.Page || null,
          casinoMenuImageUrl: pageInfo.casinoMenu || null,
          providerId: casinoItem.provider,
          uuid: casinoItem.uuid,
          isFavorite: userId ? (favoriteMap.has(`${casinoItem.uuid}_${providerId}`) || false) : null
        }
      })

      // Map sliders to their respective games
      const games = gameList.map(game => game.get())
      const result = sliders.map(slider => {
        const gamesList = games.filter(game => game.popularGameId === slider.id)
        const gameData = menuItemsWithData.filter(menuItem =>
          gamesList.some(game => parseInt(menuItem.id) === game.gameId)
        )
        return {
          id: slider.id,
          name: slider.sliderTitle,
          is_primary: slider.isPrimary,
          games: gameData
        }
      })

      return result
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
