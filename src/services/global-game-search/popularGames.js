import { ApolloError } from 'apollo-server-express'
import { Op, Sequelize } from 'sequelize'
import { POPULAR_GAMES_PAGINATION_CONSTANT } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 *
 * get popular games details
 * @export
 * @class PopularGames
 * @extends {ServiceBase}
 */
export default class PopularGames extends ServiceBase {
  async run () {
    const {
      MenuItem: MenuItemModel,
      CasinoItem: CasinoItemModel,
      PageMenu: PageMenuModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel
    } = this.context.databaseConnection

    const tenantId = this.context.tenant.id
    const limit = POPULAR_GAMES_PAGINATION_CONSTANT.LIMIT
    const offset = POPULAR_GAMES_PAGINATION_CONSTANT.OFFSET

    const {
      context: {
        req: { headers: { language } }
      }
    } = this

    try {
    const pageMenus = await PageMenuModel.findAll({
      attributes: ['id'],
      include: [
        {
          model: PageModel,
          attributes: ['title'],
          where: {
            enabled: true,
            tenantId: tenantId,
            topMenuId: {
              [Op.in]: Sequelize.literal(`(SELECT id FROM menu_tenant_setting WHERE tenant_id = ${tenantId})`)
            }
          }
        },
        {
          model: CasinoMenuModel,
          as: 'casinoMenu',
          attributes: ['imageUrl'],
          where: {
            enabled: true,
            tenantId
          }
        }
      ]
    })

    const pageMenuIds = pageMenus.map((pageMenu) => pageMenu.id)

    const menuItems = await MenuItemModel.findAll({
      include: [
        {
          model: CasinoItemModel,
          where: {
            tenantId: tenantId
          }
        }
      ],
      where: {
        popular: true,
        active: true,
        pageMenuId: {
          [Op.in]: pageMenuIds
        }
      },
      order: [['updatedAt', 'DESC']],
      limit,
      offset
    })

    const pageMenuMap = Object.fromEntries(pageMenus.map(({ id, Page, casinoMenu }) => [id, { Page, casinoMenu }]))

    const menuItemsWithData = menuItems.map((menuItem) => ({
      ...menuItem.dataValues,
      pageMenuTitle: pageMenuMap[menuItem.pageMenuId]?.Page.title,
      casinoMenuImageUrl: pageMenuMap[menuItem.pageMenuId]?.casinoMenu.imageUrl
    }))

    return menuItemsWithData
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
