import { ApolloError } from 'apollo-server-express'
import { PAGINATION_CONSTANT } from '../../common/constants'
import <PERSON>rro<PERSON><PERSON><PERSON>H<PERSON><PERSON> from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import { isUserActive } from '../../lib/isUserActive'
import translate from '../../lib/languageTranslate'

/**
 * get combine games details based on same menu
 * @export
 * @class GameConsole
 * @extends {ServiceBase}
 */
export default class GameConsole extends ServiceBase {
  async run () {
    const {
      context: {
        tenant: { id: tenantId },
        req: { headers: { language, authorization } },
        databaseConnection: {
          Wallet: WalletModel,
          WebsiteContent: WebsiteContentModel,
        }
      },

      args: { pageId, topMenuId, categoryId },

    } = this
    const limit = this.args.limit || PAGINATION_CONSTANT.LIMIT
    const offset = this.args.offset || PAGINATION_CONSTANT.OFFSET
    try {

      let userId
      if (!userId && authorization) {
        const activeUser = await isUserActive(authorization, tenantId, this.context.databaseConnection, false)
        userId = activeUser.id
      }

      let currencyId;
      if (this.context.authUser) {
        const wallet = await WalletModel.findOne({ where: { ownerId: this.context.authUser.id, ownerType: 'User' }, attributes: ['currencyId'], raw: true })
        currencyId = wallet.currencyId
      }

      const result = await sequelize.query(`
        SELECT cc.id::INT AS id, cc.title::TEXT AS name, cc.top_menu_id::INT,
               cm.image_url::TEXT AS icon_url, cc.ordering::INT,
               COALESCE(
                json_agg(
                  jsonb_build_object(
                    'heading', wc.heading,
                    'content', wc.content
                  )
                ) FILTER (WHERE wc.id IS NOT NULL), '[]'
               ) AS cms
        FROM custom_category cc
        JOIN casino_menus cm ON cc.menu_icon_id = cm.id
        LEFT JOIN website_content wc ON cc.top_menu_id = wc.casino_top_menu_id AND wc.casino_page_menu_id = cc.id AND wc.is_active = true
        WHERE cc.status = true AND cc.is_deleted = false
          ${tenantId ? 'AND cc.tenant_id = :tenant_id' : ''}
          ${topMenuId ? 'AND cc.top_menu_id = :top_menu_id' : ''}
          ${categoryId ? 'AND cc.id = :category_id' : ''}
        GROUP BY cc.id, cc.title, cc.top_menu_id, cm.image_url, cc.ordering
        ORDER BY cc.ordering ASC;
      `, {
        replacements: { tenant_id: tenantId,
          top_menu_id: topMenuId || null,
          category_id: categoryId || null
         },
        type: sequelize.QueryTypes.SELECT,
        useMaster: false
      });

      if (!result?.length) return { id: 0, title: 'All', cms: [], menus: [] }

      const categoryIds = result.map(item => item.id);

      const rankedGames = await sequelize.query(`
        SELECT ccg.uuid,
         COALESCE(CASE WHEN ci.is_image_modified THEN ci.image ELSE ci2.image END, ci.image) AS image,
         ci.featured, ci.has_lobby, ccg.page_id, p.title, ccg.provider_id, ccg.ordering,
         ci.is_image_modified, mt.name AS name, cp.name AS provider_name, ccg.category_id
  FROM custom_category_games ccg
  INNER JOIN casino_items ci ON ccg.uuid = ci.uuid AND ccg.tenant_id = ci.tenant_id and  ccg.provider_id = NULLIF(ci.provider, '')::INT
  LEFT JOIN casino_items ci2 ON ccg.uuid = ci2.uuid AND ci2.tenant_id = 0 AND NULLIF(ci2.provider, '')::INT = ccg.provider_id
  JOIN public.pages p ON p.id = ccg.page_id
  JOIN aggregator a ON a.page_id = p.id AND a.status = true
  JOIN menu_tenant_setting mtg ON p.top_menu_id = mtg.id
  JOIN menu_master mm ON mtg.menu_id = mm.id
  JOIN menu_items mt ON ci.id = mt.casino_item_id
  JOIN page_menus pm ON mt.page_menu_id = pm.id
  JOIN casino_menus cmi ON pm.casino_menu_id = cmi.id
  JOIN casino_providers cp ON ccg.provider_id = cp.id
  WHERE ccg.is_deleted = false AND ci.active = true AND p.enabled = true
    AND p.tenant_id = ccg.tenant_id AND mtg.status = true
    AND mm.active = true AND mt.active = true
    AND (cmi.tenant_id = ccg.tenant_id OR cmi.tenant_id = 0)
          AND cmi.enabled = true AND ccg.category_id IN (:category_ids)
          ${pageId ? 'AND ccg.page_id = :page_id' : currencyId ?`AND (:currencyId = ANY(p.allowed_currencies) OR p.allowed_currencies = '{}'::INTEGER[])`:''}
          ${tenantId ? 'AND ccg.tenant_id = :tenant_id' : ''}
          ${topMenuId ? 'AND p.top_menu_id = :top_menu_id' : ''}
      `, {
        replacements: {
          tenant_id: tenantId,
          category_ids: categoryIds,
          page_id: pageId || null,
          top_menu_id: topMenuId || null,
          currencyId: currencyId || null
        },
        type: sequelize.QueryTypes.SELECT,
        useMaster: false
      });

      const gameMap = new Map();

      rankedGames.forEach(game => {
        const { category_id, page_id, uuid } = game;
        if (!gameMap.has(category_id)) gameMap.set(category_id, new Map());
        if (!gameMap.get(category_id).has(page_id)) gameMap.get(category_id).set(page_id, new Map());
        gameMap.get(category_id).get(page_id).set(uuid, game);
      });

      const finalGames = Array.from(gameMap.values())
        .flatMap(pageMap => Array.from(pageMap.values()))
        .flatMap(uuidMap => Array.from(uuidMap.values()))
        .sort((a, b) => a.ordering - b.ordering);

      let finalResult = result.reduce((acc, item) => {
        const games = finalGames.filter(game => game.category_id == item.id);
        if (games.length) {
          item.topMenuId = item.top_menu_id;
          item.totalgamecount = games.length;
          item.games = games.slice(offset, offset + limit);
          if (item.games.length) acc.push(item);
        }
        return acc;
      }, []);
      let favoriteGames = []
      if (userId) {
        favoriteGames = await sequelize.models.FavoriteGames.findAll({
          where: { userId, tenantId },
          attributes: ['gameId', 'casinoProviderId']
        })
      }

      const favoriteMap = new Map()
      favoriteGames.forEach(fav => {
        favoriteMap.set(`${fav.gameId}_${fav.casinoProviderId}`, true)
      })

      finalResult.forEach((item) => {
        item.topMenuId = item.top_menu_id
        if (item.games === null) {
          item.games = []
        } else if (Array.isArray(item.games) && userId) {
          item.games = item.games.map(game => {
            return {
              ...game,
              isFavorite: favoriteMap.has(`${game.uuid}_${game.provider_id}`) || false
            }
          })
        }
      })

      if (finalResult.length === 0) return { id: 0, title: 'All', cms: [], menus: [] }
      let websiteContent = []
      if (categoryId && pageId) {
        websiteContent = await WebsiteContentModel.findAll({
          attributes: ['content', 'id', 'casinoTopMenuId', 'heading'],
          where: {
            tenantId,
            casinoPageMenuId: categoryId,
            casinoPageId: pageId,
            isActive: true
          },
          raw: true
        })
      }

      return { id: 0, title: 'All', cms: websiteContent || [], menus: finalResult }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId });
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
