import { ApolloError } from 'apollo-server-express'
import <PERSON>rrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import { isUserActive } from '../../lib/isUserActive'
import translate from '../../lib/languageTranslate'

/**
 * Get popular games details (console style, similar to GameConsole)
 * @export
 * @class PopularGameConsole
 * @extends {ServiceBase}
 */
export default class PopularGameConsole extends ServiceBase {
  async run () {
    const {
      context: {
        tenant: { id: tenantId },
        req: { headers: { language, authorization } },
        databaseConnection: {
          Wallet: WalletModel
        }
      },
      args: { pageId, categoryId },
    } = this

   try {
       let userId
       if (!userId && authorization) {
         const activeUser = await isUserActive(authorization, tenantId, this.context.databaseConnection, false)
         userId = activeUser.id
       }

       let currencyId;
       if (this.context.authUser) {
         const wallet = await WalletModel.findOne({ where: { ownerId: this.context.authUser.id, ownerType: 'User' }, attributes: ['currencyId'], raw: true })
         currencyId = wallet.currencyId
       }

       let result = await sequelize.query(`
              SELECT
          PopularGames.id::INT AS id,
          PopularGames.slider_title::TEXT AS name,
          PopularGames.is_primary,
          PopularGames.ordering::INT,
          array_agg(popular_games_details.uuid) AS uuids
      FROM public.popular_games AS PopularGames
      JOIN public.popular_games_details
          ON PopularGames.id = popular_games_details.popular_game_id
      WHERE PopularGames.status = true

                ${tenantId ? 'AND PopularGames.tenant_id = :tenant_id' : ''}
                ${categoryId ? 'AND PopularGames.id = :category_id' : ''}
                GROUP BY
          PopularGames.id,
          PopularGames.slider_title,
          PopularGames.is_primary,
          PopularGames.ordering;
       `, {
         replacements: { tenant_id: tenantId,
           category_id: categoryId || null
          },
         type: sequelize.QueryTypes.SELECT,
         useMaster: false,
         raw: true
       });

       if (!result?.length) return {
         id: 0,
         title: 'All',
         menus: [] // Return empty array instead of null
       }
        result = result.sort((a, b) => a.ordering - b.ordering);

       const categoryUUID = result.map(item => item.uuids).flat();
       const categoryIds = result.map(item => item.id);

       const rankedGames = await sequelize.query(`
                  SELECT
          ccg.uuid,
          p.top_menu_id,
          COALESCE(CASE WHEN ci.is_image_modified THEN ci.image ELSE ci2.image END, ci.image) AS image,
          ci.featured,
          ci.has_lobby,
          ccg.page_id,
          p.title,
          ccg.provider,
          ccg.ordering,
          ci.is_image_modified,
          mt.name,
          cp.name AS provider_name,
          ccg.provider AS provider_id,
          ccg.popular_game_id
   FROM popular_games_details ccg
   INNER JOIN casino_items ci ON ccg.uuid = ci.uuid AND ccg.tenant_id = ci.tenant_id and  ccg.provider = NULLIF(ci.provider, '')::INT
   LEFT JOIN casino_items ci2 ON ccg.uuid = ci2.uuid AND ci2.tenant_id = 0 AND NULLIF(ci2.provider, '')::INT = ccg.provider
    join custom_category_games on custom_category_games.provider_id = ccg.provider and custom_category_games.uuid = ccg.uuid and custom_category_games.tenant_id = ccg.tenant_id
       join custom_category on custom_category_games.category_id = custom_category.id
   JOIN public.pages p ON p.id = ccg.page_id
   JOIN aggregator a ON a.page_id = p.id AND a.status = true
   JOIN menu_tenant_setting mtg ON p.top_menu_id = mtg.id
   JOIN menu_master mm ON mtg.menu_id = mm.id
   JOIN menu_items mt ON ci.id = mt.casino_item_id
   JOIN page_menus pm ON mt.page_menu_id = pm.id
   JOIN casino_menus cmi ON pm.casino_menu_id = cmi.id
   JOIN casino_providers cp ON ccg.provider = cp.id
   WHERE ci.active = true AND p.enabled = true and a.status = true and
   custom_category_games.uuid in (:category_UUID) and custom_category_games.tenant_id = :tenant_id
      AND custom_category_games.is_deleted = false and custom_category.is_deleted = false and custom_category.status = true
        AND ccg.popular_game_id IN (:category_ids)
     AND p.tenant_id = ccg.tenant_id AND mtg.status = true
     AND mm.active = true AND mt.active = true
     AND (cmi.tenant_id = ccg.tenant_id OR cmi.tenant_id = 0)
           AND cmi.enabled = true
           ${pageId ? 'AND ccg.page_id = :page_id' : currencyId ?`AND (:currencyId = ANY(p.allowed_currencies) OR p.allowed_currencies = '{}'::INTEGER[])`:''}
       `, {
         replacements: {
           tenant_id: tenantId,
           category_UUID: categoryUUID,
           category_ids: categoryIds,
           page_id: pageId || null,
           currencyId: currencyId || null
         },
         type: sequelize.QueryTypes.SELECT,
         useMaster: false,
         raw: true
       });

      //  if  same uuid and provider_id  then take the one with highest ordering from custom_category_games
      const uniqueGames = rankedGames.reduce((acc, game) => {
         const key = `${game.uuid}_${game.provider_id}_${game.popular_game_id}`;
         if (!acc.has(key) || game.ordering > acc.get(key).ordering) {
           acc.set(key, game);
         }
         return acc;
       }, new Map());
       let rankedGamesFiltered = Array.from(uniqueGames.values());
       const gameMap = new Map();

       rankedGamesFiltered.forEach(game => {
         const { popular_game_id, page_id, uuid } = game;
         if (!gameMap.has(popular_game_id)) gameMap.set(popular_game_id, new Map());
         if (!gameMap.get(popular_game_id).has(page_id)) gameMap.get(popular_game_id).set(page_id, new Map());
         gameMap.get(popular_game_id).get(page_id).set(uuid, game);
       });

       // Safely extract values from nested maps
       const finalGames = [];

       // Iterate through the gameMap
       gameMap.forEach((pageMap, _popularGameId) => {
         // Iterate through each page map
         pageMap.forEach((uuidMap, _pageId) => {
           // Iterate through each UUID map
           uuidMap.forEach((game, _uuid) => {
             finalGames.push(game);
           });
         });
       });

       // Sort the games by ordering
       finalGames.sort((a, b) => a.ordering - b.ordering);

       let finalResult = result.reduce((acc, item) => {
         const games = finalGames.filter(game => game.popular_game_id == item.id);
         if (games.length) {
           // Ensure topMenuId is set correctly
           item.topMenuId = item.top_menu_id || null;
           item.totalgamecount = games.length;
           item.games = games
           if (item.games.length) acc.push(item);
         }
         return acc;
       }, []);
       let favoriteGames = []
       if (userId) {
         favoriteGames = await sequelize.models.FavoriteGames.findAll({
           where: { userId, tenantId },
           attributes: ['gameId', 'casinoProviderId']
         })
       }

       const favoriteMap = new Map()
       favoriteGames.forEach(fav => {
         favoriteMap.set(`${fav.gameId}_${fav.casinoProviderId}`, true)
       })

       // Ensure games array is valid and add favorite status
       finalResult.forEach((item) => {
         // If games is null or undefined, initialize as empty array
         if (!item.games) {
           item.games = [];
         }
         // If we have games and a userId, add favorite status
         else if (Array.isArray(item.games) && userId) {
           item.games = item.games.map(game => {
             return {
               ...game,
               isFavorite: favoriteMap.has(`${game.uuid}_${game.provider_id}`) || false
             };
           });
         }
       });

       // Return the structure expected by the GraphQL schema
       return {
         id: 0,
         title: 'All',
         menus: finalResult.length > 0 ? finalResult : [] // Ensure menus is never null but an empty array
       }

     } catch (error) {
       await ErrorLogHelper.logError(error, this.context, { tenantId });
       throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
     }
  }
}
