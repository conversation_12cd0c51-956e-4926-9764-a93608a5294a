import { ApolloError } from 'apollo-server-express'
import { PAGINATION_CONSTANT } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
const { fn, col, Op, literal } = require('sequelize')
const Sequelize = require('sequelize')

/**
 * get combine games details based on same menu
 * @export
 * @class CombineGames
 * @extends {ServiceBase}
 */
export default class CombineGames extends ServiceBase {
  async run () {
    const {
      context: { databaseConnection: { CombinedMenuGames: CombinedMenuGamesModel }, tenant: { id: tenantId }, req: { headers: { language } } },
      args: {
        pageMenuName,
        topMenuId
      }
    } = this

    const limit = this.args.limit || PAGINATION_CONSTANT.LIMIT
    const offset = this.args.offset || PAGINATION_CONSTANT.OFFSET

    try {
      const result = await CombinedMenuGamesModel.findAll({
        attributes: [
          'id',
          'name',
          'casinoMenu',
          'menuItems',
          'totalMenuItem',
          [literal('\'[]\'::json'), 'cms']
        ],
        where: {
          topMenuId,
          tenantId,
          status: true,
          ...(pageMenuName && {
            [Op.and]: [
              Sequelize.where(
                fn('lower', col('name')),
                fn('lower', pageMenuName)
              )
            ]
          })
        },
        order: [['ordering', 'ASC']],
        raw: true
      })

      const paginatedResults = result.map(item => {
        const { menuItems } = item
        const paginatedMenuItems = menuItems.slice(offset, offset + limit)
        return {
          ...item,
          menuItems: paginatedMenuItems
        }
      })

      return { id: 0, title: 'All', cms: [], menus: paginatedResults }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
