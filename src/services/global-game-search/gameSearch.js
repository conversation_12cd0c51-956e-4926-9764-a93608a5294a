import { ApolloError } from 'apollo-server-express'
import { Op } from 'sequelize'
import { PAGINATION_CONSTANT } from '../../common/constants'
import Error<PERSON>ogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
/**
 *
 * get games details based on search keywords
 * @export
 * @class GameSearcher
 * @extends {ServiceBase}
 */
export default class GameSearcher extends ServiceBase {
  async run () {
    const {
      MenuItem: MenuItemModel,
      CasinoItem: CasinoItemModel,
      PageMenu: PageMenuModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel
    } = this.context.databaseConnection

    const limit = this.args.limit ? this.args.limit : PAGINATION_CONSTANT.LIMIT
    const offset = this.args.offset ? this.args.offset : PAGINATION_CONSTANT.OFFSET
    const tenantId = this.context.tenant.id

    const {
      context: {
        req: { headers: { language } }
      }
    } = this

    try {
    const pageMenus = await PageMenuModel.findAll({
      attributes: ['id'],
      include: [
        {
          model: PageModel,
          attributes: ['title'],
          where: {
            ...(this.args.pageId ? { id: this.args.pageId } : {}),
            enabled: true,
            tenantId: tenantId
          }
        },
        {
          model: CasinoMenuModel,
          as: 'casinoMenu',
          attributes: ['imageUrl'],
          where: {
            enabled: true,
            tenantId
          }
        }
      ],
      where: {
        ...(this.args.pageMenuId ? { id: this.args.pageMenuId } : {})
      }
    })

    const pageMenuIds = pageMenus.map((pageMenu) => pageMenu.id)

    const { rows: menuItems } = await MenuItemModel.findAndCountAll({
      include: [
        {
          model: CasinoItemModel,
          where: {
            tenantId: tenantId
          }
        }
      ],
      where: {
        name: {
          [Op.iLike]: `%${this.args.searchKey}%`
        },
        active: true,
        pageMenuId: {
          [Op.in]: pageMenuIds
        }
      },
      order: [['featured', 'DESC'], ['order', 'ASC'], ['id', 'ASC']],
      limit,
      offset
    })

    // todo - need to change the concept
    const seenCasinoItemIds = new Set()
    const uniqueMenuItems = menuItems.filter(item => {
      const casinoItemId = item.casinoItemId
      if (!seenCasinoItemIds.has(casinoItemId)) {
        seenCasinoItemIds.add(casinoItemId)
        return true
      }
      return false
    })

    const pageMenuMap = Object.fromEntries(pageMenus.map(({ id, Page, casinoMenu }) => [id, { Page, casinoMenu }]))

    const menuItemsWithData = uniqueMenuItems.map((menuItem) => ({
      ...menuItem.dataValues,
      pageMenuTitle: pageMenuMap[menuItem.pageMenuId]?.Page.title,
      casinoMenuImageUrl: pageMenuMap[menuItem.pageMenuId]?.casinoMenu.imageUrl
    }))

    const total = uniqueMenuItems.length
    return { total, data: menuItemsWithData }
  } catch (error) {
    await ErrorLogHelper.logError(error, this.context, { tenantId })
    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
  }
  }
}
