import { ApolloError } from 'apollo-server-express'
import { QueryTypes } from 'sequelize'
import { PAGINATION_CONSTANT } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import ServiceBase from '../../common/serviceBase'
import { sequelize } from '../../db/models'
import { isUserActive } from '../../lib/isUserActive'
import translate from '../../lib/languageTranslate'

/**
 * Fetch games based on custom category games and other criteria
 * @export
 * @class GameConsoleSearcher
 * @extends {ServiceBase}
 */
export default class GameConsoleSearcher extends ServiceBase {
  async run () {
    const {
      req: { headers: { language, authorization } },
      databaseConnection: {
        Wallet: WalletModel,
      }
    } = this.context
    const { searchKey, limit, offset, topMenuId, pageId } = this.args

    const paginationLimit = limit || PAGINATION_CONSTANT.LIMIT
    const paginationOffset = offset || PAGINATION_CONSTANT.OFFSET

    try {
      let userId
      if (authorization) {
        const activeUser = await isUserActive(authorization, this.context.tenant.id, this.context.databaseConnection, false)
        userId = activeUser.id
      }

      let currencyId;
      if (this.context.authUser) {
        const wallet = await WalletModel.findOne({ where: { ownerId: this.context.authUser.id, ownerType: 'User' }, attributes: ['currencyId'], raw: true })
        currencyId = wallet.currencyId
      }

      const query = `
        SELECT
            subquery.uuid,
            subquery.image,
            subquery.featured,
            subquery.has_lobby,
            subquery.page_id,
            subquery.title,
            subquery.provider_id,
            subquery.ordering,
            subquery.is_image_modified,
            subquery.provider_name,
            subquery.name,
            subquery.top_menu_id
        FROM (
            SELECT
                ccg.uuid,
                CASE
                    WHEN ci.is_image_modified THEN ci.image
                    WHEN ci2.image IS NOT NULL THEN ci2.image
                    ELSE ci.image
                END AS image,
                 ci.featured,
                ci.has_lobby,
                ccg.page_id,
                p.title,
                ccg.provider_id,
                ccg.ordering,
                ci.is_image_modified,
                p.top_menu_id,
                mt.name as name,
            cp.name as provider_name,
                ROW_NUMBER() OVER (PARTITION BY ccg.uuid ORDER BY ccg.ordering) AS row_num
            FROM custom_category_games ccg
             join custom_category cc on  ccg.category_id = cc.id
              INNER JOIN casino_items ci ON ccg.uuid = ci.uuid AND ccg.tenant_id = ci.tenant_id and ccg.provider_id = NULLIF(ci.provider, '')::INT
  LEFT JOIN casino_items ci2 ON ccg.uuid = ci2.uuid AND ci2.tenant_id = 0 AND NULLIF(ci2.provider, '')::INT = ccg.provider_id
            JOIN public.pages p
                ON p.id = ccg.page_id
            JOIN aggregator a ON a.page_id = p.id AND a.status = true
            JOIN menu_tenant_setting mtg
                ON p.top_menu_id = mtg.id
            JOIN menu_master mm
                ON mtg.menu_id = mm.id
            JOIN menu_items mt
                ON ci.id = mt.casino_item_id
            JOIN page_menus pm
                ON mt.page_menu_id = pm.id
            JOIN casino_menus cmi
                ON pm.casino_menu_id = cmi.id
            JOIN casino_providers cp
                        on ccg.provider_id = cp.id
          WHERE cc.status = true and  cc.is_deleted = false and a.status = true and
                ccg.is_deleted = false
                AND ci.active = true
                AND p.enabled = true
                AND p.tenant_id = ccg.tenant_id
                AND mtg.status = true
                AND mm.active = true
                AND mt.active = true
                AND (cmi.tenant_id = ccg.tenant_id OR cmi.tenant_id = 0)
                AND cmi.enabled = true
                AND p.tenant_id = :tenantId
                AND mt.name ILIKE :searchKey
                ${topMenuId ? 'AND p.top_menu_id = :topMenuId' : ''}
                ${pageId ? 'AND p.id = :pageId' : currencyId ?`AND (:currencyId = ANY(p.allowed_currencies) OR p.allowed_currencies = '{}'::INTEGER[])`:''}
        ) subquery
        WHERE subquery.row_num = 1
        ORDER BY subquery.ordering ASC
        LIMIT :limit OFFSET :offset
      `

      const games = await sequelize.query(query, {
        type: QueryTypes.SELECT,
        replacements: {
          searchKey: `%${searchKey}%`,
          limit: paginationLimit,
          offset: paginationOffset,
          topMenuId,
          pageId,
          tenantId: this.context.tenant.id,
          currencyId: currencyId || null
        },
        useMaster: false
      })

      let favoriteGames = []
      if (userId) {
        favoriteGames = await sequelize.models.FavoriteGames.findAll({
          where: { userId, tenantId: this.context.tenant.id },
          attributes: ['gameId', 'casinoProviderId']
        })
      }

      const favoriteMap = new Map()
      favoriteGames.forEach(fav => {
        favoriteMap.set(`${fav.gameId}_${fav.casinoProviderId}`, true)
      })

      const gamesWithFavorites = games.map(game => {
        return {
          ...game,
          isFavorite: userId ? (favoriteMap.has(`${game.uuid}_${game.provider_id}`) || false) : false
        }
      })

      return { games: gamesWithFavorites }
    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id })
      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500)
    }
  }
}
