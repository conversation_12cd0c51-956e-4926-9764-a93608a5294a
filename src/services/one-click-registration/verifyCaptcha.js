import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express';
import crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { CAPTCHA_VERIFICATION_STATUS } from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import verifyRecaptcha from '../../common/verifyRecaptcha';
import config from '../../config/app';
import { encrypt } from '../../lib/encryption';
import translate from '../../lib/languageTranslate';

/**
 * Provides service for the VerifyCaptcha functionality
 * @export
 * @class VerifyCaptcha
 * @extends {ServiceBase}
 */

const constraints = {
  uuid: {
    type: 'string',
    presence: { message: 'UUID is required' }
  },
  captchaText: {
    type: 'string',
    presence: { message: 'Captcha text is required' }
  },
  isGameLaunch: {
    type: 'boolean'
  }
}

export default class VerifyCaptcha extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        CaptchaVerification: CaptchaVerificationModel,
        TenantCredential: TenantCredentialModel,
        TenantThemeSetting: TenantThemeSettingModel
      },
      tenant: Tenant
    } = this.context

    try {
      const providers = await TenantThemeSettingModel.findOne({
        attributes: ['googleRecaptchaKeys'],
        where: {
          tenantId: Tenant.id
        },
        raw: true
      });

      const secretKey = providers.googleRecaptchaKeys;
      const { uuid, captchaText: base64Text, isGameLaunch = false } = this.args;
      if (!isGameLaunch && (!grcptv3 || !secretKey || !await verifyRecaptcha(this.context, grcptv3))) {
        throw new AuthenticationError(translate('INVALID_SIGNATURE', language));
      }

      const CAPTCHA_EXPIRY_MS = 45 * 1000; // 45 seconds

      // Decode base64 captchaText
      let decodedCaptchaText = Buffer.from(base64Text, 'base64').toString('ascii');
      const encryptionKey = crypto.createHash('sha256').update(config.get('captcha_verification')).digest(); // Must be 32 bytes
      const hashedCaptchaText = encrypt(decodedCaptchaText, encryptionKey);

      // Fetch DB record
      const captchaRecord = await CaptchaVerificationModel.findOne({
        attributes: ['id', 'captchaText', 'createdAt'],
        where: {
          uuidToken: uuid,
          captchaText: hashedCaptchaText,
          status: CAPTCHA_VERIFICATION_STATUS.UNVERIFIED
        }
      });

      if (!captchaRecord) throw new UserInputError(translate('INVALID_OR_ALREADY_VERIFIED_UUID', language), 400);

      // Expiry check
      const createdAt = new Date(captchaRecord.createdAt).getTime();
      const now = Date.now();

      if (now - createdAt > CAPTCHA_EXPIRY_MS) throw new ApolloError(translate('CAPTCHA_EXPIRED', language), 410);

      // Mark as verified
      await CaptchaVerificationModel.update(
        { status: CAPTCHA_VERIFICATION_STATUS.VERIFIED, updatedAt: new Date() },
        { where: { uuidToken: uuid } }
      );

      // Fetch updated record (or use returning: true if supported)
      const updatedCaptcha = await CaptchaVerificationModel.findOne({
        attributes: ['id', 'uuidToken', 'status', 'updatedAt'],
        where: { uuidToken: uuid },
        raw: true,
        useMaster: true
      });

      // Fetch JWT secret from TenantCredentialModel
      const credentials = await TenantCredentialModel.findOne({
        attributes: ['value'],
        where: {
          key: 'APP_JWT_SECRET_KEY',
          tenantId: Tenant.id
        },
        raw: true
      });

      const jwtSecret = credentials?.value;
      if (!jwtSecret) {
        throw new ApolloError(translate('JWT_SECRET_NOT_CONFIGURED', language), 410);
      }

      // Generate JWT token for the captcha verification info
      const tokenPayload = {
        id: updatedCaptcha.id,
        uuidToken: updatedCaptcha.uuidToken,
        status: updatedCaptcha.status,
        updatedAt: updatedCaptcha.updatedAt
      };

      const token = jwt.sign(tokenPayload, jwtSecret, { expiresIn: '1m' }); // 1 minutes or adjust as needed

      return {
        verified: true,
        captchaToken: token
      };

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id });

      // Re-throw known ApolloError, otherwise throw a generic internal error
      if (error instanceof ApolloError) {
        throw error;
      }

      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
