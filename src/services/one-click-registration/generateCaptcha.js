import { ApolloError, AuthenticationError } from 'apollo-server-express';
import crypto from 'crypto';
import svgCaptcha from 'svg-captcha';
import { CAPTCHA_VERIFICATION_STATUS, SVG_CAPTCHA_OPTIONS } from '../../common/constants';
import <PERSON>rror<PERSON>ogHelper from '../../common/errorLog';
import ServiceBase from '../../common/serviceBase';
import verifyRecaptcha from '../../common/verifyRecaptcha';
import config from '../../config/app';
import { encrypt } from '../../lib/encryption';
import translate from '../../lib/languageTranslate';

/**
 * Provides service for the GenerateCaptcha functionality
 * @export
 * @class GenerateCaptcha
 * @extends {ServiceBase}
 */

export default class GenerateCaptcha extends ServiceBase {
  async run () {
    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        CaptchaVerification: CaptchaVerificationModel,
        TenantThemeSetting: TenantThemeSettingModel
      },
      tenant: Tenant
    } = this.context

    try {
      const providers = await TenantThemeSettingModel.findOne({
        attributes: ['googleRecaptchaKeys'],
        where: {
          tenantId: Tenant.id
        },
        raw: true
      });

      const secretKey = providers.googleRecaptchaKeys;

      if (!grcptv3 || !secretKey || !await verifyRecaptcha(this.context, grcptv3)) {
        throw new AuthenticationError(translate('INVALID_SIGNATURE', language));
      }

      // Generate captcha
      const captcha = svgCaptcha.create(SVG_CAPTCHA_OPTIONS);
      const encryptionKey = crypto.createHash('sha256').update(config.get('captcha_verification')).digest(); // Must be 32 bytes
      const hashedCaptchaText = encrypt(captcha.text, encryptionKey);

      // Save to DB
      const newCaptcha = await CaptchaVerificationModel.create({
        captchaText: hashedCaptchaText,
        status: CAPTCHA_VERIFICATION_STATUS.UNVERIFIED,
      });

      return {
        uuid: newCaptcha.uuidToken,
        captcha: Buffer.from(captcha.data).toString('base64') // send base64 SVG image
      };

    } catch (error) {
      await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id });

      // Re-throw known ApolloError, otherwise throw a generic internal error
      if (error instanceof ApolloError) {
        throw error;
      }

      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
