import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express';
import * as jwt from 'jsonwebtoken';
import md5 from 'md5';
import { v4 as uuidv4 } from 'uuid';
import {
  AFFILIATE,
  ALANBASE_EVENT_TYPES, ALLOWED_PERMISSIONS, BONUS_TYPES, CAPTCHA_VERIFICATION_STATUS,
  QUEUE_WORKER_CONSTANT, REFERRAL_EVENT, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL, VIP_CODE,
  USER_CREATION_TYPE
} from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import mobileLogin from '../../common/mobileLogin';
import ServiceBase from '../../common/serviceBase';
import verifyPromoCode from '../../common/verifyPromoCode';
import verifyRecaptcha from '../../common/verifyRecaptcha';
import verifyReferralCode from '../../common/verifyReferralCode';
import config from '../../config/app';
import generateSecurePassword from '../../lib/generateSecurePassword';
import translate from '../../lib/languageTranslate';

/**
 * Provides service for the OneClickRegistration functionality
 * @export
 * @class OneClickRegistration
 * @extends {ServiceBase}
 */

const constraints = {
  captchaToken: {
    type: 'string',
    presence: { message: 'Captcha token is required' }
  },
  currencyId: {
    type: 'string',
    presence: { message: 'Currency is required' }
  },
  deviceId: {
    type: 'string'
  },
  deviceType: {
    type: 'string'
  },
  deviceModel: {
    type: 'string'
  },
  ip: {
    type: 'string'
  }
}

export default class OneClickRegistration extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      req: { headers: { language, grcptv3 } },
      databaseConnection: {
        AdminUser: AdminUserModel,
        AdminRole: AdminRoleModel,
        CaptchaVerification: CaptchaVerificationModel,
        TenantThemeSetting: TenantThemeSettingModel,
        QueueLog: QueueLogModel,
        PlayerCategory: PlayerCategoryModel,
        PlayerCategoryLevel: PlayerCategoryLevelModel,
        User: UserModel,
        Wallet: WalletModel,
        UserReferralCode: UserReferralCodeModel,
        TenantCredential: TenantCredentialModel,
        TenantConfiguration: TenantConfigurationsModel,
        UsersAffiliate: UsersAffiliateModel,
        sequelize
      },
      tenant: Tenant,
    } = this.context;

    const tenantTheme = await TenantThemeSettingModel.findOne({
      attributes: ['userLoginType', 'smsGateway', 'allowedModules', 'googleRecaptchaKeys', 'emailGateway'],
      where: {
        tenantId: Tenant.id
      },
      raw: true
    });

    const secretKey = tenantTheme.googleRecaptchaKeys;

    if (!grcptv3 || !secretKey || !await verifyRecaptcha(this.context, grcptv3)) {
      throw new AuthenticationError(translate('INVALID_SIGNATURE', language));
    }

    const { captchaToken } = this.args;

    // Step 1: Get the JWT secret for the tenant
    const credentials = await TenantCredentialModel.findOne({
      attributes: ['value'],
      where: {
        key: 'APP_JWT_SECRET_KEY',
        tenantId: Tenant.id
      },
      raw: true
    });

    const jwtSecret = credentials?.value;
    if (!jwtSecret) {
      throw new ApolloError(translate('JWT_SECRET_NOT_CONFIGURED', language), 410);
    }

    // Step 2: Decode and verify token
    let decoded;
    try {
      decoded = jwt.verify(captchaToken, jwtSecret);
    } catch (err) {
      throw new UserInputError(translate('INVALID_OR_EXPIRED_CAPTCHA_TOKEN', language), 403);
    }

    const { uuidToken, status } = decoded;

    if (!uuidToken || status !== CAPTCHA_VERIFICATION_STATUS.VERIFIED) {
      throw new UserInputError(translate('INVALID_CAPTCHA_TOKEN_DATA', language), 403);
    }

    // Step 3: Check if captcha is verified
    const captchaRecord = await CaptchaVerificationModel.findOne({
      where: {
        uuidToken,
        status: CAPTCHA_VERIFICATION_STATUS.VERIFIED
      },
      useMaster: true
    });

    if (!captchaRecord) {
      throw new UserInputError(translate('VERIFY_CAPTCHA_FIRST', language), 403);
    }

    const currencyAllowedCurrencies = await TenantConfigurationsModel.findOne({
      attributes: ['allowedCurrencies'],
      where: { tenantId: Tenant.id },
      raw: true
    });

    const allowedCurrencyList = currencyAllowedCurrencies?.allowedCurrencies?.split(',').map((currencyId) => currencyId.trim()) || [];

    if (!allowedCurrencyList.includes(this.args.currencyId)) {
      throw new UserInputError(translate('CURRENCY_NOT_FOUND', language), 403);
    }

    const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
    if (profileVerifiedPermissionCheck) {
      this.args.profileVerified = false
    }

    this.context.sequelizeTransaction = await sequelize.transaction();

    try {
      // Step 4: Mark captcha as used
      await CaptchaVerificationModel.update(
        { status: CAPTCHA_VERIFICATION_STATUS.USED },
        { where: { uuidToken } },
        { transaction: this.context.sequelizeTransaction }
      );

      let parent = await AdminUserModel.findAll({
        attributes: ['id', 'kycRegulated', 'timezone'],
        where: {
          tenantId: Tenant.id
        },
        order: ['createdAt'],
        include: {
          model: AdminRoleModel,
          attributes: [],
          where: {
            name: 'owner'
          }
        },
        raw: true,
        nest: true
      })

      if (parent.length === 0) throw new UserInputError(translate('ADMIN_NOT_FOUND', language));

      parent = parent[0]

      // Step 2: Generate credentials
      const password = generateSecurePassword(); // default length is 8
      const userEntity = {
        userName: null,
        password,
        parentId: parent.id,
        dateOfBirth: null,
        parentType: 'AdminUser',
        tenantId: Tenant.id,
        kycDone: !parent.kycRegulated,
        encryptedPassword: md5(password),
        affiliatedData: this.args?.affiliatedData || null,
        creationType: USER_CREATION_TYPE.ONE_CLICK,
        profileVerified: this.args?.profileVerified,
      }

      // The base64 encoded affiliateToken string
      const { affiliateToken } = this.args
      if (affiliateToken) {
        const agentData = await AdminUserModel.findOne({
          attributes: ['id', 'affiliateStatus'],
          where: { affiliateToken }
        })

        if (!agentData) throw new UserInputError(translate('NO_AFFILIATE_FOUND', language));

        if (agentData.affiliateStatus) userEntity.parentId = agentData.id
      }

      if (this.args.vipKey) {
        const vipData = VIP_CODE[this.args?.vipKey] ? VIP_CODE[this.args?.vipKey] : null
        userEntity.vipLevel = vipData ? vipData?.vipLevel : 0
      }

      const hasPlayerCategory = tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PLAYER_CATEGORIZATION)
      let subcategoryLevelData = null;

      if (hasPlayerCategory) {
        subcategoryLevelData = await PlayerCategoryLevelModel.findOne({
          include: [{
            model: PlayerCategoryModel,
            where: {
              status: true,
              tenantId: Tenant.id
            },
            attributes: []
          }],
          order: [[PlayerCategoryModel, 'id', 'ASC'], ['id', 'ASC']],
          attributes: ['id']
        })

        // If no category or subcategory is found, subcategoryLevelData will be null
        userEntity.playerCategoryLevel = subcategoryLevelData ? subcategoryLevelData?.id : null
      }

      const newUser = await UserModel.create(userEntity, { transaction: this.context.sequelizeTransaction });

      // Final userName generation using last 4 chars of uuidToken + userId
      const finalUserName = `u${Date.now().toString(36).slice(-3)}${Math.random().toString(36).slice(2, 4)}${uuidToken.slice(-5)}${newUser.id}`;

      // Update the userName inside transaction
      await UserModel.update(
        { userName: finalUserName },
        { where: { id: newUser.id }, transaction: this.context.sequelizeTransaction }
      );

      // Set referral code using new userName
      let referralCode = (finalUserName).toUpperCase().substring(0, 4) + uuidv4().replace(/-/g, '').substring(0, 4).toUpperCase();

      await UserReferralCodeModel.create(
        { userId: newUser.id, tenantId: Tenant.id, referralCode },
        { transaction: this.context.sequelizeTransaction }
      );

      await WalletModel.create({
        ownerId: newUser.dataValues.id,
        ownerType: 'User',
        amount: 0,
        non_cash_amount: 0,
        oneTimeBonusAmount: 0,
        sportsFreebetAmount: 0,
        currencyId: this.args.currencyId
      },
        {
          transaction: this.context.sequelizeTransaction
        })

      const bulkData = []

      if (this.args.promoCode) {
        const isValidPromo = await verifyPromoCode(this.context, this.args.promoCode, this.args.currencyId)

        if (!isValidPromo) throw new UserInputError(translate('INVALID_PROMOCODE', language));

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.BONUS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{
            bonusType: BONUS_TYPES.PROMO_CODE,
            userId: newUser.id,
            promoCode: this.args.promoCode,
            tenantId: Tenant.id
          }]
        }
        bulkData.push(queueLogObject)
      }

      const joiningFlag = !!this.args.promoCode // If both the joining bonus and promo bonus are available, only the promo bonus will be awarded to the user.

      const responseObj = await mobileLogin(this.context, newUser.id, joiningFlag, this.args, bulkData)

      let token = responseObj.token
      let resToken = responseObj.resToken
      responseObj.user.referralCode = referralCode

      // Smartigo code
      const smartigoTenants = config.get('env') === 'production' ? SMARTIGO_TENANTS.PROD : SMARTIGO_TENANTS.STAGE
      if (smartigoTenants && smartigoTenants.includes(Tenant.id)) {
        const smartiGoObject = {
          type: QUEUE_WORKER_CONSTANT.SMARTICO_USER,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [newUser.id],
          tenantId: parseInt(Tenant.id)
        }
        bulkData.push(smartiGoObject)
      }

      // Alanbase entry
      const hasAlanBase = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ALANBASE)
      if (hasAlanBase && this.args.alanbaseClickId) {
        await UsersAffiliateModel.create({ userId: newUser.id, clickId: this.args?.alanbaseClickId, tenantId: Tenant?.id, affiliate : AFFILIATE.ALANBASE }, { transaction: this.context.sequelizeTransaction })
        const alanbaseObject = {
          type: ALANBASE_EVENT_TYPES.REGISTRATION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [newUser.id],
          tenantId: parseInt(Tenant.id)
        }
        bulkData.push(alanbaseObject)
      }

      // wynta entry
      const hasWynta = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WYNTA)
      if (hasWynta && this.args.wyntaClickId) {
        await UsersAffiliateModel.create({ userId: newUser.id, clickId: this.args?.wyntaClickId, tenantId: Tenant?.id, affiliate : AFFILIATE.WYNTA  }, { transaction: this.context.sequelizeTransaction })
      }

      // Referral code verification and Pushing to queue
      if (this.args.referralCode && tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REFERRAL_CODE)) {
        const referralDetails = await verifyReferralCode(this.context, newUser.id, this.args)

        if (!referralDetails.status) throw new UserInputError(translate('INVALID_REFERRAL_CODE', language));

        if (referralDetails.status && referralDetails?.event == REFERRAL_EVENT.SIGNUP) {
          const queueLogObject = {
            type: QUEUE_WORKER_CONSTANT.BONUS,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [{
              bonusType: BONUS_TYPES.REFERRAL_CODE,
              referralId: referralDetails.referral.id,
              referralSettingsId: referralDetails.referralSettingsId
            }]
          }
          bulkData.push(queueLogObject)
        }
      }

      // Add auto-activate bonus to queue if allowed.
      const isAutoActivateBonusModuleAlllowed = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.AUTO_ACTIVATE_BONUS);
      if (isAutoActivateBonusModuleAlllowed) {
        const autoActivateQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.BONUS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{
            bonusType: BONUS_TYPES.AUTO_BONUS_ACTIVATE,
            userId: newUser.id,
            skipKycCheck: true,
            timezone: parent?.timezone
          }]
        }
        bulkData.push(autoActivateQueueLogObject)
      }

      // Referal code creation for the user
      const createdRecords = await QueueLogModel.bulkCreate(bulkData, { transaction: this.context.sequelizeTransaction });

      // Commit the transaction to finalize all changes
      await this.context.sequelizeTransaction.commit();

      if (createdRecords) {
        try {
          createdRecords.forEach(record => {
            this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
              QueueLog: { queueLogId: record.id }
            })
          })
        } catch (error) {
          await ErrorLogHelper.logError(error, this.context, { tenantId: Tenant.id })
        }
      }

      responseObj.user.password = Buffer.from(password).toString('base64')

      if (!responseObj.user.profileVerified) {
        token = null
        resToken = null
      }

      return { token, user: responseObj.user, resToken };

    } catch (error) {
      await this.context.sequelizeTransaction.rollback()
      await ErrorLogHelper.logError(error, this.context, { tenantId: this.context.tenant.id });

      // Re-throw known ApolloError, otherwise throw a generic internal error
      if (error instanceof ApolloError) {
        throw error;
      }

      throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
    }
  }
}
