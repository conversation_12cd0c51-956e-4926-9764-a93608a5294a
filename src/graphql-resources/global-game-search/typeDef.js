import { gql } from 'apollo-server-express'

export const typeDef = gql`

  type CasinoItem {
    """
    contains casino item id for unique item identification in database
    """
    id: ID
    """
    contains casino item name
    """
    name: String
    """
    contains unique game id provided by <PERSON><PERSON><PERSON>
    """
    uuid: String
    """
    contains menu items image url
    """
    image: String
    """
    contains menu items type
    """
    itemType: String
    """
    contains menu item provider id
    """
    provider: String
    """
    contains menu item technology
    """
    technology: String
    """
    contains a boolean for showing wether the menu items has lobby or not
    """
    hasLobby: Boolean
    """
    contains a boolean for showing wether the menu items is mobile or not
    """
    isMobile: Boolean
    """
    contains a boolean for showing wether the menu items has free spins or not
    """
    hasFreeSpins: Boolean
  }

  """
  contains all the fields related to the searched games
  """
  type MenuItem {
    """
    contains menu item id for unique menu item identification in database
    """
    id: Int
    """
    contains menu item name
    """
    name: String
    """
    contains the order in which the menu item will be displayed
    """
    order: Int
    """
    contains a boolean for showing wether the menu items is active or not
    """
    active: Boolean
    """
    contains a boolean for showing wether the menu items is featured or not
    """
    featured: Boolean
    """
    contains a boolean for showing wether the menu items is popular or not
    """
    popular: Boolean
    """
    contains page menu title corresponding to menu item
    """
    pageMenuTitle: String
    """
    contains casino menu image url corresponding to menu item
    """
    casinoMenuImageUrl: String
    """
    contains fields related to casino items
    """
    CasinoItem: CasinoItem
    """
    indicates whether the game is marked as favorite by the authenticated user
    """
    isFavorite: Boolean
  }

  """
  contains all the fields related to the slider games
  """
  type slider {
  """
  id of the slider
  """
  id: Int

  """
  name of the slider
  """
  name: JSON
  """
  contains menu items count
  """
  is_primary: Boolean
  """
  contains fields related to menu items
  """
  games: [MenuItem]
  }

  """
  represents the input type of GameSearch query
  """
  input GameSearchInput {
    searchKey: String
    pageId: Int
    pageMenuId: Int
    limit: Int
    offset: Int
  }

  """
  represents the input type of CombineGames query
  """
  input CombineGamesInput {
    topMenuId: Int
    pageMenuName: String
    limit: Int
    offset: Int
  }

  type CasinoItems {
    """
    The ID of the casino item.
    """
    id: Int!
    """
    The UUID of the casino item.
    """
    uuid: String!
    """
    The image associated with the casino item.
    """
    image: String
    """
    Whether the casino item has a lobby.
    """
    hasLobby: Boolean
    """
    The name of the provider for the casino item.
    """
    providerName: String!
  }

  type MenuItems {
    """
    The ID of the menu item.
    """
    id: Int!
    """
    The name of the menu item.
    """
    name: String!
    """
    Whether the menu item is featured or not.
    """
    featured: Boolean
    """
    Page menu name of games.
    """
    pageMenuName: String
    """
    The casino item associated with this menu item.
    """
    casinoItem: CasinoItems!
  }

  type CasinoMenu {
    """
    The ID of the casino menu.
    """
    id: Int!
    """
    The name of the casino menu.
    """
    name: String!
    """
    The image URL of the casino menu.
    """
    imageUrl: String
    """
    The tenant ID associated with the casino menu.
    """
    tenantId: Int!
  }

  type TotalMenuItem {
    """
    The total count of menu items in this menu.
    """
    count: Int
  }

  type Menu {
    """
    The id of the menu.
    """
    id: Int!
    """
    The name of the menu.
    """
    name: String!
    """
    The CMS data for the menu.
    """
    cms: [String]
    """
    The casino menu information.
    """
    casinoMenu: CasinoMenu!
    """
    List of menu items.
    """
    menuItems: [MenuItems!]!
    """
    Total number of menu items in this menu.
    """
    totalMenuItem: [TotalMenuItem!]!
  }

  type CombineGamesResponse {
    id: Int
    title: String
    cms: [String]
    """
    contains fields related to menus
    """
    menus: [Menu!]!
  }

  type GameSearchResponse {
    """
    contains menu items count
    """
    count: Int
    """
    contains fields related to menu items
    """
    data: [MenuItem]
  }

  """
  represents the input type of PageProviders query
  """
  input PageProvidersInput {
    topMenuId: Int
    pageMenuName: String
    limit: Int
    offset: Int
  }

  type cmsObjResponse {
    heading: JSON
    content: JSON
  }

  type PageProvidersResponse {
  """
    id of the page
    """
    id: Int
    """
    title of the page
    """
    title: String
    """
    contains fields related to menus
    """
    order: Int
    """
    category count of the page
    """
    category_count: Int
    """
    image url of the page
    """
    image: String
    """
    contains topMenuIds
    """
    topMenuIds: [Int]
    """
    contains cms
    """
    cms: [cmsObjResponse]
  }

  type Page {
    id: Int
    title: String
    order: Int
  }

  extend type Query {
    """
    fetches all the related games and returns GameSearcher response
    """
    GameSearch(input: GameSearchInput!): GameSearchResponse
    """
    fetches all the popular games
    """
    PopularGames: [MenuItem]
    """
    fetches all popular games sliders
    """
    PopularGamesSliders: [slider]
    """
    fetches all the combine games of same menu
    """
    CombineGames(input: CombineGamesInput!): CombineGamesResponse
    """
    fetches all the related games and returns GameSearcher response
    """
    GameConsole(input: GameConsoleInput!): GameConsoleResponse
    """
    fetches all the pages providers
    """
    PageProviders(input: PageProvidersInput): [PageProvidersResponse]
    """
    fetches games using the GameConsoleSearcher service
    """
    GameConsoleSearcher(input: GameConsoleSearchInput!): [GameConsoleSearchResponse]
    """
    fetches all the popular games in console style
    """
    PopularGameConsole(input: PopularGameConsoleInput!): PopularGameConsoleResponse
  }

  """
  represents the input type of GameConsole query
  """
  input GameConsoleInput {
    """
    The ID of the page.
    """
    pageId: Int
    """
    The ID of the top menu.
    """
    topMenuId: Int
    """
    The ID of the category.
    """
    categoryId: Int
    """
    The limit for the number of results.
    """
    limit: Int
    """
    The offset for the results.
    """
    offset: Int
  }

  type GameConsoleSearchResponse {
    uuid: String
    image: String
    featured: Boolean
    page_id: Int
    title: String
    provider_id: Int
    ordering: Int
    is_image_modified: Boolean
    name: String
    """
    The ID of the top menu.
    """
    top_menu_id: Int
    """
    has lobby status of the game menu item
    """
    has_lobby: Boolean
    """
    main provider name of the game menu item
    """
    provider_name: String
    """
    indicates whether the game is marked as favorite by the authenticated user
    """
    isFavorite: Boolean
  }

  input GameConsoleSearchInput {
    searchKey: String,
    topMenuId: Int,
    pageId: Int,
    limit: Int,
    offset : Int
  }

  type GameMenuItem {
  """
  uuid of the game menu item
  """
  uuid : String
  """
  image url of the game menu item
  """
  image: String
  """
  title of the game menu item
  """
  title: String
  """
  page id of the game menu item
  """
  page_id: Int
  """
  featured status of the game menu item
  """
  featured: Boolean
  """
  ordering of the game menu item
  """
  ordering: Int
  """
  provider id of the game menu item
  """
  provider_id: Int
  """
  image modified status of the game menu item
  """
  is_image_modified: Boolean
  """
  name of the game menu item
  """
  name: String
  """
  has lobby status of the game menu item
  """
  has_lobby: Boolean
  """
  main provider name of the game menu item
  """
  provider_name: String
  """
  indicates whether the game is marked as favorite by the authenticated user
  """
  isFavorite: Boolean

  }


  """
  represents the game menu type
  """
  type GameMenu {
    """
    The ID of the game menu.
    """
    id: Int
    """
    The name of the game menu.
    """
    name: String
    """
    The ID of the top menu.
    """
    top_menu_id: Int
    """
    The URL of the icon.
    """
    icon_url: String
    """
    The ordering of the game menu.
    """
    ordering: Int
    """
    The total count of games in the menu.
    """
    totalgamecount: Int
    """
    games in the menu.
    """
    games: [GameMenuItem]
    """
    contains cms array for menu.
    """
    cms: [cmsObjResponse]
  }


  type PopularGameMenuItem {
  """
  uuid of the game menu item
  """
  uuid : String
  """
  popular game id of the game menu item
  """
  popular_game_id: Int
  """
  top menu id of the game menu item
  """
  top_menu_id: Int
  """
  image url of the game menu item
  """
  image: String
  """
  title of the game menu item
  """
  title: String
  """
  page id of the game menu item
  """
  page_id: Int
  """
  featured status of the game menu item
  """
  featured: Boolean
  """
  ordering of the game menu item
  """
  ordering: Int
  """
  provider id of the game menu item
  """
  provider_id: Int
  """
  image modified status of the game menu item
  """
  is_image_modified: Boolean
  """
  name of the game menu item
  """
  name: String
  """
  has lobby status of the game menu item
  """
  has_lobby: Boolean
  """
  main provider name of the game menu item
  """
  provider_name: String
  """
  indicates whether the game is marked as favorite by the authenticated user
  """
  isFavorite: Boolean

  }


  """
  represents the response type of GameConsole query
  """
  type GameConsoleResponse {
    """
    The ID of the game console.
    """
    id: Int
    """
    The title of the game console.
    """
    title: String
    """
    The CMS data for the game console.
    """
    cms: [JSON]
    """
    contains fields related to game menus
    """
    menus: [GameMenu!]!
  }

  """
  represents the response type of PopularGameConsole query
  """
  type PopularGameConsoleResponse {
    id: Int
    title: String
    menus: [PopularGameMenu!]!
  }

  """
  represents the input type of PopularGameConsole query
  """
  input PopularGameConsoleInput {
    pageId: Int
    topMenuId: Int
    categoryId: Int
    limit: Int
    offset: Int
  }

  type PopularGameMenu {
    id: Int
    name: String
    is_primary: Boolean
    games: [PopularGameMenuItem!]!
  }

`
