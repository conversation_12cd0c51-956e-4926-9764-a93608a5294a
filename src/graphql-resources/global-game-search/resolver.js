import CombineGames from '../../services/global-game-search/combineGames'
import GameConsole from '../../services/global-game-search/gameconsole'
import GameConsoleSearcher from '../../services/global-game-search/gameConsoleSearcher'
import GameSearch from '../../services/global-game-search/gameSearch'
import PopularGameConsole from '../../services/global-game-search/popularGameConsole'
import PopularGames from '../../services/global-game-search/popularGames'
import PopularGamesSliders from '../../services/global-game-search/popularGamesSliders'
import PageProvider from '../../services/tenant-setting/pageProvider'

/**
 * Global Game Search resolver will handle game search related queries and mutation.
 */
export const resolvers = {

  /**
   * All the queries related to the game search are present in this query resolver
   */
  Query: {
    /**
     * It will fetch all the games related with search keyword
     * @returns {[MenuItem]}
     */
    GameSearch: async (_, { input }, context) => {
      const result = await GameSearch.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It will fetch all the popular games
     * @returns {[MenuItem]}
     */
    PopularGames: async (_, __, context) => {
      const result = await PopularGames.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It will fetch all the popular games sliders
     */
    PopularGamesSliders: async (_, __, context) => {
      const result = await PopularGamesSliders.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * It will fetch all the combine games of same menu
     */
    CombineGames: async (_, { input }, context) => {
      const result = await CombineGames.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It will fetch all the related games and returns GameSearcher response
     */
    GameConsole: async (_, { input }, context) => {
      const result = await GameConsole.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It will fetch all the pages
     */
    PageProviders: async (_, { input }, context) => {
      const result = await PageProvider.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result.pages
    },
    /**
     * It will fetch all the related games and returns GameConsoleSearcher response
     */
    GameConsoleSearcher: async (_, { input }, context) => {
      const result = await GameConsoleSearcher.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result.games
    },
    /**
     * It will fetch all the popular games in console style
     */
    PopularGameConsole: async (_, { input }, context) => {
      const result = await PopularGameConsole.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    }
  }
}
