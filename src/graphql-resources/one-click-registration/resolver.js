import GenerateCaptcha from '../../services/one-click-registration/generateCaptcha'
import OneClickRegistration from '../../services/one-click-registration/oneClickRegistration'
import VerifyCaptcha from '../../services/one-click-registration/verifyCaptcha'

export const resolvers = {
  Query: {
    /**
     * Generates a CAPTCHA image and key for the user to solve.
     * Typically used as part of security verification before allowing actions user registration.
     * @returns {GenerateCaptchaResponse}
     */
    GenerateCaptcha: async (_, __, context) => {
      const result = await GenerateCaptcha.execute(__, context)
      return result.result
    }
  },

  Mutation: {
    /**
     * Verifies the user-provided CAPTCHA input.
     * @param {VerifyCaptchaInput} input - Includes CAPTCHA solution and verification key.
     * @param {Context} context - GQL context containing shared information.
     * @returns {VerifyCaptchaResponse}
     */
    VerifyCaptcha: async (_, { input }, context) => {
      const result = await VerifyCaptcha.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * Registers the user via one-click registration.
     * @param {OneClickRegistrationInput} input - Includes registration details.
     * @param {Context} context - GQL context containing shared information.
     * @returns {OneClickRegistrationResponse}
     */
    OneClickRegistration: async (_, { input }, context) => {
      const result = await OneClickRegistration.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { accessToken: result.result.token, user: result.result.user, resToken: result.result.resToken }
    }
  },

  /**
    * It is responsible to fetch user details along with wallet details
    * @param {User} User
    * @param {Context} context
    * @returns {User}
  */
  UserEntityResponse: {
    userWallet: async (User, _, { getUserWalletDataLoader, getDemoUserWalletDataLoader }) => {
      if (User.isDemoUser) {
        return getDemoUserWalletDataLoader.load(User.id)
      } else {
        const d = await getUserWalletDataLoader.load(User.id)
        return d
      }
    },

    userNotification: async (User, __, { getUserNotificationDataLoader }) => {
      return getUserNotificationDataLoader.load(User.id)
    }
  },

  /**
  * It is responsible to fetch wallet details along with currency details
  * @param {UserWalletEntityResponse}
  * @param {Context} context
  * @returns {wallet}
  */
  UserWalletEntityResponse: {
    userCurrency: async (UserWalletEntityResponse, _, { getUserWalletCurrencyDataLoader }) => {
      return getUserWalletCurrencyDataLoader.load(UserWalletEntityResponse.currencyId)
    }
  }
}
