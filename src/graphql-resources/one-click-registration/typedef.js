import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  contains all the fields related to the one-click registration
  """

  type GenerateCaptchaResponse {
    uuid: String!
    captcha: String!
  }

  type VerifyCaptchaResponse {
    verified: Boolean!
    captchaToken: String!
  }

  # scalar JSON
  """
  contains access token, res token and user details who just logged in
  """
  type OneClickRegistrationResponse {
    """
    contains the jwt token of the corresponding user
    """
    accessToken: String
    """
    contains user details
    """
    user: UserEntityResponse!
    """
    contains the res token of the corresponding user
    """
    resToken: String

    """
    to know whether password should update or not
    """
    shouldPasswordUpdate: Boolean
    """
    to know whether screen is idlee or not
    """
    idleScreenLogoutTime: Int
    """
    contains a message if any
    """
    message: String
    """
    contains a success status
    """
    success: Boolean
  }

  """
  contains all the fields related to user
  """
  type UserEntityResponse {
    """
    contains phone country code of the user
    """
    countryCode: String
    """
    contains the timestamp of a user
    """
    createdAt: String!
    """
    contains the date of birth of a user
    """
    dateOfBirth: String
    """
    contains email of a user
    """
    email: String
    """
    contains a boolean representing wether the email of user is verified or not
    """
    emailVerified: Boolean
    """
    contains first name of a user
    """
    firstName: String
    """
    contains gender of a user
    """
    gender: String
    """
    contains user id for unique identification in database
    """
    id: ID!
    """
    contains last name of a user
    """
    lastName: String
    """
    contains phone number of a user
    """
    phone: String
    """
    contains a boolean representing wether the phone number of user is verified or not
    """
    phoneVerified: Boolean
    """
    contains the number of time a user logged in
    """
    signInCount: Int
    """
    contains the last ip address from where user logged-in
    """
    signInIp: [JSON]
    """
    contains the timestamp of last time when user updated its profile
    """
    updatedAt: String
    """
    contains username of a user
    """
    userName: String
    """
    contains nickname of a user
    """
    nickName: String
    """
    contains self exclusion date of a user
    """
    selfExclusion: String
    """
    contains wallet balance of a user
    """
    userWallet: UserWalletEntityResponse
    """
    contains phone code of the user
    """
    phoneCode: String
    """
    contains document verification of user
    """
    isDocumentVerified: Boolean
    """
    contains city of new user
    """
    city: String
    """
    contains zip code new user
    """
    zipCode: String
    """
    contains kyc status of user
    """
    kycDone: Boolean
    """
    contains deposit bonus status of user
    """
    activeDepositBonus: Boolean
    """
    contains notification of the user
    """
    userNotification: [NotificationReceiverResponse]
    """
    gets you the VIP level of user
    """
    vipLevel: String
    """
    Indicates whether the user is a demo user.
    """
    isDemoUser: Boolean
    """
    contains national id of logged-in user
    """
    nationalId: String
    """
    Indicates whether the user is allowed to create withdrawal requests.
    """
    enableWithdrawRequests: Boolean

    """
    Indicates smarticoUserId for user used in smartico dashboard
    """
    smarticoUserId: String
    """
    Indicates the file name or URL of the user's avatar image, used for profile display.
    """
    avatarImage: String
    """
    Indicates login type of user
    """
    isAdminUser: Boolean
    """
    contains otp preference of user
    """
    otpPreference: Int
    """
    financial activity enabled or not
    """
    isFinancialActivityEnabled: Boolean
    """
    Indicates the category type for User.
    """
    categoryType: Int
    """
    User's Referral Code
    """
    referralCode: String
    """
    Bot User Check
    """
    isBotUser: Boolean
    """
    Referral Code Check
    """
    isReferralCodeBlocked: Boolean
    password: String
    creationType: Int
  }

  """
  user wallet amount
  """
  type UserWalletEntityResponse {
    amount: Float
    id: ID
    currencyId: ID
    nonCashAmount: Float
    oneTimeBonusAmount: Float
    sportsFreebetAmount: Float
    userCurrency: CurrencyResponse
    bonusAmount: Float
    rolloverBalance: Float
    initialRolloverBalance: Float
    rolloverAchived: Float
    kind: String
    code: String
    bonusesInQueue: JSON
  }

  """
  represents the input type of VerifyCaptcha mutation
  """
  input VerifyCaptchaInput {
    uuid: String!
    captchaText: String!
    isGameLaunch: Boolean
  }

  """
  represents the input type of OneClickRegistration mutation
  """
  input OneClickRegistrationInput {
    captchaToken: String!
    currencyId: String!
    promoCode: String
    referralCode: String
    affiliateToken: String
    affiliatedData: String
    deviceId: String
    deviceType: String
    deviceModel: String
    ip: String
    network: String
    version: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
    alanbaseClickId: String
    vipKey: String
    wyntaClickId: String
  }

  extend type Query {
    GenerateCaptcha: GenerateCaptchaResponse!
  }

  extend type Mutation {
    VerifyCaptcha(input: VerifyCaptchaInput!): VerifyCaptchaResponse!
    OneClickRegistration(input: OneClickRegistrationInput!): OneClickRegistrationResponse!
  }
`
