import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  Input type for managing favorite games (add/remove)
  """
  input ManageFavoriteGameInput {
    """
    Identifier for the game
    """
    gameId: String!

    """
    Identifier for the casino provider
    """
    casinoProviderId: Int!

    """
    Determines whether to add (true) or remove (false) the favorite game
    """
    request: Boolean!
  }

  """
  Represents a favorite game
  """
  type FavoriteGame {
    """
    Unique identifier for the favorite game
    """
    id: ID!

    """
    User ID who favorited the game
    """
    userId: ID!

    """
    Identifier for the game
    """
    gameId: String!

    """
    Identifier for the casino provider
    """
    casinoProviderId: Int!

    """
    Tenant ID
    """
    tenantId: Int!

    """
    Game detail
    """
    gameDetail: GameDetail

    """
    Provider detail
    """
    providerDetail: ProviderDetail

    """
    Creation date
    """
    createdAt: String

    """
    Last update date
    """
    updatedAt: String
  }

  """
  Response type for managing a favorite game
  """
  type ManageFavoriteGameResponse {
    """
    Success status of the operation
    """
    success: Boolean!

    """
    Message related to the operation
    """
    message: String!

    """
    The updated favorite game data (if applicable)
    """
    favoriteGame: FavoriteGame
  }

  """
  Response type for game detail
  """
  type GameDetail {
    """
    Unique identifier
    """
    uuid: String!
    
    """
    Category identifier
    """
    categoryId: String
    
    """
    Ordering position
    """
    ordering: Int
  }

  """
  Response type for provider detail
  """
  type ProviderDetail {
    """
    Provider name
    """
    name: String!
  }

  """
  Response type for fetching the user's favorite games
  """
  type FavoriteGamesResponse {
    """
    List of favorite games
    """
    favoriteGames: [FavoriteGame!]!

    """
    Total number of favorite games
    """
    totalFavorites: Int!
  }

  """
  Input type for GameConsoleFavorites query
  """
  input GameConsoleFavoritesInput {
    """
    The ID of the page.
    """
    pageId: Int
    """
    The ID of the top menu.
    """
    topMenuId: Int
    """
    The ID of the category.
    """
    categoryId: Int
    """
    The limit for the number of results.
    """
    limit: Int
    """
    The offset for the results.
    """
    offset: Int
  }

  extend type Mutation {
    """
    Add or remove a favorite game
    """
    ManageFavoriteGame(input: ManageFavoriteGameInput!): ManageFavoriteGameResponse! @isAuthenticated
  }

  extend type Query {
    
    """
    Get favorite games in GameConsole format
    """
    FavoriteGames(input: GameConsoleFavoritesInput!): GameConsoleResponse! @isAuthenticated
  }
`
