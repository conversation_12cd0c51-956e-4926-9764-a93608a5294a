import ManageFavoriteGame from '../../services/favorite-games/manageFavoriteGames'
import GetFavoriteGames from '../../services/favorite-games/favoriteGames'

export const resolvers = {
  Query: {
    FavoriteGames: async (_, __, context) => {
      const result = await GetFavoriteGames.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    }
  },

  Mutation: {
    ManageFavoriteGame: async (_, { input }, context) => {
      const result = await ManageFavoriteGame.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    }

  }
}
