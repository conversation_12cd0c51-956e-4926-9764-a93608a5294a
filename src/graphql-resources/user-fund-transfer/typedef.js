import { gql } from 'apollo-server-express'

export const typeDef = gql`

  input WithdrawRequestInput {
    amount: Float!
    bankId: Int!
    password: String!
    network: String!
    ip: String!
    deviceId: String!
    deviceType: String!
    version: String!
    deviceModel: String!
    data: JSON
  }

  type WithdrawRequestResponse {
    id: ID!
    status: String
    name: String
    accountNumber: String
    ifscCode: String
    amount: Float!
    phoneNumber: String
    transactionId: Int
    tenantId: Int
    actionableType: String
    actionableId: Int
    actionedAt: String
    createdAt: String
    remark: String
    bankId: Int
    withdrawalType: String
    verify_status: String
    bankName: String
  }



  type DepositRequestResponse {
    id: ID!
    userId: Int
    orderId: String
    status: String
    ledgerId: Int
    utrNumber: String
    transactionReceipt: String
    depositType: String
    createdAt: String
    updatedAt: String
    paymentProviderId: Int
    amount: Float
    remark: String
    paymentProvider: paymentProviderResponses
    countryCode: String
    currencyConversion: Float
    requestedCurrencyCode: String
    requestedAmount: Float
    manualDepositType: Int
    userRemark: String
    oldAmount: Float
  }

  type paymentProviderResponses {
    """
    contains the Provider id
    """
    id: ID!
    """
    contains the Provider Name
    """
    providerName: String
    tenantPaymentConfigurations: [tenantPaymentProviderResponses]
  }

  type tenantPaymentProviderResponses {
    """
    contains description of the Provider Name
    """
    description: String
  }

  input ManualDepositRequestInput {

    files: Upload!
    amount: Float!
    utrNumber: String!
    countryCode: String
    manualDepositType: Int!
    userRemark: String
    requestedCurrencyCode: String
  }

  type ManualDepositRequestResponse {
    id: ID!
    userId: Int
    utrNumber: String
    transactionReceipt: String
    depositType: String
    createdAt: String
    updatedAt: String
    manualDepositType: Int
    userRemark: String
  }



  """
  contain fields related to sort filter
  """
  input RequestOrderBy {
    createdAt: SortOrder
    status: SortOrder
  }

  """
  represents the input type of Transactions query
  """
  input WithdrawRequestsInput {
    page: Int
    limit: Int
    dateFrom: String
    dateTo: String
    orderBy: RequestOrderBy
  }

  type WithdrawRequestsResponse {
    requests: [WithdrawRequestResponse]!
    count: Int!
  }

  input ShowDepositRequestsInput {
    page: Int
    limit: Int
    dateFrom: String
    dateTo: String
    depositType: String!
    searchKey: String
    orderBy: RequestOrderBy
  }

  type ShowDepositRequestResponse {
    requests: [DepositRequestResponse]!
    count: Int
  }

  type CheckWithdrawalLimitResponse {
    totalWithdrawalLimit: Float!
    usedWithdrawalLimit: Float!
    remainingWithdrawalLimit: Float!
    isWithdrawalEnabled: Int!
  }

  extend type Query {
    WithdrawRequests(input: WithdrawRequestsInput): WithdrawRequestsResponse! @isAuthenticated
    ShowDepositRequests(input: ShowDepositRequestsInput): ShowDepositRequestResponse! @isAuthenticated
    CheckWithdrawalLimit: CheckWithdrawalLimitResponse! @isAuthenticated
  }

  extend type Mutation {
    CreateWithdrawRequest(input: WithdrawRequestInput!): WithdrawRequestResponse! @isAuthenticated
    CancelWithdrawRequest(requestId: Int!, password: String!): WithdrawRequestResponse! @isAuthenticated
    DepositRequest(amount: Float!): DepositRequestResponse! @isAuthenticated
    CreateManualDepositRequest(input: ManualDepositRequestInput!): ManualDepositRequestResponse! @isAuthenticated
  }

`
