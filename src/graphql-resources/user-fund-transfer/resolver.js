import CancelWithdrawRequest from '../../services/user-fund-transfer/cancelWithdrawRequest'
import CheckWithdrawalLimit from '../../services/user-fund-transfer/checkWithdrawalLimit'
import CreateManualDepositRequest from '../../services/user-fund-transfer/createManualDepositRequest'
import CreateWithdrawRequest from '../../services/user-fund-transfer/createWithdrawRequest'
import DepositRequest from '../../services/user-fund-transfer/depositRequest'
import ShowDepositRequest from '../../services/user-fund-transfer/showDepositRequest'
import WithdrawRequest from '../../services/user-fund-transfer/withdrawRequest'

export const resolvers = {
  Query: {
    /**
     * It is responsible to fetch all the withdraw requests user made till date
     * @returns {[WithdrawRequestResponse]}
     */
    WithdrawRequests: async (_, input, context) => {
      const result = await WithdrawRequest.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },
     /**
     * It is responsible to fetch all the withdraw requests user made till date
     * @returns {[DepositRequestResponse]}
     */
    ShowDepositRequests: async (_, input, context) => {
      const result = await ShowDepositRequest.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },
    /**
     * It is responsible to fetch all the withdraw requests user made till date and sum the amount
     * @returns {[DepositRequestResponse]}
     */
    CheckWithdrawalLimit: async (_, input, context) => {
      const result = await CheckWithdrawalLimit.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    }
  },


  Mutation: {
    /**
     * It is responsible for requesting user wallet money from the user's wallet
     * @param {Object} input it contains the withdraw request input fields
     * @returns {WithdrawRequestResponse}
     */
    CreateWithdrawRequest: async (_, input, context) => {
      const result = await CreateWithdrawRequest.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },

    /**
    * It is responsible for canceling withdraw request from the user's wallet
    * @param {Integer} requestId it contains the withdraw request id
    * @param {String} password it contains the user password
    * @returns {WithdrawRequestResponse}
    */
    CancelWithdrawRequest: async (_, { requestId, password }, context) => {
      const result = await CancelWithdrawRequest.execute({ requestId, password }, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },

    /**
    * It is responsible for generating unique order id for the user's deposit request
    * @returns {DepositRequestResponse}
    */
    DepositRequest: async (_, amount, context) => {
      const result = await DepositRequest.execute(amount, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },

    /**
    * It is responsible for generating for the user's manual deposit request
    * @returns {manualDepositRequestResponse}
    */
    CreateManualDepositRequest: async (_, { input }, context) => {
      const result = await CreateManualDepositRequest.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }
  }
}
