// Import necessary dependencies or services
import MarkThreadAsRead from '../../services/thread/markThreadAsRead';
import Threads from '../../services/thread/thread';

/**
 * Thread resolver handles queries related to threads.
 */
export const resolvers = {

  /**
   * Query resolver for fetching threads.
   */
  Query: {

    /**
     * Resolver to fetch all threads.
     * @returns {Thread[]}
     */
    Threads: async (_, { input }, context) => {
      const result = await Threads.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }
  },
  Mutation: {

    /**
     * Resolver to mark a thread (message) as read.
     * @param {ID!} id - The ID of the thread (message) to mark as read.
     * @returns {Boolean} - Indicates whether the operation was successful.
     */
    MarkThreadAsRead: async (_, { id }, context) => {
      const result = await MarkThreadAsRead.execute({ id }, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
  },
};
