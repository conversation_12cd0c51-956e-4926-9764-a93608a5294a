import { gql } from 'apollo-server-express';

export const typeDef = gql`
  type Thread {
    id: ID!
    title: JSON!
    messageBody: JSON
    isRead: Boolean
    createdAt: String
  }

  type ThreadResponse {
    """
    contains FAQ count
    """
    count: Int
    """
    contains fields related to Threads
    """
    threads: [Thread!]
  }

  input ThreadQueryInput {
    limit: Int!
    offset: Int!
    read: <PERSON><PERSON><PERSON>
  }

  extend type Query {
    Threads(input: ThreadQueryInput!): ThreadResponse @isAuthenticated
  }

  extend type Mutation {
    MarkThreadAsRead(id: ID!): Boolean! @isAuthenticated
  }
`;
