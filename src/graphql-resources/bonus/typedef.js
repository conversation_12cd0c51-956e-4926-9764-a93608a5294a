import { gql } from 'apollo-server-express'

export const typeDef = gql`

  """
  contains all the fields related to losing bonus setting table
  """
  type LosingBonusSettingResponse {
    id: ID!
    claimDays: Int
    createdAt: String
    updatedAt: String
    claimIntervalType: String
    bonusCalculationType: String
    bonusClaimType: String
    weekDay: String
    burningDays: Int
    burnType: Int
    providers: JSON
    losingBonusTiers: [LosingBonusTierResponse]!
  }

  type JoiningBonusResponse {
    id: ID
    code: String
    promotionTitle: JSON
    image: String
    termsAndConditions: JSON
    flat: Float
  }

  """
  contains all the fields related to losing bonus tier table
  """
  type LosingBonusTierResponse {
    id: ID!
    minLosingAmount: Float
    maxLosingAmount: Float
    percentage: Float
    maxBonus: Float
  }

  """
  contains all the fields related to losing bonus table
  """
  type LosingBonusResponse {
    id: ID!
    code: String
    percentage: Float
    enabled: Boolean
    validFrom: String
    validUpto: String
    currencyId: Int
    promotionTitle: JSON
    image: String
    termsAndConditions: JSON
    vipLevels: [String]
    usageCount: Int
    losingBonusSetting: LosingBonusSettingResponse!
    userBonus: UserBonusResponse @isAuthenticated
    kind: String
    isInBonusQueue: Boolean
    userBonusQueue: JSON @isAuthenticated
    bonusCategory: JSON @isAuthenticated
  }

  """
  contains all the fields related to user bonus table
  """
  type UserBonusResponse {
    id: ID
    status: String
    bonusAmount: Float
    rolloverBalance: Float
    userId: ID
    bonusId: ID
    kind: String
    transactionId: ID
    expiresAt: String
    claimedAt: String
    createdAt: String
    updatedAt: String
    initialRolloverBalance: Float
  }

  """
  contains all fields related to recurring bonus tier rollover
  """
  type RecurringRolloverResponse {
    id: ID!
    userBonusId: ID
    bonusId: ID
    bonusAmount: Float
    rolloverTarget: Float
    remainingRollover: Float
    status: Int
    createdAt: String
    claimedAt: String
  }

  """
  contains all the fields related to deposit bonus tier table
  """
  type DepositBonusTierResponse {
    id: ID!
    minDepositAmount: Float
    maxDepositAmount: Float
    percentage: Float
    maxBonus: Float
  }

  """
  contains all the fields related to deposit bonus table
  """
  type DepositBonusResponse {
    id: ID!
    code: String
    percentage: Float
    enabled: Boolean
    validFrom: String
    validUpto: String
    currencyId: Int
    promotionTitle: JSON
    image: String
    kind: String
    termsAndConditions: JSON
    vipLevels: [String]
    usageCount: Int
    depositBonusSetting: DepositBonusSettingResponse
    userBonus: UserBonusResponse @isAuthenticated
    userBonusQueue: JSON @isAuthenticated
    isInBonusQueue: Boolean
    bonusCategory: JSON @isAuthenticated
    recurringRolloverHistory: [RecurringRolloverResponse] @isAuthenticated
  }

  """
  contains all the fields related to deposit bonus setting table
  """
  type DepositBonusSettingResponse {
    id: ID!
    minDeposit: Float
    maxDeposit: Float
    maxBonus: Float
    rolloverMultiplier: Int
    maxRolloverPerBet: Float
    validForDays: Int
    bonusId: Int
    depositType: Int
    depositBonusType: String
    recurringBonusType: String
    tierType: Int
    customDeposits: Int
    weekDay: String
    burningDays: Int
    burnType: Int
    providers: JSON,
    tierConfigType: Int
    applyToDepositSequenceGte: Int
    depositBonusTiers: [DepositBonusTierResponse]!
  }

  """
  contains the amount to be credited after claiming losing bonus
  """
  type ClaimLosingBonusResponse {
    bonusCredited: Float!
  }

  """
  contains boolean representing wether bonus got added
  """
  type AddDepositBonusResponse {
    bonusAdded: Boolean
    bonusInQueue: Boolean
  }

  """
  contains boolean representing wether bonus got added
  """
  type AddLosingBonusResponse {
    bonusAdded: Boolean,
    bonusInQueue: Boolean
  }

  """
  contains boolean representing wether bonus got canceled
  """
  type CancelDepositBonusResponse {
    bonusCanceled: Boolean
  }

  """
  contains general bonus information
  """
  type AllBonusResponse {
    promotionTitle: JSON
    image: String
    kind: String
    id: Int
  }

  """
  contains burning bonus information
  """
  type BurningBonusResponse {
    flag: Boolean!
    message: String
  }

  type HasRecurringBonusInQueueResponse {
    exists: Boolean
  }

  extend type Query {
    """
    fetches all the the losing bonus that are allowed for a logged in user
    """
    LosingBonus: [LosingBonusResponse]!

    """
    fetches the joining bonus for that tenant
    """
    JoiningBonus: JoiningBonusResponse

    """
    fetches all the the deposit bonus that are allowed for a logged in user
    or which are currently active
    """
    DepositBonus: [DepositBonusResponse]!
    """
    fetches all the bonus that are valid and enable
    """
    AllBonus: [AllBonusResponse]
    """
    check if a user has any active bonuses with burning days remaining that they haven't utilized
    """
    BurningBonus(bonusType: String!, losingBonusId: Int): BurningBonusResponse! @isAuthenticated
    HasRecurringBonusInQueue: HasRecurringBonusInQueueResponse! @isAuthenticated
  }

  extend type Mutation {
    """
    it claim a losing bonus having given losing bonus id, and credits losing bonus amount
    in the user's wallet if the user is eligible
    """
    ClaimLosingBonus(bonusId: Int!, ipAddress: String): ClaimLosingBonusResponse! @isAuthenticated

    """
    it activates a deposit bonus having given deposit bonus id
    """
    AddDepositBonus(bonusId: Int!, bonusType: String!): AddDepositBonusResponse! @isAuthenticated

    """
    it activates a losing bonus having given losing bonus id
    """
    AddLosingBonus(bonusId: Int!): AddLosingBonusResponse! @isAuthenticated

    """
    it cancels a deposit bonus having given deposit bonus id
    """
    CancelDepositBonus(bonusId: Int!): CancelDepositBonusResponse! @isAuthenticated
  }

`
