import AddDepositBonus from '../../services/bonus/addDepositBonus'
import AddLosingBonus from '../../services/bonus/addLosingBonus'
import AllBonus from '../../services/bonus/allBonus'
import BurningBonus from '../../services/bonus/burningBonus'
import CancelDepositBonus from '../../services/bonus/cancelDepositBonus'
import CheckRecurringBonusAvailable from '../../services/bonus/checkRecurringBonusAvailable'
import ClaimLosingBonus from '../../services/bonus/claimLosingBonus'
import DepositBonus from '../../services/bonus/depositBonus'
import JoiningBonus from '../../services/bonus/joiningBonus'
import LosingBonus from '../../services/bonus/losingBonus'

export const resolvers = {
  Query: {
    /**
     * This resolver is responsible to fetch losing bonus for the current user
    * @returns {[LosingBonusResponse]}
     */
    LosingBonus: async (_, __, context) => {
      const result = await LosingBonus.execute(__, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
    * This resolver is responsible to fetch losing bonus for the current user
    * @returns {JoiningBonusResponse}
    */
    JoiningBonus: async (_, __, context) => {
      const result = await JoiningBonus.execute(__, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * This resolver is responsible to fetch deposit bonus for the current user
    * @returns {[DepositBonusResponse]}
     */
    DepositBonus: async (_, __, context) => {
      const result = await DepositBonus.execute(__, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
 * This resolver is responsible to fetch all bonus
* @returns {[AllBonusResponse]}
 */
    AllBonus: async (_, __, context) => {
      const result = await AllBonus.execute(__, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
    * This resolver is responsible to fetch burning bonus
    * @returns {BurningBonusResponse}
    */
    BurningBonus: async (_, { bonusType, losingBonusId }, context) => {
      const result = await BurningBonus.execute({ bonusType, losingBonusId }, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * @returns {HasRecurringBonusInQueueResponse}
     */
    HasRecurringBonusInQueue: async (_, __, context) => {
      const result = await CheckRecurringBonusAvailable.execute(__, context)
      return result.result
    }
  },

  Mutation: {
    /**
    * This resolver is responsible to fetch casino items of the current current casino item
    * @returns {ClaimLosingBonusResponse}
    */
    ClaimLosingBonus: async (_, bonusId, ipAddress, context) => {
      const result = await ClaimLosingBonus.execute(bonusId, ipAddress, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
    * This resolver is responsible to add a deposit bonus in user profile.
    * @returns {AddDepositBonusResponse}
    */
    AddDepositBonus: async (_, bonusId, context) => {
      const result = await AddDepositBonus.execute(bonusId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
    * This resolver is responsible to cancel an active deposit bonus.
    * @returns {CancelDepositBonusResponse}
    */
    CancelDepositBonus: async (_, bonusId, context) => {
      const result = await CancelDepositBonus.execute(bonusId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
    * This resolver is responsible to add a losing bonus in user profile.
    * @returns {AddLosingBonusResponse}
    */
    AddLosingBonus: async (_, bonusId, context) => {
      const result = await AddLosingBonus.execute(bonusId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }

  },

  LosingBonusResponse: {
    /**
    * This resolver is responsible to fetch losing bonus setting of the current current losing bonus
    * @returns {LosingBonusSettingResponse}
    */
    losingBonusSetting: async (bonus, _, { getLosingBonusSettingDataLoader }) => {
      const result = await getLosingBonusSettingDataLoader.load(bonus.id)
      return result
    },

    /**
    * This resolver is responsible to fetch user bonus for for deposit and losing bonus
    * @returns {UserBonusResponse}
    */
    userBonus: async (depositBonus, _, { getUserBonusDataLoader, auth }) => {
      const result = await getUserBonusDataLoader.load({ bonusId: depositBonus.id, userId: auth.id })
      return result
    }
  },

  LosingBonusSettingResponse: {
    /**
    * This resolver is responsible to fetch losing bonus tiers of the current current losing bonus setting
    * @returns {losingBonusTiers}
    */
    losingBonusTiers: async (losingBonusSetting, _, { getLosingBonusTierDataLoader }) => {
      const result = await getLosingBonusTierDataLoader.load(losingBonusSetting.id)
      return result
    }
  },

  DepositBonusResponse: {
    /**
    * This resolver is responsible to fetch deposit bonus setting of the current deposit bonus setting
    * @returns {DepositBonusSettingResponse}
    */
    depositBonusSetting: async (depositBonus, _, { getDepositBonusSettingDataLoader }) => {
      const result = await getDepositBonusSettingDataLoader.load(depositBonus.id)
      return result
    },

    /**
    * This resolver is responsible to fetch user bonus for for deposit and losing bonus
    * @returns {UserBonusResponse}
    */
    userBonus: async (depositBonus, _, { getUserBonusDataLoader, auth }) => {
      const result = await getUserBonusDataLoader.load({ bonusId: depositBonus.id, userId: auth.id })
      return result
    }
  },

  DepositBonusSettingResponse: {
    /**
    * This resolver is responsible to fetch deposit bonus tiers of the current current deposit bonus setting
    * @returns {depositBonusTiers}
    */
    depositBonusTiers: async (depositBonusSetting, _, { getDepositBonusTierDataLoader }) => {
      const result = await getDepositBonusTierDataLoader.load(depositBonusSetting.id)
      return result
    }
  },

}
