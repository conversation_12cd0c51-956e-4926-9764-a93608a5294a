import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  Input type for BonusCancellationCheck
  """
  input BonusCancellationCheckInput {
    """
    Identifier for the game
    """
    actionType: String!

    """
    bonusId need to activate
    """
    bonusId: Int
  }

  """
  Response for BonusCancellationCheck
  """
  type BonusCancellationCheckResponse {
    """
    boolean indicating if the popup should be shown
    """
    showPopup: Boolean!

    """
    Message to be displayed in the popup
    """
    popupData: String!

  }

  """
  Response for BonusExpiryNotification
  """
  type BonusExpiryNotificationResponse {
    """
    Boolean indicating if the popup should be shown
    """
    showPopup: Boolean!

    """
    Message to be displayed in the popup
    """
    popupData: String!
  }


  extend type Query {

    """
    Get Bomus cancellation check
    """
    BonusCancellationCheck(input: BonusCancellationCheckInput!): BonusCancellationCheckResponse! @isAuthenticated

    """
    Get bonus expiry notification for user login
    """
    BonusExpiryNotification: BonusExpiryNotificationResponse! @isAuthenticated
  }
`
