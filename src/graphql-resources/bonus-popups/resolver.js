import BonusCancellationCheck from "../../services/bonus-popups/bonusCancellationCheck"
import BonusExpiryNotification from "../../services/bonus-popups/bonusExipryNotification"

export const resolvers = {
  Query: {
    BonusCancellationCheck: async (_, __, context) => {
      const result = await BonusCancellationCheck.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    BonusExpiryNotification: async (_, __, context) => {
      const result = await BonusExpiryNotification.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    }

  }
}
