import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  contains all the fields related to the responsible gaming
  """

  type ResponsibleGamingResponse {
    key: String
    value: String
    updatedAt: String
  }

  type MainResponsibleGamingResponse {
    selfExclusion: String
    setting: [ResponsibleGamingResponse]
  }

  """
  contains all the fields related to self exclusion
  """
  type SelfExclusionResponse {
    """
    contains the date upto which user restricts it self from playing games
    """
    date: String!
  }

  """
  represents the input type of UserSetting mutation
  """
  input UserSettingInput {
    """
    contains the name of setting to be added or updated
    """
    key: String!
    """
    contains the value of setting to be added or updated
    """
    value: String!
    """
    contains user password of the corresponding user
    """
    password: String!
  }

  """
  represents the input type of UpdateSelfExclusion mutation
  """
  input SelfExclusionInput{
    """
    contains the date upto which user restricts itself from playing games
    """
    date: String!
    """
    contains user password of the corresponding user
    """
    password: String!
  }

  extend type Query {
    """
    fetches all responsible gaming settings of a user,
    requires auth token of the user in the header
    and returns ResponsibleGamingResponse
    """
    ResponsibleGaming: MainResponsibleGamingResponse! @isAuthenticated
  }

  extend type Mutation {
    """
    adds or update betting limit and deposit limit of the current logged in user,
    requires auth token of the user in the header,
    takes input of type UserSettingInput and returns UserSettingResponse
    """
    SetUserSetting(input: UserSettingInput!): ResponsibleGamingResponse! @isAuthenticated

    """
    Update self exclusion of the current logged in user,
    requires auth token of the user in the header,
    takes input of type SelfExclusionInput and returns SelfExclusionResponse
    """
    UpdateSelfExclusion(input: SelfExclusionInput!): SelfExclusionResponse! @isAuthenticated
  }
`
