import SetUserSetting from '../../services/responsible-gaming/setUserSetting'
import UpdateSelfExclusion from '../../services/responsible-gaming/updateSelfExclusion'
import ResponsibleGaming from '../../services/responsible-gaming/responsibleGaming'

/**
 *  responsible gaming resolver will handle responsible-gaming related queries and mutation.
 */
export const resolvers = {
  /**
   * All the queries related to the responsible gaming are present in this query resolver
   */
  Query: {

    /**
    * This resolver is responsible fetch all the responsible gaming settings of a user
    * @returns {ResponsibleGamingResponse}
    */
    ResponsibleGaming: async (_, __, context) => {
      const result = await ResponsibleGaming.execute(__, context)
      return result.result
    }
  },

  /**
   * all the mutations related to the responsible gaming are present in mutation resolver
   */
  Mutation: {

    /**
     *
    * This resolver is responsible for setting or updating responsible gaming settings for the user
    * @param {UserSettingInput} input it contains key-value pair of settings and user password.
    * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
    * @returns {UserSettingInput}
    */
    SetUserSetting: async (_, { input }, context) => {
      const result = await SetUserSetting.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * It is responsible for setting or updating the self exclusion for the user.
     * @param {SelfExclusionInput} input it the self exclusion date and user password.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {SelfExclusionResponse}
     */
    UpdateSelfExclusion: async (_, { input }, context) => {
      const result = await UpdateSelfExclusion.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }
  }
}
