import { withFilter } from 'graphql-subscriptions'
import { SUBSCRIPTION_CHANNEL } from '../../common/constants'
import Notification from '../../services/notification/notification'
import NotificationSeen from '../../services/notification/notificationSeen'

/**
 *  User resolver will handle user related queries and mutation.
 */
export const resolvers = {

  /**
   * All the queries related to the notification are present in this query resolver
   */
  Query: {

    /**
     * This resolver is responsible to fetch  notifications
     * @returns {String}
     */
    Notification: async (_, __, context) => {
      const result = await Notification.execute(__, context)
      return result.result
    }
  },

  /**
   * all the mutations related to the notification are present in mutation resolver
   */
  Mutation: {

    /**
     * It will allow a user to update isRead
     * @param {NotificationSeenInput} input it contains notificationId.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    NotificationSeen: async (_, { input }, context) => {
      const result = await NotificationSeen.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }

  },

  /**
   * All the subscription related to the notification are present in this subscription resolver
   */
  Subscription: {
  /**
   * This subscription is responsible to fetch current wallet balance user
   * @returns {PlayerNotificationResponse}
   */
    PlayerNotification: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.PLAYER_NOTIFICATION), (payload, args) => {
        return (payload.PlayerNotification.userId === args.userId)
      })
    }
  },

  /**
  * It is responsible to fetch user details along with wallet details
  * @param {User} User
  * @param {Context} context
  * @returns {User}
  */
  NotificationReceiverResponse: {
    notification: async (notificationReceiver, _, { getNotificationDataLoader, databaseConnection }) => {
      return getNotificationDataLoader.load(notificationReceiver.notificationId)
    }
  }

}
