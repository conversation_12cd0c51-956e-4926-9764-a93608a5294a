import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  contains all the fields related to notification
  """
  type NotificationResponse {
    """
    contains sender id of notification
    """
    senderId: Int
    """
    contains the sender type of notification
    """
    senderType: String
    """
    contains the reference id of notification
    """
    referenceId: Int
    """
    contains reference type of notification
    """
    referenceType: String
    """
    contains message of notification
    """
    message: String
    """
    contains user id for unique identification in database
    """
    id: ID
    """
    contains createdAt of notification
    """
    createdAt: String
    """
    contains the timestamp of last time when user updated its profile
    """
    updatedAt: String
    """
    Notification type
    """
    type: String
    """
    Notification content or message.
    """
    value: String
}

  """
  contains all the fields related to notificationReceiver
  """
  type NotificationReceiverResponse {
    """
    contains sender id of notification
    """
    notificationId: Int
    """
    contains the sender type of notification
    """
    receiverId: String
    """
    contains the reference id of notification
    """
    receiverType: String
    """
    contains reference type of notification
    """
    isRead: Boolean
    """
    contains user id for unique identification in database
    """
    id: ID
    """
    contains createdAt of notification
    """
    createdAt: String
    """
    contains the timestamp of last time when user updated its profile
    """
    updatedAt: String
    """
    contains the notification details
    """
    notification: NotificationResponse
  }

  """
  contains all the fields related to notificationSeen
  """
  type NotificationSeenResponse {
    """
    contains id of notification
    """
    notificationId: Int
    """
    contains isRead of notification
    """
    isRead: Boolean
  }

  """
  contains all the fields related to notification
  """
  type PlayerNotificationResponse {
    """
    contains sender id of notification
    """
    senderId: Int
    """
    contains the sender type of notification
    """
    senderType: String
    """
    contains the reference id of notification
    """
    referenceId: Int
    """
    contains reference type of notification
    """
    referenceType: String
    """
    contains message of notification
    """
    message: String
    """
    contains user id for unique identification in database
    """
    userId: String
    """
    contains createdAt of notification
    """
    createdAt: String
    """
    contains the timestamp of last time when user updated its profile
    """
    updatedAt: String
    """
    contains notification id for unique identification in database
    """
    id: ID
    """
    Notification type
    """
    type: String
    """
    Notification content or message.
    """
    value: String
}

  """
  represents the input type of NotificationSeen mutation
  """
  input NotificationSeenInput {
    """
    contains notificationId
    """
    notificationId: String!
  }


  extend type Query {
    """
    fetches string quoting 'Hello word'
    """
    Notification: [NotificationReceiverResponse!] @isAuthenticated
  }

  extend type Mutation {
    """
    updating notification isRead
    takes input of type NotificationSeenInput and returns NotificationReceiverResponse
    """
    NotificationSeen(input: NotificationSeenInput): NotificationSeenResponse @isAuthenticated
  }

  extend type Subscription {
    """
    live notification
    """
    PlayerNotification(userId: String!): PlayerNotificationResponse!
  }
`
