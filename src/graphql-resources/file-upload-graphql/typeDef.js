import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  contains all the fields related to the uploaded file
  """
  type FileResponse {
    """
    unique file id for file identification in database
    """
    id: ID!
    """
    contains id of the user who uploaded the file
    """
    userId: ID!
    """
    contains boolean representing whether the file is verified or not
    """
    isVerified: Boolean!
    """
    contains the url to access the file
    """
    documentUrl: String!
    """
    contains the status of the file
    """
    status: String
    """
    contains the server generated file name
    """
    documentName: String!
    """
    contains the timestamp of creation
    """
    createdAt: String!
    """
    contains the timestamp of update
    """
    updatedAt: String!
    """
    contains the reason
    """
    reason: String
  }

  """
  represents the input type of UploadFiles mutation
  """
  input FileUploadInput{
    """
    contain the files to be uploaded, and is of type Upload (read apollo graphql documentation)
    """
    files: [Upload!]
  }

  """
  represents the input type of DeleteFile mutation
  """
  input FileDeleteInput {
    id: ID!
    documentUrl: String!
  }

  extend type Query {
    """
    fetches all the uploaded files of a user, requires auth token of the user in the header
    and returns array of FileResponse
    """
    Files: [FileResponse]! @isAuthenticated
  }

  extend type Mutation {
    """
    uploads a file with respect to the current logged in user,
    requires auth token of the user in the header,
    takes input of type FileUploadInput and returns array of FileResponse
    """
    UploadFiles(input: FileUploadInput!): [FileResponse]! @isAuthenticated

    """
    deletes a file with respect to the current logged in user,
    requires auth token of the user in the header,
    takes input of type FileDeleteInput and returns array of FileResponse
    """
    DeleteFile(input: FileDeleteInput!): Boolean! @isAuthenticated
  }
`
