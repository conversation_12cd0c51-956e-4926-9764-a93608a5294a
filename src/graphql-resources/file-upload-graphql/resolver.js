import DeleteFile from '../../services/file-upload/deleteFile'
import FetchFiles from '../../services/file-upload/fetchFiles'
import UploadFiles from '../../services/file-upload/uploadFiles'

/**
 * File upload resolver will handle file related queries and mutation.
 */
export const resolvers = {

  /**
   * All the queries related to the files are present in this query resolver
   */
  Query: {
    /**
     * It will fetch all the uploaded file details of a user
     * @returns {[FileResponse]}
     */
    Files: async (_, __, context) => {
      const result = await FetchFiles.execute(__, context)
      return result.result
    }
  },

  /**
   * all the mutations related to the files
   */
  Mutation: {

    /**
     * It will allow a user to upload file
     * @param {LoginInInput} input it contains file upload params and data of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {[FileResponse]}
     */
    UploadFiles: async (_, { input }, context) => {
      const result = await UploadFiles.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * It will allow a user to delete file
     * @param {LoginInInput} input it contains file params.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {[FileResponse]}
     */
    DeleteFile: async (_, { input }, context) => {
      const result = await DeleteFile.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }
  }
}
