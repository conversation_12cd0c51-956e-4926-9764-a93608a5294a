import { gql } from 'apollo-server-express'
export const typeDef = gql`
  scalar JSON

  """
  contains all the fields related to the uploaded file
  """
  type CMSPageLayoutResponse {
    """
    contains CMS id for unique identification in database
    """
    id: Int
    """
    contains the cms title
    """
    title: JSON
    """
    contains slug of the cms page
    """
    slug: String
    """
    contains the content of the cms
    """
    content: JSON
    """
    contains the flag to show in registeration page
    """
    enableCmsForRegister: Boolean
    type: Int
    link: String
  }

  type FooterLicenseImage {
    id: Int
    imageUrl: String
    redirectUrl: String
  }

  type FooterContentResponse {
    heading: String
    content_1: JSON
    content_2: JSON
    headerContent: String
    FooterLicenseImages: [FooterLicenseImage]
  }

  type CmsQueryResponse {
    CmsPagesContent: [CMSPageLayoutResponse]
    FooterContent: FooterContentResponse
  }

  extend type Query {
    """
    fetches all information layout or appearance of a tenant
    and returns TenantPageLayoutResponse
    """
    StaticContent: CmsQueryResponse!
  }
`
