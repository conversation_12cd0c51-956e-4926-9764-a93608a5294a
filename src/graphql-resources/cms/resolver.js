import CmsPages from './../../services/cms/cmsPage'

export const resolvers = {

  /**
   * All the queries related to the tenant-setting are present in this query resolver
   */
  Query: {
    /**
     * This resolver is responsible to fetch all the details/information of page layout
     * of the current tenant
     * @returns {CMSPageResponse}
     */
    StaticContent: async (_, __, context) => {
      const result = await CmsPages.execute(__, context)
      return result.result
    }
  }
}
