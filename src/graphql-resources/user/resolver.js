import { withFilter } from 'apollo-server-express'
import { SUBSCRIPTION_CHANNEL } from '../../common/constants'
import MobileLogin from '../../services/user/MobileLogin'
import MobileOTPVerification from '../../services/user/MobileOTPVerification'
import ChangeLoginPin from '../../services/user/changeLoginPin'
import ChangePassword from '../../services/user/changePassword'
import Currency from '../../services/user/currency'
import Deactivate from '../../services/user/deactivate'
import EmailOTPVerification from '../../services/user/emailOtpVerification'
import ForgotPassword from '../../services/user/forgotPassword'
import GenerateOTP from '../../services/user/generateOTP'
import HelloWorld from '../../services/user/helloWorld'
import Login from '../../services/user/login'
import LoginAsUser from '../../services/user/loginAsUser'
import LoginUsingPin from '../../services/user/loginUsingPin'
import MobileSignup from '../../services/user/mobileSignup'
import QrCodeLogin from '../../services/user/qrCodeLogin'
import RefreshToken from '../../services/user/refreshToken'
import RfidLogin from '../../services/user/rfidLogin'
import SignUp from '../../services/user/signUp'
import SocialMediaLogin from '../../services/user/socialMediaLogin'
import StoreNetworkAndIPDetails from '../../services/user/storeNetworkAndIPDetails'
import UpdateEmail from '../../services/user/updateEmail'
import UpdatePassword from '../../services/user/updatePassword'
import UpdatePhone from '../../services/user/updatePhone'
import UpdateProfile from '../../services/user/updateProfile'
import UpdateUserAvatar from '../../services/user/updateUserAvatar'
import User from '../../services/user/user'
import UserLogout from '../../services/user/userLogout'
import VerifyEmailToken from '../../services/user/verifyEmailToken'
import VerifyPasswordToken from '../../services/user/verifyForgotPassword'
import VerifyOTP from '../../services/user/verifyOTP'
import VerifyOTPAuthorized from '../../services/user/verifyOTPAuthorized'
import VerifyPhoneNumber from '../../services/user/verifyPhoneNumber'
import WebsiteDemoLogin from '../../services/user/websiteDemoLogin'
/**
 *  User resolver will handle user related queries and mutation.
 */
export const resolvers = {

  /**
   * All the queries related to the user are present in this query resolver
   */
  Query: {

    /**
     * This resolver is a test resolver
     * @returns {String}
     */
    HelloWorld: async (_, __, context) => {
      const result = await HelloWorld.execute(__, context)
      return result.result
    },

    /**
     * This resolver is responsible to fetch current logged in user's detail
     * @returns {UserResponse}
     */
    User: async (_, __, context) => {
      const result = await User.execute(__, context)
      return result.result
    },

    /**
     * This resolver is responsible to fetch currency of logged in user
     * @returns {[CurrencyResponse]}
     */
    Currency: async (_, __, context) => {
      const result = await Currency.execute(__, context)
      return result.result
    },

    /**
     * This resolver runs when user logs out
     * @returns {Boolean}
     */
    Logout: async (_, __, context) => {
      const result = await UserLogout.execute(__, context)
      return result.result
    }
  },

  /**
   * All the subscription related to the user are present in this subscription resolver
   */
  Subscription: {
    /**
     * This subscription is responsible to fetch current wallet balance user
     * @returns {UserWalletBalanceResponse}
     */
    UserWalletBalance: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE), (payload, args) => {
        return (parseInt(payload.UserWalletBalance.userId) === parseInt(args.userId))
      })
    },
    /**
     * This subscription is responsible to fetch to check tenant maintenance mode flag
     * @returns {MaintenanceResponse}
     */
    MaintenanceMode: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.IS_MAINTENANCE_MODE), (payload, args) => {
        return (parseInt(payload.MaintenanceMode.tenantId) === parseInt(args.tenantId))
        //  return true
      })
    },
    /**
     * This subscription is responsible to send user token expiration notification
     * @returns {UserTokenExpirationNotificationResponse}
     */
    UserTokenExpirationNotification: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.USER_TOKEN_EXPIRATION_NOTIFICATION), (payload, args) => {
        return (parseInt(payload.UserTokenExpirationNotification.userId) === parseInt(args.userId))
      })
    },
    /**
    * This subscription is responsible to multiple login notification
    * @returns {MultipleLoginNotificationResponse}
    */
    MultipleLoginNotification: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.MULTIPLE_LOGIN_NOTIFICATION), (payload, args) => {
        return (parseInt(payload.MultipleLoginNotification.userId) === parseInt(args.userId))
      })
    },
    /**
    * This subscription is responsible to user payment gateway deposit notification
    * @returns {UserPgDepositNotificationResponse}
    */
    UserPgDepositNotification: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.USER_PG_DEPOSIT_NOTIFICATION), (payload, args) => {
        return (parseInt(payload.UserPgDepositNotification.userWalletBalanceDetail.userId) === parseInt(args.userId))
      })
    },
    /**
     * This subscription is responsible to fetch Place bet details
     * @returns {TurboPlaceBetDetailsResponse}
     */
    TurboPlaceBetDetails: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.TURBO_BET_PLACED), (payload, args) => {
        return (parseInt(payload.TurboPlaceBetDetails.userId) === parseInt(args.userId))
      })
    },
    /**
     * This subscription is responsible to fetch Place bet details for cashier
     * @returns {CashierTurboPlaceBetDetailsResponse}
     */
    CashierTurboPlaceBetDetails: {
      subscribe: withFilter((_, __, { pubSub }) =>
        pubSub.asyncIterator(SUBSCRIPTION_CHANNEL.CASHIER_TURBO_BET_PLACED), (payload, args) => {
        return (parseInt(payload.CashierTurboPlaceBetDetails.userId) === parseInt(args.userId))
      })
    }
  },

  /**
   * all the mutations related to the user are present in mutation resolver
   */
  Mutation: {

    /**
     * This resolver is responsible for the creation of new user
     * @param {SignUpInput} input it contains all the params of the new user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    SignUp: async (_, { input }, context) => {
      const result = await SignUp.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { accessToken: result.result.token, user: result.result.user, resToken: result.result.resToken }
    },

    /**
     * This will change password of the authenticated user.
     * @param {string} oldPassword it contains current password of the user.
     * @param {string} newPassword it contains new password.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    ChangePassword: async (_, { oldPassword, newPassword }, context) => {
      const result = await ChangePassword.execute({ oldPassword, newPassword }, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * This will change Login PIN of the authenticated user.
     * @param {string} oldLoginPin it contains current Login PIN of the user.
     * @param {string} newLoginPin it contains new Login PIN.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    ChangeLoginPin: async (_, { oldLoginPin, newLoginPin }, context) => {
      const result = await ChangeLoginPin.execute({ oldLoginPin, newLoginPin }, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It will allow a user to log in
     * @param {LoginInInput} input it contains log in credentials of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    Login: async (_, { input }, context) => {
      const result = await Login.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time,
        success: result.result.success,
        message: result.result.message
      }
    },

    /**
     * it will store user ip and network details in login history based on events
     * @param {IpAndNetworkInput} input it contains ip and network details of the user.
     * @param {Context} context its the context object of gql, contains all the shared info between the resolvers.
     * @returns {IPAndNetworkResponse}
     */
    IPAndNetworkDetails: async (_, { input }, context) => {
      const result = await StoreNetworkAndIPDetails.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result;
    },

    /**
     * It will allow a user to log in using 6 Digit Authentication PIN
     * @param {LoginUsingPinInput} input it contains log in credentials of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    LoginUsingPin: async (_, { input }, context) => {
      const result = await LoginUsingPin.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time,
        success: result.result.success,
        message: result.result.message
      }
    },

    /**
    * It will allow a user to log in using RFID
    * @param {RfidLoginInput} input it contains log in credentials of the user.
    * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
    * @returns {AccessTokenResponse}
    */
    RfidLogin: async (_, { input }, context) => {
      const result = await RfidLogin.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time,
        success: result.result.success,
        message: result.result.message
      }
    },

    /**
    * It will allow a user to log in using QR Code
    * @param {QrCodeLoginInput} input it contains log in credentials of the user.
    * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
    * @returns {AccessTokenResponse}
    */
    QrCodeLogin: async (_, { input }, context) => {
      const result = await QrCodeLogin.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time,
        success: result.result.success,
        message: result.result.message
      }
    },

    /**
     * It will allow a user to log in
     * @param {MobileLoginInInput} input it contains log in credentials of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {MobileLoginResponse}
     */
    MobileLogin: async (_, { input }, context) => {
      const result = await MobileLogin.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { success: result.result.success, phone: result.result.phone, phoneCode: result.result.phoneCode }
    },

    /**
     * It will allow a user to log in with verify OTP
     * @param {MobileOtpVerifyInInput} input it contains log in credentials of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {MobileOtpVerifyResponse}
     */
    MobileOTPVerification: async (_, { input }, context) => {
      const result = await MobileOTPVerification.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { accessToken: result.result.token, user: result.result.user, resToken: result.result.resToken }
    },

    /**
 * It is responsible for signup using mobile
 * @param {MobileSignupInput} input it contains signup credentials of the user.
 * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
 * @returns {MobileSignupResponse}
 */
    MobileSignup: async (_, { input }, context) => {
      const result = await MobileSignup.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver phoneCode responsible to fetch resToken of logged in user
     * @returns {RefreshTokenResponse}
     */
    RefreshToken: async (_, { input }, context) => {
      const result = await RefreshToken.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * It is responsible for updating the user's email of the authenticated user, and will
     * send email verification token on the user's new email.
     * @param {UpdateEmailInput} input it contains email to be replaced with.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    UpdateEmail: async (_, { input }, context) => {
      const result = await UpdateEmail.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for updating the user's email, if the given otp matches the original one.
     * @param {EmailOTPVerificationInput} input it contains otp and new email .
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    EmailOTPVerification: async (_, { input }, context) => {
      const result = await EmailOTPVerification.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for updating the user's phone of the authenticated user, and will
     * send otp on the user's new phone number.
     * @param {UpdatePhoneInput} input it contains phone number and the countryCode to be replaced with.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    UpdatePhone: async (_, { input }, context) => {
      const result = await UpdatePhone.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for updating the user's profile of the authenticated user
     * @param {UpdateProfileInput} input it contains user details to be replaced with.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    UpdateProfile: async (_, { input }, context) => {
      const result = await UpdateProfile.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for verifying user's email, if the given token and userId
     * matches the original one.
     * @param {string} id It contains the user id.
     * @param {string} token It contains the generated user token.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    VerifyEmailToken: async (_, { id, token }, context) => {
      const result = await VerifyEmailToken.execute({ id, token }, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for verifying user's phone number, if the
     * given otp matches the original one.
     * @param {string} otp it contains otp which was sent to the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    VerifyPhoneNumber: async (_, { otp, countryCode, newPhone, phoneCode }, context) => {
      const result = await VerifyPhoneNumber.execute({ otp, countryCode, newPhone, phoneCode }, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },

    /**
     * It is responsible for soft deleting a user
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {Boolean}
     */
    Deactivate: async (_, { input }, context) => {
      const result = await Deactivate.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },

    /**
     * It is responsible for sending reset password token to the user's email
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {Boolean}
     */
    ForgotPassword: async (_, username, context) => {
      const result = await ForgotPassword.execute(username, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },

    /**
     * It is responsible for verifying user's password token, if the given token and userId
     * matches the original one.
     * @param {string} id It contains the user id.
     * @param {string} token It contains the generated password token.
     * @param {string} password It contains the password to be updated.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    VerifyPasswordToken: async (_, { id, token, password }, context) => {
      const result = await VerifyPasswordToken.execute({ id, token, password }, context)
      if (result.failed) {
        throw result.errors
      }

      return { ...result.result }
    },
    InsertTransaction: async (_, { input }, context) => {
      const result = await HelloWorld.execute(input, context)
      return result.result
    },

    /**
     * It is responsible to authorize mobile login
     * @param {string} id the id of the user
     * @param {string} token the token /otp to be verified
     * @param {string} type the type of otp sent
     * @param {Context} context context object of gql
     */
    VerifyOTP: async (_, { input }, context) => {
      const result = await VerifyOTP.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * It is responsible to authorize otp
     * @param {string} id the id of the user
     * @param {string} token the token /otp to be verified
     * @param {string} type the type of otp sent
     * @param {Context} context context object of gql
     */
    VerifyOTPAuthorized: async (_, { input }, context) => {
      const result = await VerifyOTPAuthorized.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It is responsible for generating the OTP for email and mobile
     * @param {string} id the id of user
     * @param {Context} conetxt context object of gql
     */
    GenerateOTP: async (_, { input }, context) => {
      const result = await GenerateOTP.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * It will allow a user to log in
     * @param {Context} context the context object of GraphQL, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    WebsiteDemoLogin: async (_, __, context) => {
      const result = await WebsiteDemoLogin.execute(__, context)
      if (result.failed) {
        throw result.errors
      }

      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time
      }
    },

    /**
     * It is responsible for updating the password of a user
     * @param {UpdatePasswordInput} input it contains the userId, newPassword, and confirmPassword.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UpdatePasswordResponse}
     */
    UpdatePassword: async (_, { input }, context) => {
      const result = await UpdatePassword.execute(input, context)

      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * It is responsible for updating the user's avatar image
     * @param {UpdateUserAvatarInput} input it contains user id and the new avatar image.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserResponse}
     */
    UpdateUserAvatar: async (_, { input }, context) => {
      const result = await UpdateUserAvatar.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },
    /*
    * It is responsible for logging in as a user by admin by passing token
     * @param {string} token it contains the token of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    LoginAsUser: async (_, { input }, context) => {
      const result = await LoginAsUser.execute(input, context)

      if (result.failed) {
        throw result.errors
      }
      return {
        accessToken: result.result.token,
        user: result.result.user,
        resToken: result.result.resToken,
        isAdminUser: result.result.isAdminUser,
        shouldPasswordUpdate: result.result.shouldPasswordUpdate,
        idleScreenLogoutTime: result.result.idle_screen_logout_time,
        success: result.result.success,
        message: result.result.message

      }
    },

    /**
     * This resolver is responsible for the social media login and signup of user
     * @param {SocialMediaLoginInput} input it contains all the params of the user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {AccessTokenResponse}
     */
    SocialMediaLogin: async (_, { input }, context) => {
      const result = await SocialMediaLogin.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return { accessToken: result.result.token, user: result.result.user, resToken: result.result.resToken, success: result.result.success }
    }
  },

  /**
  * It is responsible to fetch user details along with wallet details
  * @param {User} User
  * @param {Context} context
  * @returns {User}
  */
  UserResponse: {
    userWallet: async (User, _, { getUserWalletDataLoader, getDemoUserWalletDataLoader }) => {
      if (User.isDemoUser) {
        return getDemoUserWalletDataLoader.load(User.id)
      } else {
        const d = await getUserWalletDataLoader.load(User.id)
        return d
      }
    },

    userNotification: async (User, __, { getUserNotificationDataLoader }) => {
      return getUserNotificationDataLoader.load(User.id)
    }
  },

  /**
  * It is responsible to fetch wallet details along with currency details
  * @param {UserWalletResponse}
  * @param {Context} context
  * @returns {wallet}
  */
  UserWalletResponse: {
    userCurrency: async (UserWalletResponse, _, { getUserWalletCurrencyDataLoader }) => {
      return getUserWalletCurrencyDataLoader.load(UserWalletResponse.currencyId)
    }
  }

}
