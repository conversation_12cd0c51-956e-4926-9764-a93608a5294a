import { gql } from 'apollo-server-express'

export const typeDef = gql`
  # scalar JSON
  """
  contains access token, res token and user details who just logged in
  """
  type AccessTokenResponse {
    """
    contains the jwt token of the corresponding user
    """
    accessToken: String
    """
    contains user details
    """
    user: UserResponse!
    """
    contains the res token of the corresponding user
    """
    resToken: String

    """
    to know whether password should update or not
    """
    shouldPasswordUpdate: Boolean
    """
    to know whether screen is idlee or not
    """
    idleScreenLogoutTime: Int
    """
    contains a message if any
    """
    message: String
    """
    contains a success status
    """
    success: Boolean
  }

  type LoginAsUserResponse {
    """
    contains the jwt token of the corresponding user
    """
    accessToken: String
    """
    contains user details
    """
    user: UserResponse!
    """
    contains the res token of the corresponding user
    """
    resToken: String

    """
    to know whether password should update or not
    """
    shouldPasswordUpdate: Boolean
    """
    to know whether screen is idlee or not
    """
    idleScreenLogoutTime: Int
    """
    contains a message if any
    """
    message: String
    """
    contains a success status
    """
    success: Boolean
    """
    contains a boolean representing whether the user is an admin user or not
    """
    isAdminUser: Boolean
    """
    Bot User Check
    """
    isBotUser: Boolean
  }

  """
  Check Mobile Phone for Login user
  """
  type MobileOtpVerifyResponse {
    """
    contains the jwt token of the corresponding user
    """
    accessToken: String!
    """
    contains user details
    """
    user: UserResponse!
    """
    contains the res token of the corresponding user
    """
    resToken: String!
    """
    Bot User Check
    """
    isBotUser: Boolean
  }
  """
  Check Mobile Phone for Login user
  """
  type MobileLoginResponse {
    """
    true or false
    """
    success: Boolean!
    """
    contains user mobile
    """
    phone: String
    """
    contains user mobile code
    """
    phoneCode: String
    """
    contains user email
    """
    email: String
    """
    contains otp prefence type (phone, email)
    """
    otpPreference: Int
  }
  """
  Send OTP for Signup using Mobile
  """
  type MobileSignupResponse {
    """
    true or false
    """
    success: Boolean!
    """
    contains user mobile
    """
    phone: String!
    """
    contains user mobile code
    """
    phoneCode: String!
    """
    contains user name
    """
    userName: String!
  }
  """
  contains res token
  """
  type RefreshTokenResponse {
    """
    contains the token of the corresponding user
    """
    token: String!
    """
    resToken user
    """
    resToken: String!
  }
  """
  contains all the fields related to user's updated phone details
  """
  type UpdatePhoneResponse {
    """
    contains phone country code of the user
    """
    countryCode: String!
    """
    contains phone number of the user
    """
    newPhone: String!
    """
    contains phone code of the user
    """
    phoneCode: String!
    """
    contains a data where OTP has been sent
    """
    data: String
  }
  """
  contains fields related to user's phone verification details
  """
  type VerifyPhoneNumberResponse {
    """
    contains a boolean representing whether the phone of user is changed or not
    """
    phoneChanged: Boolean!
  }

  """
  contains fields related to user's update email details
  """
  type UpdateEmailResponse {
    """
    contains a data where OTP has been sent
    """
    data: String
    """
    contains user details
    """
    user: UserResponse!
  }

  """
  contains all the fields related to user's currency
  """
  type CurrencyResponse {
    """
    contains currency id for unique identification in database
    """
    id: ID!
    """
    contains name of the currency
    """
    name: String!
    """
    contains currency code
    """
    code: String!
    """
    contains country code
    """
    countryCode: String
  }

  """
  contains all the fields related to user
  """
  type UserResponse {
    """
    contains phone country code of the user
    """
    countryCode: String
    """
    contains the timestamp of a user
    """
    createdAt: String!
    """
    contains the date of birth of a user
    """
    dateOfBirth: String
    """
    contains email of a user
    """
    email: String
    """
    contains a boolean representing wether the email of user is verified or not
    """
    emailVerified: Boolean!
    """
    contains first name of a user
    """
    firstName: String
    """
    contains gender of a user
    """
    gender: String
    """
    contains user id for unique identification in database
    """
    id: ID!
    """
    contains last name of a user
    """
    lastName: String
    """
    contains phone number of a user
    """
    phone: String
    """
    contains a boolean representing wether the phone number of user is verified or not
    """
    phoneVerified: Boolean!
    """
    contains the number of time a user logged in
    """
    signInCount: Int
    """
    contains the last ip address from where user logged-in
    """
    signInIp: [JSON]
    """
    contains the timestamp of last time when user updated its profile
    """
    updatedAt: String
    """
    contains username of a user
    """
    userName: String!
    """
    contains nickname of a user
    """
    nickName: String
    """
    contains self exclusion date of a user
    """
    selfExclusion: String
    """
    contains wallet balance of a user
    """
    userWallet: UserWalletResponse
    """
    contains phone code of the user
    """
    phoneCode: String
    """
    contains document verification of user
    """
    isDocumentVerified: Boolean
    """
    contains city of new user
    """
    city: String
    """
    contains zip code new user
    """
    zipCode: String
    """
    contains kyc status of user
    """
    kycDone: Boolean
    """
    contains deposit bonus status of user
    """
    activeDepositBonus: Boolean
    """
    contains notification of the user
    """
    userNotification: [NotificationReceiverResponse]
    """
    gets you the VIP level of user
    """
    vipLevel: String
    """
    Indicates whether the user is a demo user.
    """
    isDemoUser: Boolean
    """
    contains national id of logged-in user
    """
    nationalId: String
    """
    Indicates whether the user is allowed to create withdrawal requests.
    """
    enableWithdrawRequests: Boolean

    """
    Indicates smarticoUserId for user used in smartico dashboard
    """
    smarticoUserId: String
    """
    Indicates the file name or URL of the user's avatar image, used for profile display.
    """
    avatarImage: String
    """
    Indicates login type of user
    """
    isAdminUser: Boolean
    """
    contains otp preference of user
    """
    otpPreference: Int
    """
    financial activity enabled or not
    """
    isFinancialActivityEnabled: Boolean
    """
    Indicates the category type for User.
    """
    categoryType: Int
    """
    User's Referral Code
    """
    referralCode: String
    """
    Bot User Check
    """
    isBotUser: Boolean
    """
    Referral Code Check
    """
    isReferralCodeBlocked: Boolean
    creationType: Int
  }

  """
  user update avatar image
  """
  type UpdateAvatarResponse {
    success: Boolean
    message: String
    avatarImage: String
  }

  """
  user wallet amount
  """
  type UserWalletResponse {
    amount: Float
    id: ID
    currencyId: ID
    nonCashAmount: Float
    oneTimeBonusAmount: Float
    sportsFreebetAmount: Float
    userCurrency: CurrencyResponse
    bonusAmount: Float
    rolloverBalance: Float
    initialRolloverBalance: Float
    rolloverAchived: Float
    kind: String
    code: String
    bonusesInQueue: JSON
  }

  """
  wallet balance user
  """
  type UserWalletBalanceResponse {
    """
    userId of active user
    """
    userId: String!
    """
    live wallet balance
    """
    walletBalance: Float!
    """
    live non cash wallet balance
    """
    nonCashAmount: Float!
    """
    live otb cash wallet balance
    """
    oneTimeBonusAmount: Float
    """
    live sports freebet balance
    """
    sportsFreebetAmount: Float
    """
    live bonus amount
    """
    bonusAmount: Float
    kind: String
    code: String
    bonusesInQueue: JSON
  }

  """
  token expiration notification
  """
  type UserTokenExpirationNotificationResponse {
    """
    userId of active user
    """
    userId: String!
    """
    notification message
    """
    message: String!
  }

  """
  multiple user login notification
  """
  type MultipleLoginNotificationResponse {
    """
    userId of active user
    """
    userId: String!
    """
    multiple login status of user
    """
    multipleLogin: Boolean!
    """
    contains the previous login token of the user
    """
    token: String!
  }

  """
  user payment gateway deposit notification
  """
  type UserPgDepositNotificationResponse {
    message: String
    success: Boolean
    userWithdrawAmount: Float
    userWalletBalanceDetail: UserWalletBalanceDetail
  }

  type UserWalletBalanceDetail {
    walletBalance: Float
    userId: String
    nonCashAmount: Float
    oneTimeBonusAmount: Float
    sportsFreebetAmount: Float
  }

  type OTPResponse {
    status: Boolean!
    data: String!
    userBankDetails : UserBankDetailsResponse
  }

  """
  represents the input type of SignUp mutation
  """
  input SignUpInput {
    """
    contains first name of the new user
    """
    firstName: String
    """
    contains last name of the new user
    """
    lastName: String
    """
    contains cellphone country code of the new user
    """
    countryCode: String
    """
    contains email of the new user
    """
    email: String
    """
    contains password of the new user
    """
    password: String!
    """
    contains cellphone number of the new user
    """
    phone: String
    """
    contains username of the new user
    """
    userName: String!
    """
    contains nickname of the new user
    """
    nickName: String
    """
    contains currency id of the new user
    """
    currencyId: String!
    """
    contains date of birth new user
    """
    dateOfBirth: String
    """
    contains phone code new user
    """
    phoneCode: String
    """
    contains city of new user
    """
    city: String
    """
    contains zip code new user
    """
    zipCode: String
    """
    contains affiliate token new user
    """
    affiliateToken: String
    """
    contains OTP for mobile signup
    """
    otp: String
    """
    contains promo code for bonus
    """
    promoCode: String
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains national id of the user
    """
    nationalId: String
    """
    contains vip code
    """
    vipKey: String
    """
    contains avatar image
    """
    avatarImage: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
    affiliatedData: String
    """
    contains otp prefence type (phone, email)
    """
    otpPreference: Int
    """
    referral code of the user
    """
    referralCode: String
    """
    contains alanbase click id of user
    """
    alanbaseClickId: String
    """
    contains wynta click id of user
    """
    wyntaClickId: String
  }

  """
  represents the input type of Login mutation
  """
  input LoginInInput {
    """
    contains username or email of the user
    """
    userNameOrEmail: String!
    """
    contains password of the user
    """
    password: String!
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    city: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float


  }

  """
  represents the input type of Login mutation
  """
  input LoginUsingPinInput {
    """
    contains username or email of the user
    """
    userNameOrEmail: String!
    """
    contains Phone of the user
    """
    phone: String
    """
    contains password of the user
    """
    loginPin: String!
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains phone code new user
    """
    phoneCode: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    city: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
  }

  """
  represents the input type of RfidLogin mutation
  """
  input RfidLoginInput {
    """
    contains RFID Token of the user
    """
    rfidToken: String!
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    city: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
  }

  """
  represents the input type of QR Code Login mutation
  """
  input QrCodeLoginInput {
    """
    contains RFID Token of the user
    """
    qrCode: String!
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    city: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
  }

  """
  represents the input type of Login mutation
  """
  input MobileLoginInInput {

    """
    contains Phone of the user
    """
    phone: String
    """
    contains Phone Code of the phone number
    """
    phoneCode: String
    """
    contains otp login status
    """
    resendOtp: Boolean
    """
    type defines the type of OTP
    """
    loginType: String
    """
    contains email of the user
    """
    email: String
    """
    contains otp prefence type (phone, email) of the user
    """
    otpPreference: Int
    """
    Contains if request is from kiosk or not.
    """
    isKioskMachine: Boolean
    ip: String
  }

  """
  represents the input type of MobileSignup mutation
  """
  input MobileSignupInput {
    """
    contains Phone of the user
    """
    phone: String
    """
    contains Phone Code of the phone number
    """
    phoneCode: String
    """
    contains Username of user
    """
    userName: String!
    """
    contains email of the new user
    """
    email: String
    """
    contains currency id of the new user
    """
    currencyId: String!
    """
    contains national id of the new user
    """
    nationalId: String
    """
    contains otp prefence type (phone, email)
    """
    otpPreference: Int
  }

  """
  represents the input type of Login Mobile verification
  """
  input MobileOtpVerifyInInput {

    """
    contains Phone of the user
    """
    phone: String
    """
    contains Phone Code of the phone number
    """
    phoneCode: String
    """
    contains Phone OTP of the phone number
    """
    otp: String
    """
    contains Password of user
    """
    password: String
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    city: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
    email : String
    """
    contains otp prefence type (phone, email)
    """
    otpPreference: Int
  }

  input TransactionInput {
    """
    contains transaction id for unique identification in database
    """
    id: ID!
    """
    contains transaction amount involved in the current transaction
    """
    amount: Float
    """
    contains the source wallet id
    """
    sourceWalletId: Int
    """
    contains the target wallet id
    """
    targetWalletId: Int
    """
    contains the balance of the source before the transaction occurred
    """
    sourceBeforeBalance: Float
    """
    contains the balance of the source after the transaction occurred
    """
    sourceAfterBalance: Float
    """
    contains the type of transaction that-is 0-debit, 1-credit, 2-rollback
    """
    transactionType: Int
    """
    contains the status of transaction
    """
    status: String
    """
    contains any comments related to the transaction
    """
    comments: String
    """
    contains timestamp of occurrence of the transaction
    """
    createdAt: String
    """
    contains actioneeId of occurrence of the transaction
    """
    actioneeId: Int
    """
    contains transactionId of occurrence of the transaction
    """
    transactionId: String
  }

  """
  refresh token
  """
  input RefreshTokenInput {
    resToken: String!
    userId: String!
  }
  """
  represents the input type of UpdateEmail mutation
  """
  input UpdateEmailInput {
    """
    contains email of logged-in user
    """
    newEmail: String
  }
  """
  represents the input type of EmailOTPVerification mutation
  """
  input EmailOTPVerificationInput {
    """
    contains email of logged-in user
    """
    newEmail: String
    """
    contains Phone OTP of the phone number
    """
    otp: String
  }

  """
  represents the input type of UpdateProfile mutation
  """
  input UpdateProfileInput {
    """
    contains username of logged-in user
    """
    userName: String
    """
    contains nickname of logged-in user
    """
    nickName: String
    """
    contains first name of logged-in user
    """
    firstName: String
    """
    contains last name of logged-in user
    """
    lastName: String
    """
    contains date of birth of logged-in user
    """
    dateOfBirth: String
    """
    contains gender of logged-in user
    """
    gender: String
    """
    contains national id of logged-in user
    """
    nationalId: String
    """
    contains otp prefence type (phone, email)
    """
    otpPreference: Int
    """
    contains whether otp preference should be update or not
    """
    isOtpUpdate: Boolean
  }

  """
  represents the input type of UpdatePhone mutation
  """
  input UpdatePhoneInput {
    """
    contains phone country code of logged-in user
    """
    countryCode: String!
    """
    contains new phone number of logged-in user
    """
    newPhone: String!
    """
    contains phone code of logged-in user
    """
    phoneCode: String!
    """
    contains whether api is called for social media login
    """
    isSocialMedia: Boolean
  }

  type MaintenanceResponse {
    """
    tenantId of active user
    """
    id : Int
    tenantId: Int
    type: String
    siteDown: Boolean
    title : String
    announcementTitle: JSON
    isAnnouncementActive: Boolean
  }

  type LogoutResponse {
    data: Boolean
  }

  input VerifyOTPInput {
    """
    OTP to verify
    """
    token: String!
    """
    It tells new password if its there
    """
    newPassword: String
    """
    contains Phone of the user
    """
    phone: String
    """
    contains Phone Code of the phone number
    """
    phoneCode: String
    email: String
    otpPreference: Int
    """
    It tells new pin if its there
    """
    newLoginPin: String
    """
    type defines the type of OTP
    """
    loginType: String
  }

  input VerifyOTPInputAuthorized {
    """
    OTP to verify
    """
    token: String!
    userBankId: Int
  }

  input GenerateOTPInput {
    """
    Old password if exists
    """
    oldPassword: String
    """
    Old Login PIN if exists
    """
    oldLoginPin: String
  }

  """
  input type for UpdatePassword mutation
  """
  input UpdatePasswordInput {
    userName: String!
    newPassword: String!
    confirmPassword: String!
  }

  """
  input type for UpdateUserAvatarInput mutation
  """
  input UpdateUserAvatarInput {
    avatarImage: String
    file: Upload
  }

  """
  response type for UpdatePassword mutation
  """
  type UpdatePasswordResponse {
    success: Boolean!
    message: String!
  }

  """
  input type for LoginAsUser mutation
  """
  input LoginAsUserInput {
    token: String!
  }

  """
  input type for SocialMediaLogin mutation
  """
  input SocialMediaLoginInput {
    socialMediaLoginType: Int!
    accessToken: String
    """
    contains User Login Info
    """
    ip: String
    network: String
    version: String
    region: String
    countryName: String
    postal: String
    latitude: Float
    longitude: Float
    """
    contains device ID of the user
    """
    deviceId: String
    """
    contains device Type of the user
    """
    deviceType: String
    """
    contains device Model of the user
    """
    deviceModel: String
    """
    contains country code of the new user
    """
    countryCode: String
    """
    contains city of new user
    """
    city: String
    """
    contains cellphone number of the new user
    """
    phone: String
    """
    contains currency id of the new user
    """
    currencyId: String
    isSignUp: Boolean!
    """
    contains promo code for bonus
    """
    promoCode: String
    """
    referral code of the user
    """
    referralCode: String
    """
    contains alanbase click id of user
    """
    alanbaseClickId: String
    """
    contains user information
    """
    data: JSON
    """
    contains wynta click id of user
    """
    wyntaClickId: String
  }

  """
  response type for SocialMediaLogin mutation
  """
  type SocialMediaLoginResponse {
    success: Boolean
    """
    contains the jwt token of the corresponding user
    """
    accessToken: String
    """
    contains user details
    """
    user: UserResponse
    """
    contains the res token of the corresponding user
    """
    resToken: String
    """
    Bot User Check
    """
    isBotUser: Boolean
  }

  """
  Turbo Place Bet Details
  """
  type TurboPlaceBetDetailsResponse {
    """
    userId of active user
    """
    userId: String!
    """
    Placed Bet Market Id
    """
    marketId: String!
    """
    Placed Bet Transaction Id
    """
    transactionId: String!
    """
    Game Provider Name
    """
    provider: String!
  }

  """
  Cashier Turbo Place Bet Details
  """
  type CashierTurboPlaceBetDetailsResponse {
    """
    userId of active user
    """
    userId: String!
    """
    Placed Bet Market Id
    """
    marketId: String!
    """
    Placed Bet Transaction Id
    """
    transactionId: String!
    """
    Game Provider Name
    """
    provider: String!
  }

  input IpAndNetworkInput {
    ip: String!
    network: String!
    event: String!
    version: String
    deviceId: String
    deviceType: String
    deviceModel: String
    data: JSON
  }

  type IPAndNetworkResponse {
    success: Boolean!
    message: String
  }

  input DeactivateInput {
    password: String!
    remarks: String
  }

  extend type Query {
    """
    fetches string quoting 'Hello word'
    """
    HelloWorld: String
    """
    fetches all the user details, requires auth token of the user in the header,
    and returns UserResponse
    """
    User: UserResponse! @isAuthenticated
    """
    fetches all the allowed currency for a tenant,
    and returns array of CurrencyResponse
    """
    Currency: [CurrencyResponse]!
    """
    Logout the user
    """
    Logout: LogoutResponse! @isAuthenticated
  }

  extend type Mutation {
    """
    creates a new user on based of given user details,
    takes input of type FileUploadInput and returns array of FileResponse
    """
    SignUp(input: SignUpInput!): AccessTokenResponse!
    """
    verifies the email token sent on the email to verify the email,
    takes user id and email token as an input and returns UserResponse
    """
    VerifyEmailToken(id: String!, token: String!): UserResponse!
    """
    verifies the otp sent on the phone to verify the phone number,
    requires auth token of the user in the header,
    takes otp as an input and returns UserResponse
    """
    VerifyPhoneNumber(otp: String!, countryCode: String!, newPhone: String!, phoneCode: String!): VerifyPhoneNumberResponse! @isAuthenticated
    """
    updates details of logged-in user,
    requires auth token of the user in the header,
    takes input of type UpdateProfileInput and returns UserResponse
    """
    UpdateProfile(input: UpdateProfileInput!): UserResponse! @isAuthenticated
    """
    updates password of a logged-in user,
    requires auth token of the user in the header,
    takes new password, old password as an input and returns UserResponse
    """
    ChangePassword(newPassword: String!, oldPassword: String!): UserResponse! @isAuthenticated
    """
    requires auth token of the user in the header,
    take mobile number of user from database and send OTP,
    takes input of type UpdateEmailInput and returns UserResponse
    """
    UpdateEmail(input: UpdateEmailInput!): UpdateEmailResponse! @isAuthenticated
    """
    verifies the otp sent on the phone,
    updates email of logged-in user,
    requires auth token of the user in the header,
    takes input of type EmailOTPVerificationInput and returns UserResponse
    """
    EmailOTPVerification(input: EmailOTPVerificationInput!): UserResponse! @isAuthenticated
    """
    updates phone of logged-in user,
    requires auth token of the user in the header,
    takes input of type UpdatePhoneInput and returns UpdatePhoneResponse
    """
    UpdatePhone(input: UpdatePhoneInput!): UpdatePhoneResponse! @isAuthenticated
    """
    logs a user in after verifying the user credentials,
    takes input of type LoginInInput and returns AccessTokenResponse
    """
    Login(input: LoginInInput!): AccessTokenResponse!
    """
    Take mobile number or email of user and check in database and send OTP,
    takes input of type MobileLoginInInput and returns True/false
    """
    MobileLogin(input: MobileLoginInInput!): MobileLoginResponse
    """
    Take mobile number of user for signup and send OTP,
    takes input of type MobileSignupInput and returns True/false
    """
    MobileSignup(input: MobileSignupInput!): MobileSignupResponse
    """
    Take mobile number of user check in database and verify OTP,
    takes input of type MobileOtpVerifyInInput and returns user data with token
    """
    MobileOTPVerification(input: MobileOtpVerifyInInput!): MobileOtpVerifyResponse
    """
    resToken user
    """
    RefreshToken(input: RefreshTokenInput!): RefreshTokenResponse!
    """
    deactivates a user account,
    takes user password as an input and returns a boolean
    """
    Deactivate(input: DeactivateInput!): Boolean! @isAuthenticated
    """
    send a password reset token on the user's email,
    takes user's username as an input and returns a boolean
    """
    ForgotPassword(userNameOrEmail: String!): Boolean!
    """
    verifies the reset password token sent on the email,
    takes user id, password token, new password as an input and returns UserResponse
    """
    VerifyPasswordToken(id: String!, token: String!, password: String!): UserResponse!
    """
    Used to insert transaction
    """
    InsertTransaction(input:TransactionInput):TransactionListResponse
    """
    Verifies the mobile reset password otp
    """
    VerifyOTP(input: VerifyOTPInput): OTPResponse
    """
    Verifies the other otps
    """
    VerifyOTPAuthorized(input: VerifyOTPInputAuthorized): OTPResponse @isAuthenticated
    """
    Generate the otp for authorization tasks
    """
    GenerateOTP(input: GenerateOTPInput): OTPResponse @isAuthenticated
    """
    Logs a demo user in based on the tenant ID from the context,
    and returns an AccessTokenResponse.
    """
    WebsiteDemoLogin: AccessTokenResponse!
    """
    Updates the password of the user.
    """
    UpdatePassword(input: UpdatePasswordInput!): UpdatePasswordResponse!
    """
    updates the user's avatar image
    """
    UpdateUserAvatar(input: UpdateUserAvatarInput!): UpdateAvatarResponse! @isAuthenticated
    """
    Login as user by admin by passing token
    """
    LoginAsUser(input: LoginAsUserInput!): LoginAsUserResponse!
    """
    Login by social media
    """
    SocialMediaLogin(input: SocialMediaLoginInput!): SocialMediaLoginResponse!
    """
    updates Login PIN of a logged-in user,
    requires auth token of the user in the header,
    takes new Login PIN, old Login PIN as an input and returns UserResponse
    """
    ChangeLoginPin(newLoginPin: String!, oldLoginPin: String!): UserResponse! @isAuthenticated
    """
    logs a user in after verifying the user credentials,
    takes input of type LoginUsingPinInput and returns AccessTokenResponse
    """
    LoginUsingPin(input: LoginUsingPinInput!): AccessTokenResponse!
    """
    logs a user in after verifying the RFID Token,
    takes input of type RfidLoginInput and returns AccessTokenResponse
    """
    RfidLogin(input: RfidLoginInput!): AccessTokenResponse!
    """
    logs a user in after verifying the QR Code,
    takes input of type QrCodeLoginInput and returns AccessTokenResponse
    """
    QrCodeLogin(input: QrCodeLoginInput!): AccessTokenResponse!
    """
    stores user ip and network details in login history
    """
    IPAndNetworkDetails(input: IpAndNetworkInput!): IPAndNetworkResponse! @isAuthenticated
  }

  extend type Subscription {
    """
    live wallet balance user
    """
    UserWalletBalance(userId: String!): UserWalletBalanceResponse!
    """
    check website maintenance mode
    """
    MaintenanceMode(tenantId: Int!): MaintenanceResponse!
    """
    user token expiration notification
    """
    UserTokenExpirationNotification(userId: String!): UserTokenExpirationNotificationResponse!
    """
    multiple login notification
    """
    MultipleLoginNotification(userId: String!): MultipleLoginNotificationResponse!
    """
    user payment gateway deposit notification
    """
    UserPgDepositNotification(userId: String!): UserPgDepositNotificationResponse!
    """
    user Turbo placebet notification
    """
    TurboPlaceBetDetails(userId: String!): TurboPlaceBetDetailsResponse!
    """
    Cashier Turbo placebet notification
    """
    CashierTurboPlaceBetDetails(userId: String!): CashierTurboPlaceBetDetailsResponse!
  }
`
