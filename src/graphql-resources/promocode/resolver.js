import CheckPromoCode from './../../services/promocode/checkPromoCode'
import GetAllActivePromoCodes from './../../services/promocode/getAllActivePromoCodes'
export const resolvers = {

  /**
   * All the queries related to the promo code are present in this query resolver
   */
  Query: {
    /**
     * This resolver is responsible for getting all active promo codes
     * @returns {PromoCode[]}
     */
    AllActivePromoCodes: async (_, { input }, context) => {
      const result = await GetAllActivePromoCodes.execute(input, context)
      return result?.result
    },

    /**
     * This resolver is responsible for checking promo code
     * @param {checkPromoInput} input it contains promoCode and currency
     * @returns {CheckPromoCodeResponse}
     */
    CheckPromoCode: async (_, { input }, context) => {
      const result = await CheckPromoCode.execute(input, context)
      return result.result
    }

  },

  PromoCodeResponse: {
    currency: async (PromoCodeResponse, _, { getUserWalletCurrencyDataLoader }) => {
      if (PromoCodeResponse.currencyId) {
        return getUserWalletCurrencyDataLoader.load(PromoCodeResponse.currencyId)
      }
      return null
    }
  }
}
