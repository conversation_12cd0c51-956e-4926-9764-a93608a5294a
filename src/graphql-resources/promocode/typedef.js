import { gql } from 'apollo-server-express'
export const typeDef = gql`

  """
  contains all the fields related to the promo code response
  """
  type CheckPromoCodeResponse {
    isValid: Boolean!
    message: String!
  }

  type PromoCodeResponse {
    id: Int!
    promoName: String!
    code: String!
    description: String
    bonusAmount: Float
    currency: CurrencyResponse
    image: String
    termsAndConditions: JSON
    validFrom: String
    validTill: String
    promoCodeBonusType: String
    walletType: String
  }

  input CheckPromoInput {
    """
    contains promocode
    """
    promoCode: String!
    """
    contains selected currency
    """
    currencyId: String!

  }

  extend type Query {
    AllActivePromoCodes: [PromoCodeResponse]

    """
    check promo code during user registration and return CheckPromoCodeResponse
    """
    CheckPromoCode(input: CheckPromoInput!): CheckPromoCodeResponse!
  }
`
