import { gql } from 'apollo-server-express'

export const typeDef = gql`
  # scalar JSON
  """
  contains all the fields related to the tenant page layout
  """
  type TenantPageLayoutResponse {
    """
    contains the page page layout of a tenant, in  stringified format
    """
    layout: String!
    """
    contains logo of the tenant loading screen
    """
    logoUrl: String
    """
    contains default language title of a tenant
    """
    languageTitle: String!
    """
    contains default language of a tenant
    """
    language: String!
    """
    contains page theme of a tenant
    """
    theme: String!
    """
    contains user login type of a tenant
    """
    userLoginType: String
    """
    contains social media login type of a tenant
    """
    socialMediaLoginType: String
    """
    contains fabIconUrl of a tenant
    """
    fabIconUrl: String
    """
    contains domain of a tenant
    """
    domain: String
    """
    contains tenantId of a tenant
    """
    tenantId:Int
    """
    contains the whatsapp number of tenant
    """
    whatsappNumber: String
    """
    contains the calling number of tenant
    """
    callingNumber: String

    """
    contains the calling numbers of tenant(JSON)
    """
    callingNumbers: JSON
    """
    contains the custom css url
    """
    customCssUrl: String
    """
    contains the signup Popup Image Url of tenant
    """
    signupPopupImageUrl: String
    """
    contains the powered by of tenant
    """
    poweredBy: String
    """
    contains the name of tenant
    """
    tenantName: String!
    """
    contains the chatbot of tenant
    """
    chatbotToken: String
    """
    contains the google script of tenant
    """
    googleAnalyticsScriptsCode: String
    """
    contains google Recaptcha Keys of a tenant
    """
    googleRecaptchaKeys: String
    """
    contains the ezugi operator of tenant
    """
    operator: OperatorResponse
    """
    contains the information regarding tenant user registration fields
    """
    registrationFields: RegistrationFieldsResponse
    """
    contains the information regarding tenant user minimum withdrawal limit
    """
    userMinWithdrawalLimit: String

    """
    contains the information regarding tenant user maximun withdrawal limit
    """
    userMaxWithdrawalLimit: String
    """
    contains the information regarding tenant user minimum deposit limit
    """
    userMinDepositLimit: String
    """
    contains the information regarding tenant user maximum deposit limit
    """
    userMaxDepositLimit: String
    """
    contains the information regarding tenant socket url
    """
    socketUrl: String
    """
    contains the information regarding tenant ezugi games live updates
    """
    ezugiGamesLiveUpdates: Boolean
    """
    contains the social media details
    """
    socialMediaDetails: [socialMediaDetailsResponse]
    """
    gets you the check if payment gateways categories are enabled
    """
    paymentGatewayCategoriesEnable: Boolean!
    """
    contains check for forgot password type
    """
    forgotPasswordOption: String
    """
    contains google tag manager code
    """
    googleTagManagerCode: String
    """
    contains google search console code
    """
    googleSearchConsoleCode: String
    """
    contains otp enable modules
    """
    otpEnable: String
    """
    contains allowed modules
    """
    allowedModules: String
    """
    maximum number of account a user can have
    """
    maxBankAccountLimit: Int
    """
    contains the information regarding tenant base currency
    """
    tenantBaseCurrency: String
    """
    contains the information regarding evolution game secondary currency
    """
    evolutionGameSecondaryCurrency: String
    """
    contains the chatbot type of tenant
    """
    chatbotType: String
    """
    contains the chatbot Mobile number of tenant
    """
    chatbotMobileNumber: String
    """
    CMS for home page
    """
    cms: [JSON]
    """
    contains fields related to page banners
    """
    banners: [TenantBannerResponse]!
    """
    contains fields related to sports content
    """
    sportsContent: [SportsContentResponse]!
    """
    contains allowed languages
    """
    allowedLanguages: [JSON]
    """
    contains google client id for social media login
    """
    googleLoginCLientId: String
    """
    contains facebook login App id for social media login
    """
    facebookLoginAppId: String
    """
    contains minimum wallet amount
    """
    minimumWalletAmount: Float
    loaderType: Int
    chatbotMobileToken: String
    kioskAppAutoLogout: String
  }

  type faqCategoriesResponse {
    id: ID!
    name: JSON
    image: String
    slug: String
    faqs: [faqsResponse]
  }

  type faqsResponse {
    question: JSON
    answer: JSON
    featured : Boolean
  }

  type faqSearchResponse {
    """
    contains FAQ count
    """
    count: Int
    """
    contains fields related to FAQs
    """
    faqs: [faqsResponse]
  }


  type RegistrationFieldsResponse {
    firstName: Int
    lastName: Int
    email: Int
    username: Int
    nickName: Int
    dob: Int
    currency: Int
    phone: Int
    city: Int
    zipCode: Int
    password: Int
    confirmPassword: Int
    eighteenYearCheck: Int
    promoCode: Int
    nationalId: Int
  }

  """
  contains name, image, redirectUrl regarding social media
  """
  type socialMediaDetailsResponse {
    id: Int
    name: String
    image: String
    redirectUrl: String
  }

  """
  contains id of the operator
  """
  type OperatorResponse {
    id: String
  }
  """
  contains id, name, ordering, path, component
  """
  type menuResponse {
    id: Int
    name: String
    ordering: Int
    path: String
    component: String,
    componentName: String
    image: String
    flash: Boolean
    loginRedirect: Boolean
    slug: String
    cms: [JSON]
  }
  """
  contains all the fields related to the menu items of a page
  """
  type MenuItemResponse {
    """
    contains menu item id for unique menu item identification in database
    """
    id: Int!
    """
    contains menu item name
    """
    name: String!
    """
    contains the order in which the menu item will be displayed
    """
    order: Int!
    """
    contains a boolean for showing wether the menu items is active or not
    """
    active: Boolean!
    """
    contains a boolean for showing wether the menu items is featured or not
    """
    featured: Boolean!
    """
    contains fields related to casino items
    """
    casinoItem: CasinoItemResponse!

  }

  type CasinoItemResponse {
    """
    contains casino item id for unique item identification in database
    """
    id: ID!
    """
    contains unique game id provided by ezugi
    """
    uuid: String!
    """
    contains menu items image url
    """
    image: String
    """
    contains menu items type
    """
    itemType: String
    """
    contains menu item provider
    """
    provider: String
    """
    contains menu item technology
    """
    technology: String
    """
    contains a boolean for showing wether the menu items has lobby or not
    """
    hasLobby: Boolean
    """
    contains a boolean for showing wether the menu items is mobile or not
    """
    isMobile: Boolean
    """
    contains a boolean for showing wether the menu items has free spins or not
    """
    hasFreeSpins: Boolean
    """
    contains the provider name
    """
    providerName: String
  }

  """
  contains all the fields related to the page management
  """
  type PageManagementResponse {
    """
    contains array of all the pages of a particular tenant
    """
    pages: [PageResponse]!
    """
    contains fields related to page banners
    """
    banners: [TenantBannerResponse]!
  }

  """
  contains all the fields related to the menu
  """
  type MenuResponse {
    """
    contains menu id for menu identification in database
    """
    id: Int!
    """
    contains menu name
    """
    name: String!
    """
    contains the order in which the menu will be displayed
    """
    menuOrder: Int!
    """
    contains the menu icon url
    """
    casinoMenu: CasinoMenuResponse
    """
    contains fields related to menu items
    """
    menuItems(limit: Int, offset: Int, menuId: Int): [MenuItemResponse]!

    totalMenuItem(menuId: Int): [TotalMenuItemRes]

    cms: [JSON]
  }

  type TotalMenuItemRes {
    """
    contains menu items count
    """
    count: Int
  }

  """
  contains menu icon url
  """
  type CasinoMenuResponse {
    """
    contains the menu icon url
    """
    imageUrl: String
    """
    contains the casino menu id
    """
    id: ID!
    """
    contains the casino menu name
    """
    name: String
    """
    contains the casino menu type
    """
    menuType: String
    """
    contains the casino menu name
    """
    menuOrder: Int
    """
    contains the casino menu name
    """
    enabled: Boolean!
    """
    contains the casino menu name
    """
    tenantId: ID!
  }

  """
  contains all the fields related to the page
  """
  type PageResponse {
    """
    contains page id for unique identification in database
    """
    id: Int!
    """
    contains title of the page
    """
    title: String!
    """
    contains the order in which the page will be displayed
    """
    order: Int!
    """
    contains the boolean showing whether the page is active or not
    """
    enabled: Boolean!
    """
    contains the id of the current tenant
    """
    tenantId: Int!
    """
    contains the fields related to page menu
    """
    menus(pageId: Int): [MenuResponse]!
    """
    Gets the data for content management
    """
    cms: [JSON]

  }

  """
  contains all the fields related to the page banners
  """
  type TenantBannerResponse {
    """
    contains banner id for unique identification in database
    """
    id: Int
    """
    contains the type of the banner
    """
    bannerType: String
    """
    contains name of the banner
    """
    name: JSON
    """
    contains the url of the banner
    """
    imageUrl: String
    """
    contains the url of the banner
    """
    redirectUrl: String
    """
    contains order of the page banner
    """
    order: Boolean
    """
    contains the game name of the banner
    """
    gameName: String
    """
    contains the open table of the banner
    """
    openTable: String
    """
    contains the provider name of the banner
    """
    providerName: String
    """
    contains the mobile image url of the banner
    """
    imageUrlMobile: String
    """
    Indicates the type of redirect for the banner
    """
    redirectingType: Int
    """
    Represents the text of the button in banner
    """
    buttonText: JSON
    """
    Represents the content of the banner
    """
    content: JSON
    """
    Represents the tagged pages in JSON format
    """
    taggedPages: JSON
    actionType: Int
  }

  """
  contains pop-up information
  """
  type PopupResponse {
    """
    contains popup id for unique identification in database
    """
    id: Int
    """
    contains the title of the popup
    """
    title: JSON
    """
    contains the code of the popup
    """
    popupCode: String
    """
    contains the type of the popup (content, banner)
    """
    popupType: String
    """
    contains the content of the popup
    """
    content: JSON
    """
    contains the banner of the popup
    """
    banner: String
    """
    contains the close type of the popup (automatic, manual)
    """
    closePopupType: String
    """
    contains the duration in second for which popup is visible
    """
    duration: Int
    """
    contains the status of popup
    """
    isActive: Boolean
    """
    contains the start date of popup availability
    """
    startDate: String
    """
    contains the end date of popup availability
    """
    endDate: String
    """
    contains the visibilty of popup (once, always)
    """
    visiblityStatus: String
    """
    contains the display condition of popup
    """
    displayCondition: String
  }

  type PromotionVideoResponse {
    id: Int
    title: JSON
    thumbnailUrl: String
    videoUrl: String
  }

  """
  represents the input type of Promotion Video query
  """
  input PromotionVideoInput {
    limit: Int
    offset: Int
    orderBy: String
  }

  type LatestPlayedGamesResponse {
    count: Int,
    data: [LatestPlayedGamesDataResponse]
  }

  type LatestPlayedGamesDataResponse {
    pageMenuTitle: String
    name: String
    CasinoItem: LatestPlayedCasinoItem
  }

  type LatestPlayedCasinoItem {
    hasLobby: Boolean
    id: Int
    uuid: String
    image: String
  }

  type LatestWinnersResponse {
    id: Int
    gameId: String
    tableId: String
    tableName: String
    image: String
    email: String
    winningAmount: Float
    otherCurrencyAmount: JSON
    providerName: String
  }

  type BenefitsContentResponse {
    id: Int
    title: JSON
    buttonText: JSON
    buttonRedirection: String
    content: JSON
    image: String
  }

  type TestimonialsResponse {
    id: Int
    title: JSON
    subTitle: JSON
    content: JSON
    image: String
  }

  type SportsContentResponse {
    id: Int
    title: JSON
    buttonText: JSON
    buttonRedirection: String
    content: JSON
    image: String
    imageMobile: String
  }

  type LanguageKeyResponse {
    page: String
    keys: JSON
  }

  type Prize {
    id: Int
    title: String
    description: String
  }

  type OfferResponse {
    id: Int
    promotionTitle: JSON
    offerDescription: JSON
    validFrom: String
    validTo: String
    frequency: Int
    dayOfWeek: Int
    winningType: Int
    image: String
    status: Boolean
    prizes: [Prize]
    providers: JSON
  }

  """
  contains all the fields related to SEO metadata of the tenant
  """
  type SeoPageMetaResponse {
    id: Int
    metaTitle: String
    metaDescription: String
    metaKeyword: String
    casinoPageId: Int
    casinoPageMenuId: Int
    menuId: Int
    pages: [JSON]
  }

  type OfferLeaderBoardResponse {
    username: String,
    winningAmount: Float
  }

  type OfferWinnerResponse {
    id: Int
    userName: String
    winningType: Int
    winningValue: Float
    prizeTitle: String
  }

  extend type Query {
    """
    fetches all information layout or appearance of a tenant
    and returns TenantPageLayoutResponse
    """
    TenantPageLayout: TenantPageLayoutResponse!
    """
    fetches all information about number, order, name,
    of pages, page menus and menu items of a tenant, by optionally
    taking page id as an input and returns PageManagementResponse
    """
    PageManagement(topMenuId: Int!): PageManagementResponse!
    """
    fetches all information about a menu item like
    order, uuid etc by taking menu id as an input and returns array of MenuItemResponse
    """
    MenuItem(menuId: Int!): [MenuItemResponse]!
    """
    fetches all the fields related to the page menu
    by taking page id as an input and returns array of MenuResponse
    """
    PageMenu(pageId: Int!): [MenuResponse]!
    """
    fetches all the FAQs
    """
    Faqs(categoryId: Int): [faqCategoriesResponse]
    """
    fetches FAQs on the basis of search key
    """
    FaqSearch(searchKey: String!, limit: Int, offset: Int): faqSearchResponse
    """
    fetches POP-UP information of a tenant
    """
    Popup: PopupResponse
    """
    fetches Promotional video information of a tenant
    """
    PromotionVideo(input: PromotionVideoInput): [PromotionVideoResponse]!
    """
    fetches latest played games
    """
    LatestPlayedGames: LatestPlayedGamesResponse! @isAuthenticated
    """
    fetches benefits contents
    """
    BenefitsContent: [BenefitsContentResponse]!
    """
    fetches testimonials
    """
    Testimonial: [TestimonialsResponse]!
    """
    fetches language transalation keys
    """
    LanguageKey: [LanguageKeyResponse]!
    """
    fetches SEO meta information for tenant pages, optionally by topMenuId
    """
    SeoPageMeta(topMenuId: String): [SeoPageMetaResponse!]
    """
    fetches latest winners data
    """
    LatestWinners: [LatestWinnersResponse]!
    """
    Fetches offers data for the tenant
    """
    OffersData: [OfferResponse]!
    """
    Fetches leaderboard data by offer id
    """
    OfferLeaderBoardData(offerId: Int!): [OfferLeaderBoardResponse!]!
    """
    Fetches offer winners
    """
    OfferWinners(offerId: Int!, winningDate: String!): [OfferWinnerResponse]!
    """
    contains the menu response tenant
    """
    Menus: [menuResponse]
}
`
