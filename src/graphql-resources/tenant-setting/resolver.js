import FaqSearch from '../../services/tenant-setting/faqSearch'
import LatestPlayedGames from '../../services/tenant-setting/latestPlayedGames'
import latestWinners from '../../services/tenant-setting/latestWinners.js'
import MenuItem from '../../services/tenant-setting/menuItem'
import offerLeaderBoard from '../../services/tenant-setting/offerLeaderBoard'
import OffersData from '../../services/tenant-setting/offersData.js'
import OfferWinners from '../../services/tenant-setting/offerWinners'
import PageManagement from '../../services/tenant-setting/pageManagement'
import PageMenu from '../../services/tenant-setting/pageMenu'
import Popup from '../../services/tenant-setting/popup'
import SeoPageMeta from '../../services/tenant-setting/seoPageMeta.js'
import BenefitsContent from './../../services/tenant-setting/benefitsContent'
import Faqs from './../../services/tenant-setting/faqs'
import LanguageKey from './../../services/tenant-setting/languageKey'
import Menu from './../../services/tenant-setting/menu'
import PromotionVideo from './../../services/tenant-setting/promotionVideo'
import TenantPageLayout from './../../services/tenant-setting/tenantPageLayout'
import Testimonial from './../../services/tenant-setting/testimonial'
/**
 *  Tenant setting resolver will handle tenant-setting related queries and mutation.
 */
export const resolvers = {

  /**
   * All the queries related to the tenant-setting are present in this query resolver
   */
  Query: {
    /**
     * This resolver is responsible to fetch all the details/information of page layout
     * of the current tenant
     * @returns {TenantPageLayoutResponse}
     */
    TenantPageLayout: async (_, __, context) => {
      const result = await TenantPageLayout.execute(__, context)
      return result.result
    },

    Menus: async (_, __, context) => {
      const result = await Menu.execute(__, context)
      return result.result
    },
    /**
     * This resolver is responsible to fetch all the pages, default or specified
     * menu of a page, and default menu items of specified menu
     * @param {Integer} pageId it contains the page id of which menus and items
     * are to be fetched
     * @returns {PageManagementResponse}
     */
    PageManagement: async (_, topMenuId, context) => {
      const result = await PageManagement.execute(topMenuId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * This resolver is responsible to fetch menu items for the current tenant
     * @param {Integer} menuId it contains the menu id of which items are to be fetched
     * @returns {[MenuItemResponse]}
     */
    MenuItem: async (_, menuId, context) => {
      const result = await MenuItem.execute(menuId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * This resolver is responsible to fetch page menu for the given page
     * @param {Integer} pageId it contains the page id of which menus are to be fetched
     * @returns {[MenuResponse]}
     */
    PageMenu: async (_, pageId, context) => {
      const result = await PageMenu.execute(pageId, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },
    /**
     * This resolver is responsible to fetch all the FAQs
     * of the current tenant based on category Id
     * @returns {faqsResponse}
     */
    Faqs: async (_, { categoryId }, context) => {
      const result = await Faqs.execute({ categoryId }, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * This resolver is responsible to search all the FAQs
     * of the current tenant
     * @returns {faqsResponse}
     */
    FaqSearch: async (_, { searchKey, limit, offset }, context) => {
      const result = await FaqSearch.execute({ searchKey, limit, offset }, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * This resolver is responsible to fetch POP-UP information
     * of the current tenant
     * @returns {PopupResponse}
     */
    Popup: async (_, __, context) => {
      const result = await Popup.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * This resolver is responsible to fetch promotion videos information
     * of the current tenant
     * @returns {PromotionVideoResponse}
    */
    PromotionVideo: async (_, __, context) => {
      const result = await PromotionVideo.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * This resolver is responsible to fetch footer information
     * of the current tenant
     * @returns {FooterContentResponse}
     */
    // FooterContent: async (_, __, context) => {
    //   const result = await FooterContent.execute(__, context)
    //   if (result.failed) {
    //     throw result.errors
    //   }
    //   return result.result
    // }

    LatestPlayedGames: async (_, __, context) => {
      const result = await LatestPlayedGames.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for fetching latest winner data
     * of the current tenant.
     * @returns {LatestWinnersResponse}
    */
    LatestWinners: async (_, __, context) => {
      const result = await latestWinners.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for fetching offer data
     * of the current tenant.
     * @returns {OffersResponse}
    */
    OffersData: async (_, __, context) => {
      const result = await OffersData.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for fetching leaderboard data by offer id
     * of the current tenant.
     * @returns {OfferLeaderBoardResponse}
    */
    OfferLeaderBoardData: async (_, body, context) => {
      const result = await offerLeaderBoard.execute(body, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for fetching benefits content information
     * of the current tenant.
     * @returns {BenefitsContentResponse}
    */
    BenefitsContent: async (_, __, context) => {
      const result = await BenefitsContent.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for fetching testimonials information
     * of the current tenant.
     * @returns {TestimonialsResponse}
    */
    Testimonial: async (_, __, context) => {
      const result = await Testimonial.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },
    /**
     * This resolver is responsible for fetching language translation keys information
     * of the current tenant.
     * @returns {LanguageKeyResponse}
    */
    LanguageKey: async (_, __, context) => {
      const result = await LanguageKey.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
       * Fetches the SEO meta information for tenant pages.
       * Optionally takes topMenuId to filter by menu.
       * @param {Integer} topMenuId Contains the menu id to filter SEO metadata
       * @returns {SeoPageMetaResponse}
       */
    SeoPageMeta: async (_, { topMenuId }, context) => {
      const result = await SeoPageMeta.execute({ topMenuId }, context)

      if (result.failed) {
        throw result.errors
      }

      return result.result
    },

    /**
     * This resolver is responsible for fetching offer winners data
     * of the current tenant.
     * @returns {OfferWinnersResponse}
    */
    OfferWinners: async (_, { offerId, winningDate }, context) => {
      const result = await OfferWinners.execute({ offerId, winningDate }, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    }
  },

  /**
   * all the mutations related to the tenant-setting are present in mutation resolver
   */
  Mutation: {

  },
  /**
  * This resolver is responsible to fetch operator of the current tenant
  * @returns {operator}
  */
  TenantPageLayoutResponse: {
    operator: async (parent, _, context) => {
      const result = await context.getTenantOperatorIdDataLoader.load({ databaseConnection: context.databaseConnection, Tenant: context.tenant })
      return result
    }
  },
  /**
  * This resolver is responsible to fetch  of the current tenant
  * @returns {menu}
  */
  // TenantPageLayoutResponse: {
  //   menu: async (_, __, context) => {
  //     const result = await Menu.execute(__, context)
  //     return result.result
  //   }
  // },
  /**
  * This resolver is responsible to fetch page menus of the current current page
  * @returns {menus}
  */

  PageResponse: {
    menus: async (page, { pageId }, { getPageMenuDataLoader }) => {
      const parentPageId = page.id
      if (!pageId) pageId = page.id
      const result = await getPageMenuDataLoader.load({ parentPageId, pageId })
      return result
    }
  },

  MenuResponse: {
    /**
    * This resolver is responsible to fetch casino menus of the current page menu
    * @returns {casinoMenu}
    */
    casinoMenu: async (pageMenu, _, { getCasinoMenuDataLoader }) => {
      const result = await getCasinoMenuDataLoader.load(pageMenu.casinoMenuId)
      return result
    },
    /**
    * This resolver is responsible to fetch menu items of the current page menu
    * @returns {menuItems}
    */
    menuItems: async (pageMenu, { limit, offset, menuId }, { getMenuItemDataLoader }) => {
      const parentPageMenuId = pageMenu.id
      const result = await getMenuItemDataLoader.load({ parentPageMenuId, limit, offset, menuId })
      return result
    },

    totalMenuItem: async (pageMenu, { menuId }, { getTotalMenuItemDataLoader }) => {
      const parentPageMenuId = pageMenu.id
      const result = await getTotalMenuItemDataLoader.load({ parentPageMenuId, menuId })
      return result
    }
  },

  MenuItemResponse: {
    /**
    * This resolver is responsible to fetch casino items of the current current casino item
    * @returns {casinoItem}
    */
    casinoItem: async (menuItem, _, { getCasinoItemDataLoader }) => {
      const result = await getCasinoItemDataLoader.load(menuItem.casinoItemId)
      return result
    }
  }
}
