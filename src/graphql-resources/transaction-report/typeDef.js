import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  contains all the fields related to transactions
  """
  type TransactionListResponse {
    """
    contains transaction id for unique identification in database
    """
    id: ID!
    """
    contains transaction amount involved in the current transaction
    """
    amount: Float
    """
    contains the balance of the source before the transaction occurred
    """
    sourceBeforeBalance: Float
    """
    contains the balance of the source after the transaction occurred
    """
    sourceAfterBalance: Float
    """
    contains the type of transaction that-is 0-debit, 1-credit, 2-rollback
    """
    transactionType: Int
    """
    contains the status of transaction
    """
    status: String
    """
    contains any comments related to the transaction
    """
    comments: String
    """
    contains round ID of the transaction
    """
    roundId: String
    """
    contains timestamp of occurrence of the transaction
    """
    createdAt: String
    """
    contains total count of all transactions of a user
    """

    paymentProvider: PaymentProvidersResponse
    casinoGame: CasinoGameResponse
    """
    contains game name in seat Id in case of ST8 provider
    """
    gameName: String
    """
    contains transaction id related to the transaction
    """
    transactionId: String
    """
    contains provider name related to the transaction
    """
    custom_3: String
  }

  type PaymentProvidersResponse{
    providerName: String
  }

  type GameProviderResponse {
    """
    contains casino game id for unique identification in database
    """
    id: ID
    """
    contains the name of the game provider
    """
    name: String
    """
    contains timestamp of creation of the provider
    """
    createdAt: String
  }

  """
  contains the fields related to casino games
  """
  type CasinoGameResponse {
    """
    contains casino game id for unique identification in database
    """
    id: ID
    """
    contains the name of the game
    """
    name: String
    """
    contains timestamp of creation of the game
    """
    createdAt: String
    """
    contains provider of the game
    """
    gameProvider: GameProviderResponse
  }

  """
  contains transaction list and the total count of the transactions of a user
  """
  type TransactionPaginationResponse {
    """
    contains array of all transactions of a user
    """
    transactionList: [TransactionListResponse]
    """
    contains total count of all transactions of a user
    """
    count: Int!
  }

  """
  contains constants for ascending or descending field
  """
  enum SortOrder {
    asc
    desc
  }

  """
  contain fields related to sort filter
  """
  input TransactionListOrderBy {
    createdAt: SortOrder
    amount: SortOrder
  }

  """
  represents the input type of Transactions query
  """
  input TransactionListInput {
    """
    contains page to be displayed in the pagination of the transaction list
    """
    page: Int
    """
    contains the limit of transactions to be shown in a single page
    """
    limit: Int
    """
    contains the initial date from which the transaction are to be shown
    """
    dateFrom: String
    """
    contains the final date upto which the transaction are to be shown,
    defaults to today's date
    """
    dateTo: String
    """
    contains id of the game of which transactions are to be displayed
    """
    gameId: Int
    """
    contains sort filter fields
    """
    orderBy: TransactionListOrderBy

    paymentProviderId: Int

    paymentStatus: String
    userCountryCode: String
    """
    contains reportType casino or financial transaction list
    """
    reportType: String
    """
    contains transactionType of casino or financial transaction
    """
    transactionType: String
    """
    contains roundId of casino transaction
    """
    roundId: String
  }

  extend type Query {
    """
    fetches all the transaction of a user, requires auth token of the user in the header,
    takes input of type TransactionListInput and returns array of TransactionPaginationResponse
    """
    Transactions(input: TransactionListInput): TransactionPaginationResponse! @isAuthenticated
  }
`
