// import Transactions from '../../services/transaction-report/transactions'
import Transactions from '../../services/transaction-report/transactionV2'

/**
 *  transaction report resolver will handle queries related to user transactions.
 */
export const resolvers = {

  /**
   * All the queries related to the transactions are present in this query resolver
   */
  Query: {

    /**
     * This resolver is responsible to fetch current logged in user's detail
     * @returns {UserResponse}
     */
    Transactions: async (_, { input }, context) => {
      const result = await Transactions.execute(input, context)
      return result.result
    }
  },

  /**
   * all the mutations related to the user are present in mutation resolver
   */
  Mutation: {

  },

  TransactionListResponse: {
    /**
    * This resolver is responsible to fetch casino games of the current transaction
    * @returns {CasinoGameResponse}
    */
    casinoGame: async (transaction, _, { getCasinoGameDataLoader }) => {
      const result = await getCasinoGameDataLoader.load(transaction)
      return result
    }
  },

  CasinoGameResponse: {
    /**
    * This resolver is responsible to fetch casino games provider
    * @returns {CasinoGameResponse}
    */
    gameProvider: async (game, _, { getCasinoGameProviderDataLoader }) => {
      const result = await getCasinoGameProviderDataLoader.load(game.casinoProviderId)
      return result
    }
  }
}
