import ReferralSetting from '../../services/referrals/referralSetting'
import CheckReferralCode from '../../services/referrals/checkReferralCode'

export const resolvers = {
  Query: {
    /**
     * This resolver is responsible to fetch referral setting
    * @returns {[ReferralSettingResponse]}
     */
    ReferralSetting: async (_, __, context) => {
      const result = await ReferralSetting.execute(__, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    /**
     * This resolver is responsible for checking referral code
     * @param {checkReferralInput} input it contains referralCode
     * @returns {CheckReferralCode}
     */
    CheckReferralCode: async (_, {input}, context) => {
      const result = await CheckReferralCode.execute(input, context)
      return result.result
    }
  }
}
