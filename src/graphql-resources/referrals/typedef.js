import { gql } from 'apollo-server-express'
export const typeDef = gql`

  """
  contains all the fields related to the referral setting response
  """
  type ReferralSettingResponse {
    id: ID
    title: JSON
    description: JSON
    bonusAmount: Float
    bonusType: Int
    walletType: Int
    event: Int
    applyTo: Int
    active: Boolean
    tenantId: Int
    minValue: Int
    maxValue: Int
    percentageValue: Float
    referrerBonusAmount: Float
    refereeBonusAmount: Float
  }

  """
  contains fields related to the referral code check
  """
  type CheckReferralCodeResponse {
    isValid: Boolean!
    message: String!
  }

  input CheckReferralInput {
    """
    contains referralcode
    """
    referralCode: String!
  }

  extend type Query {
    """
    check referral setting for tenant and return ReferralSettingResponse
    """
    ReferralSetting: ReferralSettingResponse
    """
    check referral code during user registration and return CheckReferralCodeResponse
    """
    CheckReferralCode(input: CheckReferralInput!): CheckReferralCodeResponse!
  }
`
