import UpdateUserBankDetails from '../../services/user-bank-details'
import userBankDelete from '../../services/user-bank-details/userBankDelete'
import UserBankDetails from '../../services/user-bank-details/userBankDetails'
import UnmaskedUserBankDetails from '../../services/user-bank-details/unmaskedUserBankDetails'

export const resolvers = {
  Query: {
    /**
     * It is responsible to fetch all the withdraw requests user made till date
     * @returns {[UserBankDetailsResponse]}
     */
    UserBankDetails: async (_, input, context) => {
      const result = await UserBankDetails.execute(input, context)
      if (result.failed) {
        throw result.error
      }

      return result.result
    },
    /**
      * It is responsible to fetch the unmasked user bank details
      * @returns {[UserBankDetailsResponse]}
    */
    UnmaskedUserBankDetails: async (_, { input }, context) => {
      const result = await UnmaskedUserBankDetails.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }
  },

  Mutation: {
    /**
     * This resolver is responsible for the creation of new user
     * @param {UpdateUserBankDetailsInput} input it contains all the params of the new user.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {UserBankDetailsResponse}
     */
    UpdateUserBankDetails: async (_, { input }, context) => {
      const result = await UpdateUserBankDetails.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    },
    /**
     * It will allow a user to delete file
     * @param {LoginInInput} input it contains file params.
     * @param {Context} context it the context object of gql, contains all the shared info between the resolvers.
     * @returns {[Boolean]}
     */
    DeleteUserBankDetail: async (_, { input }, context) => {
      const result = await userBankDelete.execute(input, context)
      if (result.failed) {
        throw result.errors
      }

      return result.result
    }

  }
}
