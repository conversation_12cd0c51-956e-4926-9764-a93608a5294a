import { gql } from 'apollo-server-express'
export const typeDef = gql`
  # scalar JSON
  """
  contains all the request parameter for update bank details
  """
  input UpdateUserBankDetailsInput {

    """
    Bank ID for update bank details
    """
    bankId: String
    """
    Bank Name of User
    """
    bankName: String
    """
    contains User Account Number of bank
    """
    accountNumber: String

    """
      Contains IFSC Code of bank
    """
    bankIfscCode: String

    """
    Contains Name of user
    """
    name: String
    """
    Contains Phone Number Of user
    """
    phoneNumber: String
    """
    contains Phone Code of user
    """
    phoneCode: String

  }

  """
  contains all the fields of USER bank Details
  """
  type UserBankDetailsResponse {
    """
    contains  id for unique identification in database
    """
    id: Int
    """
    User Unique ID
    """
    userId: Int
    """
    Bank Name of User
    """
    bankName: String
    """
    contains User Account Number of bank
    """
    accountNumber: String

    """
      Contains IFSC Code of bank
    """
    bankIfscCode: String

    """
    Contains Name of user
    """
    name: String
     """
     Contains Phone Number Of user
     """
     phoneNumber: String

    """
    contains the Active or Inactive STATUS
    """
    status: String
    """
    contains Phone Code of user
    """
    phoneCode: String

  }

  input UnmaskedUserBankDetailsInput {
    userBankId: Int
  }

  input ShowBankDetailsInput {

  page: Int
  limit: Int

  }

  type ShowBankDetailsResponse {
    requests: [UserBankDetailsResponse]!
    count: Int!
    addBankDetails: Boolean
    isWithdrawalRequestPending: Boolean
  }

  """
  represents the input type of DeleteUserBankDetail mutation
  """
  input DeleteUserBankDetailInput {
    id: ID!
    status: String!
    action: String
  }

   extend type Query {
     """
     fetches all information layout or appearance of a tenant
     and returns UserBankDetailsResponse
     """
     UserBankDetails(input: ShowBankDetailsInput): ShowBankDetailsResponse! @isAuthenticated
     """
     fetches user bank details based on user Bank Id
     """
     UnmaskedUserBankDetails(input: UnmaskedUserBankDetailsInput!): UserBankDetailsResponse! @isAuthenticated
   }

  extend type Mutation {
    """
    create and update user bank details
    """
    UpdateUserBankDetails(input: UpdateUserBankDetailsInput!): UserBankDetailsResponse! @isAuthenticated
    """
    delete user bank details
    """
    DeleteUserBankDetail(input: DeleteUserBankDetailInput!): Boolean! @isAuthenticated

  }
`
