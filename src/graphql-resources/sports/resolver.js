import ActiveLeagues from "../../services/sports/activeLeagues";
import LeagueEvents from "../../services/sports/leagueEvents";
import LiveEvents from "../../services/sports/liveEvents";
import GetLiveSports from "../../services/sports/liveSports";
import SportsWithActiveLeaguesAndEvents from "../../services/sports/sportsActiveLeaguesEvents";

export const resolvers = {
  Query: {
    LiveSports: async (_, { input }, context) => {
      const result = await GetLiveSports.execute(input, context)
      if (result.failed) {
        throw result.errors
      }
      return result.result
    },

    GetLiveEvents: async (_, __, context) => {
      const result = await LiveEvents.execute(__, context);
      if (result.failed) {
        throw result.errors
      }
      return result.result;
    },

    ActiveLeagues: async (_, __, context) => {
      const result = await ActiveLeagues.execute(__, context);
      if (result.failed) {
        throw result.errors
      }
      return result.result;
    },

    LeagueEvents: async (_, __, context) => {
      const result = await LeagueEvents.execute(__, context);
      if (result.failed) {
        throw result.errors
      }
      return result.result;
    },

    GetSportsWithActiveLeaguesAndEvents: async (_, { input }, context) => {
      const result = await SportsWithActiveLeaguesAndEvents.execute(input, context);
      if (result.failed) {
        throw result.errors
      }
      return result.result;
    },
  },
};
