import { gql } from 'apollo-server-express'

export const typeDef = gql`
  """
  Input type for filtering sports data
  """
  input LiveSportsFilterInput {
    """
    Filter by time period (all/inPlay/today/tomorrow/upcoming)
    """
    filter: String
  }

  """
  Inputs for Getting Sports with Active leagues and Events
  """
  input SportsWithActiveLeaguesAndEventsInput {
    """
    For specific sport using it's sportId"
    """
    sportId: Int

    """
    Filter by time period (inPlay/today/tomorrow/upcoming)
    """
    filter: String
  }

  """
  Represents a sport with its events counts
  """
  type Sport {
    """
    Unique identifier for the sport
    """
    sportId: String!

    """
    Internal ID of the sport
    """
    id: String!

    """
    Localized names for the sport
    """
    nameEn : String!
    """
    Number of events associated with the sport
    """
    eventCount: Int!

    """
    Active Leagues for asssociated sport
    """
    leagues: [League!]!
  }


  """
  Represents a sport of a event
  """
  type eventSport {
   
    """
    Internal ID of the sport
    """
    id: String!

    """
    Localized names for the sport
    """
    nameEn : String!
   
  }

  """
  Response type for live sports query
  """
  type LiveSportsResponse {
    """
    List of sports with their events counts
    """
    sports: [Sport!]!

    """
    Total number of sports
    """
    totalSports: Int!

    """
    Total number of events of all sports
    """
    totalEvents: Int!
  }

  """
  Represents a sports event
  """
  type PullEvent {
    id: ID!
    fixtureId: Int!
    nameEn: String!
    startDate : String!
    leagueNameEn: String!
    sportNameEn: String!
    providerName: String!
    sport : eventSport!
  }

  """
  Input type for filtering leagues data
  """
  input ActiveLeaguesFilterInput {
    """
    Filter by sport
    """
    sportId: Int!
  }

  """
  Represents a league with its events count
  """
  type League {
    """
    Internal ID of the league
    """
    id: String!

    """
    External league ID
    """
    leagueId: Int!

    """
    Sport ID the league belongs to
    """
    sportId: String!

    """
    Season of the league
    """
    season: String

    """
    Location ID of the league
    """
    locationId: Int

    """
    Name of the league (English)
    """
    nameEn: String!

    """"
    Popular tag boolean
    """
    isPopular : Boolean!

    """
    Number of events associated with the league
    """
    eventCount: Int!

    """
    List of events associated with this league
    """
    events: [PullEvent!]!
  }

  """
  Response for fetching events for a specified Leagues
  """
  type LeagueEventsResponse {
    league: [League!]!
  }

  """
  Response for Sports with Active Leagues
  """
  type SportsWithActiveLeaguesAndEventsResponse {
    sports: [Sport!]!
    providerName: String!
  }

  """
  Input for filtering events with leagueId
  """
  input LeagueEventsInput {
    """
    Filter by league
    """
    leagueId: Int!
  }

  extend type Query {
    """
    Get all live sports with their events counts
    """
    LiveSports(input: LiveSportsFilterInput): LiveSportsResponse

    """
    Get live sports event
    """
    GetLiveEvents: [PullEvent]

    """
    Get all active leagues with their events
    """
    ActiveLeagues(input: ActiveLeaguesFilterInput): [League!]!

    """
    Get events of a league
    """
    LeagueEvents(input: LeagueEventsInput) : LeagueEventsResponse

    """
    Get sports with active leagues and events
    """
    GetSportsWithActiveLeaguesAndEvents(input: SportsWithActiveLeaguesAndEventsInput): SportsWithActiveLeaguesAndEventsResponse
  }
`
