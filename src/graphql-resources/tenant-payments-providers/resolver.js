import PaymentProviders from '../../services/tenant-payment-providers'
import BankLists from '../../services/tenant-payment-providers/bankLists'
import PaymentSupportImages from '../../services/tenant-payment-providers/paymentProviderImages'

export const resolvers = {
  Query: {
    PaymentProviders: async (_, __, context) => {
      const result = await PaymentProviders.execute(__, context)
      return result.result
    },
    BankLists: async (_, __, context) => {
      const result = await BankLists.execute(__, context)
      return result.result
    },
    PaymentProviderImages: async (_, __, context) => {
      const result = await PaymentSupportImages.execute(__, context)
      return result.result
    }
  }
}
