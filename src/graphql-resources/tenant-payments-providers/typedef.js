import { gql } from 'apollo-server-express'
export const typeDef = gql`
  # scalar JSON

  """
  contains all the fields related to the uploaded file
  """
  type TenantPaymentProvidersResponse {
    """
    contains  id for unique identification in database
    """
    id: Int
    """
    Selected Payment Provider ID
    """
    providerId: Int
    """
    contains the flag to show payment is active or not
    """
    active: Boolean

    paymentProvider: paymentProviderResponse
    description: String
    """
    allowed VIP level of tenants
    """
    vipLevel: JSON,
    """
    gets you the maximum deposit limit
    """
    maxDepositLimit: Int
    """
    gets you minimum deposit limit
    """
    minDepositLimit: Int
  }

  type paymentProviderResponse {
    """
    contains the Provider id
    """
    id: ID!
    """
    contains the Provider Name
    """
    providerName: String
    """
    contains the Logo Image
    """
    logo: String
    """
    contains the Active Status
    """
    active: Boolean
  }

  type BankDetailResponse {
    """
    contains the Bank id
    """
    id: ID!
    """
    contains the Bank Name
    """
    bankName: String
    """
    contains the Account Holder Name
    """
    accountHolderName: String
    """
    contains the Ifsc Code
    """
    bankIfscCode: String
    """
    contains the Account Number
    """
    accountNumber: String
    """
    contains the country code
    """
    countryCode: String
    upiId: String
    """
    Type of the bank detail
    """
    type: Int
    """
    Crypto exchange rate
    """
    cryptoExchangeRate: Float
    """
    Image URL
    """
    image: String
  }

  type PaymentSupportImage {
    id: ID!
    imageUrl: String
  }

  extend type Query {
    """
    fetches all information layout or appearance of a tenant
    and returns TenantPaymentProvidersResponse
    """
    PaymentProviders: [TenantPaymentProvidersResponse!],
    BankLists: [BankDetailResponse!]
    PaymentProviderImages: [PaymentSupportImage!]
  }
`
