import express from 'express'
import {
  addFavoriteBet,
  authCheck,
  betSlipDetail,
  betTransaction,
  betslip,
  betslipList,
  getBetLimits, getBetslipWinAmount, getCashoutAmount, getFavoriteBets, getLeagues, getResults, getSports, getTopLeagues, lastBet,
  placeLiveBets, processCashoutAmount, removeFavoriteBet,
  sapExchangeTransaction,
  sapExchangeTransactionDescription,
  searchEvents, updateFixture,
  updateSports
} from '../controllers/sports-betting.controller'
import bettingLimitCheck from '../middleware/bettingLimitCheck'
import eventCheckBeforePlaceBet from '../middleware/eventCheckBeforePlaceBet'
import TenantSettingCheck from '../middleware/tenantSettingCheck.js'
import userWalletBalanceCheck from '../middleware/userWalletBalanceCheck'
import verifyAPIKey from '../middleware/verifyAPIKey'
import verifyAuthToken from '../middleware/verifyAuthToken'
import VerifyBetSlipLimit from '../middleware/verifyBetSlipLimit'

const router = express.Router()

router.route('/authenticate').get([verifyAuthToken], authCheck)
router.route('/betslip-list').get([verifyAuthToken], betslipList)
router.route('/betslip/').post([verifyAuthToken, eventCheckBeforePlaceBet, VerifyBetSlipLimit, userWalletBalanceCheck, TenantSettingCheck, bettingLimitCheck], betslip)
router.route('/betslip/place_live_bets/').post([verifyAuthToken], placeLiveBets)

router.route('/last-bet').get([verifyAuthToken], lastBet)
router.route('/bet-transaction').get([verifyAuthToken], betTransaction)
router.route('/transaction/sap/exchange').get([verifyAuthToken], sapExchangeTransaction)
router.route('/transaction/sap/exchange/description').get([verifyAuthToken], sapExchangeTransactionDescription)
router.route('/users/get-bet-limits').get([verifyAuthToken], getBetLimits)
router.route('/update_fixture').post([verifyAPIKey], updateFixture)
router.route('/betslip/get_cashout_amount/').get([verifyAuthToken], getCashoutAmount)
router.route('/betslip/process_cashout_amount/').post([verifyAuthToken], processCashoutAmount)
router.route('/sports/').get(getSports)
router.route('/betslip/win-amount/:betslipId').get([verifyAuthToken], getBetslipWinAmount)
router.route('/betslip/:betSlipId').get([verifyAuthToken], betSlipDetail)
router.route('/leagues/:sportId').get(getLeagues)

router.route('/top-leagues').get(getTopLeagues)

router.route('/favorites')
  .post([verifyAuthToken], addFavoriteBet)
  .get([verifyAuthToken], getFavoriteBets)

router.route('/favorites/:favoriteId')
  .delete([verifyAuthToken], removeFavoriteBet)

router.route('/search_events/').get(searchEvents)

router.route('/update-sports/').patch(verifyAPIKey, updateSports)

router.route('/results').get(verifyAPIKey, getResults)

export default router
