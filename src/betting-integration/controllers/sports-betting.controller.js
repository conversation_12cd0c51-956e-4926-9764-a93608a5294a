import { sequelize } from '../../db/models'
import { response } from '../common/response'
import AddBetSlip from '../services/addBetSlip'
import AddFavoriteBet from '../services/addFavoriteBet'
import Auth from '../services/auth'
import BetSlipDetail from '../services/betSlipDetail'
import BetslipList from '../services/betslipList'
import BetTransaction from '../services/betTransaction'
import GetCashoutAmount from '../services/cashout/getCashoutAmount'
import ProcessCashoutAmount from '../services/cashout/processCashoutAmount'
import GetBetLimit from '../services/getBetLimits'
import GetBetslipWinAmount from '../services/getBetslipWinAmount'
import GetFavoriteBets from '../services/getFavoriteBets'
import GetLeagues from '../services/getLeagues'
import GetResults from '../services/GetResults'
import GetSports from '../services/getSports'
import GetTopLeagues from '../services/getTopLeagues'
import LastBet from '../services/lastBet'
import PlaceLiveBets from '../services/placeLiveBet'
import RemoveFavoriteBet from '../services/removeFavoriteBet'
import SapExchangeTransaction from '../services/sapExchangeTransaction'
import SapExchangeTransactionDescription from '../services/sapExchangeTransactionDescription'
import SearchEvents from '../services/searchEvent'
import UpdateFixture from '../services/updateFixture'
import UpdatePullSports from '../services/updatePullSports'

/**
 * Live ezugi authentication end point for validation of the user
 * @param {*} req - object contains all the request params sent from the client
 * @param {*} res - object contains all the response params sent to the client
 */
export const authCheck = async (req, res) => {
  const result = await Auth.execute(req.body, req)
  return response(res, result.result)
}

export const betslipList = async (req, res) => {
  const { result } = await BetslipList.execute(req.query, req)
  return response(res, result)
}

export const betSlipDetail = async (req, res) => {
  const result = await BetSlipDetail.execute({ ...req.params, ...req.body }, req)
  return response(res, result.result)
}

export const betslip = async (req, res) => {
  req.sequelizeTransaction = await sequelize.transaction()
  const result = await AddBetSlip.execute(req.body, req)
  if (result.successful) {
    req.sequelizeTransaction.commit()
    return response(res, result.result)
  }
  return response(res, { status: 500, data: {}, message: 'Internal server error', token: '' })
}

export const placeLiveBets = async (req, res) => {
  const result = await PlaceLiveBets.execute(req.body, req)
  if (result.successful) {
    req.sequelizeTransaction.commit()
    return response(res, result.result)
  }
  return response(res, { status: 500, data: {}, message: 'Internal server error', token: '' })
}

export const lastBet = async (req, res) => {
  const { result } = await LastBet.execute(req.query, req)
  return response(res, result)
}

export const betTransaction = async (req, res) => {
  const { result } = await BetTransaction.execute(req.query, req)
  return response(res, result)
}

export const getBetLimits = async (req, res) => {
  const result = await GetBetLimit.execute(req.body, req)
  return response(res, result.result)
}

export const updateFixture = async (req, res) => {
  const result = await UpdateFixture.execute(req.body, req)
  return response(res, result.result)
}

export const getCashoutAmount = async (req, res) => {
  const result = await GetCashoutAmount.execute(req.query, req)
  return response(res, result.result)
}

export const processCashoutAmount = async (req, res) => {
  const result = await ProcessCashoutAmount.execute(req.body, req)
  if (result.successful) {
    req.sequelizeTransaction.commit()
    return response(res, result.result)
  }
  return response(res, { status: 500, data: {}, message: 'Internal server error', token: '' })
}

export const addFavoriteBet = async (req, res) => {
  const result = await AddFavoriteBet.execute(req.body, req)
  return response(res, result.result)
}

export const getFavoriteBets = async (req, res) => {
  const result = await GetFavoriteBets.execute(req.body, req)
  return response(res, result.result)
}

export const removeFavoriteBet = async (req, res) => {
  const result = await RemoveFavoriteBet.execute({ params: req.params, body: req.body }, req)
  return response(res, result.result)
}

export const getSports = async (req, res) => {
  const result = await GetSports.execute(req.query, req)
  return response(res, result.result)
}

export const searchEvents = async (req, res) => {
  const result = await SearchEvents.execute({ ...req.query }, req)
  return response(res, result.result)
}

export const getLeagues = async (req, res) => {
  const result = await GetLeagues.execute(req.params, req)
  return response(res, result.result)
}

export const getTopLeagues = async (req, res) => {
  const result = await GetTopLeagues.execute({}, req)
  return response(res, result.result)
}

export const updateSports = async (req, res) => {
  const result = await UpdatePullSports.execute({}, req)
  return response(res, result.result)
}

export const getResults = async (req, res) => {
  const result = await GetResults.execute(req.params, req)
  return response(res, result.result)
}

export const getBetslipWinAmount = async (req, res) => {
  const result = await GetBetslipWinAmount.execute(req.params, req)
  return response(res, result.result)
}

export const sapExchangeTransaction = async (req, res) => {
  const { result } = await SapExchangeTransaction.execute(req.query, req)
  return response(res, result)
}

export const sapExchangeTransactionDescription = async (req, res) => {
  const { result } = await SapExchangeTransactionDescription.execute(req.query, req)
  return response(res, result)
}
