import { Op } from 'sequelize'

/*
* This Function is used to verify if the fixture status has changed or not.
* Take fixture status provided to you and then verify it from the event table.
*/
export const verifyFixtureStatus = async (betsArray, fixtureIds, context) => {
  let inTransaction = true
  const PullsEventModel = context.databaseConnection.PullsEvent
  const eventDetail = await PullsEventModel.findAll({
    where: {
      fixtureId: {
        [Op.in]: fixtureIds
      },
      isDeleted: false
    },
    raw: true
  })

  if (betsArray.length !== eventDetail.length) {
    return null
  }

  betsArray.map(bet => {
    eventDetail.forEach(event => {
      if (parseInt(bet.fixture_id) === event.fixtureId) {
        bet.event_id = event.id
      }
      if (event.fixtureStatus === 2 && inTransaction) {
        inTransaction = false // means the fixture status is in progress(live), it is not needed to insert it in transaction table
      }
    })
  })

  return { betsArray, inTransaction }
}
