import translate from '../../lib/languageTranslate'

export default async (stake, totalStake, betsArray, context) => {
  const {
    databaseConnection: {
      TenantEventLiability: TenantEventLiabilityModel,
      TenantSportsBetSetting: TenantSportsBetSettingModel
    },
    headers: { language },
    tenant: Tenant,
    sequelizeTransaction
  } = context

  const responseObject = {}

  const betSetting = await TenantSportsBetSettingModel.findOne({ where: { tenantId: Tenant.id }, raw: true, transaction: sequelizeTransaction })

  for (const bet of betsArray) {
    let stakePercentage = ((+bet.price) / (totalStake))
    stakePercentage = +(stakePercentage.toFixed(2))
    let betStakeAmount = stakePercentage * stake
    betStakeAmount = +(betStakeAmount.toFixed(2))

    const betEventLiability = await TenantEventLiabilityModel.findOne({ where: { eventId: bet.fixture_id, tenantId: Tenant.id }, transaction: sequelizeTransaction })

    if (betEventLiability) {
      if (+betEventLiability.totalLiability + betStakeAmount > +betSetting.eventLiability) {
        responseObject.status = 500
        responseObject.message = translate('EVENT_LIABILITY_EXCEEDED', language)
        return responseObject
      }
      betEventLiability.totalLiability = +betEventLiability.totalLiability + (betStakeAmount)
      await betEventLiability.save({ transaction: sequelizeTransaction })
    } else {
      if (betStakeAmount > +betSetting.eventLiability) {
        responseObject.status = 500
        responseObject.message = translate('EVENT_LIABILITY_EXCEEDED', language)
        return responseObject
      }
      await TenantEventLiabilityModel.create({ totalLiability: (betStakeAmount), tenantId: Tenant.id, eventId: bet.fixture_id }, { transaction: sequelizeTransaction })

      return null
    }
  }
}
