import { Op, Sequelize } from 'sequelize'
import keyValue<PERSON><PERSON><PERSON><PERSON> from '../../lib/keyValueToJSON'

export const validationChecks = {
  /**
   * checks for self exclusion before starting the game
   * @param {User} user contains user object
   * @param {object} responseObject contains ezugi response object
   * @returns {boolean}
   */
  selfExclusionCheck: async (user, responseObject) => {
    let allowed = true
    if (user.selfExclusion) {
      allowed = new Date().getTime() > new Date(user.selfExclusion).getTime()
    }

    if (!allowed) {
      responseObject.errorCode = 7
      responseObject.errorDescription = 'User not found'
    }

    return allowed
  },

  /**
   * checks for betting limit before a bet is placed
   * @param {object} context contains context object
   * @param {User} user contains user object
   * @param {float} amount contains the amount of the bet placed
   * @param {object} responseObject contains ezugi response object
   * @returns {boolean}
   */
  betLimitCheck: async (context, user, amount, responseObject) => {
    const TransactionModel = context.databaseConnection.Transaction
    const WalletModel = context.databaseConnection.Wallet
    const UserSettingModel = context.databaseConnection.UserSetting

    let allowed = true
    const userSetting = await keyValueToJson(UserSettingModel, ['bettingLimit'], 'userId', user.id)
    const wallet = await WalletModel.findOne({ where: { ownerId: user.id, ownerType: 'User' }, raw: true })

    const userTransaction = await TransactionModel.findOne({
      attributes: ['sourceWalletId', [Sequelize.fn('sum', Sequelize.col('amount')), 'totalBet']],
      group: ['sourceWalletId'],
      raw: true,
      where: {
        sourceWalletId: wallet.id,
        [Op.and]:
          [Sequelize.where(Sequelize.fn('date', Sequelize.col('created_at')), '=', Sequelize.literal('CURRENT_DATE'))]
      }
    })

    if (userTransaction && userSetting?.bettingLimit) {
      allowed = userSetting.bettingLimit >= (userTransaction.totalBet + (+amount))
    }

    if (!allowed) {
      responseObject.errorCode = 3
      responseObject.errorDescription = 'Insufficient funds'
    }
    return allowed
  }
}
