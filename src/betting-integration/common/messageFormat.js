const debitAmount = '"debitAmount":'
const creditAmount = '"creditAmount":'
const rollbackAmount = '"rollbackAmount":'

/**
 * This utility function is only for credit, debit and rollback APIs to convert the amount present in the
 * argument object to required precision, and make a string of that object suitable to hash check.
 * @export
 * @param {*} args The argument object
 * @return {*} Stringified argument object
 */
export default function messageFormat (args) {
  let compareString

  if (args.debitAmount) {
    compareString = debitAmount
    args.debitAmount = args.debitAmount.toFixed(2)
  } else if (args.creditAmount !== undefined) {
    compareString = creditAmount
    args.creditAmount = args.creditAmount.toFixed(2)
  } else if (args.rollbackAmount) {
    compareString = rollbackAmount
    args.creditAmount = args.rollbackAmount.toFixed(2)
  }

  const message = JSON.stringify(args)

  const reg = RegExp(compareString)

  const strArray = message.split(reg)
  const matchedString = strArray[1].matchAll('"')

  const FI = matchedString.next().value.index
  const SI = matchedString.next().value.index

  strArray[1] = strArray[1].slice(FI + 1)
  strArray[1] = strArray[1].slice(0, SI - 1) + strArray[1].slice(SI)

  return strArray[0] + compareString + strArray[1]
}
