import { SUBSCRIPTION_CHANNEL } from '../../common/constants'
import { paymentForCodes, transactionAmountFrom, transactionDescriptionType, transactionTypeConstant } from './constant'
import userCurrencyExchange from '../../common/userCurrencyExchange'

export default async (betSlipId, paymentFor, transactionType, amountSource, description, context) => {
  const {
    databaseConnection: {
      User: UserModel,
      Wallet: WalletModel,
      BetsBetslip: BetSlipModel,
      BetsTransaction: BetsTransactionModel
    },
    tenant: { id: tenantId },
    sequelizeTransaction
  } = context

  const betSlip = await BetSlipModel.findOne({ where: { id: betSlipId }, raw: true })

  const user = await UserModel.findOne({ where: { id: betSlip.userId }, include: WalletModel })

  let amount = 0
  let nonCashAmount = 0

  if (paymentFor === paymentForCodes.BET_PLACEMENT) {
    if (user.Wallet.nonCashAmount > 0) {
      nonCashAmount = parseFloat(betSlip.stake) > user.Wallet.nonCashAmount ? user.Wallet.nonCashAmount : parseFloat(betSlip.stake)
      amount = parseFloat(betSlip.stake) > user.Wallet.nonCashAmount ? parseFloat(betSlip.stake) - user.Wallet.nonCashAmount : 0
    } else {
      amount = parseFloat(betSlip.stake)
    }
  } else

  if (transactionType === transactionTypeConstant.DEBIT || transactionType === transactionTypeConstant.CREDIT) {
    const betPlaceTransaction = await BetsTransactionModel.findOne({
      where: {
        userId: user.id,
        betslipId: betSlipId,
        paymentFor: paymentForCodes.BET_PLACEMENT
      },
      raw: true,
      transaction: sequelizeTransaction
    })

    if (amountSource === transactionAmountFrom.STAKE) {
      if (betPlaceTransaction.nonCashAmount) {
        nonCashAmount = parseFloat(betSlip.stake) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(betPlaceTransaction.nonCashAmount) : parseFloat(betSlip.stake)
        amount = parseFloat(betSlip.stake) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(betSlip.stake) - parseFloat(betPlaceTransaction.nonCashAmount) : 0
      } else {
        amount = parseFloat(betSlip.stake)
      }
    } else

    if (amountSource === transactionAmountFrom.POSSIBLE_WIN_AMOUNT) {
      if (betPlaceTransaction.nonCashAmount) {
        nonCashAmount = parseFloat(betSlip.possibleWinAmount) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(betPlaceTransaction.nonCashAmount) : parseFloat(betSlip.possibleWinAmount)
        amount = parseFloat(betSlip.possibleWinAmount) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(betSlip.possibleWinAmount) - parseFloat(betPlaceTransaction.nonCashAmount) : 0
      } else {
        amount = parseFloat(betSlip.possibleWinAmount)
      }
    }
  } else

  if (transactionType === transactionTypeConstant.CASHOUT) {
    const betPlaceTransaction = await BetsTransactionModel.findOne({
      where: {
        userId: user.id,
        betslipId: betSlipId,
        paymentFor: paymentForCodes.BET_PLACEMENT
      },
      raw: true,
      transaction: sequelizeTransaction
    })

    nonCashAmount = parseFloat(amountSource) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(betPlaceTransaction.nonCashAmount) : parseFloat(amountSource)
    amount = parseFloat(amountSource) > parseFloat(betPlaceTransaction.nonCashAmount) ? parseFloat(amountSource) - parseFloat(betPlaceTransaction.nonCashAmount) : 0
  }

  const transactionDetailsObj = {}
  if (transactionType === transactionTypeConstant.CREDIT || transactionType === transactionTypeConstant.CASHOUT) {
    transactionDetailsObj.targetCurrencyId = user.Wallet.currencyId
    transactionDetailsObj.targetWalletId = user.Wallet.id
  } else {
    transactionDetailsObj.sourceCurrencyId = user.Wallet.currencyId
    transactionDetailsObj.sourceWalletId = user.Wallet.id
  }

  if (description === transactionDescriptionType.SETTLEMENT) {
    description = `settlement, ${transactionType}-${amount + nonCashAmount}, user-${user.userName}`
  } else if (description === transactionDescriptionType.RESETTLEMENT) {
    description = `resettlement, ${transactionType}-${amount + nonCashAmount}, user-${user.userName}`
  } else if (description === transactionDescriptionType.BETSLIP) {
    description = `betslip, ${transactionType}-${amount + nonCashAmount}, user-${user.userName}`
  } else if (description === transactionDescriptionType.CASHOUT) {
    description = `cashout, ${transactionType}-${amount + nonCashAmount}, user-${user.userName}`
  }

  const transactionObject = {
    isDeleted: false,
    amount,
    nonCashAmount,
    journalEntry: transactionType,
    merchantId: null,
    reference: `${user.userName}-${new Date().toISOString()}`,
    description,
    userId: user.id,
    betslipId: betSlipId,
    createdAt: new Date(),
    updatedAt: new Date(),
    tenantId,
    actioneeId: user.id,
    conversionRate: await userCurrencyExchange(context, user.Wallet.currencyId),
    paymentFor,
    currentBalance: (user.Wallet.amount + user.Wallet.nonCashAmount),
    ...transactionDetailsObj
  }

  user.Wallet.amount = (transactionType === transactionTypeConstant.CREDIT) ||
    (transactionType === transactionTypeConstant.CASHOUT) ? (user.Wallet.amount + amount) : (user.Wallet.amount - amount)

  user.Wallet.nonCashAmount = (transactionType === transactionTypeConstant.CREDIT) || (transactionType === transactionTypeConstant.CASHOUT) ? (user.Wallet.nonCashAmount) + nonCashAmount : (user.Wallet.nonCashAmount - nonCashAmount)
  await user.Wallet.save({ transaction: sequelizeTransaction })

  await BetsTransactionModel.create(transactionObject, { transaction: sequelizeTransaction })

  if (context.pubSub.publish) {
    context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: user.Wallet.amount, userId: user.id, nonCashAmount: user.Wallet.nonCashAmount } })
  }

  return { amount, nonCashAmount, userId: user.id }
}
