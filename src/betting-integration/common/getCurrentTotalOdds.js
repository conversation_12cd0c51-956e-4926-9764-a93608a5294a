import { BET_STANDARD_SETTLEMENT_CODES, DYNAMO_BET_STATUS, FIXTURE_STATUS } from './constant'

export default async (bets, docClient) => {
  let totalOdds = 1
  for (const bet of bets) {
    if (bet.settlementStatus === BET_STANDARD_SETTLEMENT_CODES.REFUND) {
      return null
    }

    if (bet.settlementStatus === BET_STANDARD_SETTLEMENT_CODES.WINNER) {
      continue
    }

    let eventCurrentData = null

    if (!bet?.PullsEvent) {
      return null
    }

    if (+bet.PullsEvent.fixtureStatus === FIXTURE_STATUS.IN_PROGRESS) {
      const queryParams = {
        TableName: 'LiveMarkets',
        KeyConditionExpression: 'fixture_id = :id',

        ExpressionAttributeValues: {
          ':id': `${bet.fixtureId}_market_${bet.marketId}`
        }
      }

      eventCurrentData = await docClient.query(queryParams).promise()
    } else

    if (+bet.PullsEvent.fixtureStatus === FIXTURE_STATUS.ABOUT_TO_START ||
      +bet.PullsEvent.fixtureStatus === FIXTURE_STATUS.NOT_STARTED) {
      const queryParams = {
        TableName: 'DeventMarkets',
        KeyConditionExpression: 'fixture_id = :fixtureId',

        ExpressionAttributeValues: {
          ':fixtureId': `${bet.fixtureId}`
        }

      }

      eventCurrentData = await docClient.query(queryParams).promise()
    } else {
      return null
    }

    if (!eventCurrentData?.Count) {
      totalOdds = totalOdds * parseFloat(bet.price)
    } else {
      const tableData = eventCurrentData.Items[0]

      const market = JSON.parse(tableData.market)

      const betDetails = market[`id_${bet.marketId}`]?.Bets[`id_${bet.betId}`]

      if (betDetails && (betDetails.Status === DYNAMO_BET_STATUS.SETTLED || betDetails.Status === DYNAMO_BET_STATUS.SUSPENDED)) {
        return null
      }

      if (betDetails) {
        totalOdds = totalOdds * parseFloat(betDetails.Price)
      } else {
        totalOdds = totalOdds * parseFloat(bet.price)
      }
    }
  }
  return totalOdds
}
