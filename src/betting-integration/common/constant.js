
export const settlementStatus = {
  IN_GAME: 'in_game',
  WON: 'won',
  LOST: 'lost',
  REFUND: 'refund'
}

export const transactionTypeConstant = {
  CREDIT: 'credit',
  DEBIT: 'debit',
  CASHOUT: 'cashout'
}

export const transactionDescriptionType = {
  SETTLEMENT: 'settlement',
  RESETTLEMENT: 'resettlement',
  BETSLIP: 'betslip',
  CASHOUT: 'cashout'
}

export const transactionAmountFrom = {
  STAKE: 'stake',
  POSSIBLE_WIN_AMOUNT: 'possibleWinAmount'
}

export const paymentForCodes = {
  BET_PLACEMENT: 1,
  WON: 2,
  CASHOUT: 3,
  REFUND: 4,
  LOST_BY_RESETTLEMENT: 5,
  DEPOSIT_BONUS_CLAIMED: 6,
  DEPOSIT_BONUS_PENDING: 7,
  DEPOSIT_BONUS_CANCELLED: 8
}

export const codeToPaymentFor = {
  1: 'BET_PLACEMENT',
  2: 'WON',
  3: 'CASHOUT',
  4: 'REFUND',
  5: 'LOST_BY_RESETTLEMENT',
  6: 'DEPOSIT_BONUS_CLAIMED',
  7: 'DEPOSIT_BONUS_PENDING',
  8: 'DEPOSIT_BONUS_CANCELLED'
}

export const API_KEY = {
  '37a1e18013e259ce0dec2f9b111aa69': '4fc0f680d3117294eb60ac1ba30a01b'
}

export const ALLOWED_SPORTS = ['154830', '35232', '6046', '48242', '154919', '54094']

export const BET_STANDARD_SETTLEMENT_CODES = {
  CANCELED: -1,
  LOSER: 1,
  WINNER: 2,
  REFUND: 3,
  HALF_LOST: 4,
  HALF_WON: 5
}

export const FIXTURE_STATUS = {
  NOT_STARTED: 0,
  IN_PROGRESS: 1,
  FINISHED: 2,
  CANCELLED: 4,
  POSTPONED: 5,
  INTERRUPTED: 6,
  ABANDONED: 7,
  COVERAGE_LOST: 8,
  ABOUT_TO_START: 9
}

export const DYNAMO_BET_STATUS = {
  OPEN: 1,
  SUSPENDED: 2,
  SETTLED: 3
}

export const SPORT_PROVIDER = {
  POWERPLAY: 'Powerplay'
}
