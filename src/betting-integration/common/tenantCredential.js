import ServiceBase from '../../common/serviceBase'

/**
 * Provides tenant credentials
 * @export
 * @class TenantCredential
 * @extends {ServiceBase}
 */
export default class TenantCredential extends ServiceBase {
  async run () {
    const credentials = await this.context.databaseConnection.TenantCredential.findAll({
      attributes: ['key', 'value'],
      where: {
        tenantId: this.context.tenant.id
      },
      raw: true
    })
    const ezugiCredentials = credentials.reduce((platformConfig, config) => {
      platformConfig[config.key] = config.value
      return platformConfig
    }, {})
    return ezugiCredentials
  }
}
