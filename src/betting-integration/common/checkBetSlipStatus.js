
/**
 *
 */
export default async (betSlipId, context) => {
  const {
    databaseConnection: {
      BetsBet: BetModel
    }
  } = context

  const bets = await BetModel.findAll({ where: { betslipId: betSlipId }, raw: true })

  let settlementCategory = null
  for (const bet of bets) {
    // in game
    if ((!settlementCategory || settlementCategory === 'won' || settlementCategory === 'refund') && (!bet.settlementStatus)) {
      settlementCategory = 'in_game'
    } else
    // lost
    if ((settlementCategory === 'won' || settlementCategory === 'refund' || !settlementCategory || settlementCategory === 'in_game') && (bet.settlementStatus === 1 || bet.settlementStatus === 4 || bet.settlementStatus === 5)) {
      settlementCategory = 'lost'
      break
    } else
    // won
    if ((!settlementCategory || settlementCategory === 'refund') && bet.settlementStatus === 2) {
      settlementCategory = 'won'
    } else
    // refund
    if (!settlementCategory) {
      settlementCategory = 'refund'
    }
  }

  return settlementCategory
}
