
export default async (stake, betSlipId, context) => {
  const {
    databaseConnection: {
      BetsBet: BetModel
    }
  } = context

  const bets = await BetModel.findAll({ where: { betslipId: betSlipId }, raw: true })

  let winningOdds = 1

  for (const bet of bets) {
    if (bet.settlementStatus === 2) {
      winningOdds *= bet.price
    }
  }

  return parseFloat((winningOdds * stake).toFixed(2))
}
