import { paymentForCodes, settlementStatus, transactionAmountFrom, transactionDescriptionType, transactionTypeConstant } from '../common/constant'
import createSettlementTransactionAndUpdateWallet from './createTransactionAndUpdateWallet'

export default async (oldSettlementStatus, newSettlementStatus, betSlipId, context) => {
  // refund
  if (oldSettlementStatus === settlementStatus.LOST && newSettlementStatus === settlementStatus.REFUND) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.REFUND, transactionTypeConstant.CREDIT, transactionAmountFrom.STAKE, transactionDescriptionType.RESETTLEMENT, context)
  } else

  if (oldSettlementStatus === settlementStatus.WON && newSettlementStatus === settlementStatus.REFUND) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.REFUND, transactionTypeConstant.DEBIT, transactionAmountFrom.POSSIBLE_WIN_AMOUNT, transactionDescriptionType.RESETTLEMENT, context)
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.REFUND, transactionTypeConstant.CREDIT, transactionAmountFrom.STAKE, transactionDescriptionType.RESETTLEMENT, context)
  } else

  // lost
  if (oldSettlementStatus === settlementStatus.WON && newSettlementStatus === settlementStatus.LOST) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.LOST_BY_RESETTLEMENT, transactionTypeConstant.DEBIT, transactionAmountFrom.POSSIBLE_WIN_AMOUNT, transactionDescriptionType.RESETTLEMENT, context)
  } else

  if (oldSettlementStatus === settlementStatus.REFUND && newSettlementStatus === settlementStatus.LOST) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.LOST_BY_RESETTLEMENT, transactionTypeConstant.DEBIT, transactionAmountFrom.STAKE, transactionDescriptionType.RESETTLEMENT, context)
  } else

  // won
  if (oldSettlementStatus === settlementStatus.REFUND && newSettlementStatus === settlementStatus.WON) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.WON, transactionTypeConstant.DEBIT, transactionAmountFrom.STAKE, transactionDescriptionType.RESETTLEMENT, context)
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.WON, transactionTypeConstant.CREDIT, transactionAmountFrom.POSSIBLE_WIN_AMOUNT, transactionDescriptionType.RESETTLEMENT, context)
  } else

  if (oldSettlementStatus === settlementStatus.LOST && newSettlementStatus === settlementStatus.WON) {
    await createSettlementTransactionAndUpdateWallet(betSlipId, paymentForCodes.WON, transactionTypeConstant.CREDIT, transactionAmountFrom.POSSIBLE_WIN_AMOUNT, transactionDescriptionType.RESETTLEMENT, context)
  }
}
