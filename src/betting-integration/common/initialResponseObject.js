/**
 * The initial response in all the APIs (debit, credit and rollback) is same, new fields will be
 * added after every different validation in each API.
 * @export
 * @param {*} args The argument object
 * @return {*} A response object
 */
export default function initialResponseObject (args) {
  return {
    uid: args.uid,
    roundId: args.roundId,
    operatorId: args.operatorId,
    transactionId: args.transactionId,
    timestamp: new Date().getTime(),
    balance: 0.00,
    currency: args.currency,
    errorCode: 0,
    errorDescription: 'Complete'
  }
}
