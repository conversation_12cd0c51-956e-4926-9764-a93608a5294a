import { parse } from 'node-html-parser';
import { HTTP_STATUS } from '../../common/constants';
import ServiceBase from '../../common/serviceBase';
import translate from '../../lib/languageTranslate';
import { errorChecks } from '../common/commonErrorCodeChecks';
import TenantCredential from '../common/tenantCredential';
const axios = require('axios')

/**
 * Get Sap Exchange Transaction
 * @export
 * @class GetSapExchangeTransactions
 * @extends {ServiceBase}
 */
export default class SapExchangeTransactionDescription extends ServiceBase {
  async run () {
    try {
      const responseObject = {}
      const {
        context: {
          tenant: Tenant,
          databaseConnection: {
            Transaction: TransactionModel
          },
        },
      } = this

      // Token verification process
      const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context);
      const siteKey = ezugiCredentials['ST8_SITE_KEY'];

      const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject);
      if (!tokenVerificationData) {
        return {
          status: HTTP_STATUS.BAD_REQUEST,
          message: translate('INVALID_TOKEN', this.context.language),
        };
      }

      const transaction = await TransactionModel.findOne({
        attributes: ['id', 'transactionId', 'roundId'],
        where: { id: this.args.transactionId, tenantId: Tenant.id },
        raw: true,
      });

      if (!transaction) {
        return {
          status: HTTP_STATUS.BAD_REQUEST,
          message: 'Transaction not found',
        };
      }

      const { transactionId, roundId } = transaction;

      if (!siteKey || !transactionId || !roundId) {
        return {
          status: HTTP_STATUS.BAD_REQUEST,
          message: 'Missing required parameters',
        };
      }

      const url = this.context.headers.host;
      const substringUrl = url.substring(4);
      const modifiedDomain = substringUrl === 'hal567.com' ? `https://st8-api.${substringUrl}` : `https://st8.${substringUrl}`;
      const st8_microservice_url = `${modifiedDomain}/api/v1/`;

      const st8Params = {
        roundId,
        transactionId,
        site: siteKey,
      };

      const config = {
        method: 'post',
        url: `${st8_microservice_url}st8/getRoundInfo`,
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(st8Params)
      };

      let st8Response = await axios(config)

      const { data: response } = st8Response;
      if (!response || response.status !== 'ok') {
          return {
              status: HTTP_STATUS.BAD_REQUEST,
              message: 'Failed to get round info',
          };
      }
      if (response.url) {
        const finalResponse = await axios.get(response.url, {
          headers: { 'Content-Type': 'application/json' },
        });

        const root = parse(finalResponse.data);
        const preTag = root.querySelector('body main pre');

        if (!preTag) {
          responseObject.status = HTTP_STATUS.BAD_REQUEST;
          responseObject.message = 'Failed to extract content from HTML';
          return responseObject;
        }

        const htmltoJsonData = JSON.parse(preTag.text);
        const mainData = htmltoJsonData?.original_metadata?.bets[0];

        responseObject.data = {
          sportName: mainData?.sportName,
          eventName: mainData?.eventName,
          marketName: mainData?.marketName,
          competitionName: mainData?.competitionName,
          status: mainData?.status
        }
      }

      responseObject.status = HTTP_STATUS.OK;
      responseObject.token = tokenVerificationData.newToken;
      return responseObject;
    } catch(error) {
    }
  }
}
