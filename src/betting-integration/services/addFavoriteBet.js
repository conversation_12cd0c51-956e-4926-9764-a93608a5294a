import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Add a favorite bet for corresponding user
 * @export
 * @class AddFavoriteBet
 * @extends {ServiceBase}
 */
export default class AddFavoriteBet extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          PullsEvent: PullsEventModel,
          BetsFavorite: BetsFavoriteModel
        },
        headers: { language }
      },
      args: {
        userDetail,
        events
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const result = []
    for (const data of events) {
      const event = await PullsEventModel.findOne({ where: { fixtureId: data }, raw: true })
      if (!event) {
        responseObject.message = translate('EVENT_NOT_FOUND', language)
        responseObject.status = 404
        return responseObject
      }

      const alreadyExist = await BetsFavoriteModel.findOne({ where: { eventId: event.id, userId: userDetail.id, isDeleted: false }, raw: true })

      if (alreadyExist) {
        responseObject.message = translate('ALREADY_ADDED_FAVORITE_BET', language)
        responseObject.status = 404
        return responseObject
      }

      result.push(await BetsFavoriteModel.create({ eventId: event.id, userId: userDetail.id, isDeleted: false }))
    }

    responseObject.data = result
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 201
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
