import { Op } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import { ALLOWED_SPORTS } from '../common/constant'

/**
 * Provides the service to update pulls sports table
 * @export
 * @class UpdatePullSports
 * @extends {ServiceBase}
 */
export default class UpdatePullSports extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          PullsSport: PullsSportModel
        }
      }
    } = this
    const responseObject = {}

    await PullsSportModel.update({ isDeleted: true },
      {
        where: { sportId: { [Op.notIn]: ALLOWED_SPORTS } }
      })

    responseObject.status = 204
    return responseObject
  }
}
