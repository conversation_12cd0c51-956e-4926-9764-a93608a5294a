import ServiceBase from '../../common/serviceBase'

/**
 * Get top leagues
 * @export
 * @class GetTopLeagues
 * @extends {ServiceBase}
 */
export default class GetTopLeagues extends ServiceBase {
  async run () {
    const responseObject = {}

    const homePageLeagues = [
      { league_name_en: 'UEFA Champions League', league_id: 32644, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'UEFA Europa League', league_id: 30444, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'Bundesliga', league_id: 65, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'LaLiga', league_id: 8363, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'Super Lig', league_id: 63, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'Premier League', league_id: 67, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'Eredivisie', league_id: 2944, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'Serie A', league_id: 4, is_favorite: false, sport_id: 6046 },
      { league_name_en: 'NBA', league_id: 64, is_favorite: false, sport_id: 48242 },
      { league_name_en: 'UFC', league_id: 14896, is_favorite: false, sport_id: 154919 }
    ]

    responseObject.data = { homePageLeagues }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 201

    return responseObject
  }
}
