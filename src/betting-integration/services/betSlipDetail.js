import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Provides betSlip details
 * @export
 * @class BetSlipDetail
 * @extends {ServiceBase}
 */
export default class BetSlipDetail extends ServiceBase {
  async run () {
    let {
      context: {
        headers: { language },
        databaseConnection: {
          BetsBet: BetModel,
          BetsBetslip: BetSlipModel,
          // PullsEventparticipant: PullsEventParticipantModel,
          // PullsParticipant: PullsParticipantModel,
          PullsEvent: PullsEventModel,
          PullsLeague: PullsLeagueModel
        }
      },
      args: {
        betSlipId,
        id: userId
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)
    betSlipId = parseInt(betSlipId)

    if (isNaN(betSlipId)) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_INPUT', language)
      return responseObject
    }

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    // const name = !language ? 'en' : language.split('-')[0]
    let betSlip = await BetSlipModel.findOne({
      where: {
        id: betSlipId,
        userId
      },
      include: [
        {
          model: BetModel,
          as: 'bets',
          include: [
            // {
            //   model: PullsEventParticipantModel,
            //   as: 'Participants',
            //   attributes: ['position'],
            //   include: {
            //     model: PullsParticipantModel,
            //     as: 'participant',
            //     attributes: ['id', 'participant_id', ['name_en', 'name']]
            //   }
            // },
            {
              model: PullsEventModel,
              attributes: ['nameEn'],
              include: {
                model: PullsLeagueModel,
                attributes: ['leagueId', ['name_en', 'name']]
              }
            }
          ]
        }
      ]
    })

    betSlip = JSON.parse(JSON.stringify(betSlip))

    responseObject.data = betSlip
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
