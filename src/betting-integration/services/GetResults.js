import ServiceBase from '../../common/serviceBase'
import languageTranslate from '../../lib/languageTranslate'

/**
 * Get results for the finished sports
 * @export
 * @class GetResults
 * @extends {ServiceBase}
 */
export default class GetResults extends ServiceBase {
  async run () {
    let {
      context: {
        databaseConnection: {
          PullsEvent: PullsEventModel,
          PullsParticipant: PullsParticipantModel
        },
        headers: { language }
      },
      args: {
        limit,
        sportId,
        page,
        locationId
      }
    } = this

    const responseObject = {}

    const whereCondition = {}

    page = +page || 1
    limit = +limit || 20
    const offset = (page - 1) * limit

    whereCondition.fixtureStatus = 3

    if (sportId) {
      whereCondition.sportId = sportId
    }

    if (locationId) {
      whereCondition.locationId = locationId
    }

    const matches = await PullsEventModel.findAndCountAll({
      where: whereCondition,
      limit,
      offset,
      distinct: true,
      order: [['createdAt', 'DESC']],
      include: {
        model: PullsParticipantModel
      }
    })

    if (matches.count === 0) {
      responseObject.message = languageTranslate('NO_DATA', language)
    }

    responseObject.data = { rows: matches.rows, count: matches.count }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200

    return responseObject
  }
}
