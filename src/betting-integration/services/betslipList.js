import { Op } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

const constraints = {
  betStatus: {
    type: 'string'
  },
  page: {
    type: 'string',
    presence: true
  },
  limit: {
    type: 'string',
    presence: true
  },
  startDate: {
    type: 'string'
  },
  endDate: {
    type: 'string'
  }

}

/**
 * Provides the bets slip list for a user
 * @export
 * @class BetslipList
 * @extends {ServiceBase}
 */
export default class BetslipList extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const language = this.context.headers.language
    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    // Get list of betslips
    const {
      BetsBetslip: BetSlipModel,
      BetsBet: BetsBetModel,
      PullsParticipant: PullsParticipantModel,
      PullsEventparticipant: PullsEventparticipantModel
    } = this.context.databaseConnection

    const whereCondition = {}
    const limit = +this.args.limit || 25
    const offset = limit * (parseInt(this.args.page) - 1)

    whereCondition.userId = this.args.id
    whereCondition.is_deleted = false
    whereCondition.betslipStatus = { [Op.notILike]: 'pending' }

    const startDate = this.args?.startDate
    const endDate = new Date(this.args.endDate || new Date()).setDate(new Date(this.args.endDate || new Date()).getDate())

    if (startDate && startDate !== '') {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      }
    } else {
      whereCondition.createdAt = {
        [Op.lte]: new Date(endDate)
      }
    }

    // if (this.args.betType) {
    //   whereCondition.bettype = this.args.betType
    // }

    if (this.args.betStatus === 'unsettled') {
      whereCondition.settlementStatus = 'in_game'
    } else if (this.args.betStatus === 'settled') {
      whereCondition.settlementStatus = { [Op.notILike]: 'in_game' }
    }

    // const name = !language ? 'en' : language.split('-')[0]

    const betslipList = await BetSlipModel.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: BetsBetModel,
          as: 'bets',
          attributes: ['id'],
          include: {
            model: PullsEventparticipantModel,
            as: 'Participants',
            attributes: ['position'],
            include: {
              model: PullsParticipantModel,
              as: 'participant',
              attributes: ['id', 'participant_id', ['name_en', 'name']]
              // attributes: ['id', 'participant_id', [`name_${name}`, 'name']]
            }
          }
        }
      ],
      limit,
      distinct: true,
      offset,
      order: [['created_at', 'DESC']]
    })

    responseObject.data = { betslips: betslipList?.rows, count: betslipList?.count }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
