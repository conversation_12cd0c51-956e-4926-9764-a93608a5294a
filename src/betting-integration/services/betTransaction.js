import moment from 'moment'
import { Op, Sequelize } from 'sequelize'
import { CASINO_PROVIDER, ENVIORNMENT } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'
/**
 * Provides service for the Auth api in the live-ezugi
 * @export
 * @class Bet Transaction
 * @extends {ServiceBase}
 */
export default class BetTransaction extends ServiceBase {
  async run () {
    const language = this.context.headers.language
    const responseObject = {}
    const {
      from,
      to,
      id: userId,
      provider
    } = this.args

    // Token verification process
    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const page = +this.args.page
    const whereCondition = { userId }
    const limit = +this.args.limit || 25
    const offset = limit * (page - 1)
    const userCountryCode = this.args.user_country_code

    // get data from BetTransaction Model
    const BetTransactionModel = this.context.databaseConnection.BetsTransaction
    const BetsBetModel = this.context.databaseConnection.BetsBet

    // get value of query params
    const activityType = this.args.activity_type

    let fromDate = from
    let endDate = to || new Date()

    // finding timezone based on user country code
    const timezone = userCountryCode && moment.tz.zonesForCountry(userCountryCode)[0]
    // adjusting fromDate and endDate based on timezone
    if (timezone) {
      fromDate = moment.tz(fromDate, timezone).utc().format()
      endDate = moment.tz(endDate, timezone).utc().format()
    }

    // endDate = new Date(to || new Date()).setDate(new Date(to || new Date()).getDate() + 1)

    if (fromDate) {
      whereCondition.createdAt = {
        [Op.gte]: new Date(fromDate),
        [Op.lte]: new Date(endDate)
      }
    } else {
      whereCondition.createdAt = {
        [Op.lte]: new Date(endDate)
      }
    }

    // const timezoneOffset = parseFloat(this.args.timezone_offset)
    // const startDate = moment.utc(fromDate).add('hours', timezoneOffset).format('YYYY-MM-DD HH:mm')
    // const endDate = moment.utc(toDate).add('hours', timezoneOffset).format('YYYY-MM-DD HH:mm')

    // check activity type and strat_date and end_date
    if (activityType && activityType !== 'all') {
      whereCondition.journalEntry = activityType
    }

    const isProduction = config.get('env') === ENVIORNMENT.PRODUCTION

    const providerMap = {
      jetfair: isProduction ? CASINO_PROVIDER.JETFAIR.PROD : CASINO_PROVIDER.JETFAIR.STAGE,
      powerplay: isProduction ? CASINO_PROVIDER.POWERPLAY.PROD : CASINO_PROVIDER.POWERPLAY.STAGE,
      turbostars: isProduction ? CASINO_PROVIDER.TURBO_STARS.PROD : CASINO_PROVIDER.TURBO_STARS.STAGE
    }

    if (providerMap[provider]) {
      whereCondition.providerId = providerMap[provider]
    }

    // get data from BetTransactionModel based on where condition
    const transactions = await BetTransactionModel.findAndCountAll({
      attributes: ['id', 'amount', 'created_at', 'journal_entry', 'description', 'payment_for', 'betslip_id', 'market_id', 'commission_per',
        'commission_amount', 'net_pl', 'source_before_balance', 'source_after_balance', 'target_before_balance', 'target_after_balance',
        'source_non_cash_before_balance', 'source_non_cash_after_balance', 'target_non_cash_before_balance', 'target_non_cash_after_balance',
        'source_wallet_id', 'target_wallet_id', 'transaction_id',
        [Sequelize.literal(
          'COALESCE(amount, 0) + COALESCE(non_cash_amount, 0)'
        ), 'total_amount'
        ]
      ],
      where: whereCondition,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      group: ['id']
    })
    //  To do with query
    const transObj = { ...transactions }
    const newTransaction = await Promise.all(
      transObj.rows.map(async object => {
        // Formatting created_at date with optional user timezone
        const date = timezone ? moment.tz(object.dataValues.created_at, timezone) : moment(object.dataValues.created_at)
        object.dataValues.created_at = date.format('DD/MM/YYYY HH:mm')
        if (object.dataValues.source_wallet_id) {
          object.dataValues.source_before_balance = parseFloat(object.dataValues?.source_before_balance || 0) + parseFloat(object.dataValues?.source_non_cash_before_balance || 0)
          object.dataValues.source_after_balance = parseFloat(object.dataValues?.source_after_balance || 0) + parseFloat(object.dataValues?.source_non_cash_after_balance || 0)
        } else {
          object.dataValues.source_before_balance = parseFloat(object.dataValues?.target_before_balance || 0) + parseFloat(object.dataValues?.target_non_cash_before_balance || 0)
          object.dataValues.source_after_balance = parseFloat(object.dataValues?.target_after_balance || 0) + parseFloat(object.dataValues?.target_non_cash_after_balance || 0)
        }

        object.dataValues.source_before_balance = object.dataValues.source_before_balance.toFixed(2)
        object.dataValues.source_after_balance = object.dataValues.source_after_balance.toFixed(2)

        object.dataValues.marketName = null
        if (object.dataValues.market_id) {
          let whereClause = {
            marketId: object.dataValues.market_id
          }

          // In case of turbostars marketId will not be there  in betsbets table
          if (provider == 'turbostars') {
            whereClause = {
              betId: object.dataValues.transaction_id
            }
          }

          const marketDetail = await BetsBetModel.findOne({
            where: whereClause
          })
          object.dataValues.marketName = (marketDetail?.market) ? marketDetail?.market : object.dataValues.market_id
        }
        return object
      })
    )
    responseObject.data = { transactions: newTransaction, count: transactions.count.length }
    responseObject.status = 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
