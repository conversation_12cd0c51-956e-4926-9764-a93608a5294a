import { Op } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Get all favorite bets marked by the user
 * @export
 * @class GetFavoriteBets
 * @extends {ServiceBase}
 */
export default class GetFavoriteBets extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          BetsFavorite: BetsFavoriteModel,
          PullsEvent: PullsEventModel
        },
        headers: { language }
      },
      args: {
        userDetail
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    let result = await BetsFavoriteModel.findAll({
      where: {
        userId: userDetail.id,
        isDeleted: false,
        '$PullsEvent.fixture_status$': { [Op.notIn]: [3, 4] }
      },
      attributes: [],
      include: {
        model: PullsEventModel,
        attributes: ['fixtureId', 'fixtureStatus', 'startDate']
      }
    })

    result = result.map(ele => {
      return ele.PullsEvent
    })

    result.sort((eventOne, eventTwo) => {
      return new Date(eventTwo.startDate).getTime() - new Date(eventOne.startDate).getTime()
    })

    responseObject.data = result
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 201
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
