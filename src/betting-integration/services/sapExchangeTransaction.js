import moment from 'moment'
import { Op, Sequelize } from 'sequelize'
import { HTTP_STATUS } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Get Sap Exchange Transaction
 * @export
 * @class GetSapExchangeTransactions
 * @extends {ServiceBase}
 */
export default class SapExchangeTransaction extends ServiceBase {
  async run () {
    const responseObject = {}
    const {
      context: {
        databaseConnection: {
          Transaction: TransactionModel,
        },
        headers: { language },
        tenant: { id: tenantId }
      },
      args: {
        from,
        to,
        id: userId,
      }
    } = this

    // Token verification process
    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = HTTP_STATUS.BAD_REQUEST
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const page = +this.args.page
    const whereCondition = { actioneeId: userId, tenantId, gameId: 'sap_lobby' }
    const limit = +this.args.limit || 25
    const offset = limit * (page - 1)
    const userCountryCode = this.args.user_country_code

    let fromDate = from
    let endDate = to || new Date()

    // Finding timezone based on user country code
    const timezone = userCountryCode && moment.tz.zonesForCountry(userCountryCode)[0]

    // Adjusting fromDate and endDate based on timezone
    if (timezone) {
      fromDate = moment.tz(fromDate, timezone).utc().format()
      endDate = moment.tz(endDate, timezone).utc().format()
    }

    if (fromDate) {
      whereCondition.createdAt = {
        [Op.gte]: new Date(fromDate),
        [Op.lte]: new Date(endDate)
      }
    } else {
      whereCondition.createdAt = {
        [Op.lte]: new Date(endDate)
      }
    }

    const transactions = await TransactionModel.findAndCountAll({
      attributes: ['id', 'amount', 'transactionType', 'metaData',
        [Sequelize.fn('to_char', Sequelize.col('created_at'), 'YYYY-MM-DD HH24:MI:SS'), 'date']
      ],
      where: whereCondition,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
      raw: true
    })

    let transactionList = []

    if (transactions) {
      transactionList = await Promise.all(
        transactions.rows.map(async object => {
          object['side'] = object.metaData?.provider?.metadata?.bet_type
          object['stake'] = object.metaData?.provider?.amount ? (+object.metaData?.provider?.amount).toFixed(1) : null
          object['price'] = object.metaData?.provider?.metadata?.odd_value? (+object.metaData?.provider?.metadata?.odd_value).toFixed(1) : null
          object['profit_loss'] = object.amount
          object['status'] = [1, 9].includes(object.transactionType) ? 'WIN' : 'LOSS'

          delete object.metaData
          return object
        })
      )
    }

    responseObject.data = { transactions: transactionList, count: transactions.count.length }
    responseObject.message = 'Success'
    responseObject.status = responseObject.status ? responseObject.status : HTTP_STATUS.OK

    return responseObject
  }
}
