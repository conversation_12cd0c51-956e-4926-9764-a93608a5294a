import { Op } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Get all events for a sport
 * @export
 * @class SearchEvents
 * @extends {ServiceBase}
 */
export default class SearchEvents extends ServiceBase {
  async run () {
    let {
      context: {
        databaseConnection: {
          PullsEvent: PullsEventModel,
          PullsParticipant: PullsParticipantModel,
          TenantBlockedSport: TenantBlockedSportModel
        },
        headers: { language },
        tenant: { id: tenantId }
      },
      args: {
        sport_id: sportId,
        search,
        page,
        limit
      }
    } = this

    const responseObject = {}

    const blockedSport = await TenantBlockedSportModel.findOne({
      where: {
        tenantId,
        pullsSportId: sportId
      },
      raw: true
    })

    if (blockedSport) {
      responseObject.status = 404
      responseObject.message = translate('EVENT_NOT_FOUND', language)
      return responseObject
    }

    limit = limit || 10
    const offset = ((page || 1) - 1) * limit

    const result = await PullsEventModel.findAll({
      where: {
        sportId,
        isDeleted: false
      },
      attributes: ['fixtureId', 'fixtureStatus'],
      limit,
      offset,
      include: {
        model: PullsParticipantModel,
        attributes: [],
        where: {
          [Op.or]: [
            { nameEn: { [Op.iLike]: `%${search}%` } },
            { nameDe: { [Op.iLike]: `%${search}%` } },
            { nameFr: { [Op.iLike]: `%${search}%` } },
            { nameRu: { [Op.iLike]: `%${search}%` } },
            { nameTr: { [Op.iLike]: `%${search}%` } }
          ]
        }
      }
    })

    if (result.length === 0) {
      responseObject.status = 404
      responseObject.message = translate('EVENT_NOT_FOUND', language)
    }

    responseObject.data = { result }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 201

    return responseObject
  }
}
