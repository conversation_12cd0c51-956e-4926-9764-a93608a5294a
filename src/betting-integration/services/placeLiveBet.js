//import { BONUS_TYPES, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
//import depositBonusRolloverAmountCheck from '../../ezugi-integration/common/depositBonusRolloverAmountCheck'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import { paymentForCodes, transactionAmountFrom, transactionDescriptionType, transactionTypeConstant } from '../common/constant'
import createTransactionAndUpdateWallet from '../common/createTransactionAndUpdateWallet'
import TenantCredential from '../common/tenantCredential'

const constraints = {
  bet_id: {
    type: 'string'
  },
  bet_status: {
    type: 'integer'
  },
  champ: {
    type: 'string'
  },
  fixture_id: {
    type: 'string'
  },
  fixture_status: {
    type: 'integer'
  },
  livescore: {
    type: 'object'
  },
  market: {
    type: 'string'
  },
  market_id: {
    type: 'integer'
  },
  match: {
    type: 'string'
  },
  name: {
    type: 'string'
  },
  price: {
    type: 'double'
  },
  provider_id: {
    type: 'integer'
  },
  sport_id: {
    type: 'integer'
  },
  start_date: {
    type: 'string'
  },
  bettype: {
    type: 'integer'
  },
  language: {
    type: 'string'
  },
  stake: {
    type: 'double'
  }
}

/**
 * Provides the service to update the betslip slip and bets
 * @export
 * @class PlaceLiveBets
 * @extends {ServiceBase}
 */
export default class PlaceLiveBets extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        sequelizeTransaction,
        databaseConnection: {
          BetsBetslip: BetSlipModel
        },
        headers: { language }
      },
      args: {
        userDetail: user,
        bets,
        stake,
        betslip_id: betslipId
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    let multiPrice = 1
    /*
    * As the odds can be cahnged.
    * So we need to re calucate the possible win amount and the multi price for bet slip
    * And then update it in the DB
    */

    /*
    * Updating DB for bets, need to update price.
    * Not verifing price from the event table as they don't have one.
    * Will loop on the bets and update the bet.
    */

    bets.map(element => {
      multiPrice *= (+element.price)
      this.context.databaseConnection.BetsBet.update(
        {
          price: element.price
        }, {
          where: {
            betId: element.bet_id,
            betslipId: betslipId
          }
        },
        { transaction: sequelizeTransaction }
      )
    })

    await BetSlipModel.update(
      {
        possibleWinAmount: multiPrice * stake,
        multiPrice,
        betslipStatus: 'accepted'
      }, {
        where: {
          id: betslipId
        }
      },
      { transaction: sequelizeTransaction }
    )

    const amountObject = await createTransactionAndUpdateWallet(betslipId, paymentForCodes.BET_PLACEMENT, transactionTypeConstant.DEBIT, transactionAmountFrom.STAKE, transactionDescriptionType.BETSLIP, this.context)

    // const updatedUserWallet = await depositBonusRolloverAmountCheck(this.context, {
    //   DEBIT: { amount: amountObject.amount },
    //   DEBIT_NO_CASH: { amount: amountObject.nonCashAmount }
    // }, BONUS_TYPES.DEPOSIT_SPORTS, user)

    // if (updatedUserWallet) {
    //   this.context.pubSub.publish(
    //     SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE,
    //     {
    //       UserWalletBalance: {
    //         walletBalance: updatedUserWallet?.amount,
    //         userId: user.id
    //       }
    //     }
    //   )
    // }

    responseObject.data = { betslip_id: betslipId }
    responseObject.message = responseObject.message ? responseObject.message : 'Bet placed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
