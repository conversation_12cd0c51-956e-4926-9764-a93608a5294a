import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Provides the detail for the user bet limit
 * @export
 * @class GetBetLimit
 * @extends {ServiceBase}
 */
export default class GetBetLimit extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          TenantSportsBetSetting: TenantSportsBetSettingModel
        },
        tenant: { id: tenantId },
        headers: { language }
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const betLimits = await TenantSportsBetSettingModel.findOne({ where: { tenantId }, raw: true })

    if (!betLimits) {
      responseObject.message = translate('BET_LIMIT_NOT_FOUND', language)
      responseObject.status = 404
    }

    responseObject.data = { betLimits }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
