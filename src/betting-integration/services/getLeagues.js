import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'

/**
 * Get leagues for the given sports id
 * @export
 * @class GetLeagues
 * @extends {ServiceBase}
 */
export default class GetLeagues extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          PullsLeague: PullsLeagueModel,
          PullsEvent: PullsEventModel,
          TenantBlockedLeague: TenantBlockedLeagueModel
        },
        headers: { language },
        tenant: { id: tenantId }
      },
      args: {
        sportId
      }
    } = this

    const responseObject = {}

    const blockedLeagues = await TenantBlockedLeagueModel.findAll({
      where: {
        tenantId
      },
      raw: true
    })

    const blockedLeagueArray = blockedLeagues.map(ele => ele.pullsLeagueId)

    const leagues = await PullsLeagueModel.findAll({
      where: {
        isDeleted: false,
        sportId,
        id: { [Op.notIn]: blockedLeagueArray }
      },
      attributes: ['id', 'leagueId', 'nameDe', 'season', 'locationId', 'sportId',
        'nameEn', 'nameFr', 'nameRu', 'nameTr', 'nameNl', 'createdAt', 'updatedAt',
        [Sequelize.fn('count', Sequelize.col('PullsEvents.league_id')), 'eventCount']],
      include: {
        model: PullsEventModel,
        required: true,
        attributes: []
      },
      group: ['PullsLeague.id']
    })

    if (!leagues) {
      responseObject.message = translate('LEAGUE_NOT_FOUND', language)
      responseObject.status = 404
      return responseObject
    }

    responseObject.data = { leagues }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 201

    return responseObject
  }
}
