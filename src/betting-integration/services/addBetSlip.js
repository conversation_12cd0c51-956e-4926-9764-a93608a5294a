//import { BONUS_TYPES, SUBSCRIPTION_CHANNEL } from '../../common/constants'
import ServiceBase from '../../common/serviceBase'
//import depositBonusRolloverAmountCheck from '../../ezugi-integration/common/depositBonusRolloverAmountCheck'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import { paymentForCodes, settlementStatus, transactionAmountFrom, transactionDescriptionType, transactionTypeConstant } from '../common/constant'
import createTransactionAndUpdateWallet from '../common/createTransactionAndUpdateWallet'
import eventLiabilityCheck from '../common/eventLiabilityCheck'
import TenantCredential from '../common/tenantCredential'
import { verifyFixtureStatus } from '../common/transaction'

const constraints = {
  bet_id: {
    type: 'string'
  },
  bet_status: {
    type: 'integer'
  },
  champ: {
    type: 'string'
  },
  fixture_id: {
    type: 'string'
  },
  fixture_status: {
    type: 'integer'
  },
  livescore: {
    type: 'object'
  },
  market: {
    type: 'string'
  },
  market_id: {
    type: 'integer'
  },
  match: {
    type: 'string'
  },
  name: {
    type: 'string'
  },
  price: {
    type: 'double'
  },
  provider_id: {
    type: 'integer'
  },
  sport_id: {
    type: 'integer'
  },
  start_date: {
    type: 'string'
  },
  bettype: {
    type: 'integer'
  },
  language: {
    type: 'string'
  },
  stake: {
    type: 'double'
  }
}

/**
 * Provides the service to add the bets slip in the database
 * @export
 * @class AddBetSlip
 * @extends {ServiceBase}
 */
export default class AddBetSlip extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      context: {
        databaseConnection: {
          BetsBetslip: BetSlipModel,
          BetsBet: BetModel
        },
        headers: { language }
      },
      args: {
        bets,
        bettype,
        stake,
        id: userId,
        userDetail: user,
        totalStake,
        possibleWinAmount
      }
    } = this
    const { sequelizeTransaction } = this.context
    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    let multiPrice = 1
    const betCreateObject = []
    const fixtureIds = []
    bets.forEach(element => {
      multiPrice *= element.price
      fixtureIds.push(element.fixture_id)
    })

    /* Need to fetch events from dynamo to check match status and use odds from dynamo */
    const insertInTransaction = await verifyFixtureStatus(bets, fixtureIds, this.context)

    if (!insertInTransaction) {
      responseObject.message = translate('EVENT_NOT_FOUND', language)
      responseObject.status = 400
      return responseObject
    }

    const createObject = {
      settlementStatus: settlementStatus.IN_GAME,
      isDeleted: false,
      bettype: bettype,
      stake: stake,
      userId: userId,
      multiPrice,
      betslipStatus: insertInTransaction.inTransaction ? 'accepted' : 'pending',
      couponId: 12345,
      possibleWinAmount: possibleWinAmount,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const { betsArray } = insertInTransaction

    const errorObject = await eventLiabilityCheck(stake, totalStake, betsArray, this.context)

    if (errorObject) {
      return errorObject
    }

    const betslipDetail = await BetSlipModel.create(createObject, { transaction: sequelizeTransaction })

    const betSlipId = betslipDetail.id
    betslipDetail.couponId = Number(`${new Date().getMilliseconds()}${betSlipId}`)
    await betslipDetail.save({ transaction: sequelizeTransaction })

    for (const bet of betsArray) {
      betCreateObject.push({
        isDeleted: false,
        betId: bet.bet_id,
        fixtureId: bet.fixture_id,
        providerId: bet.provider_id,
        champ: bet.champ,
        match: bet.match,
        market: bet.market,
        name: bet.name,
        price: bet.price,
        startDate: bet.start_date,
        betslipId: betSlipId,
        marketId: bet.market_id,
        betStatus: bet.bet_status,
        eventId: bet.event_id,
        livescore: {},
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }

    await BetModel.bulkCreate(betCreateObject, { transaction: sequelizeTransaction })
    let amountObject
    let updatedUserWallet = null
    if (createObject.betslipStatus === 'accepted') {
      // insert in transaction.
      amountObject = await createTransactionAndUpdateWallet(
        betslipDetail.id,
        paymentForCodes.BET_PLACEMENT,
        transactionTypeConstant.DEBIT,
        transactionAmountFrom.STAKE,
        transactionDescriptionType.BETSLIP,
        this.context
      )

      // updatedUserWallet = await depositBonusRolloverAmountCheck(this.context, {
      //   DEBIT: { amount: amountObject.amount },
      //   DEBIT_NO_CASH: { amount: amountObject.nonCashAmount }
      // }, BONUS_TYPES.DEPOSIT_SPORTS, user)
    }

    // if (updatedUserWallet) {
    //   this.context.pubSub.publish(
    //     SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE,
    //     {
    //       UserWalletBalance: {
    //         walletBalance: updatedUserWallet?.amount,
    //         userId: user.id
    //       }
    //     }
    //   )
    // }

    responseObject.data = { betslip_id: betslipDetail.id }
    responseObject.message = responseObject.message ? responseObject.message : 'Bet created'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
