import axios from 'axios'
import ServiceBase from '../../common/serviceBase'

const constraints = {
  fixture_id: {
    type: 'integer',
    presence: { message: 'is required' }
  },
  fixture_status: {
    type: 'integer',
    presence: { message: 'is required' }
  }
}

/**
 * Provides the service to update the fixture status in event
 * table in the database
 * @export
 * @class updateFixture
 * @extends {ServiceBase}
 */
export default class UpdateFixture extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const responseObject = {}

    const { fixture_status: fixtureStatus, fixture_id: fixtureID } = this.args

    axios.defaults.headers.post['x-api-key'] = this.context.headers['x-api-key']
    axios.defaults.headers.post['domain-key'] = 'http://fgsubtest1.com'
    axios.post('http://3.1.124.46/betting/update_fixture', {
      data: this.args
    })

    const { PullsEvent: PullsEventModel } = this.context.databaseConnection
    await PullsEventModel.update(
      {
        fixtureStatus: fixtureStatus
      }, {
        where: {
          fixture_id: fixtureID
        }
      }
    )

    responseObject.data = { fixture_id: fixtureID }
    responseObject.message = responseObject.message ? responseObject.message : 'Event updated'
    responseObject.status = responseObject.status ? responseObject.status : 200

    return responseObject
  }
}
