import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * remove favorite bet of the user
 * @export
 * @class RemoveFavoriteBet
 * @extends {ServiceBase}
 */
export default class RemoveFavoriteBet extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          BetsFavorite: BetsFavoriteModel,
          PullsEvent: PullsEventModel
        },
        headers: { language }
      },
      args: {
        params: { favoriteId },
        body: { userDetail: { id: userId } }
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const event = await PullsEventModel.findOne({ where: { fixtureId: favoriteId }, raw: true })

    if (!event) {
      responseObject.message = translate('EVENT_NOT_FOUND', language)
      responseObject.status = 404
      return responseObject
    }

    await BetsFavoriteModel.destroy({ where: { eventId: event.id, userId } })

    responseObject.result = {}
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 204
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
