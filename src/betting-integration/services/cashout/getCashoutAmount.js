import ServiceBase from '../../../common/serviceBase'
import translate from '../../../lib/languageTranslate'
import CalculateCashout from '../../common/cashoutFormula/calculatCashout'
import { errorChecks } from '../../common/commonErrorCodeChecks'
import getCurrentTotalOdds from '../../common/getCurrentTotalOdds'
import TenantCredential from '../../common/tenantCredential'

const constraints = {
  betslip_id: {
    type: 'string'
  },
  total_odds: {
    type: 'string'
  },
  stake_price: {
    type: 'string'
  }
}
/**
 * Provides the cashout amount
 * @export
 * @class GetCashoutAmount
 * @extends {ServiceBase}
 */
export default class GetCashoutAmount extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const language = this.context.headers.language
    const responseObject = {}
    let { betslip_id: betslipId } = this.args
    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)
    const { docClient } = this.context
    betslipId = +betslipId

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const {
      BetsBetslip: BetsBetSlipModel,
      BetsBet: BetsBetModel,
      PullsEvent: PullsEventModel,
      TenantSportsBetSetting: TenantSportsBetSettingModel
    } = this.context.databaseConnection

    const tenantLimits = await TenantSportsBetSettingModel.findOne({ where: { tenantId: this.context.tenant.id }, raw: true })

    const betSlip = await BetsBetSlipModel.findOne({
      where: { id: betslipId },
      include: {
        model: BetsBetModel,
        as: 'bets',
        include: {
          model: PullsEventModel
        }
      }
    })

    if (betSlip.betslipStatus === 'completed') {
      responseObject.status = 400
      responseObject.message = translate('CASH_OUT_CAN_NOT_PROCESS', language)
      return responseObject
    }

    const totalOdds = await getCurrentTotalOdds(betSlip.bets, docClient)

    if (!totalOdds) {
      responseObject.status = 400
      responseObject.message = translate('CASH_OUT_CAN_NOT_PROCESS', language)
      return responseObject
    }

    const initialOdd = betSlip.multiPrice
    const cashout = await CalculateCashout(+initialOdd, totalOdds, +betSlip.stake, +tenantLimits.cashoutPercentage)
    responseObject.data = { cashout }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
