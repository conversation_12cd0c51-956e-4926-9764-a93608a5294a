import ServiceBase from '../../../common/serviceBase'
import translate from '../../../lib/languageTranslate'
import { errorChecks } from '../../common/commonErrorCodeChecks'
import { paymentForCodes, transactionDescriptionType, transactionTypeConstant } from '../../common/constant'
import createTransactionAndUpdateWallet from '../../common/createTransactionAndUpdateWallet'
import TenantCredential from '../../common/tenantCredential'

const constraints = {
  betslip_id: {
    type: 'integer'
  },
  cashout_amount: {
    type: 'double'
  }
}

/**
 * Provides the detail for the user bet limit
 * @export
 * @class GetBetLimit
 * @extends {ServiceBase}
 */
export default class ProcessCashoutAmount extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      args: {
        userDetail,
        betslip_id: betslipId,
        cashout_amount: cashoutAmount
      },
      context: {
        sequelizeTransaction,
        headers: { language },
        databaseConnection: {
          BetsBetslip: BetSlipModel
        }
      }
    } = this

    const responseObject = {}

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const createObject = {}
    createObject.betslipId = betslipId
    createObject.userId = userDetail.id
    createObject.tenantId = this.context.tenant.id
    createObject.stake = cashoutAmount
    await createTransactionAndUpdateWallet(betslipId, paymentForCodes.CASHOUT, transactionTypeConstant.CASHOUT, cashoutAmount, transactionDescriptionType.CASHOUT, this.context)

    await BetSlipModel.update({ betslipStatus: 'cashout', settlementStatus: 'cashout' }, { where: { id: betslipId } }, { transaction: sequelizeTransaction })

    responseObject.data = { betslipId }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
