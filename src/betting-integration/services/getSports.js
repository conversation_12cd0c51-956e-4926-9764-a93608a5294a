import { Op } from 'sequelize'
import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Provides the detail for the user bet limit
 * @export
 * @class GetSports
 * @extends {ServiceBase}
 */
export default class GetSports extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          PullsSport: PullsSportModel,
          TenantBlockedSport: TenantBlockedSportModel
        },
        tenant: { id: tenantId },
        headers: { language }
      }
    } = this
    const responseObject = {}
    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) return responseObject

    // Get list of sports

    const blockedSports = await TenantBlockedSportModel.findAll({
      where: {
        tenantId
      },
      raw: true
    })

    const blockedSportsArray = blockedSports.map(ele => ele.pullsSportId)

    const sports = await PullsSportModel.findAll({
      where: {
        isDeleted: false,
        id: { [Op.notIn]: blockedSportsArray }
      },
      raw: true
    })

    if (!sports.length) {
      responseObject.message = translate('SPORTS_NOT_FOUND', language)
      responseObject.status = 404
    }

    responseObject.data = { sports }
    responseObject.message = responseObject.message ? responseObject.message : 'Completed'
    responseObject.status = responseObject.status ? responseObject.status : 200
    responseObject.token = tokenVerificationData.newToken

    return responseObject
  }
}
