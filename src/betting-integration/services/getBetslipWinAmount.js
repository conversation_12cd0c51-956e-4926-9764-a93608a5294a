import ServiceBase from '../../common/serviceBase'
import calculateWinAmount from '../common/calculateWinAmount'

/**
 * Provides the service to get betslip win amount
 * @export
 * @class GetBetslipWinAmount
 * @extends {ServiceBase}
 */
export default class GetBetslipWinAmount extends ServiceBase {
  async run () {
    const {
      context: {
        databaseConnection: {
          BetsBetslip: BetSlipModel
        }
      },
      args: {
        betslipId
      }
    } = this

    const responseObject = {}

    const betSlip = await BetSlipModel.findOne({ where: { id: betslipId }, raw: true })

    if (betSlip.settlementStatus === 'in_game') {
      responseObject.data = { winAmount: 0 }
    } else

    if (betSlip.settlementStatus === 'lost') {
      responseObject.data = { winAmount: 0 }
    } else

    if (betSlip.settlementStatus === 'refund') {
      responseObject.data = { winAmount: betSlip.stake }
    } else

    if (betSlip.settlementStatus === 'won') {
      const amount = await calculateWinAmount(betSlip.stake, betSlip.id, this.context)
      responseObject.data = { winAmount: amount }
    } else {
      responseObject.data = { winAmount: 0 }
    }

    responseObject.status = responseObject.status ? responseObject.status : 200

    return responseObject
  }
}
