import ServiceBase from '../../common/serviceBase'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import TenantCredential from '../common/tenantCredential'

/**
 * Provides service for the Auth api in the live-ezugi
 * @export
 * @class Last Bet
 * @extends {ServiceBase}
 */
export default class LastBet extends ServiceBase {
  async run () {
    const language = this.context.headers.language
    let temp = {}
    const responseObject = {}
    const dataArray = []

    // Token verification process
    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.id, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    const betslipId = this.args.betslip_id
    const BetModel = this.context.databaseConnection.BetsBet
    const PullsEventModel = this.context.databaseConnection.PullsEvent
    const betsData = await BetModel.findAll({
      where: {
        betslipId: betslipId
      },
      include: [{
        model: PullsEventModel
      }]
    })
    for (let i = 0; i < betsData.length; i++) {
      const tempData = {
        fixture_status: betsData[i].PullsEvent.fixtureStatus,
        is_event_blacklisted: betsData[i].PullsEvent.isEventBlacklisted,
        last_update: betsData[i].PullsEvent.lastUpdate,
        league: {
          Id: betsData[i].PullsEvent.leagueId,
          Name: betsData[i].PullsEvent.leagueNameEn,
          NAME_De: betsData[i].PullsEvent.leagueNameDe,
          NAME_FR: betsData[i].PullsEvent.leagueNameFr,
          NAME_Ru: betsData[i].PullsEvent.leagueNameRu,
          NAME_Tr: betsData[i].PullsEvent.leagueNameTr,
          league_id: betsData[i].PullsEvent.leagueId,
          location: {
            Id: betsData[i].PullsEvent.locationId,
            Name: betsData[i].PullsEvent.locationNameEn,
            NAME_De: betsData[i].PullsEvent.locationNameDe,
            NAME_FR: betsData[i].PullsEvent.locationNameFr,
            NAME_Ru: betsData[i].PullsEvent.locationNameRu,
            NAME_Tr: betsData[i].PullsEvent.locationNameTr
          },
          location_id: betsData[i].PullsEvent.locationId,
          fixture_id: betsData[i].PullsEvent.fixtureId,
          start_date: betsData[i].PullsEvent.startDate,
          livescore: {}
        }
      }
      const bets = {}
      bets['id_' + betsData[i].betId.replace(/\n/g, '')] = {
        Id: betsData[i].betId.replace(/\n/g, ''),
        Name: betsData[i].name,
        Status: betsData[i].betStatus,
        Price: betsData[i].price,
        ProviderBetId: betsData[i].providerId,
        LastUpdate: betsData[i].updatedAt
      }
      temp = {
        bet_id: betsData[i].betId.replace(/\n/g, ''),
        providerId: betsData[i].providerId,
        market: {
          Id: betsData[i].id,
          Name: betsData[i].market,
          bets: bets
        },
        event: tempData
      }
      dataArray.push(temp)
    }
    responseObject.data = { bets: dataArray }
    responseObject.status = 200
    responseObject.message = 'completed'
    return responseObject
  }
}
