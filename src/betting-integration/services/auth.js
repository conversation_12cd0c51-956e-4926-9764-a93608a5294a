import ServiceBase from '../../common/serviceBase'
import { hashCheck } from '../../lib/encryption'
import translate from '../../lib/languageTranslate'
import { errorChecks } from '../common/commonErrorCodeChecks'
import initialResponseObject from '../common/initialResponseObject'
import TenantCredential from '../common/tenantCredential'
import { validationChecks } from '../common/validationErrorChecks'

const constraints = {
  operatorId: {
    type: 'integer'
  },
  token: {
    type: 'string'
  },
  platformId: {
    type: 'integer'
  },
  timestamp: {
    type: 'integer'
  }
}

/**
 * Provides service for the Auth api in the live-ezugi
 * @export
 * @class Auth
 * @extends {ServiceBase}
 */
export default class Auth extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const language = this.context.headers.language
    const responseObject = initialResponseObject(this.args)

    const { result: ezugiCredentials } = await TenantCredential.execute({}, this.context)

    // Hash check
    if (!await hashCheck(ezugiCredentials, this.context.headers.hash, JSON.stringify(this.args))) {
      responseObject.errorCode = 1
      responseObject.errorDescription = 'Hash signature does not match'

      return responseObject
    }

    // Token verification process
    const tokenVerificationData = await errorChecks.tokenNotFoundCheck(ezugiCredentials, this.args.token, responseObject)
    if (!tokenVerificationData) {
      responseObject.status = 400
      responseObject.message = translate('INVALID_TOKEN', language)
      return responseObject
    }

    // user check
    const user = await errorChecks.userNotFoundCheck(this.context, tokenVerificationData, responseObject)
    if (!user) return responseObject

    // user document check
    const isVerifiedDocument = await errorChecks.userDocumentCheck(this.context, tokenVerificationData, responseObject)
    if (!isVerifiedDocument) return responseObject

    // self exclusion check
    const allowed = await validationChecks.selfExclusionCheck(user, responseObject)
    if (!allowed) return responseObject

    responseObject.token = tokenVerificationData.newToken
    responseObject.status = 201
    responseObject.data = {
      balance: user.Wallet.amount + user.Wallet.nonCashAmount,
      uid: tokenVerificationData.id,
      currency: user.Wallet.Currency.code,
      nickName: user.firstName
    }

    return responseObject
  }
}
