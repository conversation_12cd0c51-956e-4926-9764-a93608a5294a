import translate from '../../lib/languageTranslate'
import { response } from '../common/response'

/**
 * Verify user auth token and check if the user is active
 * @param {object} req
 * @param {object} res
 * @param {object} next
 */
export default async (req, res, next) => {
  const {
    body: data,
    databaseConnection: {
      Wallet: WalletModel
    },
    headers: { language }
  } = req

  const userWallet = await WalletModel.findOne({ where: { ownerId: data.userDetail.id, ownerType: 'User' }, raw: true })

  if (((+userWallet.amount) + (+userWallet.nonCashAmount)) < +data.stake) {
    return response(res, { status: 400, message: translate('INSUFFICIENT_WALLET_BALANCE', language) })
  }

  next()
}
