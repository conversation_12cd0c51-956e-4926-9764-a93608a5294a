import { API_KEY } from '../../common/constants'
import translate from '../../lib/languageTranslate'
import { response } from '../common/response'

/**
 * Verify api key
 * @param {object} req
 * @param {object} res
 * @param {object} next
 */
export default async (req, res, next) => {
  const { headers } = req

  const key = headers['x-api-key']
  if (!key) {
    return response(res, { status: 403, message: translate('API_KEY_REQUIRED', headers.language) })
  }

  if (!API_KEY[key]) {
    return response(res, { status: 403, message: translate('INVALID_API_KEY', headers.language) })
  }
  next()
}
