import translate from '../../lib/languageTranslate'
import toFixedDecimal from '../../lib/toFixedDecimal'
import { response } from '../common/response'

export default async (req, res, next) => {
  const {
    body: {
      bets,
      fastBet
    },
    headers: { language },
    docClient
  } = req

  for (const bet of bets) {
    if (+bet.fixture_status === 2) {
      const queryParams = {
        TableName: 'BetBox',
        KeyConditionExpression: 'bet_id = :betId',

        ExpressionAttributeValues: {
          ':betId': `${bet.bet_id}`
        },

        ScanIndexForward: false,

        Limit: 1
      }

      const result = await docClient.query(queryParams).promise()

      if (!result.Count) {
        return response(res, { status: 404, message: translate('EVENT_NOT_FOUND', language) })
      }
      const tableData = result.Items[0]

      if (fastBet) {
        bet.price = +tableData.price
      } else {
        if (bet.price !== parseFloat(toFixedDecimal(+tableData.price))) {
          return response(res, { status: 400, message: translate('DATA_MISMATCH', language) })
        }
      }
    } else {
      const queryParams = {
        TableName: 'DeventMarkets',
        KeyConditionExpression: 'fixture_id = :fixtureId',

        ExpressionAttributeValues: {
          ':fixtureId': `${bet.fixture_id}`
        }

      }

      const result = await docClient.query(queryParams).promise()

      if (!result.Count) {
        return response(res, { status: 404, message: translate('EVENT_NOT_FOUND', language) })
      }
      const tableData = result.Items[0]

      const market = JSON.parse(tableData.market)

      const betDetails = market[`id_${bet.market_id}`]?.Bets[`id_${bet.bet_id}`]

      if (!betDetails) {
        return response(res, { status: 404, message: translate('EVENT_NOT_FOUND', language) })
      }

      if (fastBet) {
        bet.price = +betDetails.Price
      } else {
        if (bet.price !== parseFloat(toFixedDecimal(+betDetails.Price))) {
          return response(res, { status: 400, message: translate('DATA_MISMATCH', language) })
        }
      }
    }
  }

  next()
}
