import translate from '../../lib/languageTranslate'
import { response } from '../common/response'

/**
 * Verify user auth token and check if the user is active
 * @param {object} req
 * @param {object} res
 * @param {object} next
 */
export default async (req, res, next) => {
  const {
    body: data,
    databaseConnection: {
      TenantSportsBetSetting: TenantSportsBetSettingModel
    },
    headers: { language }
  } = req

  let maximumWinAmount = 0
  let totalStake = 0
  let allPriceSum = 1
  const betsLimits = await TenantSportsBetSettingModel.findOne({ where: { tenantId: req.tenant.id }, raw: true })

  if (betsLimits.betDisabled) {
    return response(res, { status: 500, message: translate('BET_DISABLED', language) })
  }

  for (const bet of data.bets) {
    totalStake += bet.price
    allPriceSum *= bet.price
  }
  totalStake = +(totalStake.toFixed(2))
  maximumWinAmount = allPriceSum * data.stake

  if (data.bettype === 1 && (data.stake < +betsLimits.minBet || data.stake > +betsLimits.maxSingleBet)) {
    return response(res, { status: 400, message: translate('INVALID_STACK_AMOUNT', language) })
  }

  if (data.bettype === 2 && (data.stake < +betsLimits.minBet || data.stake > +betsLimits.maxMultipleBet)) {
    return response(res, { status: 400, message: translate('INVALID_STACK_AMOUNT', language) })
  }

  // to do
  if (allPriceSum * data.stake > +betsLimits.maxWinAmount) {
    maximumWinAmount = +betsLimits.maxWinAmount
  }

  req.body.possibleWinAmount = maximumWinAmount
  req.body.totalStake = totalStake

  next()
}
