import translate from '../../lib/languageTranslate'
import { response } from '../common/response'

/**
 * Verify user auth token and check if the user is active
 * @param {object} req
 * @param {object} res
 * @param {object} next
 */
export default async (req, res, next) => {
  const {
    body: data,
    databaseConnection: {
      TenantBlockedEvent: TenantBlockedEventModel,
      TenantBlockedLeague: TenantBlockedLeagueModel,
      TenantBlockedSport: TenantBlockedSportModel,
      PullsLeague: PullsLeagueModel,
      PullsSport: PullsSportModel
    },
    headers: { language }
  } = req

  for (const bet of data.bets) {
    const tenantBlockedSport = await TenantBlockedSportModel.findOne({
      where: {
        tenant_id: req.tenant.id,
        '$sport.sport_id$': bet.sport_id
      },
      include: {
        model: PullsSportModel,
        as: 'sport'
      }
    })

    if (tenantBlockedSport) {
      return response(res, { status: 400, message: translate('SPORT_IS_BLOCKED', language) })
    }

    const tenantBlockedLeague = await TenantBlockedLeagueModel.findOne({
      where: {
        tenant_id: req.tenant.id,
        '$league.league_id$': bet.league_id
      },
      include: {
        model: PullsLeagueModel,
        as: 'league'
      }
    })

    if (tenantBlockedLeague) {
      return response(res, { status: 400, message: translate('LEAGUE_IS_BLOCKED', language) })
    }

    const tenantBlockedEvent = await TenantBlockedEventModel.findOne({
      where: {
        tenant_id: req.tenant.id,
        pullsEventFixtureId: bet.fixture_id
      },
      raw: true
    })

    if (tenantBlockedEvent) {
      return response(res, { status: 400, message: translate('EVENT_IS_BLOCKED', language) })
    }
  }

  next()
}
