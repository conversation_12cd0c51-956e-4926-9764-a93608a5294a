import jwt from 'jsonwebtoken'
import config from '../../config/app'
import translate from '../../lib/languageTranslate'
import { response } from '../common/response'

/**
 * Verify user auth token and check if the user is active
 * @param {object} req
 * @param {object} res
 * @param {object} next
 */
export default async (req, res, next) => {
  const authConfig = config.getProperties().auth
  const { headers, databaseConnection: model } = req

  const token = headers.authorization
  if (!token) {
    return response(res, { status: 403, message: translate('INVALID_TOKEN', headers.language) })
  }
  const splitToken = token.replace('Bearer ', '')
  const secretKey = authConfig.jwt_secret
  let decodedToken

  try {
    decodedToken = await jwt.verify(splitToken, secretKey)
  } catch (e) {
    return response(res, { status: 403, message: translate('INVALID_TOKEN', headers.language) })
  }

  const user = await model.User.findOne({
    where: {
      id: decodedToken.id,
      active: true
    },
    raw: true
  })
  if (!user) {
    return response(res, { status: 403, message: translate('USER_INACTIVE', headers.language) })
  }
  req.body.id = user.id
  req.body.userDetail = user
  req.query.id = user.id
  req.query.userDetail = user
  req.body.impersonated = decodedToken?.impersonated || false
  next()
}
