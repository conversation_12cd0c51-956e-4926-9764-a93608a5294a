import { Op, Sequelize } from 'sequelize'
import keyValueTo<PERSON><PERSON> from '../../lib/keyValueToJSON'
import translate from '../../lib/languageTranslate'
import { paymentForCodes } from '../common/constant'
import { response } from '../common/response'

export default async (req, res, next) => {
  const {
    body: {
      userDetail: { id: userId },
      stake: amount
    },
    databaseConnection: {
      BetsTransaction: BetsTransactionModel,
      Wallet: WalletModel,
      UserSetting: UserSettingModel
    },
    headers: { language }
  } = req

  let allowed = true
  const userSetting = await keyValueTo<PERSON>son(UserSettingModel, ['bettingLimit'], 'userId', userId)
  const wallet = await WalletModel.findOne({ where: { ownerId: userId, ownerType: 'User' }, raw: true })

  const userTransaction = await BetsTransactionModel.findOne({
    attributes: ['sourceWalletId', [Sequelize.fn('sum', Sequelize.col('amount')), 'totalBet']],
    group: ['sourceWalletId'],
    raw: true,
    where: {
      sourceWalletId: wallet.id,
      paymentFor: paymentForCodes.BET_PLACEMENT,
      [Op.and]:
          [Sequelize.where(Sequelize.fn('date', Sequelize.col('created_at')), '=', Sequelize.literal('CURRENT_DATE'))]
    }
  })

  if (userSetting?.bettingLimit) {
    allowed = (+userSetting.bettingLimit) >= ((+userTransaction?.totalBet || 0) + amount)
  }

  if (!allowed) {
    return response(res, { status: 400, message: translate('BETTING_LIMIT_EXCEEDED', language) })
  }

  next()
}
