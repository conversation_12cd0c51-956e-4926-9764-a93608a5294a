
module.exports = (sequelize, DataTypes) => {
  const SportsContent = sequelize.define('SportsContent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    buttonText: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    buttonRedirection: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    imageMobile: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'sports_content',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'sports_content_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return SportsContent
}
