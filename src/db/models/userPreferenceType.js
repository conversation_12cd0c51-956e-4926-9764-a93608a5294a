'use strict'
module.exports = (sequelize, DataTypes) => {
  const UserPreferenceType = sequelize.define('UserPreferenceType', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      default: null
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    preferenceType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    value: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_preference_type',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_preference_type_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  UserPreferenceType.associate = models => {
    UserPreferenceType.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }

  return UserPreferenceType
}
