'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerCategoryLevel = sequelize.define('PlayerCategoryLevel', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    bonus: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    level: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    minRolloverLimit: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    maxRolloverLimit: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'player_category_levels',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'player_category_levels_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PlayerCategoryLevel.associate = (models) => {
    PlayerCategoryLevel.belongsTo(models.PlayerCategory, {
      foreignKey: 'categoryId',
    });
  };

  return PlayerCategoryLevel
}
