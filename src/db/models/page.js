'use strict'
module.exports = (sequelize, DataTypes) => {
  const Page = sequelize.define('Page', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    topMenuId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    enableInstantGame: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    allowedCurrencies: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      allowNull: true,
      defaultValue: []
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'pages',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_pages_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'pages_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

Page.associate = models => {
  Page.hasMany(models.PageMenu, {
    as: 'menus',
    onDelete: 'cascade',
    foreignKey: 'pageId'
  });

  // Optional: Keep the direct relation to Aggregator if needed
  Page.hasMany(models.Aggregator, {
    foreignKey: 'pageId',
    as: 'aggregators'
  });
};


  return Page
}
