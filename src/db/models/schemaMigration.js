
module.exports = (sequelize, DataTypes) => {
  const SchemaMigration = sequelize.define('SchemaMigration', {
    version: {
      type: DataTypes.STRING,
      allowNull: false,
      primaryKey: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'schema_migrations',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'schema_migrations_pkey',
        unique: true,
        fields: [
          { name: 'version' }
        ]
      }
    ]
  })

  return SchemaMigration
}
