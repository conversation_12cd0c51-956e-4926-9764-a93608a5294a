module.exports = (sequelize, DataTypes) => {
  const PullsEvent = sequelize.define('PullsEvent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    fixtureId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'fixture_id'
    },
    fixtureStatus: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'fixture_status'
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date'
    },
    lastUpdate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_update'
    },
    leagueId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'league_id'
    },
    locationId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'location_id'
    },
    sportId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'sport_id'
    },
    livescore: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    market: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    isEventBlacklisted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_event_blacklisted'
    },
    locationNameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_nl'
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'tenant_id'
    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'provider_id'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'status'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    isVirtual: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_virtual'
    },
    marketType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'market_type'
    },
    marketOpenDate: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'market_open_date'
    }
  }, {
    sequelize,
    tableName: 'pulls_events',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_pulls_events_on_league_id',
        fields: [
          { name: 'league_id' }
        ]
      },
      {
        name: 'index_pulls_events_on_location_id',
        fields: [
          { name: 'location_id' }
        ]
      },
      {
        name: 'index_pulls_events_on_sport_id',
        fields: [
          { name: 'sport_id' }
        ]
      },
      {
        name: 'pulls_events_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  PullsEvent.associate = models => {
    PullsEvent.hasMany(models.TenantBlockedEvent, {
      foreignKey: 'pullsEventFixtureId'
    })

    PullsEvent.belongsTo(models.PullsSport, {
      foreignKey: 'sportId',
      targetKey: 'id',
      as: 'sport'
    })

    PullsEvent.belongsTo(models.PullsLeague, {
      foreignKey: 'leagueId'
    })

    PullsEvent.belongsToMany(models.PullsParticipant, {
      through: models.PullsEventparticipant,
      foreignKey: 'eventId',
      otherKey: 'participantId'
    })
  }
  return PullsEvent
}
