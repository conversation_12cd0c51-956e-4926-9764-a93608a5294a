'use strict'
module.exports = (sequelize, DataTypes) => {
  const LanguageKey = sequelize.define('LanguageKey', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false
    },
    page: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'language_keys',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'language_keys_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  LanguageKey.associate = models => {
    LanguageKey.hasOne(models.LanguageString, {
      foreignKey: 'languageKeyId'
    })
  }

  return LanguageKey
}
