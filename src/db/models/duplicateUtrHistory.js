
module.exports = (sequelize, DataTypes) => {
  const DuplicateUtrHistory = sequelize.define('DuplicateUtrHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    utrNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    depositRequestId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    receipt: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'duplicate_utr_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'utr_duplicate_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return DuplicateUtrHistory
}
