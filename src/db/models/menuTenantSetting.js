'use strict'
module.exports = (sequelize, DataTypes) => {
  const MenuTenantSetting = sequelize.define('MenuTenantSetting', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    tenant_id: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    menu_id: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    flash: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    loginRedirect: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'menu_tenant_setting',
    schema: 'public',
    timestamps: true,
  })
  MenuTenantSetting.associate = models => {
    MenuTenantSetting.belongsTo(models.MenuMaster, {
      onDelete: 'cascade',
      foreignKey: 'menuId'
    })
    MenuTenantSetting.hasMany(models.WebsiteContent, {
      foreignKey: 'casinoTopMenuId'
    })
  }
  return MenuTenantSetting
}
