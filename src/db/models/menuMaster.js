'use strict'
module.exports = (sequelize, DataTypes) => {
  const MenuMaster = sequelize.define('MenuMaster', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    path: {
      type: DataTypes.STRING,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    component: {
      type: DataTypes.STRING,
      allowNull: false
    },
    componentName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'menu_master',
    schema: 'public',
    timestamps: true
  })
  MenuMaster.associate = models => {
    MenuMaster.belongsTo(models.MenuTenantSetting, {
      onDelete: 'cascade',
      foreignKey: 'menuId'
    })
    MenuMaster.hasMany(models.TenantPageMeta, {
      foreignKey: 'menuId',
      onDelete: 'cascade'
    })
  }

  return MenuMaster
}
