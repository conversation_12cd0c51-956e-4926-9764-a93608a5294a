'use strict'
module.exports = (sequelize, DataTypes) => {
  const Layout = sequelize.define('Layout', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    value: {
      type: DataTypes.STRING,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'layouts',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'layouts_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Layout.associate = models => {
    Layout.hasMany(models.TenantThemeSetting, {
      onDelete: 'cascade',
      foreignKey: 'layoutId'
    })
  }

  return Layout
}
