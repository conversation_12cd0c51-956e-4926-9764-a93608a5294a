'use strict'

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true
    },
    emailVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    encryptedPassword: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    dateOfBirth: {
      type: DataTypes.DATE,
      allowNull: true
    },
    gender: {
      type: DataTypes.STRING,
      allowNull: true
    },
    signInCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    signInIp: {
      type: DataTypes.JSON,
      allowNull: true
    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    lastLoginDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    selfExclusion: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    vipLevel: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    nickName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    disabledAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    disabledByType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    disabledById: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    phoneCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    demo: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true
    },
    zipCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    kycDone: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    passwordUpdatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    affiliatedData: {
      type: DataTypes.STRING,
      allowNull: true
    },
    forceResetPassword: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    nationalId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    playerCategoryLevel: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    enableWithdrawRequests: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    withdrawWagerAllowed: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    avatarImage: {
      type: DataTypes.STRING,
      allowNull: true
    },
    profileVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    categoryType: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 4
    },
    wagerMultiplier: {
      type: DataTypes.SMALLINT,
      allowNull: true
    },
    userType: {
      type: DataTypes.INTEGER,
      allowNull: false, // 1 Online 2 Kiosk 3 Kiosk & Online 4 Guest
      defaultValue: 1
    },
    creationType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0 = SIGNUP, 1 = ONE_CLICK, 2 = ADMIN_CREATED, 3 = GOOGLE, 4 = FACEBOOK',
      defaultValue: 0
    },
    disabledRemarks: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_users_on_disabled_by_type_and_disabled_by_id',
        fields: [
          { name: 'disabled_by_type' },
          { name: 'disabled_by_id' }
        ]
      },
      {
        name: 'index_users_on_parent_type_and_parent_id',
        fields: [
          { name: 'parent_type' },
          { name: 'parent_id' }
        ]
      },
      {
        name: 'index_users_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'index_users_on_tenant_id_and_email',
        unique: true,
        fields: [
          { name: 'tenant_id' },
          { name: 'email' }
        ]
      },
      {
        name: 'users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  User.associate = models => {
    User.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })

    User.belongsTo(models.AdminUser, {
      foreignKey: 'parentId'
    })

    User.belongsTo(models.AdminUserSetting, {
      foreignKey: 'parentId',
      targetKey: 'adminUserId'
    })

    User.hasMany(models.UserToken, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasOne(models.Wallet, {
      foreignKey: 'ownerId',
      onDelete: 'cascade',
      scope: {
        ownerType: 'User'
      }
    })

    User.belongsTo(models.PlayerCategoryLevel, {
      foreignKey: 'playerCategoryLevel'
    })

    User.hasMany(models.UserSetting, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })

    User.hasMany(models.UserDocument, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })

    User.hasMany(models.Transaction, {
      foreignKey: 'actioneeId',
      onDelete: 'cascade'
    })

    User.hasMany(models.UserLoginHistory, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })

    User.hasMany(models.UserPreferenceType, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasMany(models.GamePlayHistory, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasMany(models.UserBlockedGames, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasMany(models.SocialMediaLogin, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasOne(models.UserReferralCode, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasMany(models.FavoriteGames, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })

    User.hasOne(models.UserPermissions, {
      foreignKey: 'userId'
    })

  }

  return User
}
