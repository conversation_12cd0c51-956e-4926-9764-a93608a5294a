module.exports = (sequelize, DataTypes) => {
  const BetsFavorite = sequelize.define('BetsFavorite', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_deleted'
    },
    eventId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'event_id'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'user_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'bets_favorites',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bets_favorites_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bets_favorites_on_event_id',
        fields: [
          { name: 'event_id' }
        ]
      },
      {
        name: 'index_bets_favorites_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      }
    ]
  })

  BetsFavorite.associate = models => {
    BetsFavorite.belongsTo(models.PullsEvent, {
      foreignKey: 'eventId'
    })
  }

  return BetsFavorite
}
