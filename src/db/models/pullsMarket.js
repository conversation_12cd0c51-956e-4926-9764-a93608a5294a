module.exports = (sequelize, DataTypes) => {
  const PullsMarket = sequelize.define('PullsMarket', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    marketId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'market_id'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    nameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_fr'
    },
    nameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_tr'
    },
    nameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_de'
    },
    nameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_ru'
    },
    nameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_nl'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'pulls_markets',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'pulls_markets_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return PullsMarket
}
