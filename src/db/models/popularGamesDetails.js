module.exports = function (sequelize, DataTypes) {
  const PopularGamesDetails = sequelize.define('PopularGamesDetails', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    popularGameId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    uuid: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    pageId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    provider: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'popular_games_details',
    underscored: true,
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'popular_games_details_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PopularGamesDetails.associate = function (models) {
    PopularGamesDetails.belongsTo(models.PopularGames, {
      foreignKey: 'popularGameId',
      as: 'popularGame'
    })
  }

  return PopularGamesDetails
}
