'use strict'

module.exports = (sequelize, DataTypes) => {
  const OfferProvider = sequelize.define(
    'OfferProvider',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true
      },
      offerId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      providerId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      tenantId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'offer_provider',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'offer_provider_pkey',
          unique: true,
          fields: [
            { name: 'id' }
          ]
        },
        {
          name: 'index_offer_provider_on_offer_id_and_tenant_id',
          unique: false,
          fields: [
            { name: 'offer_id' },
            { name: 'tenant_id' }
          ]
        }
      ]
    }
  )

  OfferProvider.associate = (models) => {
    OfferProvider.belongsTo(models.Offer, {
      foreignKey: 'offerId',
      as: 'provider'
    })

    OfferProvider.belongsTo(models.CasinoProvider, {
      foreignKey: 'providerId'
    })

    OfferProvider.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
  }

  return OfferProvider
}
