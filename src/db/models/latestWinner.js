'use strict'
module.exports = (sequelize, DataTypes) => {
  const LatestWinner = sequelize.define('LatestWinner', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    tableId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    tableName: {
      type: DataTypes.STRING
    },
    image: {
      type: DataTypes.STRING
    },
    betCount: {
      type: DataTypes.BIGINT,
      allowNull: false,
      defaultValue: 0
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false
    },
    winningAmount: {
      type: DataTypes.DECIMAL(20, 5)
    },
    otherCurrencyAmount: {
      type: DataTypes.TEXT
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    providerName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'latest_winners',
    schema: 'public',
    timestamps: true
  })

  LatestWinner.associate = models => {
    LatestWinner.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
  }

  return LatestWinner
}
