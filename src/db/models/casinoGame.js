'use strict'
module.exports = (sequelize, DataTypes) => {
  const CasinoGame = sequelize.define('CasinoGame', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    casinoProviderId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'casino_games',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'casino_games_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  CasinoGame.associate = models => {
    CasinoGame.belongsTo(models.CasinoProvider, {
      foreignKey: 'casinoProviderId'
    })
  }

  return CasinoGame
}
