
module.exports = (sequelize, DataTypes) => {
  const ActiveStorageBlob = sequelize.define('ActiveStorageBlob', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false
    },
    filename: {
      type: DataTypes.STRING,
      allowNull: false
    },
    contentType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    metadata: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    byteSize: {
      type: DataTypes.BIGINT,
      allowNull: false

    },
    checksum: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'active_storage_blobs',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'active_storage_blobs_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_active_storage_blobs_on_key',
        unique: true,
        fields: [
          { name: 'key' }
        ]
      }
    ]
  })

  return ActiveStorageBlob
}
