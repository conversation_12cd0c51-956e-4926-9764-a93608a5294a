'use strict'

module.exports = (sequelize, DataTypes) => {
  const BetPayoutStatus = sequelize.define('BetPayoutStatus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    betId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false // 1 : Settled ,2 : Rejected
    },
    amount: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      default: 0
    },
    actioneeId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    comments: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'updated_at'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'bet_payout_status',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bet_payout_status_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return BetPayoutStatus
}
