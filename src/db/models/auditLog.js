'use strict'
module.exports = (sequelize, DataTypes) => {
  const AuditLog = sequelize.define('AuditLog', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    actioneeId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    eventType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    event: {
      type: DataTypes.STRING,
      allowNull: true
    },
    eventId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    action: {
      type: DataTypes.STRING,
      allowNull: true
    },
    actioneeIp: {
      type: DataTypes.STRING,
      allowNull: true
    },
    previousData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    modifiedData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    actioneeType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0 = ADMIN, 1 = USER, 2 = SUPER-ADMIN',
      defaultValue: 0
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'audit_logs',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'audit_log_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return AuditLog
}
