'use strict'
module.exports = (sequelize, DataTypes) => {
  const NotificationReceiver = sequelize.define('NotificationReceiver', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    notificationId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    receiverId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    receiverType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'notification_receivers',
    schema: 'public',
    timestamps: true
  })

  NotificationReceiver.associate = models => {
    NotificationReceiver.belongsTo(models.Notification, {
      foreignKey: 'notificationId'
    })
  }
  return NotificationReceiver
}
