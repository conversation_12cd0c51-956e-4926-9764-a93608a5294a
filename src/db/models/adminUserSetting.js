
module.exports = (sequelize, DataTypes) => {
  const AdminUserSetting = sequelize.define('AdminUserSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: true
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'admin_user_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'admin_user_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_admin_user_settings_on_admin_user_id',
        fields: [
          { name: 'admin_user_id' }
        ]
      }
    ]
  })

  AdminUserSetting.associate = models => {
    AdminUserSetting.hasMany(models.User, {
      foreignKey: 'parentId'
    })
  }

  return AdminUserSetting
}
