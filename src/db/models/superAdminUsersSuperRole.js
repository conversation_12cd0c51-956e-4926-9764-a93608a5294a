
module.exports = (sequelize, DataTypes) => {
  const SuperAdminUsersSuperRole = sequelize.define('SuperAdminUsersSuperRole', {
    superAdminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    superRoleId: {
      type: DataTypes.BIGINT,
      allowNull: true

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'super_admin_users_super_roles',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_super_admin_users_roles_on_super_admin_users_and_roles',
        fields: [
          { name: 'super_admin_user_id' },
          { name: 'super_role_id' }
        ]
      },
      {
        name: 'index_super_admin_users_super_roles_on_super_admin_user_id',
        fields: [
          { name: 'super_admin_user_id' }
        ]
      },
      {
        name: 'index_super_admin_users_super_roles_on_super_role_id',
        fields: [
          { name: 'super_role_id' }
        ]
      }
    ]
  })

  return SuperAdminUsersSuperRole
}
