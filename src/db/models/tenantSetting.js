'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantSetting = sequelize.define('TenantSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: true
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_pages_on_tenant_id_and_key_and_type',
        unique: true,
        fields: [
          { name: 'tenant_id' },
          { name: 'key' },
          { name: 'type' }
        ]
      },
      {
        name: 'index_pages_on_tenant_id_and_type',
        fields: [
          { name: 'tenant_id' },
          { name: 'type' }
        ]
      }
    ]
  })

  return TenantSetting
}
