module.exports = (sequelize, DataTypes) => {
  const PullsSport = sequelize.define('PullsSport', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    sportId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'sport_id'
    },
    nameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_de'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    nameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_fr'
    },
    nameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_ru'
    },
    nameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_tr'
    },
    nameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_nl'
    },
    tenantId: {
      type : DataTypes.BIGINT,
      allowNull:false,
      field : 'tenant_id'
    },
    providerId : {
      type : DataTypes.BIGINT,
      allowNull:false,
      field : 'provider_id'
    },
    status : {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'status'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'pulls_sports',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'pulls_sports_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PullsSport.associate = models => {
    PullsSport.hasMany(models.PullsEvent, {
      foreignKey: 'sportId',
      sourceKey: 'id'
    })
    PullsSport.hasMany(models.PullsLeague, { foreignKey: 'sportId', as: 'leagues' });
  }

  return PullsSport
}
