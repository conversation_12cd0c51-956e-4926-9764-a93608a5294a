'use strict'
module.exports = (sequelize, DataTypes) => {
  const GamePlayHistory = sequelize.define('GamePlayHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    deviceType: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    provider: {
      type: DataTypes.STRING,
      allowNull: false
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    casinoItemId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    launchTs_1: {
      type: DataTypes.DATE,
      allowNull: true
    },
    launchTs_2: {
      type: DataTypes.DATE,
      allowNull: true
    },
    launchTs_3: {
      type: DataTypes.DATE,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'game_play_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_game_play_history_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'game_play_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  GamePlayHistory.associate = models => {
    GamePlayHistory.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }

  return GamePlayHistory
}
