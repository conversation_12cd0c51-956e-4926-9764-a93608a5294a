module.exports = (sequelize, DataTypes) => {
  const TenantSocialMediaSettings = sequelize.define('TenantSocialMediaSettings', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    redirectUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_social_media_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_social_media_settings',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return TenantSocialMediaSettings
}
