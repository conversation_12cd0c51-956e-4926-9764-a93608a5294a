
module.exports = (sequelize, DataTypes) => {
  const SuperRole = sequelize.define('SuperRole', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    resourceType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    resourceId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'super_roles',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_super_roles_on_name_and_resource_type_and_resource_id',
        fields: [
          { name: 'name' },
          { name: 'resource_type' },
          { name: 'resource_id' }
        ]
      },
      {
        name: 'index_super_roles_on_resource_type_and_resource_id',
        fields: [
          { name: 'resource_type' },
          { name: 'resource_id' }
        ]
      },
      {
        name: 'super_roles_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  SuperRole.associate = models => {
    SuperRole.belongsToMany(models.SuperAdminUser, {
      through: models.SuperAdminUsersSuperRole,
      foreignKey: 'superRoleId'
    })
  }
  return SuperRole
}
