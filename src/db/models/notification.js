'use strict'
module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    senderId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    senderType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    referenceId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    referenceType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    message: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'notifications',
    schema: 'public',
    timestamps: true
  })

  Notification.associate = models => {
    Notification.hasMany(models.NotificationReceiver, {
      foreignKey: 'notificationId'
    })
  }
  return Notification
}
