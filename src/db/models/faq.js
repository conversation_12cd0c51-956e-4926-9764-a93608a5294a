module.exports = (sequelize, DataTypes) => {
  const Faq = sequelize.define('Faq', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    question: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    answer: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'faqs',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'faqs_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Faq.associate = (models) => {
    Faq.belongsTo(models.FaqCategories, {
      foreignKey: "categoryId",
    });
  };
  return Faq

}
