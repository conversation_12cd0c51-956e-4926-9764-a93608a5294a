module.exports = (sequelize, DataTypes) => {
  const FavoriteGames = sequelize.define('FavoriteGames', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'user_id'
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'game_id'
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'tenant_id'
    },
    casinoProviderId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'casino_provider_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'favorite_games',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'favorite_games_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_favorite_games_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      }
    ]
  })

  FavoriteGames.associate = models => {
    FavoriteGames.belongsTo(models.User, {
      foreignKey: 'userId'
    })

    FavoriteGames.belongsTo(models.CustomCategoryGame, {
      foreignKey: 'gameId',
      targetKey: 'uuid',
      as: 'gameDetail'
    })

    FavoriteGames.belongsTo(models.CasinoProvider, {
      foreignKey: 'casinoProviderId',
      as: 'providerDetail'
    })
  }

  return FavoriteGames
}
