module.exports = (sequelize, DataTypes) => {
  const BetsRefundedbet = sequelize.define('BetsRefundedbet', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    applied: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    betId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'bet_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'bets_refundedbets',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'bets_refundedbets_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bets_refundedbets_on_bet_id',
        fields: [
          { name: 'bet_id' }
        ]
      }
    ]
  })
  return BetsRefundedbet
}
