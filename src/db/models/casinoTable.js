
module.exports = (sequelize, DataTypes) => {
  const CasinoTable = sequelize.define('CasinoTable', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    isLobby: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    tableId: {
      type: DataTypes.STRING,
      allowNull: true

    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'casino_tables',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'casino_tables_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  // CasinoTable.associate = models => {
  //   CasinoTable.hasOne(models.CasinoGame, {
  //     targetKey: 'gameId'
  //   })
  // }

  return CasinoTable
}
