'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantThemeSetting = sequelize.define('TenantThemeSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false

    },
    layoutId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    languageId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    theme: {
      type: DataTypes.JSON,
      allowNull: true
    },
    userLoginType: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    fabIconUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    smsGateway: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    logoUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    assignedProviders: {
      type: DataTypes.STRING,
      allowNull: true
    },
    signupPopupImageUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    whatsappNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    poweredBy: {
      type: DataTypes.STRING,
      allowNull: true
    },
    chatbotToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    googleAnalyticsScriptsCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    googleRecaptchaKeys: {
      type: DataTypes.JSON,
      allowNull: true
    },
    allowedModules: {
      type: DataTypes.STRING,
      allowNull: true
    },
    socialMedia: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    forgotPasswordOption: {
      type: DataTypes.STRING,
      allowNull: false
    },
    googleTagManagerCode: {
      type: DataTypes.STRING,
      allowNull: false
    },
    googleSearchConsoleCode: {
      type: DataTypes.STRING,
      allowNull: false
    },
    otpEnable: {
      type: DataTypes.STRING,
      allowNull: true
    },
    maxAttempts: {
      type: DataTypes.INTEGER,
      allowNull: true,
      default: null
    },
    attemptExpiryTime: {
      type: DataTypes.INTEGER,
      allowNull: true,
      default: null
    },
    maxBankAccountLimit: {
      type: DataTypes.INTEGER,
      allowNull: false,
      default: 1
    },
    addBankDetails: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      default: null
    },
    chatbotType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    passwordResetDays: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    autoLogoutDuration: {
      type: DataTypes.INTEGER,
      allowNull: false,
      default: 6
    },
    idleScreenLogoutTime: {
      type: DataTypes.INTEGER,
      allowNull: true,
      default: 0
    },
    callingNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    callingNumbers: {
      type: DataTypes.JSON,
      allowNull: true
    },
    enableCustomCss: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    socialMediaLoginType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    smsEnable: {
      type: DataTypes.STRING,
      allowNull: true
    },
    emailGateway: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_theme_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_tenant_theme_settings_on_language_id',
        fields: [
          { name: 'language_id' }
        ]
      },
      {
        name: 'index_tenant_theme_settings_on_layout_id',
        fields: [
          { name: 'layout_id' }
        ]
      },
      {
        name: 'index_tenant_theme_settings_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_theme_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  TenantThemeSetting.associate = models => {
    TenantThemeSetting.belongsTo(models.Layout, {
      onDelete: 'cascade',
      foreignKey: 'layoutId'
    })
    TenantThemeSetting.belongsTo(models.Language, {
      onDelete: 'cascade',
      foreignKey: 'languageId'
    })
  }

  return TenantThemeSetting
}
