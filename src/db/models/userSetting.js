'use strict'
module.exports = (sequelize, DataTypes) => {
  const UserSetting = sequelize.define('UserSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_user_settings_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'user_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  UserSetting.associate = models => {
    UserSetting.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }
  return UserSetting
}
