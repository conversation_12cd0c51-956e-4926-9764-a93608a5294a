'use strict';

module.exports = (sequelize, DataTypes) => {
  const Thread = sequelize.define('Thread', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    messageBody: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    sentType: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    sentIds: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    ownerType: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'threads',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'threads_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  });

  Thread.associate = models => {
    Thread.hasMany(models.ThreadUserDetail, {
      foreignKey: 'threadId'
    });
  };

  return Thread;
};
