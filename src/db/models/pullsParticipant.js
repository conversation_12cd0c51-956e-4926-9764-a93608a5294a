module.exports = (sequelize, DataTypes) => {
  const PullsParticipant = sequelize.define('PullsParticipant', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    participantId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'participant_id'
    },
    nameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_de'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    nameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_fr'
    },
    nameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_ru'
    },
    nameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_tr'
    },
    nameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_nl'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'pulls_participants',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'pulls_participants_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PullsParticipant.associate = models => {
    PullsParticipant.belongsToMany(models.PullsEvent, {
      through: models.PullsEventparticipant,
      foreignKey: 'participantId',
      otherKey: 'eventId'
    })
  }

  return PullsParticipant
}
