
module.exports = (sequelize, DataTypes) => {
  const AdminBetLimit = sequelize.define('AdminBetLimit', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    maxSingleBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_single_bet'
    },
    minBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'min_bet'
    },
    maxMultipleBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_multiple_bet'
    },
    maxBetOnEvent: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_bet_on_event'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
    maxWinAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_win_amount'
    },
    maxOdd: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_odd'
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'admin_user_id'
    }
  }, {
    sequelize,
    tableName: 'admin_bet_limits',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'admin_bet_limits_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return AdminBetLimit
}
