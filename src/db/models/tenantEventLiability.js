
module.exports = (sequelize, DataTypes) => {
  const TenantEventLiability = sequelize.define('TenantEventLiability', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    eventId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    totalLiability: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_event_liability',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_tenant_event_liability_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_event_liability_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return TenantEventLiability
}
