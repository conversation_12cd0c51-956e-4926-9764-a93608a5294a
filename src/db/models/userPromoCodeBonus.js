module.exports = (sequelize, DataTypes) => {
  const UserPromoCodeBonus = sequelize.define('UserPromoCodeBonus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    status: {
      type: DataTypes.ENUM(['active', 'claimed', 'cancelled', 'expired']),
      allowNull: true
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    promoCodeId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    kind: {
      type: DataTypes.STRING,
      allowNull: true
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    claimedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    transactionId: {
      type: DataTypes.BIGINT
    },
    burningTrxnId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_promo_code_bonus',
    underscored: true,
    schema: 'public',
    timestamps: true
  })

  return UserPromoCodeBonus
}
