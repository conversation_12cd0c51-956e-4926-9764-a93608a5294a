'use strict'

module.exports = (sequelize, DataTypes) => {
  const UserBonusRecurringRollover = sequelize.define('UserBonusRecurringRollover', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    userBonusId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    depositBonusTierId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      defaultValue: 0
    },
    rolloverTarget: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      defaultValue: 0
    },
    remainingRollover: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      defaultValue: 0
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      comment: '0 - active, 1 - claimed, 2 - cancelled, 3 - expired'
    },
    claimedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_bonus_recurring_rollover',
    underscored: true,
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_bonus_recurring_rollover_pkey',
        unique: true,
        fields: [{ name: 'id' }]
      },
      {
        name: 'user_bonus_recurring_rollover_user_bonus_id_index',
        fields: [{ name: 'user_bonus_id' }]
      },
      {
        name: 'user_bonus_recurring_rollover_deposit_bonus_tier_id_index',
        fields: [{ name: 'deposit_bonus_tier_id' }]
      },
      {
        name: 'user_bonus_recurring_rollover_transaction_id_index',
        fields: [{ name: 'transaction_id' }]
      }
    ]
  })

  UserBonusRecurringRollover.associate = models => {
    UserBonusRecurringRollover.belongsTo(models.UserBonus, {
      foreignKey: 'userBonusId'
    })

    UserBonusRecurringRollover.belongsTo(models.DepositBonusTier, {
      foreignKey: 'depositBonusTierId'
    })

    UserBonusRecurringRollover.belongsTo(models.Transaction, {
      foreignKey: 'transactionId'
    })

    UserBonusRecurringRollover.belongsTo(models.User, {
      foreignKey: 'userId'
    })

    UserBonusRecurringRollover.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    })
  }

  return UserBonusRecurringRollover
}
