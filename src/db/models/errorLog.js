
module.exports = (sequelize, DataTypes) => {
  const ErrorLog = sequelize.define('ErrorLog', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    hostUrl: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    errorMessage: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    fileName: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    errorLineNo: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    stackTrace: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    errorStatus: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    exceptionCode: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminType: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    functionName: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      default: 0,
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'error_logs',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'error_logs_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return ErrorLog
}
