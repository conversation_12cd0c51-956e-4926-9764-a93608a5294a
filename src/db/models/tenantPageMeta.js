'use strict'

module.exports = (sequelize, DataTypes) => {
  const TenantPageMeta = sequelize.define('TenantPageMeta', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    metaTitle: {
      type: DataTypes.STRING,
      allowNull: true
    },
    metaDescription: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    metaKeyword: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    menuId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    casinoPageMenuId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    casinoPageId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_page_meta',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_page_meta_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  TenantPageMeta.associate = models => {
    TenantPageMeta.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
    TenantPageMeta.belongsTo(models.MenuMaster, {
      foreignKey: 'menuId'
    })
    TenantPageMeta.belongsTo(models.Page, {
      foreignKey: 'casinoPageId',
    });
    TenantPageMeta.belongsTo(models.PageMenu, {
      foreignKey: 'casinoPageMenuId',
    });
  }

  return TenantPageMeta
}
