'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerSubcategoryHistory = sequelize.define('PlayerSubcategoryHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    subcategoryId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'player_subcategory_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'player_subcategory_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })


  return PlayerSubcategoryHistory
}
