'use strict'

module.exports = (sequelize, DataTypes) => {
  const Offer = sequelize.define(
    'Offer',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true
      },
      tenantId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      promotionTitle: {
        type: DataTypes.JSONB,
        allowNull: false
      },
      offerDescription: {
        type: DataTypes.JSONB,
        allowNull: false
      },
      validFrom: {
        type: DataTypes.DATE,
        allowNull: false
      },
      validTo: {
        type: DataTypes.DATE,
        allowNull: false
      },
      frequency: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        comment: '0 - Daily, 1 - Weekly, 2 - Monthly, 3 - Specific Day of Week, 4 - Bi Monthly'
      },
      dayOfWeek: {
        type: DataTypes.SMALLINT,
        allowNull: true,
        comment: '1 - Monday, 2 - Tuesday, 3 - Wednesday, 4 - Thursday, 5 - Friday, 6 - Saturday, 7 - Sunday'
      },
      winningType: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        comment: '0 - Rollover winning, 1 - GGR, 2 - NGR'
      },
      image: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'offers',
      schema: 'public',
      timestamps: true
    }
  )

  Offer.associate = (models) => {
    // One-to-Many relationship with Prizes
    Offer.hasMany(models.Prize, {
      foreignKey: 'offerId',
      as: 'prizes' // Alias for included associations
    })

    Offer.hasMany(models.OfferProvider, {
      foreignKey: 'offerId',
      as: 'providers'
    })
  }

  return Offer
}
