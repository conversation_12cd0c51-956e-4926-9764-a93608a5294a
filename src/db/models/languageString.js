'use strict'
module.exports = (sequelize, DataTypes) => {
  const LanguageString = sequelize.define('LanguageString', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    languageId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    languageKeyId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    string: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'language_strings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'language_strings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  LanguageString.associate = models => {
    LanguageString.belongsTo(models.LanguageKey, {
      foreignKey: 'languageKeyId'
    })
  }

  return LanguageString
}
