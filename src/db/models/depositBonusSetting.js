
module.exports = (sequelize, DataTypes) => {
  const DepositBonusSetting = sequelize.define('DepositBonusSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    minDeposit: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    maxDeposit: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    maxBonus: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    rolloverMultiplier: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    maxRolloverPerBet: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    validForDays: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    rolloverCalculationType: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1
    },
    depositType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    depositBonusType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    recurringBonusType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    allowedPaymentProviders: {
      type: DataTypes.JSON
    },
    tierType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    customDeposits: {
      type: DataTypes.INTEGER
    },
    weekDay: {
      type: DataTypes.STRING,
      allowNull: false
    },
    burningDays: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    burnType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    providerDetails: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    tierConfigType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 1 // 1: Tier-Based (default), 2: Sequence-Based
    },
    applyToDepositSequenceGte: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: null
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_bonus_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_bonus_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_deposit_bonus_settings_on_bonus_id',
        fields: [
          { name: 'bonus_id' }
        ]
      }
    ]
  })

  DepositBonusSetting.associate = models => {
    DepositBonusSetting.hasMany(models.DepositBonusTier, {
      foreignKey: 'depositBonusSettingId'
    })

    DepositBonusSetting.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    })
  }
  return DepositBonusSetting
}
