'use strict'

module.exports = (sequelize, DataTypes) => {
  const WebsiteContent = sequelize.define('WebsiteContent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    creatorId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    casinoTopMenuId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      defaultValue: 0
    },
    casinoPageId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    casinoPageMenuId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    heading: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    content: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'website_content',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'website_content_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  WebsiteContent.associate = (models) => {
    WebsiteContent.belongsTo(models.MenuTenantSetting, {
      foreignKey: "casinoTopMenuId"
    });
  };
  return WebsiteContent
}
