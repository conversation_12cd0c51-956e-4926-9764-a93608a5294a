
module.exports = (sequelize, DataTypes) => {
  const CasinoProvider = sequelize.define('CasinoProvider', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'casino_providers',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'casino_providers_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  CasinoProvider.associate = models => {
    CasinoProvider.hasMany(models.CasinoGame, {
      foreignKey: 'gameId'
    });
  // Optional: Keep the direct relation to Aggregator if needed
  CasinoProvider.hasMany(models.Aggregator, {
    foreignKey: 'aggregatorId',
    as: 'aggregators'
  });
};


  return CasinoProvider
}
