module.exports = (sequelize, DataTypes) => {
  const TenantBlockedSport = sequelize.define('TenantBlockedSport', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'tenant_id'
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'admin_user_id'
    },
    pullsSportId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'pulls_sport_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'tenant_blocked_sports',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_tenant_blocked_sports_on_admin_user_id',
        fields: [
          { name: 'admin_user_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_sports_on_pulls_sport_id',
        fields: [
          { name: 'pulls_sport_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_sports_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_blocked_sports_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  TenantBlockedSport.associate = models => {
    TenantBlockedSport.belongsTo(models.PullsSport, {
      foreignKey: 'pullsSportId',
      as: 'sport'
    })
  }

  return TenantBlockedSport
}
