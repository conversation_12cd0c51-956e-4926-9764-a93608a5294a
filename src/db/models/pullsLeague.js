module.exports = (sequelize, DataTypes) => {
  const PullsLeague = sequelize.define('PullsLeague', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    leagueId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'league_id'
    },
    nameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_de'
    },
    season: {
      type: DataTypes.STRING,
      allowNull: true
    },
    locationId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'location_id'
    },
    sportId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'sport_id'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    nameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_fr'
    },
    nameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_ru'
    },
    nameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_tr'
    },
    nameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_nl'
    },
    tenantId: {
      type : DataTypes.BIGINT,
      allowNull:false,
      field : 'tenant_id'
    },
    providerId : {
      type : DataTypes.BIGINT,
      allowNull:false,
      field : 'provider_id'
    },
    isPopular : {
      type: DataTypes.BOOLEAN,
      default : false,
      allowNull: false,
      field: 'is_popular'
    },
    status : {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'status'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'pulls_leagues',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_pulls_leagues_on_location_id',
        fields: [
          { name: 'location_id' }
        ]
      },
      {
        name: 'index_pulls_leagues_on_sport_id',
        fields: [
          { name: 'sport_id' }
        ]
      },
      {
        name: 'pulls_leagues_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  PullsLeague.associate = models => {
    PullsLeague.hasMany(models.TenantBlockedLeague, {
      foreignKey: 'pullsLeagueId'
    })
    PullsLeague.hasMany(models.PullsEvent, {
      foreignKey: 'leagueId',
      as: 'events'
    })
    PullsLeague.belongsTo(models.PullsSport, { foreignKey: 'sportId' });
  }
  return PullsLeague
}
