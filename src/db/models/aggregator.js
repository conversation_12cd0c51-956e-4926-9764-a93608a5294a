module.exports = (sequelize, DataTypes) => {
  const Aggregator = sequelize.define('Aggregator', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
      comment: 'Primary key for aggregator'
    },
    aggregatorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'aggregator_id',
      comment: 'External aggregator identifier'
    },
    pageId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'page_id',
      comment: 'Associated page identifier'
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Status of the aggregator (active/inactive)'
    },
  }, {
    tableName: 'aggregator',
    timestamps: false, // No createdAt/updatedAt based on the schema
    comment: 'List of aggregators associated with providers',
    indexes: [
      {
        name: 'idx_aggregator_aggregator_id',
        fields: ['aggregator_id']
      },
      {
        name: 'idx_aggregator_page_id',
        fields: ['page_id']
      },
      {
        name: 'idx_aggregator_status',
        fields: ['status']
      }
    ]
  });

Aggregator.associate = (models) => {
  // 👇 Aggregator belongsTo Page
  Aggregator.belongsTo(models.Page, {
    foreignKey: 'pageId',
    as: 'page'
  });

  // 👇 Aggregator belongsTo CasinoProvider
  Aggregator.belongsTo(models.CasinoProvider, {
    foreignKey: 'aggregatorId',
    as: 'casinoProvider'
  });
};


  return Aggregator;
}
