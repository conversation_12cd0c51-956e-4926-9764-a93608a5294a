module.exports = (sequelize, DataTypes) => {
  const TenantBlockedLeague = sequelize.define('TenantBlockedLeague', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'tenant_id'
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'admin_user_id'
    },
    pullsLeagueId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'pulls_league_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'tenant_blocked_leagues',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_tenant_blocked_leagues_on_admin_user_id',
        fields: [
          { name: 'admin_user_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_leagues_on_pulls_league_id',
        fields: [
          { name: 'pulls_league_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_leagues_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_blocked_leagues_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  TenantBlockedLeague.associate = models => {
    TenantBlockedLeague.belongsTo(models.PullsLeague, {
      foreignKey: 'pullsLeagueId',
      as: 'league'
    })
  }
  return TenantBlockedLeague
}
