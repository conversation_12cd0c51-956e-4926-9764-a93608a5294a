module.exports = (sequelize, DataTypes) => {
  const TenantSportsBetSetting = sequelize.define('TenantSportsBetSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'tenant_id'
    },
    eventLiability: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'event_liability'
    },
    maxSingleBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_single_bet'
    },
    minBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'min_bet'
    },
    maxMultipleBet: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_multiple_bet'
    },
    maxBetOnEvent: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_bet_on_event'
    },
    depositLimit: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'deposit_limit'
    },
    maxWinAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_win_amount'
    },
    maxOdd: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'max_odd'
    },
    cashoutPercentage: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'cashout_percentage'
    },
    betDisabled: {
      allowNull: false,
      type: DataTypes.BOOLEAN
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_sports_bet_setting',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_tenant_sports_bet_setting_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_sports_bet_setting_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return TenantSportsBetSetting
}
