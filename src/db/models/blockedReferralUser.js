'use strict'
module.exports = (sequelize, DataTypes) => {
  const BlockedReferralUser = sequelize.define('BlockedReferralUser', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    blockType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '1 - agent, 2 - user'
    },
    blockId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'blocked_referral_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'blocked_referral_users_tenant_block',
        unique: true,
        fields: [
          { name: 'tenantId' },
          { name: 'blockId' },
          { name: 'blockType' }
        ]
      },
      {
        name: 'blocked_referral_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return BlockedReferralUser
}
