'use strict'
module.exports = (sequelize, DataTypes) => {
  const UserBankDetails = sequelize.define('UserBankDetails', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    bankName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    bankIfscCode: {
      type: DataTypes.STRING,
      allowNull: true

    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: true

    },
    name: {
      type: DataTypes.STRING,
      allowNull: true

    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true

    },
    phoneCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_bank_details',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_user_bank_details_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'user_bank_details_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  UserBankDetails.associate = models => {
    UserBankDetails.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }

  return UserBankDetails
}
