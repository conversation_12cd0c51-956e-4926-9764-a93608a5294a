'use strict'
module.exports = (sequelize, DataTypes) => {
  const OfferWinner = sequelize.define('OfferWinner', {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    offerId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'offers',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    winningType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0: Rollover Win, 1: GGR, 2: NGR'
    },
    winningValue: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      comment: 'Value based on winning_type (e. g., rollover win, GGR, NGR)'
    },
    prizeTitle: {
      type: DataTypes.STRING(255),
      comment: 'Prize Title associated with the winner'
    },
    frequency: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0: Daily, 1: Weekly, 2: Monthly'
    },
    winnerAnnounceDate: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: 'Date when the winner is announced'
    },
    isFakeUser: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE
    },
    updatedAt: {
      type: DataTypes.DATE
    }
  }, {
    tableName: 'offer_winners',
    underscored: true,
    schema: 'public',
    timestamps: true
  });

  return OfferWinner;
};
