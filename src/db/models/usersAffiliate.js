module.exports = (sequelize, DataTypes) => {
  const UsersAffiliate = sequelize.define('UsersAffiliate', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    clickId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    affiliate: {
      type: DataTypes.BIGINT,
      allowNull: false
    },

  }, {
    sequelize,
    underscored: true,
    tableName: 'users_affiliate',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_users_affiliate_for_user_id_and_tenant_id',
        fields: ['userId', 'tenantId']
      },
      {
        name: 'index_users_affiliate_for_user_id_and_click_id_and_tenant_id',
        fields: ['userId', 'clickId', 'tenantId']
      }
    ]
  })


  return UsersAffiliate
}
