'use strict'
module.exports = (sequelize, DataTypes) => {
  const UserBlockedGames = sequelize.define('UserBlockedGames', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    gameType: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'casino'
    },
    blockedGamesUuid: {
      type: DataTypes.STRING,
      allowNull: true
    },
    pageId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    pageMenuId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_blocked_games',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_blocked_games_on_user_id_tenant_id_game_type_uuid_index',
        fields: [
          { name: 'user_id' },
          { name: 'tenant_id' },
          { name: 'game_type' },
          { name: 'blocked_games_uuid' }
        ]
      },
      {
        name: 'user_blocked_games_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  UserBlockedGames.associate = models => {
    UserBlockedGames.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }

  return UserBlockedGames
}
