
module.exports = (sequelize, DataTypes) => {
  const CaptchaVerification = sequelize.define('CaptchaVerification', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    uuidToken: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false
    },
    captchaText: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    status: {
      type: DataTypes.BIGINT,
      defaultValue: 0,
      comment: '0: Unverified, 1: Verified, 2: Used'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'captcha_verifications',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'captcha_verifications_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'captcha_verifications_uuid_token_status_index',
        unique: false,
        fields: [
          { name: 'uuid_token' },
          { name: 'status' }
        ]
      },
      {
        name: 'captcha_verifications_uuid_token_captcha_text_status_index',
        unique: false,
        fields: [
          { name: 'uuid_token' },
          { name: 'captcha_text' },
          { name: 'status' },
        ]
      }
    ]
  })

  return CaptchaVerification
}
