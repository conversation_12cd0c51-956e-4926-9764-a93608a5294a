'use strict'
module.exports = (sequelize, DataTypes) => {
  const Currency = sequelize.define('Currency', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    primary: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    exchangeRate: {
      type: DataTypes.DECIMAL,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    internalCode: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'currencies',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'currencies_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Currency.associate = models => {
    Currency.hasMany(models.Transaction, {
      foreignKey: 'sourceCurrencyId',
      onDelete: 'cascade'
    })
    Currency.hasMany(models.Transaction, {
      onDelete: 'cascade',
      foreignKey: 'sourceCurrencyId'
    })
    Currency.hasMany(models.Transaction, {
      onDelete: 'cascade',
      foreignKey: 'targetCurrencyId'
    })
  }

  return Currency
}
