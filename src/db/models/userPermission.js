module.exports = (sequelize, DataTypes) => {
  const UserPermissions = sequelize.define('UserPermissions', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'user_id'
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'tenant_id'
    },
    blockedPermissions: {
      type: DataTypes.JSONB,
      allowNull: true,
      field: 'blocked_permissions',
      defaultValue: []
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'updated_at'
    }
  }, {
    tableName: 'user_permissions',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    indexes: [
      {
        fields: ['tenant_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['tenant_id', 'user_id']
      }
    ]
  })

  UserPermissions.associate = function (models) {
    UserPermissions.belongsTo(models.User, {
      foreignKey: 'userId'
    })

    UserPermissions.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
  }

  return UserPermissions
}
