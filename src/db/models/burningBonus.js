
module.exports = (sequelize, DataTypes) => {
  const BurningBonus = sequelize.define('BurningBonus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    userBonusId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    },
    instantBonusId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    userPromoCodeId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    },
    bonusType: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'deposit'
    },
    claimIntervalBonusId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'burning_bonus',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'burning_bonus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  BurningBonus.associate = (models) => {
    BurningBonus.belongsTo(models.UserBonus, { foreignKey: 'userBonusId' })
    BurningBonus.belongsTo(models.InstantDepositBonusHistory, { foreignKey: 'instantBonusId' })
  }
  return BurningBonus
}
