'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerCategory = sequelize.define('PlayerCategory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    withdrawalLimit: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'player_categories',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'player_categories_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PlayerCategory.associate = (models) => {
    PlayerCategory.hasMany(models.PlayerCategoryLevel, {
      foreignKey: 'categoryId',
    });
  };

  return PlayerCategory
}
