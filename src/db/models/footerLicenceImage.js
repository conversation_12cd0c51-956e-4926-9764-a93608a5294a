module.exports = (sequelize, DataTypes) => {
  const FooterLicenseImage = sequelize.define('FooterLicenseImage', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    footerLicenseId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    imageUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    redirectUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'footer_license_images',
    timestamps: true,
    schema: 'public',
    indexes: [
      {
        name: 'footer_license_images_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  FooterLicenseImage.associate = models => {
    FooterLicenseImage.belongsTo(models.FooterLicense, {
      onDelete: 'cascade',
      hooks: true,
      foreignKey: {
        name: 'footerLicenseId',
        allowNull: false
      }
    })
  }

  return FooterLicenseImage
}
