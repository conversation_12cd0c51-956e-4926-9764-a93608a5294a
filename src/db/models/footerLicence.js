module.exports = (sequelize, DataTypes) => {
  const FooterLicense = sequelize.define('FooterLicense', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    creatorId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    creatorType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    heading: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content_1: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    content_2: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    headerContent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'footer_license',
    timestamps: true,
    schema: 'public',
    indexes: [
      {
        name: 'footer_license_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  FooterLicense.associate = models => {
    FooterLicense.hasMany(models.FooterLicenseImage, {
      onDelete: 'cascade',
      hooks: true,
      foreignKey: {
        name: 'footerLicenseId',
        allowNull: false
      }
    })
  }

  return FooterLicense
}
