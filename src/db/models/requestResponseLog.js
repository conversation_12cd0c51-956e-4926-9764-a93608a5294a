
module.exports = (sequelize, DataTypes) => {
  const RequestResponseLog = sequelize.define('RequestResponseLog', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    requestJson: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    responseJson: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    service: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    responseCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    responseStatus: {
      type: DataTypes.STRING,
      allowNull: true
    },
    errorCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'request_response_log',
    schema: 'public',
    timestamps: true
  })

  return RequestResponseLog
}
