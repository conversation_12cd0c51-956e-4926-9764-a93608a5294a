'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantBankConfiguration = sequelize.define('TenantBankConfiguration', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    usedFor: {
      type: DataTypes.STRING,
      allowNull: true

    },
    bankName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    accountHolderName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    bankIfscCode: {
      type: DataTypes.STRING,
      allowNull: true

    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: true

    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    createdBy: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    lastUpdatedOwnerId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    upiId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    type: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      allowNull: true,
      comment: '1- bank, 2- virtual'
    },
    cryptoExchangeRate: {
      type: DataTypes.DECIMAL(10, 5),
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    playerCategory: {
      type: DataTypes.JSONB,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_bank_configuration',
    schema: 'public',
    timestamps: true
    // indexes: [
    //   {
    //     name: 'index_tenant_configurations_on_tenant_id',
    //     fields: [
    //       { name: 'tenant_id' }
    //     ]
    //   },
    //   {
    //     name: 'tenant_configurations_pkey',
    //     unique: true,
    //     fields: [
    //       { name: 'id' }
    //     ]
    //   }
    // ]
  })
  return TenantBankConfiguration
}
