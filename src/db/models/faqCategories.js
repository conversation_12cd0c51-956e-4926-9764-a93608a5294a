module.exports = (sequelize, DataTypes) => {
  const FaqCategories = sequelize.define('FaqCategories', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'faq_categories',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'faq_categories_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  FaqCategories.associate = (models) => {
    FaqCategories.hasMany(models.Faq, {
      foreignKey: "categoryId",
    });
  };

  return FaqCategories
}
