'use strict'
module.exports = (sequelize, DataTypes) => {
  const CasinoItem = sequelize.define('CasinoItem', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.STRING,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    itemType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    itemOrder: {
      type: DataTypes.INTEGER,
      allowNull: true

    },
    provider: {
      type: DataTypes.STRING,
      allowNull: true
    },
    technology: {
      type: DataTypes.STRING,
      allowNull: true
    },
    hasLobby: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    isMobile: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    hasFreeSpins: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'casino_items',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'casino_items_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_casino_items_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      }
    ]
  })

  CasinoItem.associate = models => {
    CasinoItem.hasOne(models.MenuItem, {
      onDelete: 'cascade',
      foreignKey: 'casinoItemId'
    })


    CasinoItem.belongsTo(models.CasinoProvider, {
      foreignKey: 'provider',
    })

    CasinoItem.hasMany(models.FavoriteGames, {
      foreignKey: 'gameId',
      as: 'favoriteGames'
    })
  }

  return CasinoItem
}
