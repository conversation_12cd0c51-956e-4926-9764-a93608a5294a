module.exports = (sequelize, DataTypes) => {
  const UtrHistory = sequelize.define('UtrHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    utrNumber: {
      type: DataTypes.STRING,
      allowNull: false
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    actioneeId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    actioneeType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    utrType: {
      type: DataTypes.STRING,
      allowNull: false // 'deposit', 'transaction', 'withdraw'
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0 // 0: pending, 1: approved, 2: rejected
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    transactionDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    currencyId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'utr_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'utr_history_utr_status_idx',
        fields: ['utrNumber', 'status']
      }
    ]
  });


  return UtrHistory;
};
