module.exports = (sequelize, DataTypes) => {
  const BetsBetcountonevent = sequelize.define('BetsBetcountonevent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_deleted'
    },
    userEvent: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'user_event'
    },
    betCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'bet_count'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'bets_betcountonevent',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bets_betcountonevent_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return BetsBetcountonevent
}
