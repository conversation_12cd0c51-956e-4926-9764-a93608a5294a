'use strict'
module.exports = (sequelize, DataTypes) => {
  const CasinoMenu = sequelize.define('CasinoMenu', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    menuType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    menuOrder: {
      type: DataTypes.INTEGER,
      allowNull: true

    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    imageUrl: {
      type: DataTypes.STRING,
      allowNull: true

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'casino_menus',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'casino_menus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_casino_menus_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      }
    ]
  })

  CasinoMenu.associate = models => {
    CasinoMenu.hasOne(models.PageMenu, {
      onDelete: 'cascade',
      foreignKey: 'casinoMenuId'
    })
  }

  return CasinoMenu
}
