'use strict'
module.exports = function (sequelize, DataTypes) {
  const PopularGames = sequelize.define('PopularGames', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    sliderTitle: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'popular_games',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'popular_games_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PopularGames.associate = function (models) {
    PopularGames.hasMany(models.PopularGamesDetails, {
      foreignKey: 'popularGameId',
      as: 'details'
    })
  }

  return PopularGames
}
