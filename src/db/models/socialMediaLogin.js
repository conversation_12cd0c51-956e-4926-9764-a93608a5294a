'use strict'
module.exports = (sequelize, DataTypes) => {
  const SocialMediaLogin = sequelize.define('SocialMediaLogin', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    socialMediaUserId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    loginType: {
      type: DataTypes.SMALLINT,
      allowNull: false
    },
    signInCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'social_media_login',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_social_media_login_on_user_id_tenant_id',
        fields: [
          { name: 'user_id' },
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'index_social_media_login_on_user_id_tenant_id_login_type',
        fields: [
          { name: 'user_id' },
          { name: 'tenant_id' },
          { name: 'login_type' }
        ]
      },
      {
        name: 'index_social_media_login_on_user_id_tenant_id',
        fields: [
          { name: 'user_id' },
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'social_media_login_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  SocialMediaLogin.associate = models => {
    SocialMediaLogin.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }
  return SocialMediaLogin
}
