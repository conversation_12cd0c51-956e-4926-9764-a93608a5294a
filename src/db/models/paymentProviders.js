'use strict'
module.exports = (sequelize, DataTypes) => {
  const paymentProviders = sequelize.define('paymentProviders', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    providerName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    providerKeys: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    logo: {
      type: DataTypes.STRING,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    providerType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currencies: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'payment_providers',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'payment_providers_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  paymentProviders.associate = models => {
    paymentProviders.hasMany(models.tenantPaymentConfiguration, {
      foreignKey: 'providerId'
    })
    paymentProviders.hasMany(models.Transaction, {
      foreignKey: 'paymentProviderId'
    })
  }

  return paymentProviders
}
