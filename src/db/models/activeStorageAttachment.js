
module.exports = (sequelize, DataTypes) => {
  const ActiveStorageAttachment = sequelize.define('ActiveStorageAttachment', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    recordType: {
      type: DataTypes.STRING,
      allowNull: false

    },
    recordId: {
      type: DataTypes.BIGINT,
      allowNull: false

    },
    blobId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'active_storage_blobs',
        key: 'id'
      }

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'active_storage_attachments',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'active_storage_attachments_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_active_storage_attachments_on_blob_id',
        fields: [
          { name: 'blob_id' }
        ]
      },
      {
        name: 'index_active_storage_attachments_uniqueness',
        unique: true,
        fields: [
          { name: 'record_type' },
          { name: 'record_id' },
          { name: 'name' },
          { name: 'blob_id' }
        ]
      }
    ]
  })

  return ActiveStorageAttachment
}
