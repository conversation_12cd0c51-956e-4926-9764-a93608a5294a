
module.exports = (sequelize, DataTypes) => {
  const Bonus = sequelize.define('Bonus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    bonusCancellationType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    percentage: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    validFrom: {
      type: DataTypes.DATE,
      allowNull: true
    },
    validUpto: {
      type: DataTypes.DATE,
      allowNull: true
    },
    kind: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currencyId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    promotionTitle: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    termsAndConditions: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    vipLevels: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      allowNull: true,
      defaultValue: []
    },
    usageCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    flat: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    walletType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    promoCodes: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    referType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    referValue: {
      type: DataTypes.STRING,
      allowNull: true
    },
    depositBonusType: {
      type: DataTypes.STRING,
      allowNull: true
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'bonus',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bonus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Bonus.associate = models => {
    Bonus.hasOne(models.DepositBonusSetting, {
      foreignKey: 'bonusId'
    })
    Bonus.hasOne(models.LosingBonusSetting, {
      foreignKey: 'bonusId'
    })
    Bonus.hasMany(models.UserBonus, {
      foreignKey: 'bonusId',
      as: 'userBonus'
    })
    Bonus.hasOne(models.UserBonusQueue, {
      foreignKey: 'bonusId',
      as: 'userBonusQueue'
    });
    Bonus.hasMany(models.BonusCategory, {
      foreignKey: 'bonusId',
      as: 'bonusCategory'
    })
  }
  return Bonus
}
