module.exports = (sequelize, DataTypes) => {
  const BenefitsContent = sequelize.define('BenefitsContent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    buttonText: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    buttonRedirection: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    image: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'benefits_content',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'benefits_content_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return BenefitsContent
}
