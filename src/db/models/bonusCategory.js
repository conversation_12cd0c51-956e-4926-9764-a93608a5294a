module.exports = (sequelize, DataTypes) => {
  const BonusCategory = sequelize.define('BonusCategory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    category: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: 'A = 1, B = 2, C = 3, D = 4'
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'bonus_category',
    schema: 'public',
    timestamps: true,
    updatedAt: false,
    indexes: [
      {
        name: 'bonus_category_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bonus_category_on_bonus_id_and_tenant_id',
        fields: [
          { name: 'bonus_id' },
          { name: 'tenant_id' }
        ]
      }
    ]
  })

  BonusCategory.associate = models => {
    BonusCategory.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    })
  }

  return BonusCategory
}
