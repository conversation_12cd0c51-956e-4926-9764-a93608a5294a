
module.exports = (sequelize, DataTypes) => {
  const ArInternalMetadata = sequelize.define('ArInternalMetadatum', {
    key: {
      type: DataTypes.STRING,
      allowNull: false,
      primaryKey: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'ar_internal_metadata',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'ar_internal_metadata_pkey',
        unique: true,
        fields: [
          { name: 'key' }
        ]
      }
    ]
  })

  return ArInternalMetadata
}
