
module.exports = (sequelize, DataTypes) => {
  const DepositBonusTier = sequelize.define('DepositBonusTier', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    depositBonusSettingId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    minDepositAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    maxDepositAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    maxBonus: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    percentage: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    flat: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_bonus_tiers',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_bonus_tiers_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  DepositBonusTier.associate = (models) => {
    DepositBonusTier.belongsTo(models.DepositBonusSetting, { foreignKey: 'depositBonusSettingId' });
    DepositBonusTier.belongsTo(models.Bonus, { foreignKey: 'bonusId' });
  };
  return DepositBonusTier
}
