'use strict'
module.exports = (sequelize, DataTypes) => {
  const PageBanner = sequelize.define('PageBanner', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    tenantId: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    name: {
      allowNull: false,
      type: DataTypes.JSONB
    },
    imageUrl: {
      default: false,
      type: DataTypes.STRING
    },
    redirectUrl: {
      default: false,
      type: DataTypes.STRING
    },
    bannerType: {
      allowNull: true,
      type: DataTypes.STRING
    },
    order: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    enabled: {
      allowNull: false,
      type: DataTypes.BOOLEAN
    },
    gameName: {
      allowNull: true,
      type: DataTypes.STRING
    },
    openTable: {
      allowNull: true,
      type: DataTypes.STRING
    },
    providerName: {
      allowNull: true,
      type: DataTypes.STRING
    },
    imageUrlMobile: {
      allowNull: true,
      type: DataTypes.STRING
    },
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE
    },
    taggedPages: {
      allowNull: true,
      type: DataTypes.JSON
    },
    redirectingType: {
      allowNull: true,
      type: DataTypes.INTEGER
    },
    buttonText: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    content: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    actionType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      comment: '0 - Casino, 1 - Sports'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'page_banners',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_page_banners_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'page_banners_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return PageBanner
}
