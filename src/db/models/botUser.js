'use strict'
module.exports = (sequelize, DataTypes) => {
  const BotUser = sequelize.define('BotUser', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      default: null
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'bot_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bot_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bot_users_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },

    ]
  })

  BotUser.associate = models => {
    BotUser.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }

  return BotU<PERSON>
}
