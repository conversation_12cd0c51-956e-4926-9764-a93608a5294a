'use strict'

module.exports = (sequelize, DataTypes) => {
  const UserRfidQrcodeDetails = sequelize.define('UserRfidQrcodeDetails', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    rfidToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    qrCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    rfidStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    qrCodeStatus: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_rfid_qrcode_details',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_rfid_qrcode_details_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return UserRfidQrcodeDetails
}
