'use strict'
module.exports = (sequelize, DataTypes) => {
  const Language = sequelize.define('Language', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    abbreviation: {
      type: DataTypes.STRING,
      allowNull: false
    },
    direction: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'languages',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'languages_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Language.associate = models => {
    Language.hasMany(models.TenantThemeSetting, {
      onDelete: 'cascade',
      foreignKey: 'languageId'
    })
    Language.hasMany(models.LanguageKey, {
      foreignKey: 'languageId'
    })
  }

  return Language
}
