'use strict'

module.exports = (sequelize, DataTypes) => {
  const UserLoginPin = sequelize.define('UserLoginPin', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    encryptedLoginPin: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_login_pin',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_user_login_pin_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      }
    ]
  })

  return UserLoginPin
}
