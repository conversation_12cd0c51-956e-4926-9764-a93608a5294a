
module.exports = (sequelize, DataTypes) => {
  const DepositRequest = sequelize.define('DepositRequest', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    orderId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'opened'
    },
    data: {
      type: DataTypes.HSTORE,
      allowNull: true,
      defaultValue: ''
    },
    ledgerId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    paymentProviderId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    utrNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    transactionReceipt: {
      type: DataTypes.STRING,
      allowNull: true
    },
    bankDetails: {
      type: DataTypes.JSON,
      allowNull: true
    },
    depositType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    remark: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    actionId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    actionType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    sessionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    trackingId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    paymentInitiate: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currencyConversion: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    requestedCurrencyCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    requestedAmount: {
      type: DataTypes.FLOAT,
      allowNull: true
    },
    manualDepositType: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: '1- bank, 2- virtual'
    },
    userRemark: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    oldAmount: {
      type: DataTypes.FLOAT,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_requests',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_requests_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_deposit_requests_on_order_id',
        fields: [
          { name: 'order_id' }
        ]
      },
      {
        name: 'index_deposit_requests_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'index_deposit_requests_on_payment_provider_id',
        fields: [
          { name: 'paymentProviderId' }
        ]
      }
    ]
  })
  DepositRequest.associate = models => {
    DepositRequest.belongsTo(models.paymentProviders, {
      foreignKey: 'paymentProviderId'
    })
  }

  return DepositRequest
}
