'use strict'
module.exports = (sequelize, DataTypes) => {
  const PageMenu = sequelize.define('PageMenu', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    pageId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    casinoMenuId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    menuOrder: {
      type: DataTypes.INTEGER,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'page_menus',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_page_menus_on_casino_menu_id',
        fields: [
          { name: 'casino_menu_id' }
        ]
      },
      {
        name: 'index_page_menus_on_page_id',
        fields: [
          { name: 'page_id' }
        ]
      },
      {
        name: 'page_menus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PageMenu.associate = models => {
    PageMenu.hasMany(models.MenuItem, {
      onDelete: 'cascade',
      hooks: true,
      foreignKey: {
        name: 'pageMenuId',
        allowNull: false
      }
    })
    PageMenu.belongsTo(models.Page, {
      onDelete: 'cascade',
      hooks: true,
      foreignKey: {
        name: 'pageId',
        allowNull: false
      }
    })
    PageMenu.belongsTo(models.CasinoMenu, {
      as: 'casinoMenu',
      onDelete: 'cascade',
      foreignKey: 'casinoMenuId'
    })
  }

  return PageMenu
}
