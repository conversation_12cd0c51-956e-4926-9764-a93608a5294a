module.exports = (sequelize, DataTypes) => {
  const TenantUserRegistrationFields = sequelize.define('TenantUserRegistrationFields', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    firstName: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    lastName: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    email: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    username: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    nickName: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    dob: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    currency: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    phone: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    city: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    zipCode: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    password: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    confirmPassword: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    eighteenYearCheck: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    promoCode: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    nationalId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_user_registration_fields',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_user_registration_fields_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return TenantUserRegistrationFields
}
