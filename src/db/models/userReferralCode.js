module.exports = (sequelize, DataTypes) => {
  const UserReferralCode = sequelize.define('UserReferralCode', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      },
      field: 'user_id'
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    referralCode: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'referral_code'
    }
  }, {
    sequelize,
    tableName: 'user_referral_code',
    underscored: true,
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: "referral_code_index",
        fields: [
          { name: "referral_code" },
          { name: "tenant_id" },
        ]
      },
      {
        name: "referral_code_user_index",
        fields: [
          { name: "referral_code" },
          { name: "user_id" },
          { name: "tenant_id" },
        ]
      },
      {
        name: "user_referral_code_pkey",
        unique: true,
        fields: [
          { name: "id" },
        ]
      },
    ]
  });

  UserReferralCode.associate = models => {
    UserReferralCode.belongsTo(models.User, {
      foreignKey: 'userId',
    });
  };
  return UserReferralCode;
};
