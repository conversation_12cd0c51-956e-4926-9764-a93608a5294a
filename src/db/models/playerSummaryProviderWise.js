'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerSummaryProviderWise = sequelize.define(
    'PlayerSummaryProviderWise',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER
      },
      tenantId: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      userId: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      providerId: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      gameId: {
        allowNull: false,
        type: DataTypes.STRING(150)
      },
      type: {
        allowNull: false,
        type: DataTypes.SMALLINT
      },
      currencyId: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      agentId: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      amount: {
        allowNull: false,
        type: DataTypes.DECIMAL(20, 5)
      },
      txCount: {
        allowNull: false,
        type: DataTypes.INTEGER
      },
      date: {
        allowNull: false,
        type: DataTypes.DATE
      },
      createdAt: {
        allowNull: false,
        type: DataTypes.DATE
      },
      updatedAt: {
        allowNull: false,
        type: DataTypes.DATE
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'player_summary_provider_wise',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'player_summary_provider_wise_pkey',
          unique: true,
          fields: [{ name: 'id' }]
        }
      ]
    }
  )

  return PlayerSummaryProviderWise
}
