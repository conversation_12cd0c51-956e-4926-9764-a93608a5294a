module.exports = (sequelize, DataTypes) => {
  const TenantBlockedEvent = sequelize.define('TenantBlockedEvent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'tenant_id'
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'admin_user_id'
    },
    pullsEventFixtureId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'pulls_event_fixture_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'tenant_blocked_events',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_tenant_blocked_events_on_admin_user_id',
        fields: [
          { name: 'admin_user_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_events_on_pulls_event_id',
        fields: [
          { name: 'pulls_event_id' }
        ]
      },
      {
        name: 'index_tenant_blocked_events_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_blocked_events_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  TenantBlockedEvent.associate = models => {
    TenantBlockedEvent.belongsTo(models.PullsEvent, {
      foreignKey: 'pullsEventFixtureId',
      as: 'event'
    })
  }
  return TenantBlockedEvent
}
