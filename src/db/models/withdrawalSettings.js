'use strict'

module.exports = (sequelize, DataTypes) => {
  const WithdrawalSettings = sequelize.define('WithdrawalSettings', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isEnabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    maxWithdrawalCount: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'withdrawal_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'withdrawal_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return WithdrawalSettings
}
