'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantCredential = sequelize.define('TenantCredential', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_credentials',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_tenant_credentials_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'tenant_credentials_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  TenantCredential.associate = models => {
    TenantCredential.belongsTo(models.Tenant, {
      foreignKey: 'tenantId',
      allowNull: false
    })
  }

  return TenantCredential
}
