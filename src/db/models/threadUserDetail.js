'use strict';

module.exports = (sequelize, DataTypes) => {
  const ThreadUserDetail = sequelize.define('ThreadUserDetail', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    threadId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'thread_user_details',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'thread_user_details_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  });

  ThreadUserDetail.associate = models => {
    ThreadUserDetail.belongsTo(models.Thread, {
      foreignKey: 'threadId'
    });
  };

  return ThreadUserDetail;
};
