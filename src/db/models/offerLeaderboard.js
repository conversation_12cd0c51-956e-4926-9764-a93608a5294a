'use strict'
module.exports = (sequelize, DataTypes) => {
  const OfferLeaderboard = sequelize.define('OfferLeaderboard', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    offerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    winningAmount: {
      type: DataTypes.DECIMAL(20, 5),
      allowNull: false,
      default: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'offer_leaderboards',
    schema: 'public',
    timestamps: true
  })

  OfferLeaderboard.associate = (models) => {
    // Many-to-One relationship with Offer
    OfferLeaderboard.belongsTo(models.Offer, {
      foreignKey: 'offerId',
      as: 'offer'
    })
  }

  return OfferLeaderboard
}
