'use strict'

module.exports = (sequelize, DataTypes) => {
  const DepositWagerLinkage = sequelize.define('DepositWagerLinkage', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    depositId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    depositWagerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_wager_linkage',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_wager_linkage_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_deposit_wager_linkage_on_deposit_id_and_deposit_wager_id',
        unique: true,
        fields: [
          { name: 'deposit_id' },
          { name: 'deposit_wager_id' }
        ]
      },
    ]
  })

  return DepositWagerLinkage
}
