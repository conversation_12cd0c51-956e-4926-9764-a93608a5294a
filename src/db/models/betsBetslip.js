module.exports = (sequelize, DataTypes) => {
  const BetsBetslip = sequelize.define('BetsBetslip', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    bettype: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    stake: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'user_id'
    },
    multiPrice: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      field: 'multi_price'
    },
    betslipStatus: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'betslip_status'
    },
    couponId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'coupon_id'
    },
    possibleWinAmount: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      field: 'possible_win_amount'
    },
    settlementStatus: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'settlement_status'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
    run: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      field: 'run'
    }
  }, {
    sequelize,
    tableName: 'bets_betslip',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'bets_betslip_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bets_betslip_on_coupon_id',
        fields: [
          { name: 'coupon_id' }
        ]
      },
      {
        name: 'index_bets_betslip_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      }
    ]
  })
  BetsBetslip.associate = models => {
    BetsBetslip.hasMany(models.BetsBet, {
      foreignKey: 'betslipId',
      onDelete: 'cascade',
      as: 'bets'
    })

    BetsBetslip.hasMany(models.BetsTransaction, {
      foreignKey: 'betslipId',
      onDelete: 'cascade'
    })
  }
  return BetsBetslip
}
