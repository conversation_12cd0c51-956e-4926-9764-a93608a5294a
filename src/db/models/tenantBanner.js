'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantBanner = sequelize.define('TenantBanner', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    tenantId: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    name: {
      allowNull: false,
      type: DataTypes.JSONB
    },
    imageUrl: {
      default: false,
      type: DataTypes.STRING
    },
    redirectUrl: {
      default: false,
      type: DataTypes.STRING
    },
    bannerType: {
      allowNull: true,
      type: DataTypes.STRING
    },
    order: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    enabled: {
      allowNull: false,
      type: DataTypes.BOOLEAN
    },
    gameName: {
      allowNull: true,
      type: DataTypes.STRING
    },
    openTable: {
      allowNull: true,
      type: DataTypes.STRING
    },
    providerName: {
      allowNull: true,
      type: DataTypes.STRING
    },
    imageUrlMobile: {
      allowNull: true,
      type: DataTypes.STRING
    },
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE
    },
    taggedPages: {
      allowNull: true,
      type: DataTypes.JSON
    },
    redirectingType: {
      allowNull: true,
      type: DataTypes.INTEGER
    },
    buttonText: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    actionType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      defaultValue: 0,
      comment: '0 - Casino, 1 - Sports'
    }
  }, {
    tableName: 'page_banners',
    underscored: true
  })

  TenantBanner.associate = models => {
    TenantBanner.belongsTo(models.Tenant, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
  }
  return TenantBanner
}
