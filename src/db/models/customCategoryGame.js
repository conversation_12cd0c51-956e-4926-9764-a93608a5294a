'use strict'
module.exports = (sequelize, DataTypes) => {
  const CustomCategoryGame = sequelize.define('CustomCategoryGame', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.STRING,
      allowNull: false
    },
    categoryId: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'category_id'
    },
    pageId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'page_id'
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: true

    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'provider_id'
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: 'is_deleted'
    },
    isOrderModified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: 'is_order_modified'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'tenant_id'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'custom_category_games',
    schema: 'public',
    timestamps: true
  })

  CustomCategoryGame.associate = models => {
    CustomCategoryGame.belongsTo(models.CasinoProvider, {
      foreignKey: 'providerId'
    })

    CustomCategoryGame.hasMany(models.FavoriteGames, {
      foreignKey: 'gameId',
      sourceKey: 'uuid',
      as: 'favoriteGames'
    })
  }

  return CustomCategoryGame
}
