
module.exports = function (sequelize, DataTypes) {
  const LosingBonusTier = sequelize.define('LosingBonusTier', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    losingBonusSettingId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'losing_bonus_settings',
        key: 'id'
      }
    },
    minLosingAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    percentage: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    maxBonus: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    maxLosingAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'losing_bonus_tiers',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_losing_bonus_tiers_on_losing_bonus_setting_id',
        fields: [
          { name: 'losing_bonus_setting_id' }
        ]
      },
      {
        name: 'losing_bonus_tiers_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  LosingBonusTier.associate = models => {
    LosingBonusTier.belongsTo(models.LosingBonusSetting, {
      foreignKey: 'losingBonusSettingId'
    })
  }
  return LosingBonusTier
}
