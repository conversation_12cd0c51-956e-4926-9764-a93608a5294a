
module.exports = (sequelize, DataTypes) => {
  const CombinedMenuGames = sequelize.define('CombinedMenuGames', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    casinoMenu: {
      type: DataTypes.JSON,
      allowNull: false
    },
    menuItems: {
      type: DataTypes.JSON,
      allowNull: false
    },
    totalMenuItem: {
      type: DataTypes.JSON,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    topMenuId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    isCustomCategory: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'combined_menu_games',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'combined_menu_games_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'tenant_top_menu_index',
        fields: [
          { name: 'tenant_id' },
          { name: 'top_menu_id' }
        ]
      },
      {
        name: 'name_tenant_top_menu_index',
        fields: [
          { name: 'name' },
          { name: 'tenant_id' },
          { name: 'top_menu_id' }
        ]
      }
    ],
    uniqueKeys: {
      unique_tenant_topmenu_name: {
        fields: ['tenant_id', 'top_menu_id', 'name']
      }
    }
  })

  return CombinedMenuGames
}
