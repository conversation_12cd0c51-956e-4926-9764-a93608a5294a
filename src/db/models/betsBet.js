module.exports = (sequelize, DataTypes) => {
  const BetsBet = sequelize.define('BetsBet', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    betId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'bet_id'
    },
    fixtureId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'fixture_id'
    },
    providerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'provider_id'
    },
    champ: {
      type: DataTypes.STRING,
      allowNull: false
    },
    match: {
      type: DataTypes.STRING,
      allowNull: false
    },
    market: {
      type: DataTypes.STRING,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    price: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date'
    },
    betslipId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'betslip_id'
    },
    marketId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'market_id'
    },
    betStatus: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'bet_status'
    },
    eventId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      field: 'event_id'
    },
    settlementStatus: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'settlement_status'
    },
    livescore: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'bets_bets',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'bets_bets_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bets_bets_on_betslip_id',
        fields: [
          { name: 'betslip_id' }
        ]
      },
      {
        name: 'index_bets_bets_on_event_id',
        fields: [
          { name: 'event_id' }
        ]
      }
    ]
  })

  BetsBet.associate = models => {
    BetsBet.belongsTo(models.PullsEvent, {
      foreignKey: 'eventId'
    })

    BetsBet.belongsTo(models.BetsBetslip, {
      foreignKey: 'betslipId'
    })

    BetsBet.hasMany(models.PullsEventparticipant, {
      sourceKey: 'eventId',
      foreignKey: 'eventId',
      as: 'Participants'
    })
  }

  return BetsBet
}
