
module.exports = function (sequelize, DataTypes) {
  const LosingBonusSetting = sequelize.define('LosingBonusSetting', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    claimDays: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    claimIntervalType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    bonusCalculationType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    bonusClaimType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    weekDay: {
      type: DataTypes.STRING,
      allowNull: false
    },
    burningDays: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    burnType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    providerDetails: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'losing_bonus_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_losing_bonus_settings_on_bonus_id',
        fields: [
          { name: 'bonus_id' }
        ]
      },
      {
        name: 'losing_bonus_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  LosingBonusSetting.associate = models => {
    LosingBonusSetting.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    })
    LosingBonusSetting.hasMany(models.LosingBonusTier, {
      foreignKey: 'losingBonusSettingId'
    })
  }
  return LosingBonusSetting
}
