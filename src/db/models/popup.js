module.exports = (sequelize, DataTypes) => {
  const Popup = sequelize.define('Popup', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    popupCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    popupType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    banner: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminType: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    closePopupType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    visiblityStatus: {
      type: DataTypes.STRING,
      allowNull: true
    },
    displayCondition: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'popups',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'popups_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return Popup
}
