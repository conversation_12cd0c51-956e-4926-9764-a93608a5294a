module.exports = (sequelize, DataTypes) => {
  const PullsEventparticipant = sequelize.define('PullsEventparticipant', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    position: {
      type: DataTypes.STRING,
      allowNull: true
    },
    rotationId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'rotation_id'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_active'
    },
    extraData: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'extra_data'
    },
    eventId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'event_id'
    },
    participantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'participant_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    }
  }, {
    sequelize,
    tableName: 'pulls_eventparticipants',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_pulls_eventparticipants_on_event_id',
        fields: [
          { name: 'event_id' }
        ]
      },
      {
        name: 'index_pulls_eventparticipants_on_participant_id',
        fields: [
          { name: 'participant_id' }
        ]
      },
      {
        name: 'pulls_eventparticipants_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PullsEventparticipant.associate = models => {
    PullsEventparticipant.belongsTo(models.PullsParticipant, {
      foreignKey: 'participantId',
      as: 'participant'
    })
  }

  return PullsEventparticipant
}
