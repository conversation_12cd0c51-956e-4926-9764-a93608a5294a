import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, LIMIT_RANGE, MANUAL_DEPOSIT_TYPE, PLAYER_CATEGORY, RESPONSIBLE_GAMING_CONSTANT, TENANT_SETTINGS_KEYS, TENANT_SETTINGS_TYPE, TRANSACTION_TYPES, WITHDRAW_REQUEST_STATUS } from '../common/constants';
import { filterByDateCreatedAt } from '../common/filterByDateCreatedAt';
import keyValueToJson from '../lib/keyValueToJSON';

export const getLimitDates = () => {
  const today = new Date()

  let date = new Date()
  const offset = date.getTimezoneOffset()

  let monthStartDate = new Date((new Date(date.getFullYear(), date.getMonth(), 1)).getTime() - (offset * 60 * 1000))
  monthStartDate = monthStartDate.toISOString().split('T')[0]

  date = new Date()
  const day = date.getDay()
  const diff = date.getDate() - day + (day === 0 ? -6 : 1)

  const weekStartDate = new Date(date.setDate(diff))

  return {
    today: today.toISOString().substring(0, 10),
    monthStartDate,
    weekStartDate: weekStartDate.toISOString().substring(0, 10)
  }
}

export const checkLimit = {
  betLimitCheck: async (context, user, amount, sequelizeTransaction) => {
    const TransactionModel = context.databaseConnection.Transaction
    const WalletModel = context.databaseConnection.Wallet
    const UserSettingModel = context.databaseConnection.UserSetting
    const userSetting = await keyValueToJson(UserSettingModel, [RESPONSIBLE_GAMING_CONSTANT.DAILY_BETTING_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_BETTING_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_BETTING_LIMIT], 'userId', user.id)
    const wallet = await WalletModel.findOne({
      where: {
        ownerId: user.id,
        ownerType: 'User'
      },
      raw: true,
      transaction: sequelizeTransaction
    })

    const { today, monthStartDate, weekStartDate } = getLimitDates()

    const defaultQuery = {
      sourceWalletId: wallet.id
    }

    const todayQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), today, today)
    const monthQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), monthStartDate, today)
    const weekQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), weekStartDate, today)

    const totalBetAmountToday = await TransactionModel.sum('amount', { where: todayQuery, sequelizeTransaction })
    const totalBetAmountWeekly = await TransactionModel.sum('amount', { where: weekQuery, sequelizeTransaction })
    const totalBetAmountMonthly = await TransactionModel.sum('amount', { where: monthQuery, sequelizeTransaction })

    if ((!!userSetting?.dailyBettingLimit) && (parseFloat(totalBetAmountToday) + parseFloat(amount)) > parseFloat(userSetting.dailyBettingLimit)) {
      return RESPONSIBLE_GAMING_CONSTANT.DAILY_ERROR
    }

    if ((!!userSetting?.weeklyBettingLimit) && (parseFloat(totalBetAmountWeekly) + parseFloat(amount)) > parseFloat(userSetting.weeklyBettingLimit)) {
      return RESPONSIBLE_GAMING_CONSTANT.WEEKLY_ERROR
    }

    if ((!!userSetting?.monthlyBettingLimit) && (parseFloat(totalBetAmountMonthly) + parseFloat(amount)) > parseFloat(userSetting.monthlyBettingLimit)) {
      return RESPONSIBLE_GAMING_CONSTANT.MONTHLY_ERROR
    }

    return true
  },

  depositLimitCheck: async (context, user, amount) => {
    const TransactionModel = context.databaseConnection.Transaction
    const TenantThemeSettingModel = context.databaseConnection.TenantThemeSetting
    const WalletModel = context.databaseConnection.Wallet
    const UserSettingModel = context.databaseConnection.UserSetting
    const DepositRequestModel = context.databaseConnection.DepositRequest
    const TenantSettingModel = context.databaseConnection.TenantSetting
    const UserModel = context.databaseConnection.User
    const TenantBankConfigurationModel = context.databaseConnection.TenantBankConfiguration
    const tenant = context.tenant

    const userSetting = await keyValueToJson(UserSettingModel, [RESPONSIBLE_GAMING_CONSTANT.DAILY_DEPOSIT_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_DEPOSIT_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_DEPOSIT_LIMIT], 'userId', user)

    const wallet = await WalletModel.findOne({
      where: {
        ownerId: user,
        ownerType: 'User'
      },
      raw: true
    })

    const { today, monthStartDate, weekStartDate } = getLimitDates()

    // Total amount of transactions
    let defaultQuery = {
      targetWalletId: wallet.id,
      transactionType: TRANSACTION_TYPES.DEPOSIT
    }

    const getTransactionFilters = (defaultQuery) => {
      const todayTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), today, today)
      const weekTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), weekStartDate, today)
      const monthTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), monthStartDate, today)
      return {
        todayTransactionQuery,
        weekTransactionQuery,
        monthTransactionQuery
      }
    }

    const {
      todayTransactionQuery,
      weekTransactionQuery,
      monthTransactionQuery
    } = getTransactionFilters(defaultQuery)

    const totalTodayDepositAmount = await TransactionModel.sum('amount', { where: todayTransactionQuery })
    const totalWeeklyDepositAmount = await TransactionModel.sum('amount', { where: weekTransactionQuery })
    const totalMonthlyDepositAmount = await TransactionModel.sum('amount', { where: monthTransactionQuery })

    // Total amount of deposit request
    defaultQuery = {
      userId: user,
      status: DEPOSIT_REQUEST_STATUS.OPEN
    }

    const getDepositRequestFilters = (defaultQuery) => {
      const todayDepositRequestQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), today, today)
      const weeklyDepositRequestQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), weekStartDate, today)
      const monthlyDepositRequestQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), monthStartDate, today)
      return {
        todayDepositRequestQuery,
        weeklyDepositRequestQuery,
        monthlyDepositRequestQuery
      }
    }

    const {
      todayDepositRequestQuery,
      weeklyDepositRequestQuery,
      monthlyDepositRequestQuery
    } = getDepositRequestFilters(defaultQuery)

    const totalTodayDepositRequest = await DepositRequestModel.sum('amount', { where: todayDepositRequestQuery })
    const totalWeeklyDepositRequest = await DepositRequestModel.sum('amount', { where: weeklyDepositRequestQuery })
    const totalMonthlyDepositRequest = await DepositRequestModel.sum('amount', { where: monthlyDepositRequestQuery })

    const totalToday = totalTodayDepositAmount + totalTodayDepositRequest
    const totalWeekly = totalWeeklyDepositAmount + totalWeeklyDepositRequest
    const totalMonthly = totalMonthlyDepositAmount + totalMonthlyDepositRequest

    // If deposit type is VIRTUAL, fetch tenant's crypto exchange rate and apply it to the amount
    const manualDepositType = context?.req?.body?.variables?.input?.manualDepositType;

    if (manualDepositType === MANUAL_DEPOSIT_TYPE.VIRTUAL) {
      const tenantVirtualWalletInfo = await TenantBankConfigurationModel.findOne({
        attributes: ['cryptoExchangeRate'],
        where: {
          tenantId: tenant?.id,
          type: MANUAL_DEPOSIT_TYPE.VIRTUAL
        },
        order: [['id', 'ASC']]
      });

      const exchangeRate = tenantVirtualWalletInfo?.cryptoExchangeRate;

      if (exchangeRate) {
        amount *= exchangeRate;
      }
    }

    const userEntity = await UserModel.findOne({
      where: { id: user },
      attributes: ['vipLevel', 'categoryType'],
      raw: true,
    });

    // Fetch allowed modules for the tenant
    const allowedModules = await TenantThemeSettingModel.findOne({
      attributes: ['allowedModules'],
      where: { tenantId: tenant?.id },
      raw: true
    });

    // Module checks
    const moduleList = allowedModules?.allowedModules.split(',').map(module => module.trim()) || [];

    const hasPlayerCategory = moduleList.includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY);
    const hasLimitCheckSettingsEnabled = moduleList.includes(ALLOWED_PERMISSIONS.DEPOSIT_WITHDRAW_LIMIT_SETTINGS);

    // Fetch all deposit limits (Global, VIP, Category)
    const tenantSettings = await TenantSettingModel.findAll({
      where: {
        tenantId: tenant?.id,
        type: TENANT_SETTINGS_TYPE.DEPOSIT_LIMIT_SETTINGS,
      },
      attributes: ['key', 'value'],
      raw: true,
    });

    // Convert settings into a map for easy lookup
    const settingsMap = tenantSettings.reduce((map, setting) => {
      map[setting.key] = parseFloat(setting.value) || 0;
      return map;
    }, {});

    // Collect all limits with default values
    const limits = {
      // Always include user-level limits
      user: {
        daily: parseFloat(userSetting?.dailyDepositLimit) || 0,
        weekly: parseFloat(userSetting?.weeklyDepositLimit) || 0,
        monthly: parseFloat(userSetting?.monthlyDepositLimit) || 0,
      },
      // Include global limits only if module is enabled
      ...(hasLimitCheckSettingsEnabled && {
        global: {
          daily: settingsMap[TENANT_SETTINGS_KEYS.DAILY_GLOBAL_DEPOSIT_LIMIT] || 0,
          weekly: settingsMap[TENANT_SETTINGS_KEYS.WEEKLY_GLOBAL_DEPOSIT_LIMIT] || 0,
          monthly: settingsMap[TENANT_SETTINGS_KEYS.MONTHLY_GLOBAL_DEPOSIT_LIMIT] || 0,
        },
        vip: {
          daily: settingsMap[`${LIMIT_RANGE.DAILY}_${userEntity?.vipLevel}`] || 0,
          weekly: settingsMap[`${LIMIT_RANGE.WEEKLY}_${userEntity?.vipLevel}`] || 0,
          monthly: settingsMap[`${LIMIT_RANGE.MONTHLY}_${userEntity?.vipLevel}`] || 0,
        },
        category: hasPlayerCategory
          ? {
              daily: settingsMap[`${LIMIT_RANGE.DAILY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
              weekly: settingsMap[`${LIMIT_RANGE.WEEKLY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
              monthly: settingsMap[`${LIMIT_RANGE.MONTHLY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
            }
          : { daily: 0, weekly: 0, monthly: 0 },
      }),
    };

    const checkLimit = (limitType, total, maxLimit) => {
      if (maxLimit > 0 && (parseFloat(total) + parseFloat(amount)) > maxLimit) {
        return RESPONSIBLE_GAMING_CONSTANT[`${limitType.toUpperCase()}_ERROR`];
      }
      return null; // No error
    };

    if (hasLimitCheckSettingsEnabled) {
      // Fetch Global Override setting
      const globalOverrideSetting = await TenantSettingModel.findOne({
        where: { tenantId: tenant?.id, key: TENANT_SETTINGS_KEYS.FORCE_DEPOSIT_GLOBAL_LIMIT_OVERRIDE },
        attributes: ["value"],
        raw: true,
      });

      const isGlobalOverrideEnabled = parseFloat(globalOverrideSetting?.value) == 1;

      // If Global Override is enabled, check only Global limits
      if (isGlobalOverrideEnabled) {
        let error = checkLimit("daily", totalToday, limits.global.daily);
        if (!error) error = checkLimit("weekly", totalWeekly, limits.global.weekly);
        if (!error) error = checkLimit("monthly", totalMonthly, limits.global.monthly);

        return error || true;
      }
    }

    // Sequential limit validation check (User -> Category -> VIP -> Global)
    const levels = ["user", "category", "vip", "global"];

    for (const level of levels) {
      const levelLimits = limits[level];
      if (!levelLimits) continue;

      if (limits[level]?.daily || limits[level]?.weekly || limits[level]?.monthly) {
        let error = checkLimit("daily", totalToday, limits[level].daily);
        if (!error) error = checkLimit("weekly", totalWeekly, limits[level].weekly);
        if (!error) error = checkLimit("monthly", totalMonthly, limits[level].monthly);
        return error || true;
      }
    }

    return true;
  },

  withdrawLimitCheck: async (context, user, amount) => {
    const TransactionModel = context.databaseConnection.Transaction
    const TenantThemeSettingModel = context.databaseConnection.TenantThemeSetting
    const WalletModel = context.databaseConnection.Wallet
    const UserSettingModel = context.databaseConnection.UserSetting
    const TenantSettingModel = context.databaseConnection.TenantSetting
    const UserModel = context.databaseConnection.User
    const tenant = context.tenant

    const userSetting = await keyValueToJson(UserSettingModel, [RESPONSIBLE_GAMING_CONSTANT.DAILY_WITHDRAW_LIMIT, RESPONSIBLE_GAMING_CONSTANT.WEEKLY_WITHDRAW_LIMIT, RESPONSIBLE_GAMING_CONSTANT.MONTHLY_WITHDRAW_LIMIT], 'userId', user)

    const wallet = await WalletModel.findOne({
      where: {
        ownerId: user,
        ownerType: 'User'
      },
      raw: true
    })

    const { today, monthStartDate, weekStartDate } = getLimitDates()

    // Total amount of transactions
    let defaultQuery = {
      sourceWalletId: wallet.id,
      transactionType: TRANSACTION_TYPES.WITHDRAW,
      status: [WITHDRAW_REQUEST_STATUS.PENDING, WITHDRAW_REQUEST_STATUS.SUCCESS]
    }

    const getTransactionFilters = (defaultQuery) => {
      const todayTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), today, today)
      const weekTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), weekStartDate, today)
      const monthTransactionQuery = filterByDateCreatedAt(JSON.parse(JSON.stringify(defaultQuery)), monthStartDate, today)
      return {
        todayTransactionQuery,
        weekTransactionQuery,
        monthTransactionQuery
      }
    }

    const {
      todayTransactionQuery,
      weekTransactionQuery,
      monthTransactionQuery
    } = getTransactionFilters(defaultQuery)

    const totalTodayWithdrawAmount = await TransactionModel.sum('amount', { where: todayTransactionQuery })
    const totalWeeklyWithdrawAmount = await TransactionModel.sum('amount', { where: weekTransactionQuery })
    const totalMonthlyWithdrawAmount = await TransactionModel.sum('amount', { where: monthTransactionQuery })

    const totalToday = totalTodayWithdrawAmount;
    const totalWeekly = totalWeeklyWithdrawAmount;
    const totalMonthly = totalMonthlyWithdrawAmount;

    const userEntity = await UserModel.findOne({
      where: { id: user },
      attributes: ['vipLevel', 'categoryType'],
      raw: true,
    });

    // Fetch allowed modules for the tenant
    const allowedModules = await TenantThemeSettingModel.findOne({
      attributes: ['allowedModules'],
      where: { tenantId: tenant?.id },
      raw: true
    });

    // Module checks
    const moduleList = allowedModules?.allowedModules.split(',').map(module => module.trim()) || [];

    const hasPlayerCategory = moduleList.includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY);
    const hasLimitCheckSettingsEnabled = moduleList.includes(ALLOWED_PERMISSIONS.DEPOSIT_WITHDRAW_LIMIT_SETTINGS);

    // Fetch all deposit limits (Global, VIP, Category)
    const tenantSettings = await TenantSettingModel.findAll({
      where: {
        tenantId: tenant?.id,
        type: TENANT_SETTINGS_TYPE.WITHDRAW_LIMIT_SETTINGS,
      },
      attributes: ['key', 'value'],
      raw: true,
    });

    // Convert settings into a map for easy lookup
    const settingsMap = tenantSettings.reduce((map, setting) => {
      map[setting.key] = parseFloat(setting.value) || 0;
      return map;
    }, {});

    // Collect all limits with default values
    const limits = {
      // Always include user-level limits
      user: {
        daily: parseFloat(userSetting?.dailyWithdrawLimit) || 0,
        weekly: parseFloat(userSetting?.weeklyWithdrawLimit) || 0,
        monthly: parseFloat(userSetting?.monthlyWithdrawLimit) || 0,
      },
      // Include global limits only if module is enabled
      ...(hasLimitCheckSettingsEnabled && {
        global: {
          daily: settingsMap[TENANT_SETTINGS_KEYS.DAILY_GLOBAL_WITHDRAW_LIMIT] || 0,
          weekly: settingsMap[TENANT_SETTINGS_KEYS.WEEKLY_GLOBAL_WITHDRAW_LIMIT] || 0,
          monthly: settingsMap[TENANT_SETTINGS_KEYS.MONTHLY_GLOBAL_WITHDRAW_LIMIT] || 0,
        },
        vip: {
          daily: settingsMap[`${LIMIT_RANGE.DAILY}_${userEntity?.vipLevel}`] || 0,
          weekly: settingsMap[`${LIMIT_RANGE.WEEKLY}_${userEntity?.vipLevel}`] || 0,
          monthly: settingsMap[`${LIMIT_RANGE.MONTHLY}_${userEntity?.vipLevel}`] || 0,
        },
        category: hasPlayerCategory ? {
          daily: settingsMap[`${LIMIT_RANGE.DAILY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
          weekly: settingsMap[`${LIMIT_RANGE.WEEKLY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
          monthly: settingsMap[`${LIMIT_RANGE.MONTHLY}_${PLAYER_CATEGORY[userEntity?.categoryType]}`] || 0,
        } : { daily: 0, weekly: 0, monthly: 0 },  // If module is disabled, set limits to 0
      }),
    };

    const checkLimit = (limitType, total, maxLimit) => {
      if (maxLimit > 0 && (parseFloat(total) + parseFloat(amount)) > maxLimit) {
        return RESPONSIBLE_GAMING_CONSTANT[`${limitType.toUpperCase()}_ERROR`];
      }
      return null; // No error
    };

    if (hasLimitCheckSettingsEnabled) {
      // Fetch Global Override Setting
      const globalOverrideSetting = await TenantSettingModel.findOne({
        where: { tenantId: tenant?.id, key: TENANT_SETTINGS_KEYS.FORCE_WITHDRAW_GLOBAL_LIMIT_OVERRIDE },
        attributes: ["value"],
        raw: true,
      });

      const isGlobalOverrideEnabled = parseFloat(globalOverrideSetting?.value) == 1;

      // If Global Override is enabled, check only Global limits
      if (isGlobalOverrideEnabled) {
        let error = checkLimit("daily", totalToday, limits.global.daily);
        if (!error) error = checkLimit("weekly", totalWeekly, limits.global.weekly);
        if (!error) error = checkLimit("monthly", totalMonthly, limits.global.monthly);
        return error || true;
      }
    }

    // Sequential limit validation check (User -> Category -> VIP -> Global)
    const levels = ["user", "category", "vip", "global"];

    for (const level of levels) {
      const levelLimits = limits[level];
      if (!levelLimits) continue;

      if (limits[level]?.daily || limits[level]?.weekly || limits[level]?.monthly) {
        let error = checkLimit("daily", totalToday, limits[level].daily);
        if (!error) error = checkLimit("weekly", totalWeekly, limits[level].weekly);
        if (!error) error = checkLimit("monthly", totalMonthly, limits[level].monthly);
        return error || true;
      }
    }

    return true;
  }
}
