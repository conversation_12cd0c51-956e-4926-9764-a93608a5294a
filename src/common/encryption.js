import config from '../config/app'
const CryptoJS = require('crypto-js')

/**
 * This function is used for encryption.
 * @export
 * @param {Value} value
 * @return {Result} encoded value
 */
export default async (value) => {
  const secret = config.getProperties().auth.encrytion_secret
  const time = new Date()
  const ciphertext = CryptoJS.AES.encrypt(JSON.stringify({ value, time }), secret).toString()

  return ciphertext
}
