import { ENVIORNMENT, PROD_TENANTS, STAGE_TENANTS } from './constants';

const getUserSmarticoId = async (userId, tenantId, environment, Marina888UserModel) => {
  environment = environment === 'development' ? 'staging' : environment;
  const isStagingEnv = environment === ENVIORNMENT.STAGING;

  const tenants = isStagingEnv ? STAGE_TENANTS : PROD_TENANTS;
  const isMarina888Tenant = (isStagingEnv && tenantId === '54') || (!isStagingEnv && tenantId === '86');

  const tenantName = Object.values(tenants).find(tenant => tenant.id === tenantId)?.dataValues.name || '';

  if (isMarina888Tenant) {
    const marina888User = await Marina888UserModel.findOne({
      where: { internalUserId: userId },
      attributes: ['externalUserId'],
      raw: true
    });
    if (marina888User?.externalUserId) {
      return marina888User.externalUserId;
    }
  }

  return `${tenantName.replace(/\s+/g, '')}_${tenantId}_${userId}`;
};


export { getUserSmarticoId };
