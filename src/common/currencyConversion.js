import { Op } from 'sequelize'
/**
 * This function will reduce the rollover target for the user as it places the bet.
 * @export
 * @param {object} context The argument object
 * @param {Transaction} transactionObject The argument object
 * @param {User} user contains user object
 * @return {TransactionObject}
 */
export default async (context, transactionObject, userWallet, amountMain) => {
  const {
    Currency: CurrencyModel,
    TenantConfiguration: TenantConfigurationsModel
  } = context.databaseConnection
  const { sequelizeTransaction } = context

  const { id: tenantId } = context.tenant

  const currencyAllowedCurrencies = await TenantConfigurationsModel.findOne({
    where: { tenantId },
    attributes: ['allowedCurrencies'],
    transaction: sequelizeTransaction
  })

  const currencyAllAllowed = await CurrencyModel.findAll({
    where: { id: { [Op.in]: currencyAllowedCurrencies?.allowedCurrencies?.split(',') } },
    transaction: sequelizeTransaction
  })

  const exMainTransactionCurrency = userWallet.currencyId
  const otherCurrencyAmount = {}
  const currencyExchangeRate = await CurrencyModel.findOne({ where: { id: exMainTransactionCurrency }, transaction: sequelizeTransaction })

  for (const record of currencyAllAllowed) {
    if (exMainTransactionCurrency !== record.id) {
      const exchangeRate = await CurrencyModel.findOne({ where: { id: record.id }, transaction: sequelizeTransaction })
      const convertedAmountOther = (parseFloat(amountMain) * (exchangeRate.exchangeRate / currencyExchangeRate.exchangeRate)).toFixed(4)
      otherCurrencyAmount[record.code] = parseFloat(convertedAmountOther)
    } else {
      otherCurrencyAmount[record.code] = parseFloat(amountMain)
    }
  }
  transactionObject = { ...transactionObject, otherCurrencyAmount: JSON.stringify(otherCurrencyAmount) }
  return transactionObject
}
