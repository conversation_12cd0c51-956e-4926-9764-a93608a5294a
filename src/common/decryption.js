import config from '../config/app'
const CryptoJS = require('crypto-js')

/**
 * This function is used for decryption.
 * @export
 * @param {Ciphertext} ciphertext
 * @return {Result} decrypted value
 */
export default async (ciphertext) => {
  const secret = config.getProperties().auth.encrytion_secret
  const bytes = CryptoJS.AES.decrypt(ciphertext, secret)
  const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8))

  return decryptedData
}
