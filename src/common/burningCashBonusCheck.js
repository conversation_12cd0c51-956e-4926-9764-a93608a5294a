import { sequelize } from '../db/models'

export const burningCashBonusAmountCheck = async (userBonusClaimHistoryData) => {
  const totalCashSportsBetAmount = await sequelize.query(
    'SELECT * FROM get_total_cash_sports_bet_amount(:user_id, :tenant_id, :start_date, :end_date);',
    {
      replacements: {
        user_id: userBonusClaimHistoryData.userId,
        tenant_id: userBonusClaimHistoryData.tenantId,
        start_date: userBonusClaimHistoryData.createdAt,
        end_date: new Date().toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  const totalCashCasinoBetAmount = await sequelize.query(
    'SELECT * FROM get_total_cash_casino_bet_amount(:tenant_id, :actionee_id, :start_date, :end_date);',
    {
      replacements: {
        tenant_id: userBonusClaimHistoryData.tenantId,
        actionee_id: userBonusClaimHistoryData.userId,
        start_date: userBonusClaimHistoryData.createdAt,
        end_date: new Date().toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  const sumAmount = (totalCashSportsBetAmount[0].get_total_cash_sports_bet_amount + totalCashCasinoBetAmount[0].get_total_cash_casino_bet_amount).toFixed(4)

  return (userBonusClaimHistoryData.bonusAmount - sumAmount).toFixed(4)
}
