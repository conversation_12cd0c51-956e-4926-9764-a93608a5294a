import { v4 as uuidv4 } from 'uuid'
import { BONUS_STATUS, BONUS_TYPES, PROMO_BONUS_WALLET_TYPES, TRANSACTION_TYPES } from '../common/constants'
import userCurrencyExchange from '../common/userCurrencyExchange'
import verifyPromoCode from '../common/verifyPromoCode'
import { walletLocking } from '../common/walletLocking'
import currencyConversionV3 from './currencyConversionV3'

/**
 * Used to add promo code bonus
 * @export
 * @param {object} user
 * @param {String} promoCode
 * @param {object} context
 * @returns
 */

export default async (user, promoCode, context) => {
  const {
    databaseConnection: {
      UserPromoCodeBonus: UserPromoCodeBonusModel,
      Wallet: WalletModel,
      Transaction: TransactionModel,
      TenantPromoCodes: TenantPromoCodesModel
    },
    tenant: Tenant,
    sequelizeTransaction
  } = context
  const verify = await verifyPromoCode(context, promoCode)
  if (!verify) return null

  const findPromoCode = await TenantPromoCodesModel.findOne({
    attributes: ['code','promoCodeBonusType', 'bonusAmount', 'walletType', 'id', 'totalUsage', 'totalNumberOfBonusTillNow','vipLevel'],
    where: {
      tenantId: Tenant.id,
      code: promoCode,
      status: true
    }
  })

  // calculate amount according to promo code bonus type
  let convertedAmount
  if (findPromoCode.promoCodeBonusType === 0) {
    // flat amount
    convertedAmount = findPromoCode.bonusAmount
  } else {
    // random amount
    convertedAmount = Math.floor(Math.random() * findPromoCode.bonusAmount) + 1
  }

  const walletType = findPromoCode.walletType === 1 ? PROMO_BONUS_WALLET_TYPES[1] : PROMO_BONUS_WALLET_TYPES[0]

  const userWallet = await walletLocking(context, user)
  await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
  userWallet[walletType] += convertedAmount
  await userWallet.save({ transaction: sequelizeTransaction })

  if (findPromoCode && findPromoCode?.code === promoCode) {
    user.vipLevel = findPromoCode.vipLevel;
    await user.save({ transaction: sequelizeTransaction })
  }

  // creating transaction object
  const conversionRate = await userCurrencyExchange(context, userWallet.currencyId)
  let transactionObject = {
    sourceWalletId: null,
    sourceCurrencyId: null,
    targetWalletId: userWallet.id,
    targetCurrencyId: userWallet.currencyId,
    amount: convertedAmount,
    conversionRate,
    targetBeforeBalance: +userWallet[walletType] - convertedAmount,
    targetAfterBalance: +userWallet[walletType],
    transactionId: uuidv4(),
    comments: 'Promo Code Bonus Credited',
    actioneeId: user.id,
    actioneeType: 'User',
    tenantId: user.tenantId,
    timestamp: Date.now(),
    transactionType: TRANSACTION_TYPES.PROMO_CODE_BONUS_CLAIMED,
    //errorDescription: 'Completed Successfully',
    errorCode: 0,
    success: true,
    status: 'success'
  }

  transactionObject = await currencyConversionV3(context, transactionObject, userWallet, convertedAmount)

  // adding to transaction
  const transactionBanking = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction })

  // creating user promo code bonus object
  const userPromoCodeBonusObject = {
    status: BONUS_STATUS.CLAIMED,
    bonusAmount: convertedAmount,
    userId: user.id,
    promoCodeId: findPromoCode.id,
    kind: BONUS_TYPES.PROMO_CODE,
    tenantId: Tenant.id,
    claimedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    transactionId: transactionBanking.id
  }

  // adding data to user promo code bonus
  await UserPromoCodeBonusModel.create(userPromoCodeBonusObject, { transaction: sequelizeTransaction })

  // updating tenant promo code
  findPromoCode.totalUsage += 1
  findPromoCode.totalNumberOfBonusTillNow += convertedAmount
  await findPromoCode.save()
  return transactionBanking
}
