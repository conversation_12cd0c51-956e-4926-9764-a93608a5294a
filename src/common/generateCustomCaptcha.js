import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express';
import crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import svgCaptcha from 'svg-captcha';
import { CAPTCHA_VERIFICATION_STATUS, SVG_CAPTCHA_OPTIONS } from './constants';
import <PERSON>rror<PERSON>ogHelper from './errorLog';
import verifyRecaptcha from './verifyRecaptcha';
import config from '../config/app';
import { encrypt } from '../lib/encryption';
import translate from '../lib/languageTranslate';
import { head } from 'lodash';

/**
 * Generates a new captcha and stores it in the database
 * @param {Object} context - The request context containing database connections, tenant info, etc.
 * @returns {Promise<Object>} Object containing uuid and base64 encoded captcha image
 */
export async function generateCustomCaptcha(context) {
  const {
    databaseConnection: {
      CaptchaVerification: CaptchaVerificationModel,
      TenantThemeSetting: TenantThemeSettingModel
    },
    headers: { language },
    tenant: Tenant
  } = context;

  try {
    // Generate captcha
    const captcha = svgCaptcha.create(SVG_CAPTCHA_OPTIONS);
    const encryptionKey = crypto.createHash('sha256').update(config.get('captcha_verification')).digest(); // Must be 32 bytes
    const hashedCaptchaText = encrypt(captcha.text, encryptionKey);
    
    const newCaptcha = await CaptchaVerificationModel.create({
      captchaText: hashedCaptchaText,
      status: CAPTCHA_VERIFICATION_STATUS.UNVERIFIED,
    });

    return {
      uuid: newCaptcha.uuidToken,
      captcha: Buffer.from(captcha.data).toString('base64') // send base64 SVG image
    };

  } catch (error) {
    await ErrorLogHelper.logError(error, context, { tenantId: Tenant.id });

    // Re-throw known ApolloError, otherwise throw a generic internal error
    if (error instanceof ApolloError) {
      throw error;
    }

    throw new ApolloError(translate('INTERNAL_SERVER_ERROR', language), 500);
  }
}