/**
 * This function will help to check whether user KYC is done or not.
 * @export
 * @param {UserId} userId contains id of user
 * @param {object} context The argument object
 * @return {Result} boolean
 */
export default async (context, userId) => {
  const user = await context.databaseConnection.User.findOne({
    where: {
      id: userId,
      kycDone: false
    }
  })
  if (user) {
    return false
  }

  return true
}
