import * as jwt from 'jsonwebtoken'
import { BONUS_TYPES, QUEUE_WORKER_CONSTANT, SUBSCRIPTION_CHANNEL } from '../common/constants'
import config from '../config/app'
// yimport { updateUserLastLogin } from '../elastic-search'
import redisConnection from '../lib/redisConnection'

/**
 * This function will help to login user through mobile.
 * @export
 * @param {UserId} userId contains user ID
 * @param {object} context The argument object
 * @param {JoiningFlag} joiningFlag contains boolean value of joining flag
 * @return {token, user, resToken}
 */
export default async (context, userId, joiningFlag, args, bulkData, loginType = 0) => {
  const {
    TenantCredential: TenantCredentialModel,
    User: UserModel,
    UserLoginHistory: UserLoginHistoryModel,
    UserReferralCode: UserReferralCodeModel,
    UserPreferenceType: UserPreferenceTypeModel

  } = context.databaseConnection
  const { sequelizeTransaction } = context
  // Transaction is required if new user is created and commited yet
  const user = await UserModel.findOne({
    where: { id: userId },
    include: [
      {
        model: UserReferralCodeModel,
        where: { tenantId: context.tenant.id },
        attributes: ['referralCode'],
        required: false
      },
      {
        model: UserPreferenceTypeModel,
        where: { tenantId: context.tenant.id, preferenceType: 'otp' },
        attributes: ['value'],
        required: false,
        limit: 1
      }
    ],
    transaction: sequelizeTransaction
  })

  if (user?.UserPreferenceTypes && user?.UserPreferenceTypes.length > 0) {
    user.dataValues.otpPreference = user?.UserPreferenceTypes[0].value
  } else {
    user.dataValues.otpPreference = null
  }
  user.dataValues.referralCode = user?.UserReferralCode?.referralCode || ''

  const responseObj = {}

  const credentials = await TenantCredentialModel.findOne({
    attributes: ['value'],
    where: {
      key: 'APP_JWT_SECRET_KEY',
      tenantId: context.tenant.id
    },
    raw: true
  })

  const authConfig = config.getProperties().auth
  const resToken = await jwt.sign({ id: user.id }, credentials.value, { expiresIn: authConfig.res_token_expiry_time })

  user.signInCount += 1
  user.lastLoginDate = new Date()

  if (args.ip) {
    const loginHistoryCondition = {
      tenantId: context.tenant.id,
      userId: user.id,
      ip: args.ip,
      loginType
    }

    const userLoginInfo = await UserLoginHistoryModel.findOne({
      attributes: ['id', 'signInCount'],
      where: loginHistoryCondition
    })

    const userInfo = {
      userId: user.id,
      tenantId: context.tenant.id,
      ip: args.ip,
      network: args.network,
      version: args.version,
      deviceId: args.deviceId,
      deviceType: args.deviceType,
      deviceModel: args.deviceModel,
      data: {
        city: args.city,
        region: args.region,
        countryName: args.countryName
        // postal: args.postal,
        // latitude: args.latitude,
        // longitude: args.longitude
      },
      lastLoginDate: new Date(),
      signInCount: userLoginInfo ? userLoginInfo.signInCount + 1 : 1,
      loginType
    }

    userLoginInfo ? await UserLoginHistoryModel.update(userInfo, { where: loginHistoryCondition }) : await UserLoginHistoryModel.create(userInfo)
  }

  const token = await jwt.sign({ id: user.id }, authConfig.jwt_secret, {
    expiresIn: authConfig.expiry_time
  })

  if (joiningFlag === false) {
    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.BONUS,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [
        {
          userId: user.id,
          tenantId: context.tenant.id,
          bonusType: BONUS_TYPES.JOINING
        }
      ]
    }

    bulkData.push(queueLogObject)
  }

  const storedToken = await redisConnection.get(`user:${user.id}`)

  if (storedToken && (storedToken !== token)) {
    context.pubSub.publish(SUBSCRIPTION_CHANNEL.MULTIPLE_LOGIN_NOTIFICATION, { MultipleLoginNotification: { userId: user.id, multipleLogin: true, token: storedToken } })
    await redisConnection.del(`user:${user.id}`)
  }
  await redisConnection.set(`user:${user.id}`, token, { EX: authConfig.expiry_time })

  const skipUserHook = true
  await user.save({ transaction: sequelizeTransaction, skipUserHook })

  const userIds = []
  if (user) {
    userIds.push(user.id)
  }
  const queueLogObject = {
    type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
    status: QUEUE_WORKER_CONSTANT.READY,
    ids: userIds
  }

  bulkData.push(queueLogObject)

  responseObj.token = token
  responseObj.resToken = resToken
  responseObj.user = user.dataValues

  return responseObj
}
