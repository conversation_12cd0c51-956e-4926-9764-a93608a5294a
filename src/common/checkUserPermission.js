import { DEFAULT_MODULE_PERMISSIONS, ALLOWED_PERMISSIONS } from './constants'
import db from '../db/models'

export async function checkUserPermission( tenantId, userId, module, action) {
  try {

    if (!userId || !module || !action) {
      return false
    }

    if (!DEFAULT_MODULE_PERMISSIONS[module]) {
      return false
    }

    if (!DEFAULT_MODULE_PERMISSIONS[module].includes(action)) {
      return false
    }

    const tenantAllowedModules = await db.TenantThemeSetting.findOne({
      where: {
        tenantId: tenantId
      },
      attributes: ['allowedModules']
    })

    if (tenantAllowedModules && tenantAllowedModules.allowedModules) {
      const allowedModulesArray = tenantAllowedModules.allowedModules.split(',').map(m => m.trim())
      if (!allowedModulesArray.includes(ALLOWED_PERMISSIONS.USER_SPECIFIC_PERMISSIONS)) { 
        // If tenant have not enabled user specific permissions
        return true
      }
    }

    const userPermissions = await db.UserPermissions.findOne({
      where: {
        userId,
        tenantId
      }
    })

    if (!userPermissions || !userPermissions.blockedPermissions) {
      return true
    }

    const blockedPermissions = userPermissions.blockedPermissions || {}
    const blockedActions = blockedPermissions[module] || []

    return !blockedActions.includes(action)

  } catch (error) {
    return false
  }
}

export default checkUserPermission