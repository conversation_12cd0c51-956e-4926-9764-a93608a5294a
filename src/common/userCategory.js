import { sequelize } from '../db/models'

export default async (userId, tenantId, walletId) => {
  const sql = `
    SELECT
     CASE
      WHEN first_deposit_date IS NULL THEN 'D'
      WHEN DATE_PART('day', NOW() - u.created_at) <= 90 THEN 'C'
      WHEN DATE_PART('day', NOW() - u.created_at) <= 180 AND DATE_PART('day', NOW() - u.created_at) > 90 THEN 'B'
      ELSE 'A'
      END AS category
      FROM users u
      LEFT JOIN (
      SELECT
      created_at AS first_deposit_date
      FROM user_first_deposit
      WHERE user_id = ${userId}
      AND tenant_id = ${tenantId}
      ) AS t ON 1=1
      WHERE
      u.id = ${userId}
      AND u.tenant_id = ${tenantId};
  `

  const [result] = await sequelize.query(sql, {
    replacements: { tenantId: tenantId, userId: userId },
    type: sequelize.QueryTypes.SELECT,
    useMaster: false
  })

  return result ? result.category : null
}
