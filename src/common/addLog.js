import db from '../db/models'
/**
 * This function will help to add logs.
 * @export
 * @param {*} req object contains all the request params sent from the client
 * @param {Service} service contains service
 * @return {Result} logId
 */
export default async (req, service) => {
  const data = { header:req.headers, body:req.body }
  const reqLogObject = {
    requestJson: data,
    service: service,
    url: req.route.path,
    tenantId: req.tenant.dataValues.id
  }
  const createLog = await db.RequestResponseLog.create(reqLogObject)

  return createLog.id
}
