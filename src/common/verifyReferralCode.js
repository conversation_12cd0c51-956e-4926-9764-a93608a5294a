import { Sequelize } from 'sequelize'
import { AUDIT_LOG_ACTIONEE_TYPE, EVENT, EVENT_TYPE, REFERRAL_BLOCK_TYPE, REFERRAL_EVENT, REFERRAL_STATUS } from "./constants"

export default async (context, userId, args) => {
  const {
    databaseConnection: {
      User: UserModel,
      Referral: ReferralModel,
      ReferralSettings: ReferralSettingsModel,
      AuditLog: AuditLogModel,
      UserReferralCode: UserReferralCodeModel
    },
    tenant: Tenant,
    sequelizeTransaction
  } = context
  const refereeCode = args.referralCode

  try {
    const referralSetting = await ReferralSettingsModel.findOne({
      where: { tenantId: Tenant.id, active: true },
      attributes: ['id', 'bonusType', 'walletType', 'event']
    })

    if (!referralSetting) {
      return { status: false, message: 'Referral bonus is not enabled' }
    }

    const referralUser = await UserReferralCodeModel.findOne({
      where: { referralCode: refereeCode, tenantId: Tenant.id },
      attributes: [
        'id',
        'userId',
        [Sequelize.literal(`(
          SELECT COUNT(*) > 0
          FROM "blocked_referral_users" AS "bru"
          WHERE "bru"."tenant_id" = ${Tenant.id}
          AND "bru"."block_type" = ${REFERRAL_BLOCK_TYPE.USER}
          AND "bru"."block_id" = "UserReferralCode"."user_id"
        )`), 'isUserBlocked'],

        [Sequelize.literal(`(
          SELECT COUNT(*) > 0
          FROM "blocked_referral_users" AS "bru"
          JOIN "users" AS "u" ON "u"."id" = "UserReferralCode"."user_id"
          WHERE "bru"."tenant_id" = ${Tenant.id}
          AND "bru"."block_type" = ${REFERRAL_BLOCK_TYPE.AGENT}
          AND "bru"."block_id" = "u"."parent_id"
        )`), 'isAgentBlocked']
      ],
      include: [
        {
          model: UserModel,
          attributes: ['parentId']
        }
      ],
      raw: true,
      transaction: sequelizeTransaction, // Ensures consistency within the transaction
    });

    // Determine error type (Invalid Referral Code or Blocked User)
    let rejectionReason = null;
    if (!referralUser) {
      rejectionReason = 'Invalid referral code';
    } else if (referralUser?.isUserBlocked || referralUser.isAgentBlocked) {
      rejectionReason = `Referral request denied. The ${referralUser?.isUserBlocked ? 'user' : 'agent'} is blocked.`;
    }
    // If an error exists, log the audit entry and return
    if (rejectionReason) {
      const auditLogData = {
        tenantId: Tenant.id,
        actioneeId: userId,
        eventType: EVENT_TYPE.player,
        event: EVENT.create,
        eventId: userId,
        actioneeIp: args?.ip || '',
        description: 'Referral Code Verification Error',
        action: 'Referral Code Verification',
        previousData: { id: userId, verificationStatus: 'pending', status: 'pending', code: refereeCode },
        modifiedData: { id: userId, verificationStatus: 'rejected', reason: rejectionReason, status: 'rejected', code: refereeCode },
        actioneeType: AUDIT_LOG_ACTIONEE_TYPE.USER
      };

      await AuditLogModel.create(auditLogData);

      return { status: false };
    }

    const addReferralBonus = await ReferralModel.create(
      {
        referrerId: referralUser.userId,
        refereeId: userId,
        code: refereeCode,
        tenantId: Tenant.id,
        event: referralSetting.event,
        status: referralSetting.event === REFERRAL_EVENT.SIGNUP ? REFERRAL_STATUS.IN_PROGRESS : REFERRAL_STATUS.PENDING,
        bonusType: referralSetting.bonusType,
        walletType: referralSetting.walletType,
      },
      {
        transaction: sequelizeTransaction,
        returning: true,
        raw: true
      }
    )

    return { status: true, message: 'Referral bonus added', referral: addReferralBonus , referralSettingsId: referralSetting.id, event: referralSetting.event}
  } catch (error) {
    console.log('Error while adding referral bonus', error)
    return { status: false, message: 'An error occurred while adding referral bonus'}
  }
}
