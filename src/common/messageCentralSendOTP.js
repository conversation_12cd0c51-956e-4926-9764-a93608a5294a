import { DEFAULT_OTP } from '../common/constants'
import config from '../config/app'
import keyValueTo<PERSON><PERSON> from '../lib/keyValueToJSON'
import { GenerateAuthToken } from '../lib/messageCentral/generateToken'
import { SendOtp } from '../lib/messageCentral/sendOtp'

export async function messageCentralSendOTP (context, phone, phoneCode = '91', userId = 'default', type = 'default') {
  const {
    databaseConnection: {
      UserToken: UserTokenModel,
      TenantCredential: TenantCredentialModel
    },
    tenant: { id: tenantId }
  } = context

  try {
    let otp
    const environment = config.get('env')
    if (environment === 'production') {
      const messageCentralConfig = await keyValueToJson(
        TenantCredentialModel,
        [
          'APP_MESSAGECENTRALSMS_CUSTOMER_ID',
          'APP_MESSAGECENTRALSMS_ENCRYPTED_KEY',
          'APP_MESSAGECENTRALSMS_BASE_URL'
        ],
        'tenantId', context.tenant.id
      )
      const authToken = await GenerateAuthToken(messageCentralConfig)
      if (!authToken) return false
      const sendOtpResponse = await SendOtp(messageCentralConfig, phone, phoneCode, authToken)
      if (!sendOtpResponse || (sendOtpResponse?.responseCode !== 200 && sendOtpResponse?.data?.responseCode !== 200)) return false
      otp = sendOtpResponse?.data?.verificationId
    } else {
      otp = DEFAULT_OTP
    }

    const tokenType = type === 'signup' ? 'message_central_signup' : 'message_central'
    const tokenData = {
      tokenType,
      token: otp,
      phone,
      phoneCode,
      tenantId
    }
    if (type !== 'signup') {
      tokenData.userId = userId
    }
    await UserTokenModel.create(tokenData)
    return true
  } catch (error) {
    throw error
  }
}
