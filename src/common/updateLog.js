import { RESPONSE_STATUS } from '../common/constants'
import db from '../db/models'
/**
 * This function will help to update logs
 * @export
 * @param {Id} id contains log Id
 * @param {Response} response contains response object
 * @param {ResponseCode} responseCode contains response code
 */
export default async ({id, response, responseCode, isSuccess = false}) => {
  let responseStatus
  if (isSuccess) {
    responseStatus = RESPONSE_STATUS.SUCCESS
  } else {
    responseStatus = RESPONSE_STATUS.FAILED
  }
  await db.RequestResponseLog.update({
    responseJson: response,
    responseStatus: responseStatus,
    responseCode: responseCode,
    errorCode: response.errorCode
  },
    {
      where: { id: id }
    }
  )
}
