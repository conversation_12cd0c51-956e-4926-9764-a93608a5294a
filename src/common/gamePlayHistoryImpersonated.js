import { Sequelize } from 'sequelize'
import <PERSON>rrorLogHelper from './errorLog'

export default async (GamePlayHistoryModel, CasinoItemModel, data) => {
  try {
    // last 3 hours any history exist
    const gameData = await GamePlayHistoryModel.count({
      where: {
        userId: +data?.userId,
        tenantId: +data?.tenantId,
        provider: data?.provider,
        ...(data?.gameId && { gameId: data?.gameId }),
        updatedAt: {
          [Sequelize.Op.gte]: new Date(new Date() - 3 * 60 * 60 * 1000)
        }
      }
    })


    return gameData ? false : true
  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
    return false
  }
}
