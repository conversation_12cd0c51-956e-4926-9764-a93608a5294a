import redisConnection from '../lib/redisConnection'

export const MultipleLoginAttempts = () => {
  const initialAttemptValue = 1
  const createLoginAttemptIndex = (id) => {
    return `userLoginAttempts:${id}`
  }

  const addLoginAttempt = async (id, value, expiryTime) => {
    try {
      const key = createLoginAttemptIndex(id)
      await redisConnection.set(
        key,
        value,
        {
          EX: expiryTime
        }
      )
    } catch (e) {
      throw new Error(e)
    }
  }

  const updateLoginAttempt = async (id) => {
    try {
      const key = createLoginAttemptIndex(id)
      return await redisConnection.incr(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const clearLoginAttempt = async (id) => {
    try {
      const key = createLoginAttemptIndex(id)
      await redisConnection.del(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const getLoginAttempt = async (id) => {
    try {
      const key = createLoginAttemptIndex(id)
      return await redisConnection.get(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const getRemainigTime = async (id) => {
    try {
      const key = createLoginAttemptIndex(id)
      return await redisConnection.ttl(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const attempt = async (id, maxAttpemts, expiryTime) => {
    const response = {}
    const attempt = await getLoginAttempt(id)
    const expiryTimeInSeconds = expiryTime * 60
    if (attempt === null) {
      await addLoginAttempt(id, initialAttemptValue, expiryTimeInSeconds)
      response.message = `Your password is wrong, You have ${maxAttpemts - initialAttemptValue} attempt remaining`
      response.attempts = initialAttemptValue
    } else {
      if (attempt === maxAttpemts) {
        response.message = `Please wait ${expiryTime} mins to login again`
        response.attempts = attempt
      } else {
        const updatedAttempt = await updateLoginAttempt(id)
        response.attempts = updatedAttempt
        response.message = maxAttpemts - updatedAttempt > 0
          ? `Your password is wrong, You have ${maxAttpemts - updatedAttempt} attempt remaining`
          : `Please wait ${expiryTime} mins to login again`
      }
    }

    return response
  }

  return {
    attempt,
    getLoginAttempt,
    clearLoginAttempt,
    getRemainigTime
  }
}
