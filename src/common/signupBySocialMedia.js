import { v4 as uuid, v4 as uuidv4 } from 'uuid'
import { checkBlackList } from '../common/checkBlackList'
import { AFFILIATE, ALANBASE_EVENT_TYPES, ALLOWED_PERMISSIONS, BONUS_TYPES, DEFAULT_AVATAR_IMAGE, ERORR_TYPE, QUEUE_WORKER_CONSTANT, REFERRAL_EVENT, SMARTIGO_TENANTS, SUBSCRIPTION_CHANNEL,
  USER_CREATION_TYPE, USER_LOGIN_TYPES
 } from '../common/constants'
import { smsEmailUponSignup } from '../common/smsEmailUponSignup'
import verifyReferralCode from '../common/verifyReferralCode'
import config from '../config/app'
import { s3 } from '../lib/aws-s3.config'
import translate from '../lib/languageTranslate'
import createUniqueUserName from './createUniqueUserName'
import Error<PERSON><PERSON><PERSON><PERSON>per from './errorLog'
import mobileLogin from './mobileLogin'
import verifyPromoCode from './verifyPromoCode'

const axios = require('axios')


const signupBySocialMedia = async (context, args, userData, loginType) => {
  try {
    const {
      databaseConnection: {
        User: UserModel,
        AdminUser,
        Wallet: WalletModel,
        AdminRole: AdminRoleModel,
        TenantThemeSetting: TenantThemeSettingModel,
        QueueLog: QueueLogModel,
        PlayerCategory: PlayerCategoryModel,
        PlayerCategoryLevel: PlayerCategoryLevelModel,
        Blacklist: BlacklistModel,
        AdminUsersAdminRole: AdminUsersAdminRoleModel,
        SocialMediaLogin: SocialMediaLoginModel,
        UserReferralCode: UserReferralCodeModel,
        UsersAffiliate: UsersAffiliateModel
      },
      tenant: { id: tenantId },
      req: { headers: { language } },
      sequelizeTransaction
    } = context

    const { ip, currencyId } = args
    let parent = await AdminUser.findAll({
      attributes: ['id', 'kycRegulated'],
      where: {
        tenantId
      },
      order: ['createdAt'],
      include: {
        model: AdminRoleModel,
        attributes: [],
        where: {
          name: 'owner'
        }
      },
      raw: true,
      nest: true
    })

    if (parent.length === 0) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('ADMIN_NOT_FOUND', language) }
    }
    parent = parent[0]

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId
      },
      attributes: ['allowedModules', 'userLoginType', 'smsGateway', 'emailGateway']
    })

    const checkBlackListUser = await checkBlackList('', '', userData?.email, '', tenantId, BlacklistModel, ip)
    if (checkBlackListUser) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('BLACK_LISTED', language) }
    }

    let creationType = USER_CREATION_TYPE.SIGNUP;
    if (loginType === USER_LOGIN_TYPES.GOOGLE) {
      creationType = USER_CREATION_TYPE.GOOGLE
    } else if (loginType === USER_LOGIN_TYPES.GOOGLE) {
      creationType = USER_CREATION_TYPE.FACEBOOK
    }

    // creating unique user name
    const userName = await createUniqueUserName(userData?.email, UserModel, tenantId)

    const user = {
      firstName: userData?.firstName,
      lastName: userData?.lastName,
      emailVerified: true,
      email: userData?.email,
      parentId: parent?.id,
      parentType: 'AdminUser',
      kycDone: !parent?.kycRegulated,
      userName,
      tenantId,
      creationType
    }

    const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
    if (profileVerifiedPermissionCheck) {
      user.profileVerified = false
    }

    const hasPlayerCategory = tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PLAYER_CATEGORIZATION)
    let subcategoryLevelData = null
    if (hasPlayerCategory) {
      subcategoryLevelData = await PlayerCategoryLevelModel.findOne({
        include: [{
          model: PlayerCategoryModel,
          where: {
            status: true,
            tenantId
          },
          attributes: []
        }],
        order: [[PlayerCategoryModel, 'id', 'ASC'], ['id', 'ASC']],
        attributes: ['id']
      })
      // If no category or subcategory is found, subcategoryLevelData will be null
      user.playerCategoryLevel = subcategoryLevelData ? subcategoryLevelData?.id : null
    }

    const newUser = await UserModel.create(user, { transaction: sequelizeTransaction })
    await WalletModel.create({ ownerId: newUser.dataValues.id, ownerType: 'User', amount: '0', currencyId }, { transaction: sequelizeTransaction })
    await SocialMediaLoginModel.create({ userId: newUser.dataValues.id, tenantId, socialMediaUserId: userData.socialMediaUserId, loginType, signInCount: 1 }, { transaction: sequelizeTransaction })
    let referralCode = (userName).toUpperCase().substring(0, 4) + uuidv4().replace(/-/g, '').substring(0, 4).toUpperCase()
    // check if the referral code is already in use by another user
    const checkReferralCode = await UserReferralCodeModel.findOne({
      where: {
        referralCode,
        tenantId
       },
      attributes: ['id'],
    })
    if (checkReferralCode?.id) {
      referralCode = (userName).toUpperCase().substring(0, 4) + uuidv4().replace(/-/g, '').substring(0, 6).toUpperCase()
    }

    await UserReferralCodeModel.create({ userId: newUser.id, tenantId, referralCode }, { transaction: sequelizeTransaction })
    const bulkData = []
    if (args.promoCode) {
      const isValidPromo = await verifyPromoCode(context, args.promoCode, currencyId)

      if (!isValidPromo) {
        const errThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_PROMOCODE', language) }
        throw errThrow
      }

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.BONUS,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [{
          bonusType: BONUS_TYPES.PROMO_CODE,
          userId: newUser.id,
          promoCode: args.promoCode,
          tenantId
        }]
      }
      bulkData.push(queueLogObject)
    }


    const joiningFlag = !!args.promoCode // If both the joining bonus and promo bonus are available, only the promo bonus will be awarded to the user.
    const responseObj = await mobileLogin(context, newUser.id, joiningFlag, args, bulkData, loginType)
    let token = responseObj?.token
    let resToken = responseObj?.resToken
    responseObj.user.referralCode = referralCode

    // smartigo code
    const smartigoTenants = config.get('env') === 'production' ? SMARTIGO_TENANTS.PROD : SMARTIGO_TENANTS.STAGE
    if (smartigoTenants && smartigoTenants.includes(tenantId)) {
      const smartiGoObject = {
        type: QUEUE_WORKER_CONSTANT.SMARTICO_USER,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [newUser.id],
        tenantId: parseInt(tenantId)
      }
      bulkData.push(smartiGoObject)
    }

    // alanbase entry
    const hasAlanBase = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ALANBASE)
    if (hasAlanBase && args?.alanbaseClickId) {
      await UsersAffiliateModel.create({ userId: newUser.id, clickId: args?.alanbaseClickId, tenantId, affiliate: AFFILIATE.ALANBASE }, { transaction: sequelizeTransaction })
      const alanbaseObject = {
        type: ALANBASE_EVENT_TYPES.REGISTRATION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [newUser.id],
        tenantId: parseInt(tenantId)
      }
      bulkData.push(alanbaseObject)
    }

    // wynta entry
    const hasWynta = tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.WYNTA)
    if (hasWynta && args?.wyntaClickId) {
      await UsersAffiliateModel.create({ userId: newUser.id, clickId: args?.wyntaClickId, tenantId, affiliate: AFFILIATE.WYNTA }, { transaction: sequelizeTransaction })
    }

    if (args.referralCode && tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REFERRAL_CODE)) {
      const referralDetails = await verifyReferralCode(context, newUser.id, args)

      if (!referralDetails.status) {
        const errThrow = { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('INVALID_REFERRAL_CODE', language) }
        throw errThrow
      }

      if (referralDetails.status &&  referralDetails?.event == REFERRAL_EVENT.SIGNUP ) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.BONUS,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{
            bonusType: BONUS_TYPES.REFERRAL_CODE,
            referralId: referralDetails.referral.id,
            referralSettingsId: referralDetails.referralSettingsId
          }]
        }
        bulkData.push(queueLogObject)
      }
    }
    const createdRecords = await QueueLogModel.bulkCreate(bulkData, { transaction: sequelizeTransaction })

    // uploading image in s3
    let avatarImage
    try {
      const s3Config = config.getProperties().s3
      const key = `tenants/${tenantId}/user/${newUser.id}/${uuid()}.social_media_signup`
      const response = await axios({
        url: userData?.avatarImage,
        method: 'GET',
        responseType: 'arraybuffer',
      })
      await s3.upload({
        Bucket: s3Config.bucket,
        Key: key,
        Body: Buffer.from(response.data, 'binary'),
        ACL: 'public-read'
      }).promise()
      avatarImage = key
    } catch (error) {
      avatarImage = DEFAULT_AVATAR_IMAGE
    }
    await UserModel.update(
      { avatarImage },
      { where: { id: newUser.id }, transaction: sequelizeTransaction }
    )

    if (createdRecords) {
      try {
        createdRecords.forEach(record => {
          context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
            QueueLog: { queueLogId: record.id }
          })
        })
      } catch (error) {
        await ErrorLogHelper.logError(error, context, { tenantId })
      }
    }

    const isFinancialActivityEnabledPermissionCheck = tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.DISABLEAGENTUSER) ? true : false
    responseObj.user.isFinancialActivityEnabled = true
    if (isFinancialActivityEnabledPermissionCheck && responseObj.user.parentId) {
      const adminUserRole = await AdminUsersAdminRoleModel.findOne(
        {
          where: {
            adminUserId: responseObj.user.parentId
          },
          attributes: ['adminRoleId'],
          raw: true
        }
      )
      responseObj.user.isFinancialActivityEnabled = adminUserRole?.adminRoleId === '1' ? true : false  // 1 for admin role
    }

    if (!responseObj.user.profileVerified) {
      token = null
      resToken = null
    }

    smsEmailUponSignup(tenantTheme, newUser, context) // here no need for waiting for sending thank you message
    return { token, user: responseObj?.user, resToken, success: true }
  } catch (error) {
    throw error
  }
}

export { signupBySocialMedia }
