import { Sequelize } from 'sequelize'
import <PERSON><PERSON>r<PERSON>ogHelper from './errorLog'

export default async (GamePlayHistoryModel, CasinoItemModel, data) => {
  try {
    const casinoItemId = await CasinoItemModel.findOne({
      attributes: ['id'],
      where: {
        tenantId: +data?.tenantId,
        uuid: data?.gameId,
        provider: {
          [Sequelize.Op.eq]: Sequelize.literal(`(
        SELECT id::varchar
        FROM "casino_providers"
        WHERE name = '${data?.provider}'
      )`)
        }
      }
    })
    const [entry, created] = await GamePlayHistoryModel.findOrCreate({
      where: {
        userId: +data?.userId,
        tenantId: +data?.tenantId,
        deviceType: +data?.deviceType,
        provider: data?.provider,
        ...(data?.gameId && { gameId: data?.gameId }),
        ...(casinoItemId?.id && { casinoItemId: casinoItemId.id })
      }
    })
    if (!created) {
      entry.count += 1
      await entry.save()
    }
    return true
  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
    return false
  }
}
