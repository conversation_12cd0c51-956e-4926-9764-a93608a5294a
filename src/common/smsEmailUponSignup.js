import { ALLOWED_PERMISSIONS, CURRENCY_CODE, LOGIN_TYPE, PHONE_CODE, SMS_GATEWAY, TENANT_WELCOME_MESSAGES, THANK_YOU_MESSAGE, UNABLE_TO_SEND_THANK_YOU_MESSAGE } from '../common/constants'
import <PERSON><PERSON>r<PERSON><PERSON><PERSON>elper from '../common/errorLog'
import config from '../config/app'
import { sendFast2SMSPhoneVerificationOTP } from '../lib/fast2sms'
import { sendKarixSMSPhoneVerificationOTP } from '../lib/karixSMS/sendOtp'
import { sendOtpThroughSmtpRelay } from '../lib/sendgridService'
import { sendPhoneVerificationOTP } from '../lib/twilioServices'
import { sendZMessengerSMSPhoneVerificationOTP } from '../lib/zMessengerSms/sendOtp'

const smsEmailUponSignup = async (tenantTheme, user, context) => {
  try {
    if (tenantTheme?.allowedModules && !tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SMS_EMAIL_UPON_SIGNUP)) return

    const tenantId = context?.tenant?.id;
    const message = TENANT_WELCOME_MESSAGES[tenantId]?.message || THANK_YOU_MESSAGE(user?.userName);

    const environment = config.get('env')
    let result
    if (environment === 'production' && (tenantTheme?.userLoginType === LOGIN_TYPE.MOBILE || tenantTheme?.userLoginType === LOGIN_TYPE.BOTH) && user?.phone && user?.phoneCode) {
      if (tenantTheme?.smsGateway === SMS_GATEWAY.FAST2_SMS) {
        if (tenantTheme?.allowedModules && tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.SECONDARY_SMS_GATEWAY) &&
          (user['Wallet.Currency.code'] === CURRENCY_CODE.LKR)) {
          result = await sendKarixSMSPhoneVerificationOTP(user?.phone, user?.phoneCode, message, context, true)
        } else {
          result = await sendFast2SMSPhoneVerificationOTP(user?.phone, user?.phoneCode, message, context)
        }
      } else if (tenantTheme?.smsGateway === SMS_GATEWAY.ZMESSENGER_SMS) {
        if (user?.phoneCode === PHONE_CODE.SRILANKA) { result = await sendZMessengerSMSPhoneVerificationOTP(user?.firstName || user?.userName, user?.phone, message, context, true) }
      } else {
        result = await sendPhoneVerificationOTP(user?.phone, user?.phoneCode, message, context)
      }
      if (!result?.success) {
        await ErrorLogHelper.logError({ message: UNABLE_TO_SEND_THANK_YOU_MESSAGE }, context, user)
      }
    }

    if (environment === 'production' && (tenantTheme?.userLoginType === LOGIN_TYPE.EMAIL || tenantTheme?.userLoginType === LOGIN_TYPE.BOTH)) {
      await sendOtpThroughSmtpRelay(user?.userName, user?.email, context, 'registrationSms', tenantTheme?.emailGateway)
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, context, user)
  }
}

export { smsEmailUponSignup }
