// import express from 'express'
import Axios from 'axios'
import config from '../config/app'
/**
 * This function will help to verify the token.
 * @export
 * @param {token} token contains token of recaptcha
 * @param {object} context The argument object
 * @return {Result} boolean
 */
export default async (context, token) => {
  // Set your Google reCAPTCHA secret key

  const TenantThemeSettingModel = context.databaseConnection.TenantThemeSetting
  const providers = await TenantThemeSettingModel.findOne({
    attributes: ['googleRecaptchaKeys'],
    where: {
      tenantId: context.tenant.id
    }
  })
  const secretKey = providers?.googleRecaptchaKeys
  if (secretKey) {
    const googleRecaptchaUrl = config.getProperties().google_recaptcha_url
    const data = `secret=${secretKey.secret_key}&response=${token}`

    var configs = {
      method: 'POST',
      url: googleRecaptchaUrl,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: data
    }

    const response = await Axios.request(configs)
      .then((response) => {
      // Check if the token is valid
        if (response.data.success) {
          // The token is valid, so process the user's form submission
          return true
        } else {
        // The token is invalid, so return an error
          return false
        }
      })
      .catch((error) => {
        throw new Error('Errors:' + error)
      })

    return response
  } else {
    return true
  }
}
