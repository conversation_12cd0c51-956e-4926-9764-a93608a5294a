import { TABLES } from '../common/constants'
export const walletLocking = async (context, user, skipLocked = false) => {
  const {
    databaseConnection: {
      Wallet: WalletModel
    },
    sequelizeTransaction
  } = context

  const walletDetails = await WalletModel.findOne({
    where: { ownerId: user.id, ownerType: TABLES.USER },
    transaction: sequelizeTransaction,
    lock: {
      level: sequelizeTransaction.LOCK.UPDATE,
      of: WalletModel
    },
    skipLocked
  })
  return walletDetails
}
