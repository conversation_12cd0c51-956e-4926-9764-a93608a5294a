import { decode } from 'jsonwebtoken'
import { SUBSCRIPTION_CHANNEL } from '../common/constants'

/**
 * This function will help to send notification when user token is expire.
 * @export
 * @param {UserToken} userToken contains token of user
 * @param {object} context The argument object
 * @return {Result} boolean
 */
export default async (context, userToken) => {
  try {
    const user = decode(userToken)
    !!user?.id && context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_TOKEN_EXPIRATION_NOTIFICATION, { UserTokenExpirationNotification: { userId: user.id, message: 'User Token Expired' } })
  } catch (error) {

  }
}
