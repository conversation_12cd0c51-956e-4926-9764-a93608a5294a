import { Op } from 'sequelize'
import { sequelize } from '../db/models'
const checkBlackList = async (phoneNumber, phoneCode, email, userName, tenantId, BlacklistModel, ip) => {
  phoneCode = (phoneCode && !isNaN(parseInt(phoneCode))) ? parseInt(phoneCode) : 0
  const results = await BlacklistModel.findAll({
    where: {
      tenantId: tenantId,
      status: true,
      [Op.or]: [
        {
          type: 1,
          value: phoneNumber,
          phoneCode: phoneCode
        },
        {
          type: 2,
          value: email
        },
        {
          type: 2,
          value: userName
        },
        {
          type: 3,
          [Op.and]: sequelize.literal(`LOWER('${email}') LIKE CONCAT('%', LOWER(value), '%')`)
        },
        ...(ip ? [{
          type: 4,
          value: ip
        }] : [])
      ]
    }
  })
  if (results.length) {
    return true
  }

  return false
}

export { checkBlackList }
