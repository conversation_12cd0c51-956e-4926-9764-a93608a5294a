import redisConnection from '../lib/redisConnection'
import { OTP_ATTEMPTS_TYPES } from './constants'

export const MultipleOtpAttempts = () => {
  const initialAttemptValue = 1
  const createOtpAttemptIndex = (id, type) => {
    return type === OTP_ATTEMPTS_TYPES.VERIFY_OTP ? `userOtpVerifyAttempts:${id}` : `userOtpGenerateAttempts:${id}`
  }

  const addOtpAttempt = async (id, value, expiryTime, type) => {
    try {
      const key = createOtpAttemptIndex(id, type)
      await redisConnection.set(
        key,
        value,
        {
          EX: expiryTime
        }
      )
    } catch (e) {
      throw new Error(e)
    }
  }

  const updateOtpAttempt = async (id, type) => {
    try {
      const key = createOtpAttemptIndex(id, type)
      return await redisConnection.incr(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const clearOtpAttempt = async (id, type) => {
    try {
      const key = createOtpAttemptIndex(id, type)
      await redisConnection.del(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const getOtpAttempt = async (id, type) => {
    try {
      const key = createOtpAttemptIndex(id, type)
      return await redisConnection.get(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const getRemainigTime = async (id, type) => {
    try {
      const key = createOtpAttemptIndex(id, type)
      return await redisConnection.ttl(key)
    } catch (e) {
      throw new Error(e)
    }
  }

  const otpAttempt = async (id, maxAttpemts, expiryTime, type) => {

    const response = {}
    const attempt = await getOtpAttempt(id, type)

    const expiryTimeInSeconds = expiryTime * 60

    if (attempt === null) {
      await addOtpAttempt(id, initialAttemptValue, expiryTimeInSeconds, type)
      if (type = OTP_ATTEMPTS_TYPES.VERIFY_OTP) {
        response.message = `Your OTP is wrong, You have ${maxAttpemts - initialAttemptValue} attempt remaining`
      }
      else {
        response.message = ''
      }
      response.attempts = initialAttemptValue
    } else {
      if (attempt === maxAttpemts) {
        response.message = `Please wait ${expiryTime} mins to login again`
        response.attempts = attempt
      } else {
        const updatedAttempt = await updateOtpAttempt(id, type)
        response.attempts = updatedAttempt
        if (type = OTP_ATTEMPTS_TYPES.VERIFY_OTP){
          response.message = maxAttpemts - updatedAttempt > 0
          ? `Your OTP is wrong, You have ${maxAttpemts - updatedAttempt} attempt remaining`
          : `Please wait ${expiryTime} mins to login again`
        }
      }
    }

    return response
  }

  return {
    otpAttempt,
    getOtpAttempt,
    clearOtpAttempt,
    getRemainigTime
  }
}
