/**
 * This function will help to varify promo code.
 * @export
 * @param {PromoCode} promoCode contains promo code
 * @param {object} context The argument object
 * @return {Result} boolean
 */
import { Op } from 'sequelize'
export default async (context, promoCode, currencyId) => {
  const {
    TenantPromoCodes: TenantPromoCodesModel
  } = context.databaseConnection

  const tenantId = context.tenant.id
  const verifyCode = await TenantPromoCodesModel.findOne({
    attributes: ['id', 'validTill', 'validFrom'],
    where: {
      tenantId,
      code: promoCode,
      status: true,
      [Op.or]: [
        { currency_id: currencyId },
        { currency_id: null }
      ]
    }
  })

  if (!verifyCode) return false

  const currentTime = new Date().getTime()

  if ((currentTime > verifyCode.validTill.getTime()) || (currentTime < verifyCode.validFrom.getTime())) {
    return false
  }

  return true
}
