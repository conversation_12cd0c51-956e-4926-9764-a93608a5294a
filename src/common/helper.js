export function filterValidMultipliers(values) {
  return values
    .map(value => {
      // Only convert value to a number if it is not null or undefined
      if (value === null || value === undefined) return null;
      const numValue = Number(value);
      return !isNaN(numValue) ? numValue : null; // Keep valid numbers only
    })
    .filter(value => value !== null); // Filter out null values
}
