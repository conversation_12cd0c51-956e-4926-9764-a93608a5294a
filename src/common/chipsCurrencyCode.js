/**
 * This function will help to find chips currency code.
 * @export
 * @param {User} user contains user
 * @param {object} context The argument object
 * @return {Result} user
 */
export default async (context, user) => {
  const TenantCredentialModel = context.databaseConnection.TenantCredential
  let credentials = await TenantCredentialModel.findOne({
    where: {
      key: ['APP_CHIPS_CURRENCY_CODE'],
      tenantId: user.tenantId
    },
    raw: true
  })
  if (credentials ) user.Wallet.Currency.code = credentials.value
  return user
}
