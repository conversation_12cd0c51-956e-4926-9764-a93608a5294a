import config from '../config/app'
import keyValueToJson from '../lib/keyValueToJSON'
import { GenerateAuthToken } from '../lib/messageCentral/generateToken'
import { ValidateOtp } from '../lib/messageCentral/validateOtp'

export async function messageCentralVerifyOTP (context, otp, phone = 'default', phoneCode = '91', userId = 'default', type = 'default') {
  const {
    databaseConnection: {
      UserToken: UserTokenModel,
      TenantCredential: TenantCredentialModel
    },
    tenant: { id: tenantId }
  } = context

  try {
    const whereCondition = type !== 'signup'
      ? { userId, tokenType: 'message_central', tenantId }
      : { tokenType: 'message_central_signup', phone, phoneCode, tenantId }

    const verifyPhoneToken = await UserTokenModel.findOne({
      attributes: ['id', 'token', 'phone', 'phoneCode'],
      where: whereCondition,
      order: [['id', 'DESC']],
      limit: 1,
      raw: true
    })
    if (!verifyPhoneToken) return false

    const environment = config.get('env')
    const messageCentralConfig = environment === 'production' ? await keyValueToJson(
      TenantCredentialModel,
      [
        'APP_MESSAGECENTRALSMS_CUSTOMER_ID',
        'APP_MESSAGECENTRALSMS_ENCRYPTED_KEY',
        'APP_MESSAGECENTRALSMS_BASE_URL'
      ],
      'tenantId', context.tenant.id
    ) : null
    const authToken = environment === 'production' ? await GenerateAuthToken(messageCentralConfig) : null
    const verifyOtpResponse = authToken ? await ValidateOtp(messageCentralConfig, authToken, verifyPhoneToken, otp) : null

    if ((environment === 'production' && verifyOtpResponse?.data?.responseCode === '200') || (environment !== 'production' && verifyPhoneToken.token === otp)) {
      const deleteCondition = type !== 'signup' ? { userId, tokenType: 'message_central', tenantId } : { token: verifyPhoneToken.token, phone, phoneCode, tenantId }
      await UserTokenModel.destroy({ where: deleteCondition })
      return true
    }
    return false
  } catch (error) {
    throw error
  }
}
