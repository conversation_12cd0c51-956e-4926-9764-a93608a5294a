import { Op } from 'sequelize'

export default async (userId, tenantId, PlayerSummaryProviderWiseModel) => {
  try {
    const hasPlayerBetData = await PlayerSummaryProviderWiseModel.findOne({
      where: {
        tenantId: tenantId,
        userId: userId,
        type: 30,
        amount: { [Op.gt]: 0 }
      }
    })
    return Boolean(hasPlayerBetData)
  } catch (e) {
    throw new Error(e)
  }
}
