import { ApolloError, AuthenticationError, UserInputError } from 'apollo-server-express';
// import ErrorLogHelper from '../../common/errorLog';
import verifyRecaptcha from './verifyRecaptcha';
import { RATE_LIMIT_CONFIG } from './constants';
import {generateCustomCaptcha} from './generateCustomCaptcha';
// import VerifyCustomCaptcha from './verifyCustomCaptcha';

/**
 * Check rate limit and handle captcha verification for game launch
 * @param {Object} context - The context object containing request, database connections, tenant info
 * @param {Object} args - Arguments containing captchaText and uuid
 * @returns {Object} Result object with success status or captcha data
 */
export async function checkGameLaunchRateLimit(context, userId, args = {}) {
  const {
    databaseConnection: {
      TenantThemeSetting: TenantThemeSettingModel,
      GamePlayHistory: GamePlayHistoryModel,
    },
    headers: { language, grcptv3 },
    tenant: Tenant
  } = context;

  try {
    // Check if tenant has recaptcha enabled
    const providers = await TenantThemeSettingModel.findOne({
      attributes: ['googleRecaptchaKeys'],
      where: { tenantId: Tenant.id },
      raw: true
    });

    const hasRecaptchaEnabled = providers?.googleRecaptchaKeys;
    // const hasRecaptchaEnabled = null

    let isCustomCaptcha = false
    let captchaData = null;

    // Check current rate limit attempts
    const currentAttempts = await checkRateLimit(userId, Tenant.id, GamePlayHistoryModel);
    // If rate limit exceeded, handle captcha verification
    if (currentAttempts >= RATE_LIMIT_CONFIG.MAX_ATTEMPTS) {
      if (hasRecaptchaEnabled) {
        
        if (!grcptv3) {
          throw new AuthenticationError('CAPTCHA_VERIFICATION_REQUIRED');
        }
        
        const isRecaptchaValid = await verifyRecaptcha(context, grcptv3);
        
        if (!isRecaptchaValid) {
          throw new AuthenticationError('CAPTCHA_VERIFICATION_FAILED');
        }
      } else {
      
        // Custom captcha verification
        if (!args.uuid) {
          // Generate and return custom captcha
          // return await GenerateCaptcha.run(context, args);
           captchaData = await generateCustomCaptcha(context);
          isCustomCaptcha = true;
        }

      }
    }

    // Update game play history with new timestamp
    await updateGamePlayHistory(userId, Tenant.id, GamePlayHistoryModel);

    return {
      success: true,
      isCustomCaptcha,
      captchaData: isCustomCaptcha ? captchaData : null,
    };

  } catch (error) {
    if (error instanceof ApolloError) {
      throw error;
    }

    throw new ApolloError('Rate limit check failed', 'RATE_LIMIT_ERROR', {
      originalError: error.message
    });
  }
}

/**
 * Update game play history timestamps
 * @param {number} userId - User ID
 * @param {number} tenantId - Tenant ID
 * @param {Object} GamePlayHistoryModel - Sequelize model for GamePlayHistory
 */
async function updateGamePlayHistory(userId, tenantId, GamePlayHistoryModel) {
  if (!userId || !tenantId) {
    throw new Error('userId and tenantId are required');
  }

  try {
    const currentTimestamp = new Date();
    const whereCondition = { userId, tenantId, gameId: 'global-tracker' };

    const existingRecord = await GamePlayHistoryModel.findOne({
      attributes: ['id', 'launchTs_1', 'launchTs_2', 'launchTs_3', 'count'],
      where: whereCondition
    });

    if (existingRecord) {
      // Update existing record by shifting timestamps
      await GamePlayHistoryModel.update(
        {
          launchTs_3: existingRecord.launchTs_2,
          launchTs_2: existingRecord.launchTs_1,
          launchTs_1: currentTimestamp,
          updatedAt: currentTimestamp
        },
        { 
          where: { id: existingRecord.id } 
        }
      );
    } else {
      // Create new global-tracker record if it doesn't exist
      await GamePlayHistoryModel.create({
        userId: userId,
        tenantId: tenantId,
        deviceType : 1,
        provider : 'global-tracker',
        gameId: 'global-tracker',
        launchTs_1: currentTimestamp,
        launchTs_2: null, // Set to null for new records
        launchTs_3: null, // Set to null for new records
        createdAt: currentTimestamp,
        updatedAt: currentTimestamp
      });
    }
  } catch (error) {
    throw new Error(`Failed to update game play history: ${error.message}`);
  }
}

/**
 * Check rate limit based on timestamps
 * @param {number} userId - User ID
 * @param {number} tenantId - Tenant ID
 * @param {Object} GamePlayHistoryModel - Sequelize model for GamePlayHistory
 * @returns {number} Number of attempts within rate limit window
 */
async function checkRateLimit(userId, tenantId, GamePlayHistoryModel) {
  if (!userId || !tenantId) {
    return 0; // If no user/tenant, no attempts counted
  }

  try {
    const whereCondition = { userId, tenantId, gameId: 'global-tracker' };

    const record = await GamePlayHistoryModel.findOne({
      attributes: ['launchTs_1', 'launchTs_2', 'launchTs_3', 'count'],
      where: whereCondition
    });

    if (!record) {
      return 0; // No previous records means no attempts
    }

    const now = new Date();
    const RATE_LIMIT_WINDOW = 10; // 10 seconds window
    let attemptCount = 0;

    // Check each timestamp to see if it's within the rate limit window
    const timestamps = [record.launchTs_1, record.launchTs_2, record.launchTs_3];
    
    for (const timestamp of timestamps) {
      if (timestamp) {
        const timeDiff = (now - new Date(timestamp)) / 1000; // Convert to seconds
        if (timeDiff <= RATE_LIMIT_WINDOW) {
          attemptCount++;
        }
      }
    }

    return attemptCount;
  } catch (error) {
    throw new Error(`Rate limit check failed: ${error.message}`);
  }
}