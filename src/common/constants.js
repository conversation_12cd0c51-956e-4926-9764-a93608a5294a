export const MAIN_CONNECTION = 'MAIN_CONNECTION'
export const TENANT_CONNECTION = 'TENANT_CONNECTION'
export const JWT = 'jwt'
export const SALT_ROUNDS = 15
export const FAQS_LIMIT = 5

export const TABLES = {
  TENANT: 'Tenants',
  USER: 'User'
}

export const LOSING_BONUS_TYPES = {
  daily: 1,
  weekly: 7,
  biweekly: 14,
  monthly: 30
}

export const CANCEL_WITHDRAW_REQUEST = {
  TRANSACTION_STATUS: 'cancelled',
  TRANSACTION_COMMENTS: 'cancelled by the player'
}

export const QUEUE_WORKER_CONSTANT = {
  TRANSACTION_TYPE: 1,
  METHOD: 'post',
  MAX_BODY_LENGTH: Infinity,
  HEADERS: { 'Content-Type': 'application/json' },
  TYPE: 'casino_transaction',
  READY: 0,
  IN_PROGRESS: 1,
  DONE: 2,
  FAILED: 3,
  CASINO_TRANSACTION: 'casino_transaction',
  USER_TRANSACTION: 'user_transaction',
  MARINA888_USER_PASSWORD_CHANGE: 'marina888UserPasswordChange',
  BONUS: 'bonus',
  SMARTIGO_API_CALL: 'smarticoAPICall',
  SMARTICO_USER: 'smartico_user',
  SMARTICO_WITHDRAW_REQUESTED: 'smartico_withdrawal_requested',
  SMARTICO_WITHDRAW_CANCELLED: 'smartico_withdrawal_cancelled',
  LOGIN_STATS: 'smartico_login_stats',
  AUDIT_LOG: 'audit_log',
  DEP_WITHDRAW_SMS_OTP: 'dep_withdraw_sms_otp',
  OCR_TRANSACTION: 'ocr_transaction',
  PENDING_DEPOSIT_REQUEST: 'pending_deposit_request'
}

export const LOGIN_TYPE = {
  MOBILE: 1,
  EMAIL: 2,
  BOTH: 0
}

export const CURRENCY_TYPE = {
  CHIPS: 'chips'
}

export const PAGINATION_CONSTANT = {
  LIMIT: 8,
  OFFSET: 0
}

export const POPULAR_GAMES_PAGINATION_CONSTANT = {
  LIMIT: 10,
  OFFSET: 0
}

export const INDIGRIT_INTEGRATION_CONSTANT = {
  SUCCESS: 'Transaction successful',
  SUCCESS_CODE: 200,
  INVALID_AMOUNT: 'Amount is invalid',
  USER_NOT_FOUND: 'User not found',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  INVALID_DEPOSIT_REQUEST: 'No valid Deposit request found for this tracking Id',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  TRANSACTION_NOT_FOUND: 'Transaction does not found',
  VALIDATION_ERROR: 400,
  PAYMENT_FORM_OPENED: 'UPI payment instructions are displayed',
  PAYMENT_PROCESSING_STARTED: 'UPI payment has been performed',
  PAYMENT_COMPLETED: 'Deposit has been received',
  PAYMENT_FAILED: 'Deposit has failed',
  INVALID_REQUEST: 'No Transaction found for this txnId',
  INVALID_SIGNATURE: 'Invalid Signature',
  INVALID_PAYLOAD: 'Header payload and requests body are not same',
  PAYMENT_METHOD: 'UPI',
  INVALID_TOKEN: 'Invalid token',
  INDIGRIT_TRANSACTION_COMMENTS: 'Deposit Request'
}

export const SKY_PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  CONFIGURATION_KEYS_NOT_FOUND: 'Required payment configuration keys are missing',
  VALIDATION_ERROR: 400,
  INVALID_REQUEST: 'No Transaction found for this txnId',
  INVALID_HASH: 'Hash signature does not match',
  PAYMENT_METHOD: 'UPI',
  SKY_TRANSACTION_COMMENTS: 'Deposit Request',
  SUCCESS: 'Transaction',
  SUCCESS_CODE: 200,
  DEPOSIT_TYPE: 'payment_providers',
  MISSING_FIELD_REQUIRED: 'is required to make payment. Please update your profile',
  SUCCESS_STATUS_CODE: '0',
  FAILED_STATUS_CODE: '1',
  PENDING_STATUS_CODE: '2',
  EXCEPTION_STATUS_CODE: '3',
  CHARGEBACK_STATUS_CODE: '4',
  REFUNDED_STATUS_CODE: '5',
  RESPCODE_SUCCESS_CODE: '0',
  RESPCODE_FAILED_CODE: '1',
  RESPCODE_PENDING_CODE: '2',
  FAILED_ERROR_DESCRIPTION: 'Transaction Failed',
  FAILED_TXN_STATUS_STRING: 'failed',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  REQUEST_REJECTED: 'Request rejected by server',
  REQUEST_PENDING: 'Request pending by server',
  USER_NAME_VALIDATION_ERROR: 'Name is mandatory, please update your profile.'
}

export const WIZPAY_PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  VALIDATION_ERROR: 400,
  INVALID_REQUEST: 'No Transaction found for this txnId',
  APPROVED_STATUS: 'approved',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  FAILED_STATUS: 'failed',
  FAILED_ERROR_DESCRIPTION: 'Transaction Failed',
  FAILED_TXN_STATUS_STRING: 'failed',
  SUCCESS_CODE: 200,
  REQUEST_DATA_MODE: 'UPI',
  REQUEST_DATA_TYPE: 'payin',
  PENDING_ORDER_STATUS: 'pending',
  REJECTED_ORDER_STATUS: 'rejected',
  UNASSIGNED_ORDER_STATUS: 'unassigned',
  FAILED_ORDER_STATUS: 'failed',
  DEPOSIT_TYPE: 'payment_providers',
  INVALID_STATUS_CODE: 'Invalid Status Code',
  REQUEST_REJECTED: 'Request rejected',
  REQUEST_UNASSIGNED: 'Payment provider is inactive',
  REQUEST_FAILED: 'Request Failed',
  INTERNAL_ERROR: 'Internal error'
}

export const NEXA_PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  MISSING_FIELD_REQUIRED: 'is required to make payment. Please update your profile',
  INR_CURRENCY_CODE: 356,
  TXNTYPE: 'SALE',
  MOP_TYPE: 'UP',
  PAYMENT_TYPE: 'UP',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_CODE: '000',
  SUCCESS_DESCRIPTION: 'Payment request has been generated successfully. Please check your UPI address'
}

export const PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token'
}

export const SASPAY_PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  MISSING_FIELD_REQUIRED: 'is required to make payment. Please update your profile',
  SUCCESS_STATUS_CODE: 200,
  DEPOSIT_TYPE: 'payment_providers'
}

export const PAYWINGS_PG_INTEGRATION_CONSTANT = {
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  MISSING_FIELD_REQUIRED: 'is required to make payment. Please update your profile',
  SUCCESS_STATUS_CODE: '0',
  DEPOSIT_TYPE: 'payment_providers'
}

export const PAYCOOKIES_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers'
}

export const ACKOPAY_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 1
}

export const ZENXPAY_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  DEPOSIT_TYPE: 'payment_providers',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  SUCCESS_STATUS_CODE: 200
}

export const SEYLAN_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  DEPOSIT_TYPE: 'payment_providers',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  SUCCESS_STATUS_CODE: 'SUCCESS'
}

export const TECHPAY_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  DEPOSIT_TYPE: 'payment_providers',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  SUCCESS_STATUS_CODE: 100
}

export const JASPAY_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 200
}

export const CLOUD_CASH_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 200
}

export const XAMAX_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 200,
  PAYMENT_METHOD: 'upi',
  TYPE_OF_CALCULATION: {
    FORWARD_WITH_FEE: 'forward_with_fee',
    FORWARD_WITHOUT_FEE: 'forward_without_fee'
  },
  TRANSACTION_TYPE: 'link',
  TRANSACTION_COMMENTS: 'Deposit Request'
}

export const XAMAX_COUNTRY_CURRENCY_CONSTANT = {
  INR: {
    COUNTRY: 'IND',
    CURRENCY: 'INR',
    PAYMENT_METHODS: ['upi']
  },
  LKR: {
    COUNTRY: 'LKA',
    CURRENCY: 'EUR',
    PAYMENT_METHODS: ['credit_card', 'on_ramp']
  },
  chips: {
    COUNTRY: 'LKA',
    CURRENCY: 'EUR',
    PAYMENT_METHODS: ['credit_card', 'on_ramp_ideal', 'on_ramp_card']
  }
}

export const USER_MIN_DEPOSIT_WITHDRAWAL_CHECK = 500

export const ALLOWED_PERMISSIONS = {
  REQUIRED_KYC_FOR_GAME: 'requiredKycForGame',
  REQUIRED_KYC_FOR_WITHDRAWAL: 'requiredKycForWithdrawal',
  KYC_NOT_DONE: 'Your account KYC is not verified. To verify your KYC, please contact our hotline number',
  UNAUTHORIZED_ERROR_CODE: 401,
  CHECK_WITHDRAW_IS_SUSPICIOUS: 'checkSuspicious',
  SECONDARY_SMS_GATEWAY: 'secondarySmsGateway',
  WEBSITE_DEMO_LOGIN: 'websiteDemoLogin',
  CUSTOM_CSS_ENABLE: 'customCssEnable',
  PLAYER_CATEGORIZATION: 'playerCategorization',
  SKIP_OTP_VERIFICATION: 'skipOtpVerification',
  WITHDRAWAL_REQUEST_LIMITATION: 'withdrawalRequestLimitation',
  ENABLE_FRAUD_DETECTION: 'enableFraudDetection',
  PROFILE_VERIFIED: 'profileVerified',
  DISABLEAGENTUSER: 'disableAgentUser',
  WITHDRAWAL_SETTINGS: 'withdrawalSettings',
  ENABLE_PLAYER_CATEGORY: 'enablePlayerCategory',
  REFERRAL_CODE: 'enableReferral',
  ALANBASE: 'alanbaseCrm',
  AUTO_ACTIVATE_BONUS: 'autoActivateLosingBonus',
  SMS_EMAIL_UPON_SIGNUP: 'thankyouEmailAfterRegistration',
  MULTI_BONUS_ALLOWANCE: 'multiBonusAllowance',
  DEPOSIT_WITHDRAW_LIMIT_SETTINGS: 'depositWithdrawLimitSettings',
  WYNTA: 'wyntaAffiliate',
  USER_SPECIFIC_PERMISSIONS: 'enablePlayerPermission'
}

export const SPRIBE_INTEGRATION_CREDENTIALS = {
  APP_SPRIBE_OPERATOR_ID: 'APP_SPRIBE_OPERATOR_ID',
  APP_SPRIBE_HASH_KEY: 'APP_SPRIBE_HASH_KEY',
  APP_SPRIBE_API_USERNAME: 'APP_SPRIBE_API_USERNAME',
  APP_SPRIBE_API_ID: 'APP_SPRIBE_API_ID',
  APP_SPRIBE_API_ACCESS_TOKEN: 'APP_SPRIBE_API_ACCESS_TOKEN',
  APP_SPRIBE_LAUNCH_URL: 'APP_SPRIBE_LAUNCH_URL'
}

export const EZUGI_CREDENTIALS = {
  APP_EZUGI_OPERATOR_ID: 'APP_EZUGI_OPERATOR_ID',
  APP_EZUGI_LAUNCH_URL: 'APP_EZUGI_LAUNCH_URL',
  EVOLUTION_GAME_SECONDARY_CURRENCY: 'EVOLUTION_GAME_SECONDARY_CURRENCY',
  EVOLUTION_GAME_SECONDARY_CURRENCY_CONVERSION: 'EVOLUTION_GAME_SECONDARY_CURRENCY_CONVERSION_RATE',
  APP_EVOLUTION_PROXY_LAUNCH_URL: 'APP_EVOLUTION_PROXY_LAUNCH_URL'
}

export const GAME_LAUNCH = {
  OPERATOR_ID_NOT_FOUND: 'Operator id not found',
  LAUNCH_URL_NOT_FOUND: 'Launch url not found',
  PROVIDER_NOT_FOUND: 'Provider is not assigned',
  SPRIBE_PROVIDER_NAME: 'Spribe',
  EZUGI_PROVIDER_NAME: 'Ezugi',
  EVOLUTION_PROVIDER_NAME: 'Evolution',
  SUCCESS: 'Success',
  INTERNAL_ERROR_CODE: 500,
  JETFAIR_SPORT_EXCHANGE: 'Jetfair',
  SECRET_KEY_NOT_FOUND: 'Secret key not found',
  AGENT_NAME_NOT_FOUND: 'Agent name not found',
  NOT_LOGIN: 'User not login',
  BET_DETAIL_URL_NOT_FOUND: 'Bet detail url not found',
  ST8: 'st8',
  EZUGI_TOKEN: 'ezugi_token',
  EVOLUTION_TOKEN: 'evolution_token',
  PGSOFT: 'PGsoft',
  CUSTOM_GAME: '8DEX',
  DARWIN_GAMING: 'Darwin',
  POWER_PLAY: 'Powerplay',
  ARCADE_PROVIDER_NAME: 'Arcade',
  ARCADE_TOKEN: 'arcade_token',
  IMPERSONATED_USER_NOT_ALLOWED: 'Impersonated user not allowed',
  WHITECLIFF_GAMING: 'Whitecliff',
  USER_BLOCKED_GAMES: 'This game is temporarily unavailable.',
  FUNKY_GAMES_GAMING: 'Funky',
  GAME_NOT_FOUND: 'Game Not Found',
  INVALID_GAME_CODE: 'Invalid Game Code',
  CURRENCY_NOT_INTEGRATED: 'Currency not integrated',
  LOTTERY_GAMING: 'Lottery777',
  TURBO_STARS_PROVIDER_NAME: 'TurboStars',
  BOT_USER_RESPONSE: 'Access denied. Verification required to continue.',
  REDTIGER_PROVIDER_NAME: 'Red Tiger',
  REDTIGER_TOKEN: 'redtiger_token',
  NETENT_PROVIDER_NAME: 'NetEnt',
  NETENT_TOKEN: 'netent_token',
  KEYS_NOT_FOUND: 'Keys not found',
  USER_NOT_FOUND: 'User not found',
  RECENT_GAME: 'Game is not accessible as it is playing by user',
  SPINOCCHIO: 'Spinocchio',
  PAGE_BANNED: 'This Provider is temporarily unavailable.',
  MENU_BANNED: 'This Category is temporarily unavailable.',
  PERMISSION_DENIED: 'Permission Denied',
  TURBO_GAMES: 'TurboGames',
  CAPTCHA_REQUIRED_CODE: 403,
  CAPTCHA_REQUIRED_MESSAGE: 'Captcha verification required',
  PIGABOOM: 'pigaboom'
}

export const SPRIBE_INTEGRATION_CONSTANT = {
  SUCCESS: 'Success',
  SUCCESS_CODE: 200,
  DUPLICATE_TRANSACTION_OK: 'OK',
  INSUFFICIENT_FUND: 'Insufficient fund',
  INSUFFICIENT_FUND_CODE: 402,
  NEGATIVE_AMOUNT: 'Amount is negative',
  USER_NOT_FOUND: 'User not found',
  BET_LIMIT_EXCEED: 'Bet limit exceed',
  TOKEN_INVALID: 'User token is invalid',
  TOKEN_INVALID_CODE: 401,
  TOKEN_EXPIRED: 'User token is expired',
  TOKEN_EXPIRED_CODE: 403,
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  DUPLICATE_TRANSACTION_CODE: 409,
  TRANSACTION_NOT_FOUND: 'Transaction does not found',
  TRANSACTION_NOT_FOUND_CODE: 408,
  OPERATOR_ID_NOT_FOUND: 'Operator id not found',
  LAUNCH_URL_NOT_FOUND: 'Launch url not found',
  SPRIBE_INTERNAL_ERROR_CODE: 500,
  SPRIBE_INTERNAL_ERROR: 'Internal Error',
  CURRENCY_SYMBOL: 'INR',
  JSON_WEB_TOKEN_ERROR: 'JsonWebTokenError',
  INVALID_SIGNATURE: 'invalid token',
  CURRENCY_NOT_SUPPORTED: 'System currently support only INR currency',
  IMPERSONATED_USER_NOT_ALLOWED: 'Impersonated user not allowed',
  CURRENCY_CONVERSION: 1000,
  CURRENCY_ID: '2',
  LANG: 'EN',
  VALIDATION_ERROR: 400
}

export const EXCEPTIONS = {
  USER_ALREADY_REGISTERED: 'USER_ALREADY_REGISTERED',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  UPDATE_ERROR: 'UPDATE_ERROR',
  PASSWORD_MISMATCH: 'PASSWORD_MISMATCH',
  USER_CREATION_FAILED: 'USER_CREATION_FAILED',
  EMAIL_ALREADY_EXIST: 'EMAIL_ALREADY_EXIST',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  PHONE_ALREADY_EXIST: 'PHONE_ALREADY_EXIST',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  EMAIL_AND_PHONE_NOT_FOUND: 'EMAIL_AND_PHONE_NOT_FOUND',
  INVALID_PHONE_NUMBER: 'INVALID_PHONE_NUMBER',
  INVALID_TOKEN: 'INVALID_TOKEN',
  COUNTRY_CODE_NOT_FOUND: 'COUNTRY_CODE_NOT_FOUND',
  VERIFICATION_ERROR: 'VERIFICATION_ERROR',
  FETCH_FAILED: 'FETCH_FAILED',
  LOGIN_FAILED: 'LOGIN_FAILED',
  DUPLICATE_UTR_REQUEST: 'DUPLICATE_UTR'
}

export const COMMON = {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  UPDATE_SUCCESS: 'UPDATE_SUCCESS',
  USER_CREATED: 'USER_CREATED',
  VERIFICATION_SUCCESS: 'VERIFICATION_SUCCESS',
  FETCH_SUCCESS: 'FETCH_SUCCESS'
}

export const EMAIL_TOKEN_EXPIRATION = '3 hours' // value should be a postgres verified time.

export const PHONE_TOKEN_EXPIRATION = '5 minutes' // value should be a postgres verified time.

export const DB_ERROR_CODES = {
  NOT_UNIQUE: '23505'
}

export const HTTP_STATUS = {
  CONTINUE: 100,
  SWITCHING_PROTOCOLS: 101,
  PROCESSING: 102,
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NON_AUTHORITATIVE_INFORMATION: 203,
  NO_CONTENT: 204,
  RESET_CONTENT: 205,
  PARTIAL_CONTENT: 206,
  AMBIGUOUS: 300,
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  SEE_OTHER: 303,
  NOT_MODIFIED: 304,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  REQUESTED_RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  I_AM_A_TEAPOT: 418,
  UNPROCESSABLE_ENTITY: 422,
  FAILED_DEPENDENCY: 424,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505
}

export const TRANSACTION_TYPES = {
  DEBIT: 0,
  CREDIT: 1,
  ROLLBACK: 2,
  DEPOSIT: 3,
  WITHDRAW: 4,
  FAILED: 16,
  TIP: 7,
  DEBIT_NO_CASH: 8,
  CREDIT_NO_CASH: 9,
  ROLLBACK_NO_CASH: 10,
  NON_CASH_BONUS_CLAIM: 11,
  DEPOSIT_BONUS_CLAIM: 12,
  TIP_NON_CASH: 13,
  WITHDRAW_CANCEL: 14,
  JOINING_BONUS_CLAIMED: 15,
  PROMO_CODE_BONUS_CLAIMED: 17,
  ADMIN_ADD_NON_CASH: 5,
  CANCELLED_BY_ADMIN: 38,
  PLAYER_BULK_CATEGORIZATION_BONUS: 41,
  DEBIT_OTB_CASH: 46,
  ROLLBACK_OTB_CASH: 47,
  ROYALTY_BONUS_NON_CASH: 44,
  ROYALTY_BONUS_CASH: 45,
  REFERRAL_BONUS: 52,
  DEPOSIT_OTB_CASH: 48,
  WITHDRAW_OTB_CASH: 49,
  SPORTS_FREE_BET_DEPOSIT: 59,
  SPORTS_FREE_BET_WITHDRAW: 60,
  FREE_BETS_DEPOSIT_BONUS_CLAIM: 68,
  SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM: 69,
  NON_CASH_DEPOSIT_BONUS_CLAIMED: 70,
  NON_CASH_PROMO_CODE_BONUS_CLAIMED: 71,
  FREE_BETS_PROMO_CODE_BONUS_CLAIMED: 75,
  SPORTS_FREE_BETS_PROMO_CODE_BONUS_CLAIMED: 76,
  NON_CASH_REFERRAL_BONUS_CLAIM: 72
}

export const BONUS_TYPES = {
  DEPOSIT: 'deposit',
  LOSING: 'losing',
  JOINING: 'joining',
  DEPOSIT_SPORTS: 'deposit_sport',
  DEPOSIT_INSTANT: 'deposit_instant',
  PROMO_CODE: 'promo_code',
  LOSING_SPORT: 'losing_sport',
  LOSING_BOTH: 'losing_both',
  DEPOSIT_BOTH: 'deposit_both',
  REFERRAL_CODE: 'referral_code',
  AUTO_BONUS_ACTIVATE: 'auto_bonus_activate'
}

export const BONUS_TO_BONUS_TYPES = {
  deposit: 'DEPOSIT',
  losing: 'LOSING',
  joining: 'JOINING',
  deposit_sports: 'DEPOSIT_SPORTS'
}

export const BONUS_STATUS = {
  ACTIVE: 'active',
  CLAIMED: 'claimed',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired'
}

export const WITHDRAW_REQUEST_STATUS = {
  PENDING: 'pending',
  CANCELLED: 'cancelled',
  REJECTED: 'rejected',
  APPROVED: 'approved',
  IN_PROGRESS: 'in_progress',
  PENDING_BY_BANK: 'pending_by_bank',
  PENDING_BY_GATEWAY: 'pending_by_gateway',
  SUCCESS: 'success'
}

export const SUBSCRIPTION_CHANNEL = {
  USER_WALLET_BALANCE: 'USER_WALLET_BALANCE',
  DOCUMENT_NOTIFICATION: 'ADMIN_NOTIFICATION_CHANNEL',
  PLAYER_NOTIFICATION: 'PLAYER_NOTIFICATION_CHANNEL',
  USER_WITHDRAW_NOTIFICATION: 'USER_WITHDRAW_NOTIFICATION',
  USER_DEPOSIT_NOTIFICATION: 'USER_DEPOSIT_NOTIFICATION',
  IS_MAINTENANCE_MODE: 'IS_MAINTENANCE_MODE',
  USER_TOKEN_EXPIRATION_NOTIFICATION: 'USER_TOKEN_EXPIRATION_NOTIFICATION',
  MULTIPLE_LOGIN_NOTIFICATION: 'MULTIPLE_LOGIN_NOTIFICATION',
  QUEUE_WORKER: 'QUEUE_WORKER',
  USER_PG_DEPOSIT_NOTIFICATION: 'USER_PG_DEPOSIT_NOTIFICATION',
  TURBO_BET_PLACED: 'TURBO_BET_PLACED',
  CASHIER_TURBO_BET_PLACED: 'CASHIER_TURBO_BET_PLACED'
}

export const CODE_TO_TRANSACTION_TYPES = {
  0: 'DEBIT',
  1: 'CREDIT',
  2: 'ROLLBACK',
  3: 'DEPOSIT',
  4: 'WITHDRAW',
  7: 'TIP',
  8: 'DEBIT_NO_CASH',
  9: 'CREDIT_NO_CASH',
  10: 'ROLLBACK_NO_CASH',
  11: 'NON_CASH_BONUS_CLAIM',
  12: 'DEPOSIT_BONUS_CLAIM',
  13: 'TIP_NON_CASH',
  14: 'WITHDRAW_CANCEL',
  15: 'JOINING_BONUS_CLAIMED'
}

export const DEPOSIT_BONUS_TRANSACTION_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  PENDING: 'pending'
}

export const LIVE_CASINO_ERROR_CODES = {
  COMPLETED: 0,
  GENERAL_ERROR: 1,
  BLANK: 2,
  INSUFFICIENT_FUNDS: 3,
  OPERATOR_LIMIT_TO_THE_PLAYER_1: 4, // insufficient behaviour
  OPERATOR_LIMIT_TO_THE_PLAYER_2: 5, // insufficient behaviour
  TOKEN_NOT_FOUND: 6,
  USER_NOT_FOUND: 7,
  USER_BLOCKED: 8,
  TRANSACTION_NOT_FOUND: 9,
  TRANSACTION_TIMED_OUT: 10,
  BALANCE_NOT_ENOUGH_FOR_TIPPING: 11
}

export const FILE_UPLOAD_ERROR_CODES = {
  1: 'INVALID_FILE_TYPE',
  2: 'FILE_SIZE_LIMIT_EXCEEDED'
}

export const ALLOWED_FILE_TYPES = ['png', 'tiff', 'tif', 'jpg', 'jpeg', 'pdf']

export const DEPOSIT_REQUEST_STATUS = {
  OPEN: 'opened',
  COMPLETED: 'completed',
  CANCELED: 'canceled',
  FAILED: 'failed',
  IN_PROCESS: 'in_process'
}

export const FONEPESA_USER_ERROR_CODES = new Set([1000, 1005, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1025,
  1027, 1029, 1030, 1031, 1032, 1033, 1034, 1039, 1040, 1041, 1042, 1044, 1045, 1046, 1050, 1051, 1052,
  1053, 1055, 1054, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1068, 1069, 1070,
  1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085])

export const FONEPESA_INTERNAL_ERROR_CODES = new Set([1001, 1002, 1003, 1004, 1007, 1008, 1023, 1024, 9999, 1026, 1028, 1035, 1036, 1037, 1038, 1086, 1087])

export const FONEPESA_CANCELLED_ERROR_CODES = new Set([1043])

export const FONEPESA_IN_PROCESS_ERROR_CODES = new Set([1006, 1088])

export const API_KEY = {
  '37a1e18013e259ce0dec2f9b111aa69': '4fc0f680d3117294eb60ac1ba30a01b'
}

export const JETFAIR_INTEGRATION_CREDENTIALS = {
  APP_JETFAIR_LAUNCH_URL: 'APP_JETFAIR_LAUNCH_URL',
  APP_JETFAIR_AGENT_USER_NAME: 'APP_JETFAIR_AGENT_USER_NAME',
  APP_JETFAIR_SECRETKEY: 'APP_JETFAIR_SECRETKEY',
  JETFAIR_SPORT_LANDING_PAGE: 'JETFAIR_SPORT_LANDING_PAGE',
  POWERPLAY_SPORT_LANDING_PAGE: 'POWERPLAY_SPORT_LANDING_PAGE',
  JETFAIR_POST_METHOD: 'post',
  JETFAIR_CURRENCY: 'INR',
  JETFAIR_TOKEN: 'jetfair_token',
  JETFAIR_HEADER: { 'Content-Type': 'application/json' },
  APP_JETFAIR_BET_DETAIL_URL: 'APP_JETFAIR_BET_DETAIL_URL',
  APP_CHIPS_CURRENCY_CODE: 'APP_CHIPS_CURRENCY_CODE'
}

export const COMMON_RESPONSE_CODES = {
  SUCCESS: 'Success',
  INTERNAL_ERROR_CODE: { code: 500, message: 'Internal Error' },
  TOKEN_EXPIRED: { code: 401, message: 'User token is expired' }
}

export const JETFAIR_INTEGRATION_CONSTANT = {
  SUCCESS: 'Success',
  ERROR: 'Error',
  SUCCESS_CODE: 200,
  TOKEN_INVALID: 'User token is invalid',
  TOKEN_INVALID_CODE: 401,
  TOKEN_EXPIRED: 'User token is expired',
  TOKEN_EXPIRED_CODE: 403,
  JETFAIR_INTERNAL_ERROR_CODE: 500,
  JETFAIR_INTERNAL_ERROR: 'Internal Error',
  CURRENCY_ID: '2',
  USER_NOT_FOUND: 'User not found',
  JSON_WEB_TOKEN_ERROR: 'JsonWebTokenError',
  INVALID_SIGNATURE: 'invalid signature'
}

export const RESPONSIBLE_GAMING_CONSTANT = {
  DAILY_BETTING_LIMIT: 'dailyBettingLimit',
  WEEKLY_BETTING_LIMIT: 'weeklyBettingLimit',
  MONTHLY_BETTING_LIMIT: 'monthlyBettingLimit',
  DAILY_DEPOSIT_LIMIT: 'dailyDepositLimit',
  WEEKLY_DEPOSIT_LIMIT: 'weeklyDepositLimit',
  MONTHLY_DEPOSIT_LIMIT: 'monthlyDepositLimit',
  DAILY_WITHDRAW_LIMIT: 'dailyWithdrawLimit',
  WEEKLY_WITHDRAW_LIMIT: 'weeklyWithdrawLimit',
  MONTHLY_WITHDRAW_LIMIT: 'monthlyWithdrawLimit',
  DAILY_ERROR: 'Daily',
  WEEKLY_ERROR: 'Weekly',
  MONTHLY_ERROR: 'Monthly'
}

export const ST8_CONSTANT = {
  ST8_LAUNCH_URL: 'ST8_LAUNCH_URL',
  ST8_PRIVATE_KEY: 'ST8_PRIVATE_KEY',
  ST8_SIGN_ALGO: 'SHA256withECDSA',
  ST8_SITE: 'ST8_SITE_KEY',
  ST8_LOBBY: 'ST8_SITE_LOBBY'
}

export const PGSOFT_CONSTANT = {
  PG_LAUNCH_URL: 'PG_LAUNCH_URL',
  PG_OPERATOR_KEY: 'PG_OPERATOR_KEY',
  PG_LOBBY_URL: 'PG_LOBBY_URL',
  PG_LANGUAGE: 'en',
  PG_REALITY_CHECK: 290,
  PG_REALITY_CHECK_INTERVAL: 300,
  PG_URL_TYPE: 'game-entry'
}

export const PG_BET_TYPE = {
  PG_REAL_GAME: '1',
  PG_TOURNAMENT_GAME: '3'
}

export const AMOUNT_LIMIT = {
  DEPOSIT_AMOUNT_LIMIT: 10000000,
  WITHDRAW_AMOUNT_LIMIT: 10000000
}

export const ERORR_TYPE = {
  CUSTOM: 'Custom',
  AUTHRNTICATION: 'Authentication',
  INVALID_REQUEST: 'Invalid request body',
  FORBIDDEN_ERROR: 'ForbiddenError',
  APOLLO_ERROR: 'ApolloError'
}

export const RESPONSE_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed'
}

export const SPORTS = {
  DEPOSIT_BONUS_PENDING: 7
}

export const WITHDRAW_REQUEST_VERIFY_STATUS = {
  VERIFIED: 'verified'
}

export const PROMO_BONUS_WALLET_TYPES = {
  0: 'amount',
  1: 'nonCashAmount'
}

export const VERIFICATION_TYPE = {
  VERIFICATION: 'verification',
  NON_VERIFICATION: 'non_verification'
}

export const FORGOT_PASSWORD_OPTIONS = {
  email: 'email',
  mobile: 'mobile'
}

export const DEFAULT_OTP = '123456'

export const STAGE_TENANTS = {
  'pggammastack.com': { id: '1', dataValues: { id: '1', name: 'Samino' }, currency: 'EUR' },
  'lionplay.co': { id: '16', dataValues: { id: '16', name: 'Zovi24' }, currency: 'INR' },
  'wiin24.com': { id: '18', dataValues: { id: '18', name: 'Royal24' }, currency: 'INR' },
  'strikes247.com': { id: '19', dataValues: { id: '19', name: 'Crown246' }, currency: 'INR' },
  'spingt888.com': { id: '20', dataValues: { id: '20', name: 'Spingt888' }, currency: 'chips' },
  'obabet888.com': { id: '22', dataValues: { id: '22', name: 'Obabet888' }, currency: 'LKR' },
  'betmaster247.net': { id: '23', dataValues: { id: '23', name: 'Bet Master 247' }, currency: 'INR' },
  'sportway.live': { id: '24', dataValues: { id: '24', name: 'Sportway' }, currency: 'LKR' },
  'jeeto555.net': { id: '21', dataValues: { id: '21', name: 'jeeto555' }, currency: 'INR' },
  'cric888.net': { id: '53', dataValues: { id: '53', name: 'Cric888' }, currency: 'INR' },
  'roylfc.com': { id: '54', dataValues: { id: '54', name: 'Marina888' }, currency: 'LKR' },
  '11beast.com': { id: '55', dataValues: { id: '55', name: '11beast' }, currency: 'chips' },
  '8dex.store': { id: '90', dataValues: { id: '90', name: 'AceXBet365' }, currency: 'INR' },
  'betlab247.in': { id: '91', dataValues: { id: '91', name: 'Betlab247' }, currency: 'INR' },
  'moneywin365.in': { id: '119', dataValues: { id: '119', name: 'Moneywin365' }, currency: 'INR' },
  'superbetex.com': { id: '120', dataValues: { id: '120', name: 'WingtNet' }, currency: 'chips' },
  '8dex.xyz': { id: '152', dataValues: { id: '152', name: '88Punt' }, currency: 'INR' },
  'ingrandstation.com': { id: '185', dataValues: { id: '185', name: 'GoldenIsland', cashierDomain: 'https://cashier.ingrandstation.com/' }, currency: 'chips' },
  '8dex.cloud': { id: '222', dataValues: { id: '222', name: 'cric247' }, currency: 'INR' }
}
export const PROD_TENANTS = {
  'hukum247.com': { id: '3', dataValues: { id: '3', name: 'Hukum247' }, currency: 'INR' },
  'wingt888.com': { id: '20', dataValues: { id: '20', name: 'WinGt888' }, currency: 'chips' },
  'crown246.com': { id: '18', dataValues: { id: '18', name: 'Crown246' }, currency: 'INR' },
  'roylfc567.com': { id: '1', dataValues: { id: '1', name: 'Samino' }, currency: 'EUR' },
  'betmaster247.com': { id: '21', dataValues: { id: '21', name: 'BetMaster247' }, currency: 'INR' },
  'betmaster247.in': { id: '21', dataValues: { id: '21', name: 'BetMaster247' }, currency: 'INR' },
  'zovi24.com': { id: '2', dataValues: { id: '2', name: 'Zovi24' }, currency: 'INR' },
  'sportway.lk': { id: '53', dataValues: { id: '53', name: 'Sportway' }, currency: 'LKR' },
  'jeeto555.com': { id: '54', dataValues: { id: '54', name: 'jeeto555' }, currency: 'INR' },
  'playpm365.com': { id: '55', dataValues: { id: '55', name: 'playpm365' }, currency: 'INR' },
  'cric888.com': { id: '56', dataValues: { id: '56', name: 'Cric888' }, currency: 'INR' },
  'lordbet.world': { id: '57', dataValues: { id: '57', name: 'Lordbet' }, currency: 'INR' },
  'marina888.com': { id: '86', dataValues: { id: '86', name: 'Marina888' }, currency: 'LKR' },
  'acexbet365.com': { id: '119', dataValues: { id: '119', name: 'AceXBet365' }, currency: 'INR' },
  'moneywin365.com': { id: '152', dataValues: { id: '152', name: 'Moneywin365' }, currency: 'INR' },
  'betlab247.com': { id: '153', dataValues: { id: '153', name: 'Betlab247' }, currency: 'INR' },
  'wingt888.net': { id: '185', dataValues: { id: '185', name: 'wingt888.NET' }, currency: 'chips' },
  'jeeto555.co.in': { id: '54', dataValues: { id: '54', name: 'jeeto555' }, currency: 'INR' },
  'jeeto555.online': { id: '54', dataValues: { id: '54', name: 'jeeto555' }, currency: 'INR' },
  'zubabet.com.zm': { id: '218', dataValues: { id: '218', name: 'ZubaBet' }, currency: 'USD' },
  '88punt.net': { id: '251', dataValues: { id: '251', name: '88Punt' }, currency: 'INR' },
  'goldenisland.lk': { id: '284', dataValues: { id: '284', name: 'GoldenIsland', cashierDomain: 'https://cashier.goldenisland.lk/' }, currency: 'chips' }
}
export const getPGSoftGameLaunchUrl = (gameUrl) => `${gameUrl}/external-game-launcher/api/v1/GetLaunchURLHTML`

export const getPGSoftGamePath = (gameId) => `/${gameId}/index.html`

export const CUSTOM_GAME_CONSTANT = {
  OPERATOR_ID: 'CUSTOM_GAME_OPERATOR_ID',
  LAUNCH_URL: 'CUSTOM_GAME_LAUNCH_URL'
}

export const STAGE_TENANTS_MESSAGE_CENTRAL_SMS = ['21', '23'] // jeeto and betmaster
export const PROD_TENANTS_MESSAGE_CENTRAL_SMS = ['54', '21'] // jeeto and betmaster

export const FAKE_USER_INFO = {
  USERNAME: 'johndoe1985',
  PHONE: '5698584256',
  EMAIL: '<EMAIL>'
}

export const DARWIN_CREDENTIALS = {
  DARWIN_URL: 'DARWIN_URL',
  DARWIN_CASINOID: 'DARWIN_CASINOID',
  DARWIN_USERNAME: 'DARWIN_USERNAME',
  DARWIN_PASSWORD: 'DARWIN_PASSWORD'
}

export const POWER_PLAY_CREDENTIALS = {
  POWER_PLAY_BASE_URL: 'POWER_PLAY_BASE_URL',
  POWER_PLAY_PARTNER_ID: 'POWER_PLAY_PARTNER_ID',
  POWER_PLAY_PUBLIC_KEY: 'POWER_PLAY_PUBLIC_KEY',
  POWER_PLAY_PRIVATE_KEY: 'POWER_PLAY_PRIVATE_KEY'
}

export const FUNKY_GAMES_CREDENTIALS = {
  FUNKY_GAMES_URL: 'FUNKY_GAMES_URL',
  FUNKY_GAMES_AUTHENTICATION: 'FUNKY_GAMES_AUTHENTICATION',
  FUNKY_GAMES_USER_AGENT: 'FUNKY_GAMES_USER_AGENT',
  FUNKY_GAMES_TOKEN: 'funky_games_token'
}

export const FUNKY_GAMES_RESPONSE_CODES = {
  0: 'Success',
  400: 'Invalid Input',
  401: 'Player Not Login',
  402: 'Insufficient Balance',
  403: 'Bet already exists',
  404: 'Bet Was Not Found',
  405: 'Api Suspended',
  406: 'Over Max Winning',
  407: 'Over Max Lose',
  409: 'Bet Already Settled',
  410: 'Bet Already Cancelled',
  601: 'Voucher Already Exists',
  602: 'Voucher Is Not Valid',
  3002: 'Report Invalid Input',
  3003: 'Report Page Not Found',
  3004: 'Report GameCode Not Found',
  10005: 'GameCode is not allowed. GameCode is XXXX',
  9999: 'Internal Server Error',
  10001: 'Authentication Failed',
  10002: 'No provider information'
}

export const SAMBHAVPAY_PG_INTEGRATION_CONSTANT = {
  REQUEST_SEQUENCE: 'Mid|OrderNo|TotalAmount|CurrencyName|MeTransReqType|AddField1|AddField2|AddField3|AddField4|AddField5|AddField6|AddField7|AddField8|AddField9|AddField10|EmailId|MobileNo|Address|City|State|Pincode|TransactionMethod|BankCode|VPA|CardNumber|ExpiryDate|CVV|CustomerName|ResponseUrl|UPIType',
  RESPONSE_SEQUENCE: 'Mid|OrderNo|TxnRefNo|TotalAmount|CurrencyName|MeTransReqType|AddField1|AddField2|AddField3|AddField4|AddField5|AddField6|AddField7|AddField8|AddField9|AddField10|EmailId|MobileNo|Address|City|State|Pincode|RespCode|RespMessage|PayAmount|TxnRespDate|UPIString',
  USER_NOT_FOUND: 'User not found',
  INVALID_TOKEN: 'Invalid token',
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  SUCCESS_RESPCODE: '0',
  SUCCESS_RESPMSG: 'Success',
  PENDING_FOR_AUTHORIZATION_CODE: '03',
  DEPOSIT_TYPE: 'payment_providers'
}

export const ALLOWED_PARALLEL_BONUS = 'parallelBonus'
export const UNIQUE_BANK_ACCOUNT = 'uniqueBankAccount'
export const ENABLE_PLAYER_CATEGORY = 'enablePlayerCategory'

export const CURRENCY_CODE = {
  LKR: 'LKR'
}

export const DEMO_LOGIN_USER_CREDENTIAL = {
  EMAIL: '<EMAIL>',
  PASSWORD: 'Demo@123',
  TENANT_ID: 0
}

export const SMS_GATEWAY = {
  ZMESSENGER_SMS: 'ZMessengerSMS',
  FAST2_SMS: 'Fast2SMS',
  SENDGRID_SMS: 'SendgridSMS'
}

export const PHONE_CODE = {
  SRILANKA: '94'
}

export const CASINO_PROVIDER = {
  EZUGI: {
    STAGE: 1,
    PROD: 10
  },
  EVOLUTION: {
    STAGE: 2,
    PROD: 1
  },
  ST8: {
    STAGE: 16,
    PROD: 48
  },
  JETFAIR: {
    STAGE: 14,
    PROD: 15
  },
  POWERPLAY: {
    STAGE: 5035,
    PROD: 50
  },
  SPRIBE: {
    STAGE: 13,
    PROD: 13
  },
  ARCADE: {
    STAGE: 5067,
    PROD: 81
  },
  TURBO_STARS: {
    STAGE: 5133,
    PROD: 181
  },
  WHITECLIFF: {
    STAGE: 5100,
    PROD: 213
  }
}

export const EVOLUTION_PROVIDER_ID = {
  STAGE: '2',
  PROD: '1'
}

export const ST8_PROVIDER_ID = {
  STAGE: '16',
  PROD: '48'
}

export const MANUAL_DEPOSIT_TYPE = {
  BANK: 1,
  VIRTUAL: 2
}

export const VIP_CODE = {
  '0nJaumfDk4rD4gGK2s3fw': { vipLevel: 10 }
}

export const ENVIORNMENT = {
  PRODUCTION: 'production',
  STAGING: 'staging'
}

export const THREAD_MESSAGE = {
  OWNER_TYPE: {
    OWNER: 'owner',
    AGENT: 'agent'
  },
  SENT_TYPE: {
    ALL: 0,
    SELECTED_USERS: 1
  }
}

export const PGSOFT_PROVIDER_ID = {
  STAGE: '5000',
  PROD: '49'
}

export const ARCADE_PROVIDER_ID = {
  STAGE: '5067',
  PROD: ''
}

export const TENANT_IDS = {
  MARINA_888: {
    STAGE: '54',
    PROD: '86'
  },
  JEETO_555: {
    STAGE: 21,
    PROD: 54
  }
}
export const BURN_DEPOSIT_BONUS_MESSAGE = 'You currently have an unburned bonus amount. Therefore, this deposit will not be considered for the currently active deposit bonus.'
export const BURN_LOSING_BONUS_MESSAGE = "You have an unburned bonus amount, so you're not eligible for the current bonus.Please use or burn the unburned bonus before using the new one."

export const SMARTIGO_TENANTS = {
  STAGE: ['54'],
  PROD: ['86']
}

export const COUNTRIES = [
  { name: 'Afghanistan', code: 'AF' },
  { name: 'Åland Islands', code: 'AX' },
  { name: 'Albania', code: 'AL' },
  { name: 'Algeria', code: 'DZ' },
  { name: 'American Samoa', code: 'AS' },
  { name: 'AndorrA', code: 'AD' },
  { name: 'Angola', code: 'AO' },
  { name: 'Anguilla', code: 'AI' },
  { name: 'Antarctica', code: 'AQ' },
  { name: 'Antigua and Barbuda', code: 'AG' },
  { name: 'Argentina', code: 'AR' },
  { name: 'Armenia', code: 'AM' },
  { name: 'Aruba', code: 'AW' },
  { name: 'Australia', code: 'AU' },
  { name: 'Austria', code: 'AT' },
  { name: 'Azerbaijan', code: 'AZ' },
  { name: 'Bahamas', code: 'BS' },
  { name: 'Bahrain', code: 'BH' },
  { name: 'Bangladesh', code: 'BD' },
  { name: 'Barbados', code: 'BB' },
  { name: 'Belarus', code: 'BY' },
  { name: 'Belgium', code: 'BE' },
  { name: 'Belize', code: 'BZ' },
  { name: 'Benin', code: 'BJ' },
  { name: 'Bermuda', code: 'BM' },
  { name: 'Bhutan', code: 'BT' },
  { name: 'Bolivia', code: 'BO' },
  { name: 'Bosnia and Herzegovina', code: 'BA' },
  { name: 'Botswana', code: 'BW' },
  { name: 'Bouvet Island', code: 'BV' },
  { name: 'Brazil', code: 'BR' },
  { name: 'British Indian Ocean Territory', code: 'IO' },
  { name: 'Brunei Darussalam', code: 'BN' },
  { name: 'Bulgaria', code: 'BG' },
  { name: 'Burkina Faso', code: 'BF' },
  { name: 'Burundi', code: 'BI' },
  { name: 'Cambodia', code: 'KH' },
  { name: 'Cameroon', code: 'CM' },
  { name: 'Canada', code: 'CA' },
  { name: 'Cape Verde', code: 'CV' },
  { name: 'Cayman Islands', code: 'KY' },
  { name: 'Central African Republic', code: 'CF' },
  { name: 'Chad', code: 'TD' },
  { name: 'Chile', code: 'CL' },
  { name: 'China', code: 'CN' },
  { name: 'Christmas Island', code: 'CX' },
  { name: 'Cocos (Keeling) Islands', code: 'CC' },
  { name: 'Colombia', code: 'CO' },
  { name: 'Comoros', code: 'KM' },
  { name: 'Congo', code: 'CG' },
  { name: 'Congo, The Democratic Republic of the', code: 'CD' },
  { name: 'Cook Islands', code: 'CK' },
  { name: 'Costa Rica', code: 'CR' },
  { name: 'Cote D\'Ivoire', code: 'CI' },
  { name: 'Croatia', code: 'HR' },
  { name: 'Cuba', code: 'CU' },
  { name: 'Cyprus', code: 'CY' },
  { name: 'Czech Republic', code: 'CZ' },
  { name: 'Denmark', code: 'DK' },
  { name: 'Djibouti', code: 'DJ' },
  { name: 'Dominica', code: 'DM' },
  { name: 'Dominican Republic', code: 'DO' },
  { name: 'Ecuador', code: 'EC' },
  { name: 'Egypt', code: 'EG' },
  { name: 'El Salvador', code: 'SV' },
  { name: 'Equatorial Guinea', code: 'GQ' },
  { name: 'Eritrea', code: 'ER' },
  { name: 'Estonia', code: 'EE' },
  { name: 'Ethiopia', code: 'ET' },
  { name: 'Falkland Islands (Malvinas)', code: 'FK' },
  { name: 'Faroe Islands', code: 'FO' },
  { name: 'Fiji', code: 'FJ' },
  { name: 'Finland', code: 'FI' },
  { name: 'France', code: 'FR' },
  { name: 'French Guiana', code: 'GF' },
  { name: 'French Polynesia', code: 'PF' },
  { name: 'French Southern Territories', code: 'TF' },
  { name: 'Gabon', code: 'GA' },
  { name: 'Gambia', code: 'GM' },
  { name: 'Georgia', code: 'GE' },
  { name: 'Germany', code: 'DE' },
  { name: 'Ghana', code: 'GH' },
  { name: 'Gibraltar', code: 'GI' },
  { name: 'Greece', code: 'GR' },
  { name: 'Greenland', code: 'GL' },
  { name: 'Grenada', code: 'GD' },
  { name: 'Guadeloupe', code: 'GP' },
  { name: 'Guam', code: 'GU' },
  { name: 'Guatemala', code: 'GT' },
  { name: 'Guernsey', code: 'GG' },
  { name: 'Guinea', code: 'GN' },
  { name: 'Guinea-Bissau', code: 'GW' },
  { name: 'Guyana', code: 'GY' },
  { name: 'Haiti', code: 'HT' },
  { name: 'Heard Island and Mcdonald Islands', code: 'HM' },
  { name: 'Holy See (Vatican City State)', code: 'VA' },
  { name: 'Honduras', code: 'HN' },
  { name: 'Hong Kong', code: 'HK' },
  { name: 'Hungary', code: 'HU' },
  { name: 'Iceland', code: 'IS' },
  { name: 'India', code: 'IN' },
  { name: 'Indonesia', code: 'ID' },
  { name: 'Iran, Islamic Republic Of', code: 'IR' },
  { name: 'Iraq', code: 'IQ' },
  { name: 'Ireland', code: 'IE' },
  { name: 'Isle of Man', code: 'IM' },
  { name: 'Israel', code: 'IL' },
  { name: 'Italy', code: 'IT' },
  { name: 'Jamaica', code: 'JM' },
  { name: 'Japan', code: 'JP' },
  { name: 'Jersey', code: 'JE' },
  { name: 'Jordan', code: 'JO' },
  { name: 'Kazakhstan', code: 'KZ' },
  { name: 'Kenya', code: 'KE' },
  { name: 'Kiribati', code: 'KI' },
  { name: 'Korea, Democratic People\'S Republic of', code: 'KP' },
  { name: 'Korea, Republic of', code: 'KR' },
  { name: 'Kuwait', code: 'KW' },
  { name: 'Kyrgyzstan', code: 'KG' },
  { name: 'Lao People\'S Democratic Republic', code: 'LA' },
  { name: 'Latvia', code: 'LV' },
  { name: 'Lebanon', code: 'LB' },
  { name: 'Lesotho', code: 'LS' },
  { name: 'Liberia', code: 'LR' },
  { name: 'Libyan Arab Jamahiriya', code: 'LY' },
  { name: 'Liechtenstein', code: 'LI' },
  { name: 'Lithuania', code: 'LT' },
  { name: 'Luxembourg', code: 'LU' },
  { name: 'Macao', code: 'MO' },
  { name: 'Macedonia, The Former Yugoslav Republic of', code: 'MK' },
  { name: 'Madagascar', code: 'MG' },
  { name: 'Malawi', code: 'MW' },
  { name: 'Malaysia', code: 'MY' },
  { name: 'Maldives', code: 'MV' },
  { name: 'Mali', code: 'ML' },
  { name: 'Malta', code: 'MT' },
  { name: 'Marshall Islands', code: 'MH' },
  { name: 'Martinique', code: 'MQ' },
  { name: 'Mauritania', code: 'MR' },
  { name: 'Mauritius', code: 'MU' },
  { name: 'Mayotte', code: 'YT' },
  { name: 'Mexico', code: 'MX' },
  { name: 'Micronesia, Federated States of', code: 'FM' },
  { name: 'Moldova, Republic of', code: 'MD' },
  { name: 'Monaco', code: 'MC' },
  { name: 'Mongolia', code: 'MN' },
  { name: 'Montserrat', code: 'MS' },
  { name: 'Morocco', code: 'MA' },
  { name: 'Mozambique', code: 'MZ' },
  { name: 'Myanmar', code: 'MM' },
  { name: 'Namibia', code: 'NA' },
  { name: 'Nauru', code: 'NR' },
  { name: 'Nepal', code: 'NP' },
  { name: 'Netherlands', code: 'NL' },
  { name: 'Netherlands Antilles', code: 'AN' },
  { name: 'New Caledonia', code: 'NC' },
  { name: 'New Zealand', code: 'NZ' },
  { name: 'Nicaragua', code: 'NI' },
  { name: 'Niger', code: 'NE' },
  { name: 'Nigeria', code: 'NG' },
  { name: 'Niue', code: 'NU' },
  { name: 'Norfolk Island', code: 'NF' },
  { name: 'Northern Mariana Islands', code: 'MP' },
  { name: 'Norway', code: 'NO' },
  { name: 'Oman', code: 'OM' },
  { name: 'Pakistan', code: 'PK' },
  { name: 'Palau', code: 'PW' },
  { name: 'Palestinian Territory, Occupied', code: 'PS' },
  { name: 'Panama', code: 'PA' },
  { name: 'Papua New Guinea', code: 'PG' },
  { name: 'Paraguay', code: 'PY' },
  { name: 'Peru', code: 'PE' },
  { name: 'Philippines', code: 'PH' },
  { name: 'Pitcairn', code: 'PN' },
  { name: 'Poland', code: 'PL' },
  { name: 'Portugal', code: 'PT' },
  { name: 'Puerto Rico', code: 'PR' },
  { name: 'Qatar', code: 'QA' },
  { name: 'Reunion', code: 'RE' },
  { name: 'Romania', code: 'RO' },
  { name: 'Russian Federation', code: 'RU' },
  { name: 'RWANDA', code: 'RW' },
  { name: 'Saint Helena', code: 'SH' },
  { name: 'Saint Kitts and Nevis', code: 'KN' },
  { name: 'Saint Lucia', code: 'LC' },
  { name: 'Saint Pierre and Miquelon', code: 'PM' },
  { name: 'Saint Vincent and the Grenadines', code: 'VC' },
  { name: 'Samoa', code: 'WS' },
  { name: 'San Marino', code: 'SM' },
  { name: 'Sao Tome and Principe', code: 'ST' },
  { name: 'Saudi Arabia', code: 'SA' },
  { name: 'Senegal', code: 'SN' },
  { name: 'Serbia and Montenegro', code: 'CS' },
  { name: 'Seychelles', code: 'SC' },
  { name: 'Sierra Leone', code: 'SL' },
  { name: 'Singapore', code: 'SG' },
  { name: 'Slovakia', code: 'SK' },
  { name: 'Slovenia', code: 'SI' },
  { name: 'Solomon Islands', code: 'SB' },
  { name: 'Somalia', code: 'SO' },
  { name: 'South Africa', code: 'ZA' },
  { name: 'South Georgia and the South Sandwich Islands', code: 'GS' },
  { name: 'Spain', code: 'ES' },
  { name: 'Sri Lanka', code: 'LK' },
  { name: 'Sudan', code: 'SD' },
  { name: 'Suriname', code: 'SR' },
  { name: 'Svalbard and Jan Mayen', code: 'SJ' },
  { name: 'Swaziland', code: 'SZ' },
  { name: 'Sweden', code: 'SE' },
  { name: 'Switzerland', code: 'CH' },
  { name: 'Syrian Arab Republic', code: 'SY' },
  { name: 'Taiwan, Province of China', code: 'TW' },
  { name: 'Tajikistan', code: 'TJ' },
  { name: 'Tanzania, United Republic of', code: 'TZ' },
  { name: 'Thailand', code: 'TH' },
  { name: 'Timor-Leste', code: 'TL' },
  { name: 'Togo', code: 'TG' },
  { name: 'Tokelau', code: 'TK' },
  { name: 'Tonga', code: 'TO' },
  { name: 'Trinidad and Tobago', code: 'TT' },
  { name: 'Tunisia', code: 'TN' },
  { name: 'Turkey', code: 'TR' },
  { name: 'Turkmenistan', code: 'TM' },
  { name: 'Turks and Caicos Islands', code: 'TC' },
  { name: 'Tuvalu', code: 'TV' },
  { name: 'Uganda', code: 'UG' },
  { name: 'Ukraine', code: 'UA' },
  { name: 'United Arab Emirates', code: 'AE' },
  { name: 'United Kingdom', code: 'GB' },
  { name: 'United States', code: 'US' },
  { name: 'United States Minor Outlying Islands', code: 'UM' },
  { name: 'Uruguay', code: 'UY' },
  { name: 'Uzbekistan', code: 'UZ' },
  { name: 'Vanuatu', code: 'VU' },
  { name: 'Venezuela', code: 'VE' },
  { name: 'Viet Nam', code: 'VN' },
  { name: 'Virgin Islands, British', code: 'VG' },
  { name: 'Virgin Islands, U.S.', code: 'VI' },
  { name: 'Wallis and Futuna', code: 'WF' },
  { name: 'Western Sahara', code: 'EH' },
  { name: 'Yemen', code: 'YE' },
  { name: 'Zambia', code: 'ZM' },
  { name: 'Zimbabwe', code: 'ZW' }
]

export const DEMO_USER_ID = {
  STAGE: '1007',
  PROD: '87355'
}

export const DEFAULT_TRANSLATION_LANGUAGE = 'EN-US'

export const LOGIN_AS_USER_KEY = 'JgFuyed9zK0vSgZe1BkAny12yCuRelMe5dvcumGCJiY='

export const UTR = {
  TYPE: {
    DEPOSIT: 'deposit',
    WITHDRAW: 'withdraw',
    TRANSACTION: 'transaction'
  },
  STATUS: {
    OPENED: 0,
    APPROVED: 1,
    REJECTED: 2
  }
}

export const DEPOSIT_WAGER = {
  OPENED: 0,
  APPROVED: 1,
  REJECTED: 2
}

export const OTB_TENANTS_CONFIG = { // setting to enable/disable One Time Bonus Feature for tenants
  STAGE: {
    54: true,
    22: true,
    1: true // temporary enabled for testing
  },
  PROD: {
    86: true
  }
}

export const SPORTS_FREEBET_CONFIG = { // setting to enable/disable One Time Bonus Feature for tenants
  STAGE: {
    1: true,
    54: true,
    21: true
  },
  PROD: {
    1: true,
    86: true,
    54: true
  }
}

export const MAX_IMAGE_SIZE = {
  SIZE_IN_MB: 3
}

export const ERROR_LOG_TYPE = {
  ADMIN: 0,
  USER_BE: 1,
  QUEUE: 2,
  PG: 3
}

export const PLAYER_CATEGORY = {
  1: 'A',
  2: 'B',
  3: 'C',
  4: 'D'
}

export const SMS_ALERT_MESSAGE = {
  approve: 'Your withdrawal request has been approved! Payment will be processed within 24 hours. If you face any issues, please reach out to our support team for help.',
  reject: 'Your withdrawal request has been rejected. For more details or assistance, please contact our support team.',
  withdrawInitiate: 'Your withdrawal request has been initiated! Processing may take up to 24 hours. If you face any issues, please reach out to our support team for help.',
  initiateByUser: 'Your withdrawal request has been received and is being reviewed.'
}

export const LOBBY_GAMES = {
  BLACKJACK: { NAME: 'blackjack', ID: 1000001 }
}

export const TENANTS_WITHDRAW_SMS_ALERT_MESSAGE = {
  53: {
    approve: 'Your withdrawal request has been approved! Payment will be processed within 24 hours. If any issues, please reach out to our support.\nWhatsApp: +94741862774 Email: <EMAIL>',
    reject: 'Your withdrawal request has been rejected. For more details or assistance, please contact our support team.\nWhatsApp: +94741862774 Email: <EMAIL>',
    withdrawInitiate: 'Your withdrawal request has been initiated! Payment will be processed within 24 hours. If any issues, please reach out to our support.\nWhatsApp: +94741862774 Email: <EMAIL>',
    initiateByUser: 'Your withdrawal request has been received and is being reviewed. If any issues,\nWhatsApp: +94741862774 Email: <EMAIL>'
  }
}

export const TENANTS_ALLOWING_EXTRA_SPECIAL_CHARACTERS_IN_PASSWORD = {
  STAGE: ['55'],
  PROD: ['57']
}

export const SOCIAL_MEDIA_LOGIN_TYPE = {
  DEFAULT: 0,
  GOOGLE: 1,
  FACEBOOK: 2
}

export const SOCIAL_MEDIA_AUTH_LINK = {
  GOOGLE: 'https://www.googleapis.com/oauth2/v3/userinfo'
}

export const DEFAULT_AVATAR_IMAGE = 'avatar-images/Avatar-4.png'

export const REFERRAL_EVENT = {
  SIGNUP: 0,
  FIRST_DEPOSIT: 1,
  FIRST_WAGER: 2
}

export const REFERRAL_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3
}

export const REFERRAL_BONUS_TYPE = {
  FLAT: 0,
  UPTO: 1
}

export const REFERRAL_WALLET_TYPE = {
  CASH: 0,
  NON_CASH: 1
}

export const REFERRAL_APPLY_TO = {
  REFERRER: 0,
  REFEREE: 1,
  BOTH: 2
}

export const REFERRAL_BONUS_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3
}

export const EVENT_TYPE = {
  player: 'Player',
  agent: 'Agent',
  bonusCancelled: 'BonusCancelled',
  userAccountDetactivated: 'AccountDetactivated'
}

export const EVENT = {
  create: 'Create',
  read: 'Read',
  update: 'Update',
  toggle: 'Toggle',
  delete: 'Delete'
}

export const TENANT_SETTINGS_KEYS = {
  GLOBAL_WAGER_MULTIPLIER: 'GLOBAL_WAGER_MULTIPLIER',
  MINIMUM_WAGERING_PERCENTAGE: 'MINIMUM_WAGERING_PERCENTAGE',
  DAILY_GLOBAL_DEPOSIT_LIMIT: 'DAILY_GLOBAL_DEPOSIT_LIMIT',
  WEEKLY_GLOBAL_DEPOSIT_LIMIT: 'WEEKLY_GLOBAL_DEPOSIT_LIMIT',
  MONTHLY_GLOBAL_DEPOSIT_LIMIT: 'MONTHLY_GLOBAL_DEPOSIT_LIMIT',
  DAILY_GLOBAL_WITHDRAW_LIMIT: 'DAILY_GLOBAL_WITHDRAW_LIMIT',
  WEEKLY_GLOBAL_WITHDRAW_LIMIT: 'WEEKLY_GLOBAL_WITHDRAW_LIMIT',
  MONTHLY_GLOBAL_WITHDRAW_LIMIT: 'MONTHLY_GLOBAL_WITHDRAW_LIMIT',
  FORCE_WITHDRAW_GLOBAL_LIMIT_OVERRIDE: 'FORCE_WITHDRAW_GLOBAL_LIMIT_OVERRIDE',
  FORCE_DEPOSIT_GLOBAL_LIMIT_OVERRIDE: 'FORCE_DEPOSIT_GLOBAL_LIMIT_OVERRIDE'
}

export const TENANT_SETTINGS_TYPE = {
  VIP_LEVEL_WAGER_MULTIPLIER: 1,
  GLOBAL_WAGER_MULTIPLIER: 2,
  MINIMUM_WAGERING_PERCENTAGE: 3,
  BOT_CONFIGURATION: 4,
  LOYALTY_PROGRAM_SETTINGS: 5,
  DEPOSIT_LIMIT_SETTINGS: 6,
  WITHDRAW_LIMIT_SETTINGS: 7,
  CHATBOT_SETTINGS: 8,
  LOADER_TYPE: 9,
  AUTHENTICATION_SETTINGS: 11,
  BET_SHOP_SETTINGS: 10
}

export const PEERPAY_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 0,
  PURPOSE: 'Merchant Payment'
}

export const ALANBASE_EVENT_TYPES = {
  DEPOSIT: 'alanbase_deposit',
  WITHDRAWAL_COMPLETED: 'alanbase_withdrawal_completed',
  REGISTRATION: 'alanbase_registration'
}

export const TEST_ACCOUNTS = [
  '8DexDev001',
  '8DexDev002',
  '8DexDev003',
  '8DexDev004',
  '8DexDev005',
  '8DexCS006',
  '8DexCS007',
  '8DexCS008',
  '8DexCS009',
  '8DexCS010',
  '8DexDemo011',
  '8DexDemo012',
  '8DexDemo013',
  '8DexDemo014',
  '8DexDemo015',
  '8DexDemo016',
  '8dextest',
  'MarkDemo',
  '8dexdemo'
]
export const WHITECLIFF_CREDENTIALS = {
  WHITECLIFF_URL: 'WHITECLIFF_URL',
  WHITECLIFF_GAME_LIST_URL: 'WHITECLIFF_GAME_LIST_URL',
  WHITECLIFF_USER_TOKEN: 'whitecliff_user_token',
  WHITECLIFF_USER_ID: 'whitecliff_user_id',
  WHITECLIFF_HOME_REDIRECTION_URL: 'WHITECLIFF_HOME_REDIRECTION_URL',
  INR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_INR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_INR'
  },
  EUR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_EUR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_EUR'
  },
  USD: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_USD',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_USD'
  },
  LKR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_LKR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_LKR'
  }
}

export const THANK_YOU_MESSAGE = (userName) => `We are pleased to inform you that your registration has been successfully completed. Your username is ${userName}.\nPlease use this username for logging into your account. If you have any questions or need assistance, feel free to contact our support team.\nThank you for joining.`

export const TENANT_WELCOME_MESSAGES = {
  24: {
    message: "Welcome to Sportway! Thank you for Registering. Get a 100% bonus on your first deposit. Don't miss out! Click here to claim https://www.sportway.live/promotion/bonus/deposit/DEPOSITNEW Play fair, Play safe! +***********"
  },
  53: {
    message: "Welcome to Sportway! Thank you for Registering. Get a 100% bonus on your first deposit. Don't miss out! Click here to claim https://www.sportway.lk/promotion/bonus/deposit/DEPOSITNEW Play fair, Play safe! +***********"
  }
}

export const UNABLE_TO_SEND_THANK_YOU_MESSAGE = 'Unable to send thank you message'

export const MULTI_BONUS_STATUS = {
  PENDING: 0,
  COMPLETED: 1,
  FAILED: 2
}

export const DEPOSIT_BONUS_TYPE = {
  RECURRING: 'recurring',
  INSTANT: 'instant'
}
export const LOTTERY_GAMES_CREDENTIALS = {
  LOTTERY_GAMES_URL: 'LOTTERY_GAMES_URL', //
  LOTTERY_GAMES_USER_TOKEN: 'lottery_games_user_token',
  INR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_INR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_INR'
  },
  EUR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_EUR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_EUR'
  },
  USD: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_USD',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_USD'
  },
  LKR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_LKR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_LKR'
  }
}

export const TURBO_STARS_CREDENTIALS = {
  TURBO_STARS_IFRAME_URL: 'TURBO_STARS_IFRAME_URL', //
  TURBO_STARS_LOCALE: 'TURBO_STARS_LOCALE',
  TURBO_STARS_API_KEY: 'TURBO_STARS_API_KEY',
  TURBO_STARS_PARENT: 'TURBO_STARS_PARENT',
  TURBO_STARS_SUB_PARTNER_ID: 'TURBO_STARS_SUB_PARTNER_ID',
  TURBO_STARS_CUSTOM_STYLE_URL: 'TURBO_STARS_CUSTOM_STYLE_URL',
  TURBO_STARS_USER_TOKEN: 'TURBO_STARS_USER_TOKEN',
  TURBO_STARS_SECRET_KEY: 'TURBO_STARS_SECRET_KEY',
  TURBO_STARS_S2S_API_URL: 'TURBO_STARS_S2S_API_URL'
}

export const REFERRAL_BLOCK_TYPE = {
  AGENT: 1,
  USER: 2
}

export const LIMIT_RANGE = {
  DAILY: 'DAILY',
  WEEKLY: 'WEEKLY',
  MONTHLY: 'MONTHLY'
}

export const SPORT_CASINO_TXN_TYPE = {
  EXCHANGE_PLACE_BET_NON_CASH_DEBIT: 20,
  EXCHANGE_PLACE_BET_CASH_DEBIT: 21,
  EXCHANGE_PLACE_BET_CASH_CREDIT: 22,
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_DEBIT: 23,
  EXCHANGE_REFUND_CANCEL_BET_CASH_DEBIT: 24,
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_CREDIT: 25,
  EXCHANGE_REFUND_CANCEL_BET_CASH_CREDIT: 26,
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_DEBIT: 27,
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_DEBIT: 28,
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_CREDIT: 29,
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_CREDIT: 30,
  EXCHANGE_SETTLE_MARKET_CASH_CREDIT: 31,
  EXCHANGE_SETTLE_MARKET_CASH_DEBIT: 32,
  EXCHANGE_RESETTLE_MARKET_CASH_CREDIT: 33,
  EXCHANGE_RESETTLE_MARKET_CASH_DEBIT: 34,
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_CREDIT: 35,
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_DEBIT: 36,
  EXCHANGE_DEPOSIT_BONUS_CLAIM: 37,
  EXCHANGE_NON_CASH_DEPOSIT_BONUS_CLAIM: 74,
  EXCHANGE_ADJUST_SETTLE_MARKET_CASH_CREDIT: 53,
  EXCHANGE_ADJUST_SETTLE_MARKET_CASH_DEBIT: 54,
  EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_DEBIT: 55,
  EXCHANGE_CANCEL_SETTLE_BET_CASH_DEBIT: 56,
  EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_CREDIT: 57,
  EXCHANGE_CANCEL_SETTLE_BET_CASH_CREDIT: 58
}

export const WHITECLIFF_PROVIDER_ID = {
  STAGE: '5100',
  PROD: '213'
}

export const UUWALLET_PG_INTEGRATION_CONSTANT = {
  INVALID_AMOUNT: 'Amount is invalid',
  DAILY_DEPOSIT_LIMIT: 'Daily',
  WEEKLY_DEPOSIT_LIMIT: 'Weekly',
  MONTHLY_DEPOSIT_LIMIT: 'Monthly',
  DEPOSIT_LIMIT_EXCEED: 'Deposit Limit exceeded',
  PAYMENT_PROVIDER_NOT_FOUND: 'No Payment Provider Found',
  DEPOSIT_TYPE: 'payment_providers',
  SUCCESS_STATUS_CODE: 0,
  PURPOSE: 'Merchant Payment',
  CRYPTO_DEPOSIT_PERMISSION_DENIED: 'Crypto deposit permission denied'
}

export const UUWALLET_PG_RESPONSE_CODES = {
  SUCCESS: 0
}

export const UUWALLET_PG_RESPONSE = {
  ENCRYPTION_FAILED: { code: 4, message: 'ENCRYPTION FAILED' },
  DECRYPTION_FAILED: { code: 5, message: 'DECRYPTION FAILED' }
}

export const IMPERSONATED_USER_EXPIRY_TIME = '1d'

export const SVG_CAPTCHA_OPTIONS = {
  size: 6,
  noise: 5,
  background: '#ddeeff',
  color: true,
  width: 300,
  height: 300,
  ignoreChars: '0oO1lI'
}

export const CAPTCHA_VERIFICATION_STATUS = {
  UNVERIFIED: 0,
  VERIFIED: 1,
  USED: 2
}

export const SPINOCCHIO_CREDENTIALS = {
  SPINOCCHIO_URL: 'SPINOCCHIO_URL',
  SPINOCCHIO_ID: 'SPINOCCHIO_ID',
  SPINOCCHIO_SECRET: 'SPINOCCHIO_SECRET',
  SPINOCCHIO_GAMES_USER_TOKEN: 'spinocchio_games_user_token'
}

export const SPINOCCHIO_USER_TYPE = {
  REAL: 'REAL',
  DEMO: 'DEMO'
}

export const BONUS_RECURRING_STATUS = {
  ACTIVE: 0,
  CLAIMED: 1,
  CANCELLED: 2,
  EXPIRED: 3
}

export const CHATBOT_TYPE = {
  ZOVI: 'zovi',
  TWAK: 'twak',
  WATI: 'mobile'
}

export const AFFILIATE = {
  ALANBASE: 1,
  WYNTA: 2
}

export const DEFAULT_MODULE_PERMISSIONS = {
  manual_deposit: ['C'],
  online_deposit: ['C'],
  virtual_deposit: ['C'],
  qr_code_deposit: ['C'],
  crypto_deposit: ['C'],
  manual_withdraw: ['C', 'R', 'CX'],
  deposit_request_list: ['R'],
  gameplay: ['casino', 'sports']
}

export const USER_SPECIFIC_PERMISSIONS_MODULES = {
  MANUAL_DEPOSIT: 'manual_deposit',
  ONLINE_DEPOSIT: 'online_deposit',
  QRCODE_DEPOSIT: 'qr_code_deposit',
  CRYPTO_DEPOSIT: 'crypto_deposit',
  MANUAL_WITHDRAW: 'manual_withdraw',
  DEPOSIT_REQUEST_LIST: 'deposit_request_list',
  VIRTUAL_DEPOSIT: 'virtual_deposit',
  GAMEPLAY: 'gameplay'
}

export const USER_SPECIFIC_PERMISSIONS_ACTION = {
  CREATE: 'C',
  READ: 'R',
  UPDATE: 'U',
  DELETE: 'D',
  CANCEL: 'CX',
  CASINO: 'casino',
  SPORTS: 'sports'
}

export const USER_LOGIN_TYPES = {
  DEFAULT: 0,
  GOOGLE: 1,
  RFID: 2,
  QRCODE: 3,
  GAME_LAUNCH: 4,
  RECONNECT: 5,
  APP_LAUNCH: 6,
  WITHDRAW: 7
}

export const QR_ENCRYPTION_KEY = 'Pz9Qw!vX@cN2*aKc$1Re^YgHpT4u#oEM'
export const QR_ENCRYPTION_IV_LENGTH = 16

export const USER_TYPES = {
  ONLINE: 1,
  KIOSK: 2,
  KIOSK_AND_ONLINE: 3,
  GUEST: 4
}

export const SPORTS_PROVIDERS_STAGE = {
  JETFAIR: 14,
  POWERPLAY: 5035,
  TURBOSTARS: 5133
}

export const SPORTS_PROVIDER_PROD = {
  JETFAIR: 15,
  POWERPLAY: 50,
  TURBOSTARS: 181
}

export const SPORTS_TRXN_CODES = {
  PLACE_BET_TXN_CODE: 'PlaceMatchedBet',
  CANCEL_BET_TXN_CODE: 'CancelMatchedBet',
  SETTLE_MARKET_TXN_CODE: 'SettledMarket',
  MARKET_CANCEL_TXN_CODE: 'CancelMarket',
  CANCEL_SETTLED_MARKET_TXN_CODE: 'CancelSettledMarket',
  RESETTLE_TXN_CODE: 'ResettleMarket',
  UNSETTLE_TXN_CODE: 'UnsettleBet',
  ADJUST_SETTLED_MARKET_TXN_CODE: 'AdjustSettledMarket',
  BET_SLIP_STATUS_ACCEPTED: 'accepted',
  IN_GAME: 'in_game',
  PLACE_BET_DESCRIPTION: 'Place Bet Desc',
  PLACE_BET: 1,
  CANCEL_BET: 4,
  SETTLE_MARKET_WON: 2,
  SETTLE_MARKET_LOST: 5,
  MARKET_CANCEL_CREDIT: 4,
  MARKET_CANCEL_DEBIT: 5,
  CANCEL_SETTLED_MARKET: 5,
  UNSETTLE_BET_CREDIT: 4,
  UNSETTLE_BET_DEBIT: 5,
  RESETTLE: 6,
  PLACE_BET_CANCEL: 9,
  ADJUST_SETTLED_MARKET_WON: 10,
  ADJUST_SETTLED_MARKET_LOST: 11,
  SETTLE_BET: 12,
  CREDIT: 'CR',
  DEBIT: 'DR',
  SETTLE_MARKET_WON_DESCRIPTION: 'settle market won',
  SETTLE_MARKET_LOST_DESCRIPTION: 'settle market lost'
}

export const SPORTS_BET_TYPE = {
  SINGLE: 1,
  COMBO: 2
}
export const TURBO_GAMES_CREDENTIALS = {
  TURBO_GAMES_IFRAME_URL: 'TURBO_GAMES_IFRAME_URL', //
  TURBO_GAMES_LOCALE: 'TURBO_GAMES_LOCALE',
  TURBO_GAMES_SUB_PARTNER_ID: 'TURBO_GAMES_SUB_PARTNER_ID',
  TURBO_GAMES_USER_TOKEN: 'TURBO_GAMES_USER_TOKEN',
  TURBO_GAMES_SECRET_KEY: 'TURBO_GAMES_SECRET_KEY',
  TURBO_GAMES_CID: 'TURBO_GAMES_CID'
}
export const PIGABOOM_CREDENTIALS = {
  PIGABOOM_IFRAME_URL: 'PIGABOOM_IFRAME_URL', //
  PIGABOOM_BRAND_ID: 'PIGABOOM_BRAND_ID',
  PIGABOOM_USER_TOKEN: 'PIGABOOM_USER_TOKEN'
}

export const PIGABOOM_PROVIDER_ID = {
  STAGE: 5265,
  PROD: 312
}

export const RATE_LIMIT_CONFIG = {
  RATE_LIMIT_WINDOW: 10, // 10 seconds
  MAX_ATTEMPTS: 3,
  CAPTCHA_EXPIRY: 45 // 45 seconds
}

export const OTP_TYPES = {
  RESET_PASSOWRD: 'resetPassword',
  RESET_LOGIN_PIN: 'resetLoginPin',
  PHONE: 'phone',
  EMAIL: 'email'
}

export const OTP_ATTEMPTS_TYPES = {
  GENERATE_OTP: 'generateOtp',
  VERIFY_OTP: 'verifyOTP'
}

export const DEFAULT_OTP_GENERATE_ATTEMPT = 4
export const DEFAULT_OTP_GENERATE_ATTEMPT_EXPIRY_TIME = 5

export const DEFAULT_OTP_VERIFY_ATTEMPT = 4
export const DEFAULT_OTP_VERIFY_ATTEMPT_EXPIRY_TIME = 5

export const EMAIL_PROVIDERS = {
  SENDGRID: 0,
  MAILGUN: 1
}

export const EMAIL_PROVIDERS_CREDENTIAL_KEYS = {
  [EMAIL_PROVIDERS.SENDGRID]: [
    'APP_SENDGRID_HOST',
    'APP_SENDGRID_PORT',
    'APP_SENDGRID_USERNAME',
    'APP_SENDGRID_RELAY_KEY',
    'APP_SENDGRID_EMAIL'
  ],
  [EMAIL_PROVIDERS.MAILGUN]: [
    'MAILGUN_DOMAIN',
    'MAILGUN_FROM_EMAIL',
    'MAILGUN_FROM_USERNAME',
    'MAILGUN_ENDPOINT',
    'MAILGUN_API_KEY',
    'MAILGUN_REPORT_EMAIL'
  ]
}

export const USER_CREATION_TYPE = {
  SIGNUP: 0,
  ONE_CLICK: 1,
  ADMIN_CREATED: 2,
  GOOGLE: 3,
  FACEBOOK: 4
}

export const AUDIT_LOG_ACTIONEE_TYPE = {
  ADMIN: 0,
  USER: 1,
  SUPER_ADMIN: 2
}
