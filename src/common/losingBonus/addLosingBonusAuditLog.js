import db from '../../db/models'

export const addLosingBonusAuditLog = async (data) => {
  try {
    // Insert the audit log entry
    const auditLogEntry = await db.AuditLog.create({
      tenantId: data.tenantId,
      actioneeId: data.actioneeId,
      eventType: data.eventType || 'player',
      event: data.event || 'Cashback Bonus Claim',
      eventId: data.actioneeId,
      description: data.description || 'Cashback bonus Error',
      action: data.action || 'cashback validation',
      actioneeIp: data.actioneeIp || '',
      previousData: data.previousData || {},
      modifiedData: data.modifiedData || {},
    })

    return auditLogEntry
  } catch (error) {
    console.error('Error adding audit log entry:', error)
    throw new Error('Failed to add audit log entry')
  }
}
