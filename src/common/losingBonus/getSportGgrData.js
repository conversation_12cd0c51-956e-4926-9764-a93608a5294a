import { isEmpty } from 'lodash'
import { Op } from 'sequelize'
import db, { sequelize } from '../../db/models'

export const getSportGgrData = async (data) => {
  const userId = data.userId
  const tenantId = data.tenantId
  const startDate = data.startDate
  const endDate = data.endDate
  const dateObj = data.dateObj
  const auditErrorLog = data.auditErrorLog

  // sport calculation start
  const query = `select
  (ROUND(
    CAST(
        SUM(
            CASE
                WHEN bt.journal_entry = 'CR' THEN COALESCE(bt.amount,0) + COALESCE(bt.non_cash_amount,0)
                ELSE 0
            END
        ) AS NUMERIC
    ), 5
  ) -
   ROUND(
    CAST(
        SUM(
            CASE
                WHEN bt.journal_entry = 'DR' THEN COALESCE(bt.amount,0) + COALESCE(bt.non_cash_amount,0)
                ELSE 0
            END
        ) AS NUMERIC
    ), 5
  )) as amount
    from bets_transactions as bt
   where user_id=:userId AND tenant_id=:tenantId
   AND bt.market_id IN(
    select distinct(market_id) from bets_transactions
     where user_id=:userId AND tenant_id=:tenantId
     AND transaction_code IN ('SettledMarket','ResettleMarket','CancelMarket')
     AND (created_at>=:startDate AND created_at<=:endDate)
    );`
  const losingAmountResults = await sequelize.query(query, {
    replacements: { userId, tenantId, startDate, endDate },
    type: sequelize.QueryTypes.SELECT,
    useMaster: false
  })
  const sportGgrAmount = losingAmountResults?.[0]?.amount
  // sport calculation end

  // st8 sport calculation start
  const debitCreditIds = await db.Transaction.findAll({
    attributes: ['debitTransactionId'],
    where: {
      targetWalletId: data.userWallet.id,
      tenantId,
      transactionType: { [Op.in]: [1, 9] },
      gameId: { [Op.in]: ['sbs_sportsbook', 'bti_sportsbook', 'sap_lobby'] },
      ...dateObj
    },
    raw: true
  })

  const userWalletId = data.userWallet.id
  let totalBetSumResults
  if (isEmpty(debitCreditIds)) {
    totalBetSumResults = 0
  } else {
    const uniqueDebitTransactionIds = [...new Set(debitCreditIds.map(item => item.debitTransactionId))]
    const uniqueDebitTransactionIdsString = '(' + uniqueDebitTransactionIds.map(id => `'${id}'`).join(', ') + ')'
    const totalBetSumQuery = `SELECT SUM(amount)
    FROM transactions
    WHERE source_wallet_id = ${userWalletId}
    AND tenant_id = ${tenantId}
    AND transaction_type IN(0,8,46)
    AND game_id IN ('sbs_sportsbook', 'bti_sportsbook', 'sap_lobby')
    AND (transaction_id IN ${uniqueDebitTransactionIdsString})
    `
    totalBetSumResults = await sequelize.query(totalBetSumQuery, {
      type: sequelize.QueryTypes.SELECT
    })
    totalBetSumResults = totalBetSumResults?.[0]?.sum
  }

  const symbols = Object.getOwnPropertySymbols(dateObj.createdAt)
  const betStartDate = dateObj.createdAt[symbols[0]]
  const betEndDate = dateObj.createdAt[symbols[1]]

  const totalWinSumQuery = `SELECT SUM(amount)
  FROM transactions
  WHERE target_wallet_id  = ${userWalletId}
  AND amount > 0
  AND tenant_id = ${tenantId}
  AND created_at >= '${betStartDate}'
  AND created_at <= '${betEndDate}'
  AND transaction_type IN(1,9)
  AND game_id IN ('sbs_sportsbook', 'bti_sportsbook', 'sap_lobby')
  `

  let totalWinSumResults = await sequelize.query(totalWinSumQuery, {
    type: sequelize.QueryTypes.SELECT
  })
  totalWinSumResults = totalWinSumResults?.[0]?.sum ? totalWinSumResults[0].sum : 0
  const st8GgrAmount = totalWinSumResults - totalBetSumResults
  // st8 sport calculation end
  const total = parseFloat(sportGgrAmount || 0) + parseFloat(st8GgrAmount || 0)
  if (total < 0) {
    auditErrorLog.previousData.achievedLosingAmount = Math.abs(total)
    return [Math.abs(total), auditErrorLog]
  } else {
    auditErrorLog.previousData.achievedLosingAmount = 0
    return [false, auditErrorLog]
  }
}
