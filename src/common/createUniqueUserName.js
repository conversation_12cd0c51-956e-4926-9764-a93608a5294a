import { Op, Sequelize } from 'sequelize'
import <PERSON>rror<PERSON>ogHelper from './errorLog'

export default async (email, UserModel, tenantId) => {
  try {
    let baseUserName = email.split('@')[0].replace(/[^\w]/g, '').toLowerCase().slice(0, 24)
    if (baseUserName.length < 5) baseUserName = baseUserName.padEnd(5, 'a')
    let userName = baseUserName
    let suffix = 1
    while (true) {
      const user = await UserModel.findOne({
        attributes: ['id'],
        where: { tenantId,
          [Op.and]: [
            Sequelize.where(Sequelize.fn('LOWER', Sequelize.col('user_name')), userName.toLowerCase())
          ]
         }
      })
      const isDigitsOnly = /^[0-9]+$/.test(userName)
      const hasSpecialChars = /[!@#$%^&*()_+{}\[\]:;<>,.?~\\]/.test(userName)

      if (!user && !isDigitsOnly && !hasSpecialChars) {
        return userName
      }
      userName = `${baseUserName.slice(0, 24 - String(suffix).length)}${suffix++}`;
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
    throw error
  }
}
