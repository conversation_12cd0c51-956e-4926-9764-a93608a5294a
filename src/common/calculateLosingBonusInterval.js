import { LOSING_BONUS_TYPES } from './constants'

/**
 * This function will help to calculate valid dates based on interval type.
 * @export
 * @param {ActivationDate} activationDate contains activation date
 * @param {CurrentDate} currentDate contains current claiming date
 * @param {BonusType} bonusType contains type of interval daily, weekly, biweekly, monthly
 * @param {LastClaimDate} lastClaimDate contains bonus last claim date
 * @param {ValidFrom} validFrom contains bonus valid From date
 * @return {Result} boolean
 */
export default (activationDate, currentDate, bonusType, lastClaimDate, validFrom) => {
  const millisecondsInADay = 24 * 60 * 60 * 1000
  const daysSinceActivation = Math.floor((currentDate - validFrom) / millisecondsInADay)
  const duration = LOSING_BONUS_TYPES[bonusType]
  const daysToAdd = (Math.floor(daysSinceActivation / duration)) * duration
  let isBonusClaimed = false
  let startDate
  if (daysToAdd) {
    validFrom.setDate(validFrom.getDate() + daysToAdd)
    startDate = validFrom <= activationDate ? activationDate : validFrom
    isBonusClaimed = lastClaimDate && validFrom <= lastClaimDate
  } else {
    isBonusClaimed = activationDate && validFrom <= lastClaimDate
  }

  if (isBonusClaimed) {
    return null
  }
  return { start: daysToAdd ? startDate : activationDate, end: currentDate }
}
