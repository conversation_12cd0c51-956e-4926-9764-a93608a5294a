import { Op } from 'sequelize'
/**
 * This function will reduce the rollover target for the user as it places the bet.
 * @export
 * @param {object} context The argument object
 * @param {Transaction} transactionObject The argument object
 * @param {User} user contains user object
 * @return {TransactionObject}
 */
export default async (context, transactionObject, userWallet, amountMain) => {
  const {
    Currency: CurrencyModel,
    TenantConfiguration: TenantConfigurationsModel
  } = context.databaseConnection
  const { id: tenantId } = context.tenant

  const currencyAllowedCurrencies = await TenantConfigurationsModel.findOne({
    where: { tenantId },
    attributes: ['allowedCurrencies']
  })
  const currencyAllAllowed = await CurrencyModel.findAll({
    where: { id: { [Op.in]: currencyAllowedCurrencies?.allowedCurrencies?.split(',') } }
  })

  const exMainTransactionCurrency = userWallet.currencyId
  const otherCurrencyAmount = {}
  const [currencyExchangeRate] = currencyAllAllowed.filter((item) => {
    return item.id === exMainTransactionCurrency
  })

  for (const record of currencyAllAllowed) {
    if (exMainTransactionCurrency !== record.id) {
      const convertedAmountOther = (parseFloat(amountMain) * (record.exchangeRate / currencyExchangeRate.exchangeRate)).toFixed(4)
      otherCurrencyAmount[record.code] = parseFloat(convertedAmountOther)
    } else {
      otherCurrencyAmount[record.code] = parseFloat(amountMain)
    }
  }
  transactionObject = { ...transactionObject, otherCurrencyAmount: JSON.stringify(otherCurrencyAmount) }
  return transactionObject
}
