import { sequelize } from '../db/models'
/**
 * This function will reduce the rollover target for the user as it places the bet.
 * @export
 * @param {object} context The argument object
 * @param {Transaction} transactionObject The argument object
 * @param {User} user contains user object
 * @return {TransactionObject}
 */

export default async (context, transactionObject, userWallet, amountMain) => {
  const {
    sequelizeTransaction
    // sequelize
  } = context

  const { id: tenantId } = context.tenant

  const sql = 'SELECT * FROM currency_view WHERE tenant_id = :tenantId;'

  const currencyAllAllowed = await sequelize.query(sql, {
    replacements: { tenantId: tenantId },
    type: sequelize.QueryTypes.SELECT,
    transaction: sequelizeTransaction,
    useMaster: false
  })

  const exMainTransactionCurrency = userWallet.currencyId

  const currencyExchangeRate = currencyAllAllowed.find(i => i.id === exMainTransactionCurrency)
  const otherCurrencyAmount = currencyAllAllowed.reduce((acc, i) => {
    if (i.id !== exMainTransactionCurrency) {
      acc[i.code] = parseFloat(
        (parseFloat(amountMain) * (i.exchange_rate / currencyExchangeRate.exchange_rate)).toFixed(4)
      )
    } else {
      acc[i.code] = parseFloat(amountMain)
    }
    return acc
  }, {})

  transactionObject = { ...transactionObject, otherCurrencyAmount: JSON.stringify(otherCurrencyAmount) }
  return transactionObject
}
