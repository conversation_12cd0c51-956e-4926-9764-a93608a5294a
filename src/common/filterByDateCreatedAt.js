import { Op } from 'sequelize'
export const filterByDateCreatedAt = (query, startDate = null, endDate = null) => {
  endDate = endDate || Date.now()
  if (startDate) {
    query = {
      ...query,
      createdAt: {
        [Op.and]: {
          [Op.gte]: `${(new Date(startDate)).toISOString().substring(0, 10)} 00:00:00.000+00`,
          [Op.lte]: `${(new Date(endDate)).toISOString().substring(0, 10)} 23:59:59.999+00`
        }
      }
    }
  } else {
    query = {
      ...query,
      [Op.or]: {
        createdAt: { [Op.lte]: `${(new Date(endDate)).toISOString().substring(0, 10)} 23:59:59.999+00` }
      }
    }
  }
  return query
}
