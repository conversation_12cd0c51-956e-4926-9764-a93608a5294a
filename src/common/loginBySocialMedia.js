import { checkBlackList } from '../common/checkBlackList'
import { ALLOWED_PERMISSIONS, ERORR_TYPE, SUBSCRIPTION_CHANNEL } from '../common/constants'
import translate from '../lib/languageTranslate'
import <PERSON><PERSON>r<PERSON><PERSON><PERSON>elper from './errorLog'
import mobileLogin from './mobileLogin'

const loginBySocialMedia = async (context, args, userData, user, loginType) => {
  try {
    const {
      databaseConnection: {
        TenantThemeSetting: TenantThemeSettingModel,
        QueueLog: QueueLogModel,
        Blacklist: BlacklistModel,
        SocialMediaLogin: SocialMediaLoginModel
      },
      tenant: { id: tenantId },
      req: { headers: { language } },
      sequelizeTransaction
    } = context

    const { ip } = args
    const checkBlackListUser = await checkBlackList('', '', userData?.email, '', tenantId, BlacklistModel, ip)
    if (checkBlackListUser) {
      throw { errorType: ERORR_TYPE.CUSTOM, errorMsg: translate('BLACK_LISTED', language) }
    }

    if (!user.active) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('USER_ACCOUNT_DEACTIVATED', language) }
    }

    const tenantTheme = await TenantThemeSettingModel.findOne({
      where: {
        tenantId
      },
      attributes: ['allowedModules']
    })

    const profileVerifiedPermissionCheck = !!tenantTheme?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.PROFILE_VERIFIED)
    if (profileVerifiedPermissionCheck && !user.profileVerified) {
      throw { errorType: ERORR_TYPE.AUTHRNTICATION, errorMsg: translate('PROFILE_NOT_ACTIVE', language) }
    }

    let bulkData = []
    const joiningFlag = true // as it is given at the time of signup
    const responseObj = await mobileLogin(context, user.id, joiningFlag, args, bulkData, loginType)

    const createdRecords = await QueueLogModel.bulkCreate(bulkData)
    if (createdRecords) {
      try {
        createdRecords.forEach(record => {
          context.pubSub.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, {
            QueueLog: { queueLogId: record.id }
          })
        })
      } catch (error) {
        await ErrorLogHelper.logError(error, context, user)
      }
    }

    const [socialMediaLogin, created] = await SocialMediaLoginModel.findOrCreate({
      where: { socialMediaUserId: userData?.socialMediaUserId, loginType },
      defaults: {
        userId: user?.id,
        tenantId,
        socialMediaUserId: userData?.socialMediaUserId,
        loginType,
        signInCount: 1
      },
      transaction: sequelizeTransaction
    })
    if (!created) {
      // If the record already exists, increment the signInCount
      await socialMediaLogin.increment('signInCount', { transaction: sequelizeTransaction })
    }

    return { token: responseObj.token, user: responseObj.user, resToken: responseObj.resToken, success: true }
  } catch (error) {
    throw error
  }
}

export { loginBySocialMedia }
