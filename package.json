{"name": "user-backend-api", "version": "1.0.0", "description": "", "main": "./dist/index.js", "scripts": {"start": "node --stack_size=100000 ./dist/index.js", "babel-node": "babel-node --inspect=0.0.0.0:9229", "format": "standard --fix", "start:dev": "APP_SECRET=notronisca nodemon --exec npm run babel-node -- ./index.js", "test": "echo \"Error: no test specified\" && exit 1", "doc": "./node_modules/.bin/jsdoc -c ./conf.json"}, "husky": {"hooks": {"pre-commit": "lint-staged --allow-empty"}}, "lint-staged": {"src/**/*.js": ["npm run format"]}, "author": "", "license": "ISC", "engines": {"node": "12.13.1"}, "dependencies": {"@elastic/elasticsearch": "7.11.0", "@faker-js/faker": "8.4.1", "apollo-server-express": "2.19.0", "aws-sdk": "2.934.0", "axios": "^0.20.0", "bcrypt": "3.0.8", "body-parser": "1.19.0", "bull": "3.12.1", "circular-json": "0.5.9", "compression": "1.7.5", "convict": "5.2.0", "cors": "2.8.5", "crypto-js": "4.0.0", "dataloader": "2.0.0", "dataloader-sort": "0.0.5", "dotenv": "8.2.0", "easytimer.js": "4.3.0", "express": "4.17.1", "faker": "5.4.0", "form-data": "^4.0.2", "graphql": "14.5.8", "graphql-directive-auth": "0.3.2", "graphql-redis-subscriptions": "2.1.2", "i18n": "0.13.2", "jsonwebtoken": "8.5.1", "jsrsasign": "10.8.6", "lodash": "4.17.15", "mailgun.js": "^9.4.1", "md5": "2.3.0", "moment": "2.24.0", "moment-timezone": "0.5.28", "node-cron": "3.0.0", "node-html-parser": "6.1.13", "node-rsa": "1.1.1", "nodemailer": "6.4.15", "number-precision": "1.5.1", "pg": "7.14.0", "pg-hstore": "2.3.3", "probability-distributions": "0.9.1", "redis": "4.0.6", "sequelize": "5.21.2", "svg-captcha": "1.4.0", "twilio": "3.51.0", "uuid": "^8.3.2", "validate.js": "0.13.1", "winston": "3.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/cli": "7.7.4", "@babel/core": "7.7.4", "@babel/node": "7.7.4", "@babel/plugin-proposal-optional-chaining": "7.12.1", "@babel/preset-env": "7.7.4", "babel-eslint": "10.0.3", "eslint": "7.12.1", "husky": "4.3.0", "jsdoc": "3.6.6", "lint-staged": "10.5.1", "nodemon": "2.0.1", "sequelize-cli": "5.5.1", "standard": "14.3.1"}, "standard": {"parser": "babel-es<PERSON>", "env": {"jest": true, "browser": true, "node": true}}}