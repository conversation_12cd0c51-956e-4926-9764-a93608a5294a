stages:
  - versioning

version_update:
  stage: versioning
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest

  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://gitlab.com

  only:
    - master
    - staging

  variables:
    AWS_REGION: $AWS_DEFAULT_REGION
    ENVIRONMENT: $CI_COMMIT_BRANCH

  before_script:
    - |
      if [[ "$CI_COMMIT_BRANCH" == "master" ]]; then
        export CI_ENVIRONMENT_NAME="production"
      elif [[ "$CI_COMMIT_BRANCH" == "staging" ]]; then
        export CI_ENVIRONMENT_NAME="staging"
      else
        echo "Unsupported branch: $CI_COMMIT_<PERSON>ANCH"
        exit 0
      fi
    - echo "$GITLAB_OIDC_TOKEN" > /tmp/gitlab-token
    - |
      CREDS=$(aws sts assume-role-with-web-identity \
        --role-arn "$AWS_ROLE_ARN" \
        --role-session-name "gitlab-oidc-session" \
        --web-identity-token file:///tmp/gitlab-token \
        --duration-seconds 900)
    - export AWS_ACCESS_KEY_ID=$(echo "$CREDS" | jq -r '.Credentials.AccessKeyId')
    - export AWS_SECRET_ACCESS_KEY=$(echo "$CREDS" | jq -r '.Credentials.SecretAccessKey')
    - export AWS_SESSION_TOKEN=$(echo "$CREDS" | jq -r '.Credentials.SessionToken')
    - aws configure set region "$AWS_REGION"
  script:
    - |
      echo "🔁 ENVIRONMENT: $ENVIRONMENT"

      if [[ "$ENVIRONMENT" == "master" ]]; then
        S3_BUCKET="$S3_BUCKET_PRODUCTION"
      elif [[ "$ENVIRONMENT" == "staging" ]]; then
        S3_BUCKET="$S3_BUCKET_STAGING"
      else
        echo "❌ Unknown environment: $ENVIRONMENT"
        exit 1
      fi

      if [[ -z "$S3_BUCKET" ]]; then
        echo "❌ S3_BUCKET is not defined for environment: $ENVIRONMENT"
        exit 1
      fi

      S3_KEY="app/version_be.json"
      VERSION_FILE="version_be.json"
      S3_PATH="s3://${S3_BUCKET}/${S3_KEY}"

      echo "📥 Downloading version file from $S3_PATH..."
      if ! aws s3 cp "$S3_PATH" "$VERSION_FILE"; then
        echo '{"major":1,"minor":0,"patch":0,"commit":""}' > "$VERSION_FILE"
        echo "⚠️ version_be.json not found, initializing to v1.0.0"
      fi

      COMMIT_SHA="$CI_COMMIT_SHA"
      STORED_COMMIT=$(jq -r '.commit // empty' "$VERSION_FILE")

      if [[ "$STORED_COMMIT" == "$COMMIT_SHA" ]]; then
        echo "⏭️ Version already bumped for commit $COMMIT_SHA. Skipping..."
        exit 0
      fi

      MAJOR=$(jq -r '.major' "$VERSION_FILE")
      MINOR=$(jq -r '.minor' "$VERSION_FILE")
      PATCH=$(jq -r '.patch' "$VERSION_FILE")

      PATCH=$((PATCH + 1))
      if [ "$PATCH" -ge 1000 ]; then
        MINOR=$((MINOR + 1))
        PATCH=0
      fi
      if [ "$MINOR" -ge 100 ]; then
        MAJOR=$((MAJOR + 1))
        MINOR=0
      fi

      jq -n --argjson major "$MAJOR" \
            --argjson minor "$MINOR" \
            --argjson patch "$PATCH" \
            --arg commit "$COMMIT_SHA" \
            '{major: $major, minor: $minor, patch: $patch, commit: $commit}' > "$VERSION_FILE"

      VERSION="$MAJOR.$MINOR.$PATCH"
      echo "✅ New version: $VERSION"

      echo "📤 Uploading updated version_be.json to $S3_PATH ..."
      aws s3 cp "$VERSION_FILE" "$S3_PATH" --acl public-read
