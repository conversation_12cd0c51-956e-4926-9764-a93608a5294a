import { <PERSON><PERSON><PERSON><PERSON>, Apollo<PERSON>erver, gql } from 'apollo-server-express'
import cors from 'cors'
import express from 'express'
import { AuthDirective } from 'graphql-directive-auth'
import { createServer } from 'http'
import { merge } from 'lodash'
import bettingApiRoutes from './src/betting-integration/routes'
import commonRoutes from './src/common-rest-apis/routes'
import decryption from './src/common/decryption'
// import encryption from './src/common/encryption'
import { DEMO_USER_ID, PROD_TENANTS, STAGE_TENANTS, USER_SPECIFIC_PERMISSIONS_ACTION, USER_SPECIFIC_PERMISSIONS_MODULES } from './src/common/constants'
import config from './src/config/app'
import dataLoaders from './src/data-loaders'
import databaseConnection, { sequelize } from './src/db/models'
// import DynamoDbClient from './src/dynamodb-integration'
// import fhonePaisaRoutes from './src/fhonepaisa-integration/routes'
import checkUserPermission from './src/common/checkUserPermission'
import { bonusDef, bonusResolver } from './src/graphql-resources/bonus'
import { bonusCancellationDef, bonusCancellationResolvers } from './src/graphql-resources/bonus-popups'
import { cmsDef, cmsResolver } from './src/graphql-resources/cms'
import { favoriteGameDef, favoriteGameResolvers } from './src/graphql-resources/favorite-games'
import {
  fileDef,
  fileResolvers
} from './src/graphql-resources/file-upload-graphql'
import { globalGameSearchDef, globalGameSearchResolvers } from './src/graphql-resources/global-game-search'
import { notificationDef, notificationResolvers } from './src/graphql-resources/notification'
import { oneClickRegistrationDef, oneClickRegistrationResolvers } from './src/graphql-resources/one-click-registration'
import { promocodeDef, promocodeResolver } from './src/graphql-resources/promocode'
import { referralDef, referralResolver } from './src/graphql-resources/referrals'
import {
  responsibleGamingDef,
  responsibleGamingResolvers
} from './src/graphql-resources/responsible-gaming'
import { sportDef, sportResolvers } from './src/graphql-resources/sports'
import { paymentProvidersResolvers, tppDef } from './src/graphql-resources/tenant-payments-providers'
import { tenantSettingDef, tenantSettingResolvers } from './src/graphql-resources/tenant-setting'
import { threadResolvers, threadTypeDef } from './src/graphql-resources/thread'
import { reportDef, reportResolvers } from './src/graphql-resources/transaction-report'
import { userDef, userResolvers } from './src/graphql-resources/user'
import {
  bankDetailDef,
  bankDetailResolver
} from './src/graphql-resources/user-bank-details'
import {
  transferDef,
  transferResolver
} from './src/graphql-resources/user-fund-transfer'
import jetfairRoutes from './src/jetfair-integration/routes'
import { isUserActive } from './src/lib/isUserActive'
import translate from './src/lib/languageTranslate'
import redisPubSub from './src/lib/redisPubSub'
import paymentIndigrit from './src/payment-aggregator/payment/indigrit/routes'
import paymentNexa from './src/payment-aggregator/payment/nexa/routes'
import paymentRouter from './src/payment-aggregator/payment/routes'
import paymentSky from './src/payment-aggregator/payment/sky/routes'
import paymentWizpay from './src/payment-aggregator/payment/wizpay/routes'
import paymentPayInResponse from './src/payment-aggregator/zovi24/pay-in/common/paymentPayInResponse'
import zoviPayIn from './src/payment-aggregator/zovi24/pay-in/routes'
import turboStarsRoutes from './src/turboStars/routes'

const Sequelize = require('sequelize')
const Op = Sequelize.Op
const schema = gql`
  directive @isAuthenticated on FIELD | FIELD_DEFINITION

  type Query {
    _empty: String
  }
  type Mutation {
    _empty: String
  }
  type Subscription {
    _empty: String
  }
`
const jwt = require('jsonwebtoken')
const compression = require('compression');
const tenantObj = config.get('env') === 'production' ? PROD_TENANTS : STAGE_TENANTS
const catchId = { id: '1' }

// const origin = config.get('origin').split(',')
const origin = Object.keys(tenantObj)
for (const keyorigin in origin) {
    origin[keyorigin] = 'https://www.'+origin[keyorigin]
}
const corsOptions = {
  credentials: true,
  origin: origin,
  methods: ['GET, POST, PUT, PATCH, DELETE']
}


const server = new ApolloServer({
  playground: config.get('env') !== 'production',
  introspection: config.get('env') !== 'production',
  debug: config.get('env') !== 'production',
  typeDefs: [schema, userDef, threadTypeDef, fileDef, responsibleGamingDef, tenantSettingDef, reportDef, bonusDef, transferDef, notificationDef, tppDef, cmsDef, bankDetailDef, promocodeDef, globalGameSearchDef, referralDef, sportDef, favoriteGameDef, oneClickRegistrationDef, bonusCancellationDef],
  resolvers: merge({}, userResolvers, threadResolvers, fileResolvers, responsibleGamingResolvers, tenantSettingResolvers, reportResolvers, bonusResolver, transferResolver, notificationResolvers, paymentProvidersResolvers, cmsResolver, bankDetailResolver, promocodeResolver, globalGameSearchResolvers, referralResolver, sportResolvers, favoriteGameResolvers, oneClickRegistrationResolvers, bonusCancellationResolvers),
  context: async ({ req, res, connection }) => {
    const subscriptionContext = connection?.context

    let tenant
    if (!req) {
      let domain = subscriptionContext.request.headers.origin.split('://').slice(1).join('.').split(':')[0]
      if(domain.split('.').includes('www')){
        const domainArr = domain.split('.')
        domain = domainArr.slice(1).join('.')
      }
      tenant = tenantObj[domain] || catchId
    } else {
      let domainName = req.headers.domain && req.headers.domain !== 'undefined' ? req.headers.domain.split('://').slice(1).join('.') : req.headers.origin.split('://').slice(1).join('.').split(':')[0]
      if(domainName.split('.').includes('www')){
        const domainArr = domainName.split('.')
        domainName = domainArr.slice(1).join('.')
      }
      tenant = tenantObj[domainName] || catchId
      // tenant = await databaseConnection.Tenant.findOne({ where: { domain: req.hostname.split('.').slice(1).join('.'), active: true } })
      // tenant = await databaseConnection.Tenant.findOne({ where: { domain: req.headers.origin.split('://').slice(1).join('.'), active: true } })
    }
    if (!tenant) {
      throw new ApolloError('Invalid Request', 499)
    }
    let authUser = null
    if (req && req.headers.authorization) {
      const isUser = await isUserActive(req.headers.authorization, tenant.id, databaseConnection, false)
      if (!isUser) {
        throw new ApolloError('Expired token', 488)
      }
      authUser = isUser
    }

    return {
      req,
      res,
      databaseConnection,
      tenant,
      reqTimeStamp: Date.now(),
      ...subscriptionContext,
      pubSub: redisPubSub,
      ...dataLoaders(databaseConnection, tenant),
      authUser
    }
  },
  formatError: (error) => {
    return {
      message: error.message,
      reason: error.originalError && error.originalError.reason,
      locations: error.locations,
      code: error?.extensions?.exception?.code,
      path: error.path,
      extensions: error.extensions
    }
  },
  subscriptions: {
    onConnect: (_, __, context) => {
      return context
    }
  },
  schemaDirectives: {
    isAuthenticated: AuthDirective().isAuthenticated
  },
  tracing: config.get('env') !== 'production',
  onHealthCheck: async () => {
    let healthy = true


    const healthCheck = {
      uptime: process.uptime(),
      message: 'OK',
      timestamp: Date.now()
    }

    if (healthy) {
      return healthCheck
    }

    throw new Error('Not Healthy')
  }
})

const app = express()
app.use('/health-checkup', (req, res, next) => {
    res.status(200).json({ message: 'ok' })
})
app.use(compression());
if (process.env.NODE_ENV === 'production') {
    app.use(cors(corsOptions)) //commented this due to deposit callback calling from the unknow origin
}else{
    app.use(cors({ origin: '*' })) // block for not accept the request other origin
}
app.use(async function (req, res, next) {
  try {
    let tanantKeyDomain = ( req.hostname.split('.').slice(1).join('.'))
    if(tanantKeyDomain.split('.').includes('www')){
      let domainArr = tanantKeyDomain.split('.')
      tanantKeyDomain = [domainArr[1], domainArr[2]].join('.')
    }

    const tenantId = tenantObj[tanantKeyDomain] || catchId
    const siteMaintenance = await databaseConnection.TenantSiteMaintenance.findOne({
      where: {
        tenantId: tenantId?.id
      }
    })
    if (siteMaintenance?.siteDown) {
      return res.status(503).json({ error: 502, message: 'Site Maintenance' })
    }
  } catch (e) {
    return res.status(503).json({ error: 502, message: 'Site Maintenance' })
  }
  if (process.env.NODE_ENV === 'production') {
    const apiEndPoint = req?.url?.split('/')[1]
    const endPoint = ['jetfair','betting','app', 'health-checkup', 'deposit-payment', 'zovi24', 'payment']
    if (!endPoint.includes(apiEndPoint)) {
      let result = true
      try {
        const validationRequest = await decryption(req?.headers?.signature)
        if (validationRequest) {
          if (Object.keys(validationRequest.value).length > 0) {
            const keywordHostName = req.hostname.split('.').slice(1).join('.')
            if (validationRequest.value === 'www.' + keywordHostName) {
              result = false
            }
          // const startDate = new Date(validationRequest?.time)
          // // Do your operations
          // const endDate = new Date()
          // const seconds = (endDate.getTime() - startDate.getTime()) / 1000
          // if (seconds > 0 && seconds < 2) {
          //   result = false
          // }
          // result = false
          } else {
            result = true
          }
        } else {
          result = true
        }
      } catch (e) {
        result = true
      }
      if (result) {
        return res.status(400).json({ error: 'invalid request' })
      }
    }
  }
  next()
})

server.applyMiddleware({ app })

app.use(express.static('./src/public'))

app.use(express.urlencoded({ extended: false }))

app.use(express.json())

app.use(async (req, res, next) => {
  let tenant
  try {
    var keyword = (req.hostname.split('.').slice(1).join('.'))
    if(keyword.split('.').includes('www')){
      let domainArr = keyword.split('.')
      keyword = [domainArr[1], domainArr[2]].join('.')
    }
    tenant = tenantObj[keyword] || catchId
  } catch (e) {
    throw new Error(e)
  }
  // const tenant = await databaseConnection.Tenant.findOne({ where: { domain: req.hostname.split('.').slice(1).join('.'), active: true } })
  // if (!tenant) {
  //   res.status(404).end()
  //   throw new ApolloError('Invalid Request')
  // }

  if (req && req.body.token) {
    try {
      await isUserActive(req.body.token, tenant.id, databaseConnection, true)
    } catch (e) {
      throw new Error(e)
    }
  }

  req.databaseConnection = databaseConnection
  req.tenant = tenant
  req.pubSub = redisPubSub
  //req.docClient = DynamoDbClient()
  next()
})
app.use('/zovi24/pay-in', zoviPayIn)
app.use('/payment/indigrit', paymentIndigrit)
app.use('/payment/sky', paymentSky)
app.use('/payment/wizpay', paymentWizpay)
app.use('/payment/nexa', paymentNexa)
app.use('/payment', paymentRouter)

app.post('/deposit-payment', async function (req, res) {
  try {
    var responseObject = paymentPayInResponse(req.body)
    const language = req?.headers?.language
    const demoUserId = config.get('env') === 'production' ? DEMO_USER_ID.PROD : DEMO_USER_ID.STAGE
    const authConfig = config.getProperties().auth
    const secretKey = authConfig.jwt_secret
    const token = req.headers.authorization.split(' ')[1]
    const decodedToken = jwt.verify(token, secretKey)
    const userId = decodedToken.id
    if (demoUserId === userId) {
      responseObject.error = 1
      responseObject.errorDescription = 'You are not allowed to perform this action.'
      responseObject.success = 0
      res.send(417, responseObject)
    }
    if(decodedToken.impersonated){
      responseObject.error = 1
      responseObject.errorDescription = translate('IMPERSONATED_USER_NOT_ALLOWED', language)
      responseObject.success = 0
      res.send(417, responseObject)
    }
    if (req.body.paymentProvider !== '' && req.body.paymentProvider !== undefined) {
      const hasPermission = await checkUserPermission(req.tenant.id, userId, USER_SPECIFIC_PERMISSIONS_MODULES.ONLINE_DEPOSIT, USER_SPECIFIC_PERMISSIONS_ACTION.CREATE)
      if (!hasPermission) {
      responseObject.error = 1
      responseObject.errorDescription = 'ACCESS_DENIED'
      res.send(417, responseObject)

      }
      switch (req.body.paymentProvider) {
        case 'runpay1':
        case 'runpay2':
        case 'runpay3':
        case 'runpay4':
        case 'runpay5':
        case 'runpay6':
        case 'runpay7':
        case 'payIn':
          res.redirect(307, '/zovi24/pay-in/processPayment')
          break
        case 'indigrit':
          res.redirect(307, '/payment/indigrit/processPayment')
          break

        case 'sky': {
          res.redirect(307, '/payment/sky/processPayment')
          break
        }

        case 'wizpay': {
          res.redirect(307, '/payment/wizpay/processPayment')
          break
        }

        case 'nexa': {
          res.redirect(307, '/payment/nexa/processPayment')
          break
        }

        case 'saspay': {
          res.redirect(307, '/payment/saspay/processPayment')
          break
        }

        case 'paywings': {
          res.redirect(307, '/payment/paywings/processPayment')
          break
        }

        case 'sambhavPay': {
          res.redirect(307, '/payment/sambhavPay/processPayment')
          break
        }

        case 'ipay': {
          res.redirect(307, '/payment/ipay/processPayment')
          break
        }

        case 'paycookies2':
        case 'paycookies3':
        case 'paycookies4':
        case 'paycookies': {
          res.redirect(307, '/payment/paycookies/processPayment')
          break
        }

        case 'ackopay': {
          res.redirect(307, '/payment/ackopay/processPayment')
          break
        }

        case 'jaspay': {
          res.redirect(307, '/payment/jaspay/processPayment')
          break
        }

        case 'xamax': {
          res.redirect(307, '/payment/xamax/processPayment')
          break
        }

        case 'cloudCash': {
          res.redirect(307, '/payment/cloudCash/processPayment')
          break
        }

        case 'peerPay1':
        case 'peerPay': {
          res.redirect(307, '/payment/peerPay/processPayment')
          break
        }

        case "zenxpay": {
          res.redirect(307, "/payment/zenxpay/processPayment");
          break;
        }

        case "seylan": {
          res.redirect(307, "/payment/seylan/processPayment");
          break;
        }

        case "techpay": {
          res.redirect(307, "/payment/techpay/processPayment");
          break;
        }
        default:
          responseObject.error = 1
          responseObject.errorDescription = 'Payment Provider not match with any of listed provider.'
          responseObject.success = 0
          res.send(500, responseObject)
          break
      }
    } else {
      responseObject.error = 1
      responseObject.errorDescription = 'Payment Provider cannot be empty.'
      responseObject.success = 0
      res.send(500, responseObject)
    }
  } catch (error) {
    throw new Error(error)
  }
})

// app.use('/ezugi', restApiRoutes)
// app.use('/spribe', spribeRoutes)
// app.use('/fhonePaisa', fhonePaisaRoutes)
app.use('/betting', bettingApiRoutes)
app.use('/jetfair', jetfairRoutes)
app.use('/turboStars', turboStarsRoutes)
app.use('/app', commonRoutes)

app.use('*', (req, res) => { res.status(404).json({ status: 'Not Found' }) })

const httpServer = createServer(app)
server.installSubscriptionHandlers(httpServer)

httpServer.listen({ port: config.get('port') }, () => {
  console.log(`Server ready at port ${config.get('port')}`)
  console.log(`🚀 Subscriptions ready at ws://localhost:${config.get('port')}${server.subscriptionsPath}`)
})

async function handle (signal) {
  try {
    await sequelize.close()
    // await redisPubSub.close()
    console.log(`Received ${signal}`)
  } catch (err) {
    console.log('GraceFull ShutDown Failed', err)
  }
  process.exit(0)
}

process.on('SIGTERM', handle)
process.on('SIGINT', handle)
